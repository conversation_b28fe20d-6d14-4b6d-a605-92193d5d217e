!function(D,y,P){"use strict";var N,k,M=<PERSON>.ht,e=<PERSON><PERSON>,r=e.getInternal(),H=r.addEventListener,O=r.removeEventListener,z=["transitionend","webkitTransitionEnd"],L=null,c=D.parseInt,j=D.isNaN,Z={linear:"cubic-bezier(0.250, 0.250, 0.750, 0.750)",ease:"cubic-bezier(0.250, 0.100, 0.250, 1.000)","ease-in":"cubic-bezier(0.420, 0.000, 1.000, 1.000)","ease-out":"cubic-bezier(0.000, 0.000, 0.580, 1.000)","ease-in-out":"cubic-bezier(0.420, 0.000, 0.580, 1.000)","ease-in-quad":"cubic-bezier(0.550, 0.085, 0.680, 0.530)","ease-in-cubic":"cubic-bezier(0.550, 0.055, 0.675, 0.190)","ease-in-quart":"cubic-bezier(0.895, 0.030, 0.685, 0.220)","ease-in-quint":"cubic-bezier(0.755, 0.050, 0.855, 0.060)","ease-in-sine":"cubic-bezier(0.470, 0.000, 0.745, 0.715)","ease-in-expo":"cubic-bezier(0.950, 0.050, 0.795, 0.035)","ease-in-circ":"cubic-bezier(0.600, 0.040, 0.980, 0.335)","ease-in-back":"cubic-bezier(0.600, -0.280, 0.735, 0.045)","ease-out-quad":"cubic-bezier(0.250, 0.460, 0.450, 0.940)","ease-out-cubic":"cubic-bezier(0.215, 0.610, 0.355, 1.000)","ease-out-quart":"cubic-bezier(0.165, 0.840, 0.440, 1.000)","ease-out-quint":"cubic-bezier(0.230, 1.000, 0.320, 1.000)","ease-out-sine":"cubic-bezier(0.390, 0.575, 0.565, 1.000)","ease-out-expo":"cubic-bezier(0.190, 1.000, 0.220, 1.000)","ease-out-circ":"cubic-bezier(0.075, 0.820, 0.165, 1.000)","ease-out-back":"cubic-bezier(0.175, 0.885, 0.320, 1.275)","ease-in-out-quad":"cubic-bezier(0.455, 0.030, 0.515, 0.955)","ease-in-out-cubic":"cubic-bezier(0.645, 0.045, 0.355, 1.000)","ease-in-out-quart":"cubic-bezier(0.770, 0.000, 0.175, 1.000)","ease-in-out-quint":"cubic-bezier(0.860, 0.000, 0.070, 1.000)","ease-in-out-sine":"cubic-bezier(0.445, 0.050, 0.550, 0.950)","ease-in-out-expo":"cubic-bezier(1.000, 0.000, 0.000, 1.000)","ease-in-out-circ":"cubic-bezier(0.785, 0.135, 0.150, 0.860)","ease-in-out-back":"cubic-bezier(0.680, -0.550, 0.265, 1.550)"},b=e.animate=function(D){var y,r=this;if(!(r instanceof b))return new b(D);"string"==typeof D&&(D=document.querySelector(D)),N===P&&(N=function(){var D,y={webkitTransform:"-webkit-transform",msTransform:"-ms-transform",transform:"transform"},r=document.createElement("p");for(D in y)if(L!=r.style[D])return y[D];return L}()),k===P&&(y=document.body.style,k="transition"in y||"webkitTransition"in y),r._el=D,r.$1m={},r.$2m=[],r.$3m=[],r.duration(),r.$4m=new M.Notifier};e.def(b,y,{transform:function(D){var y=this;return y.$3m.push(D),"-webkit-transform"===N?(y.$5m(N,y.$3m.join(" ")),y.$6m(N),y.$5m("transform",y.$3m.join(" ")),y.$6m("transform")):(y.$5m(N,y.$3m.join(" ")),y.$6m(N)),y},translate:function(D,y){y=y==L?0:y;D=j(D=D==L?0:D)?D:D+"px",y=j(y)?y:y+"px";return this.transform("translate("+D+", "+y+")")},translateX:function(D){return D=j(D=D==L?0:D)?D:D+"px",this.transform("translateX("+D+")")},tx:function(D){this.translateX(D)},translateY:function(D){return D=j(D=D==L?0:D)?D:D+"px",this.transform("translateY("+D+")")},ty:function(D){this.translateY(D)},scale:function(D,y){return D=j(D)?1:D,y=y==L||j(y)?D:y,this.transform("scale("+D+", "+y+")")},scaleX:function(D){return D=j(D)?1:D,this.transform("scaleX("+D+")")},scaleY:function(D){return D=j(D)?1:D,this.transform("scaleY("+D+")")},rotate:function(D){return D=c(D)||0,this.transform("rotate("+D+"deg)")},skew:function(D,y){return D=c(D)||0,y=c(y)||0,this.transform("skew("+D+"deg, "+y+"deg)")},skewX:function(D){return D=c(D)||0,this.transform("skewX("+D+"deg)")},skewY:function(D){return D=c(D)||0,this.transform("skewY("+D+"deg)")},$7m:function(D){this._el.$17m=D;for(var y=0;y<z.length;y++)H(this._el,z[y],D)},$8m:function(D){for(var y=0;y<z.length;y++)O(this._el,z[y],D);delete this._el.$17m},$9m:function(y){var r=this,P=r._el;P.$17m&&r.$8m(P.$17m),r.$7m(function D(){r.$8m(D),y.apply(P,arguments)})},$5m:function(D,y){this.$1m[D]=y},$10m:function(){var D,y=this.$1m,r=this._el.style;for(D in y){var P,N=y[D];0<=D.indexOf("transition-property")&&((P=r.getPropertyValue(D))&&(0<=P.indexOf(N)?N=P:0<=N.indexOf(P)||(N=P+", "+N))),r.setProperty(D,N)}},$11m:function(D,y){this.$5m("-webkit-"+D,y),this.$5m(D,y)},$12m:function(){var D=this._el.style;D.webkitTransition=D.transition=""},duration:function(D){return j(D)&&(D=200),this.$14m=D,this.$11m("transition-duration",D+"ms"),this},delay:function(D){return D=c(D)||0,this.$11m("transition-delay",D+"ms"),this},ease:function(D){return this.$11m("transition-timing-function",D=Z[D]||D||"ease"),this},$6m:function(D){this.$2m.indexOf(D)<0&&this.$2m.push(D)},set:function(D,y){return this.$5m(D,y),this.$6m(D),this},then:function(y){var r=this,D=this.$4m;return y instanceof b?(D.add(function(D){"end"===D.kind&&y.end(r.$13m)}),this):((D=new b(r._el)).$3m=this.$3m.slice(0),r.then(D),(D.$15m=r).$16m=D)},pop:function(){return this.$15m},end:function(D){function y(D){r.$12m(),P.fire({kind:"end"}),r.$16m||r.$13m&&r.$13m.call(r,D)}var r=this,P=r.$4m;r.$11m("transition-property",r.$2m.join(", ")),r.$10m(),D&&(r.$13m=D);0!==r.$14m&&k?r.$9m(function(D){e.callLater(function(){y(D)},L,L,0)}):y({target:r._el,mock:1})}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);