<!DOCTYPE html>
<html>
    <head>
        <title>Node</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script>   
        <script>            
            
            function init(){                                                                
                dataModel = new ht.DataModel();
                graphView = new ht.graph.GraphView(dataModel);
                view = graphView.getView();            

                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    graphView.invalidate();
                }, false);                         
                    
                ht.Default.setImage('mac', 'res/mac-air.png');            
                     
                air11 = new ht.Node();
                air11.setName('11-inch MacBook Air');
                air11.setImage('mac');
                air11.setSize(80, 43);
                air11.setPosition(100, 70);                
                dataModel.add(air11);
                
                air13 = new ht.Node();
                air13.setName('13-inch MacBook Air');                
                air13.setImage('res/mac-air.png');
                air13.setPosition(260, 70);
                air13.setRotation(Math.PI/2);
                dataModel.add(air13);
                
                air11.setHost(air13);   
                
                graphView.setEditable(true);
                graphView.setRectEditableFunc(function(data){
                    return data === air11;
                });
                graphView.setRotationEditableFunc(function(data){
                    return data === air13;
                });
                
                var eventType = ht.Default.isTouchable ? 'touchend' : 'mouseup';
                graphView.getView().addEventListener(eventType, function(e){
                    var data = graphView.getDataAt(e);
                    if(data && ht.Default.isDoubleClick(e)){
                        alert(data.getName() + ' is double clicked.');
                    }
                });
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
