<!DOCTYPE html>
<html>
    <head>
        <title>Text Type</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script>   
        <script>
            function init() {
                dataModel = new ht.DataModel();
                graphView = new ht.graph.GraphView(dataModel);
                view = graphView.getView();

                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function(e) {
                    graphView.invalidate();
                }, false);
                
                ht.Default.setImage('text', {
                    width: 420,
                    height: 70,
                    comps: [
                        {
                            type: 'rect',
                            rect: [0, 0, 420, 70],
                            background: '#2C3E50'
                        },
                        {
                            type: 'text',
                            text: 'Now:',
                            rect:[10, 0, 60, 70],
                            align: 'left',                            
                            color: 'yellow',
                            font: 'bold 20px Arial'
                        },
                        {
                            type: 'text',
                            text: new Date(),
                            rect:[70, 0, 340, 70],
                            align: 'right',
                            vAlign: 'bottom',
                            color: 'white',
                            font: 'bold 11px Arial',
                            shadow: true,
                            shadowColor: '#3498DB'
                        }
                    ]
                });

                var node = new ht.Node();
                node.setPosition(250, 50);
                node.setImage('text');
                dataModel.add(node);                 

                graphView.setEditable(true);
            }


        </script>
    </head>
    <body onload="init();">
    </body>
</html>
