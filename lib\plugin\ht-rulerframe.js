!function(W,Y){"use strict";function m(W){return s().createElement(W)}function x(){return m("canvas")}function p(W,Y,m){W.style.setProperty(Y,m,KK)}function Z(W,Y){W.appendChild(Y)}function v(W,Y){W.removeChild(Y)}var o,M="position",K="absolute",F="px",w="left",n="right",L="top",G="bottom",$="display",U=ht.Default,T=U.getInternal(),O=Math.floor,u=Math.ceil,z=Math.PI,KK=null,P=W.parseInt,s=function(){return document},D=(T=U.getInternal()).addEventListener,k=T.removeEventListener;ht.widget.RulerFrame=function(W){var m=this,o=m._view=T.createView(null,m),Y=m.$1k=x(),U=m.$2k=x(),v=m.$3k=x(),u=m.$4k=x();m._defaultRulerConfig={size:20,borderWidth:1,borderStyle:"solid",borderColor:"#888",defaultMajorTickSpacing:50,minMajorTickSpacing:10,minPhysicalMajorTickSpacing:40,maxPhysicalMajorTickSpacing:100,tickSpacingAdaptable:!0,majorTickTextFont:"12px Arial",majorTickTextColor:"#666",majorTickColor:"#888",minorTickColor:"#ccc",background:"rgba(0,0,0,0)",guideColor:"rgb(0, 173, 239)",guideWidth:2,guideVisible:!1,guideTipVisible:!1,guideTipBorderColor:"#666",guideTipTextColor:"#666",guideTipTextFont:"12px Arial",guideTipBackground:"#fff"},m._topRulerConfig={visible:!0},m._rightRulerConfig={visible:!1},m._bottomRulerConfig={visible:!1},m._leftRulerConfig={visible:!0},Z(o,Y),Z(o,v),Z(o,U),Z(o,u),p(o,M,"relative"),p(o,"box-sizing","border-box"),p(o,"-moz-box-sizing","border-box"),p(Y,M,K),p(U,M,K),p(v,M,K),p(u,M,K),m.$14k=function(W){m.$13k=1,m.iv()},m.$15k=function(W){var Y;(m._topRulerConfig.guideVisible||m._rightRulerConfig.guideVisible||m._bottomRulerConfig.guideVisible||m._leftRulerConfig.guideVisible||m._defaultRulerConfig.guideVisible||(m._topRulerConfig.guideTipVisible||m._rightRulerConfig.guideTipVisible||m._bottomRulerConfig.guideTipVisible||m._leftRulerConfig.guideTipVisible||m._defaultRulerConfig.guideTipVisible)&&m._component)&&(Y=o.getBoundingClientRect(),m.$16k={x:W.clientX-Y.left,y:W.clientY-Y.top},m.$5k())},m.setComponent(W)},W="RulerFrame",o={ms_v:1,ms_fire:1,ms_ac:["defaultRulerConfig","topRulerConfig","rightRulerConfig","bottomRulerConfig","leftRulerConfig","component"],setComponent:function(W){var Y,m=this,o=m._component,U=m.getView();U&&(m._component=W,m.fp("component",o,W),o&&(Y=m.getComponentView(o),v(U,Y),k(U,"mousemove",m.$15k),m.removeComponentPropertyChangeListener(o,m.$14k)),W&&(Y=m.getComponentView(W),Z(U,Y),p(Y,M,K),D(U,"mousemove",m.$15k),m.addComponentPropertyChangeListener(W,m.$14k)))},addComponentPropertyChangeListener:function(W,Y){W&&W.mp&&W.mp(Y)},removeComponentPropertyChangeListener:function(W,Y){W&&W.ump&&W.ump(Y)},getComponentHZoom:function(W){return W&&W.getZoom?W.getZoom():1},getComponentVZoom:function(W){return W&&W.getZoom?W.getZoom():1},getComponentViewRect:function(W){if(W&&W.getViewRect)return W.getViewRect()},getComponentView:function(W){return W&&W.getView?W.getView():W},invalidateComponent:function(W){W&&W.iv&&W.iv()},validateComponent:function(W){W&&W.validate&&W.validate()},$7k:function(W,Y,m,o,U,v,u,x,Z,M,K,s){var D,k,F,w,n;Y.visible?(n=this._defaultRulerConfig,D=Y[D="borderStyle"]||n[D],k=Y[k="borderColor"]||n[k],F=Y[F="borderWidth"]||n[F],w=(Y.size!=KK?Y:n).size,n=Y[Y="background"]||n[Y],Y=this.$6k(F,D,k,W,m,U,w+F,u,x,Z,M,K,s),p(W,U,"0px"),v?T.setCanvas(W,o-Y,w):T.setCanvas(W,w,o-Y),p(W,"background",n),p(W,$,"block")):(p(W,$,"none"),p(this.getComponentView(this._component),U,"0px"))},$6k:function(W,Y,m,o,U,v,u,x,Z,M,K,s,D){var k=0;return p(o,U,W+"px "+Y+" "+m),p(this.getComponentView(this._component),v,u+F),x?(p(o,Z,M+F),k+=M):p(o,Z,"0px"),K?(p(o,s,D+F),k+=D):p(o,s,"0px"),k},validateImpl:function(){var W,Y,m,o=this,U=o._component,v=o.$1k,u=o.$2k,x=o.$3k,Z=o.$4k,M=o._view,K=o._defaultRulerConfig,s=o._topRulerConfig,D=o._rightRulerConfig,k=o._bottomRulerConfig,F=o._leftRulerConfig,K=K.size;M&&U&&(W=s.size!=KK?s.size:K,Y=D.size!=KK?D.size:K,m=k.size!=KK?k.size:K,K=F.size!=KK?F.size:K,o.$7k(v,s,"border-bottom",M.offsetWidth,L,!0,F.visible,w,K,D.visible,n,Y),o.$7k(u,D,"border-left",M.offsetHeight,n,!1,s.visible,L,W,k.visible,G,m),o.$7k(x,k,"border-top",M.offsetWidth,G,!0,F.visible,w,K,D.visible,n,Y),o.$7k(Z,F,"border-right",M.offsetHeight,w,!1,s.visible,L,W,k.visible,G,m),o.$13k?delete o.$13k:o.invalidateComponent(U),o.validateComponent(U),o.$5k())},$5k:function(){var f=this,W=f.$1k,Y=f.$2k,m=f.$3k,o=f.$4k,U=f._topRulerConfig,v=f._rightRulerConfig,u=f._bottomRulerConfig,x=f._leftRulerConfig,Z=f._defaultRulerConfig,M=f._component,K=f.getComponentViewRect(M),S=f.getComponentHZoom(M),_=f.getComponentVZoom(M),T=K.x*S,O=T+K.width*S,z=K.y*_,P=z+K.height*_,h=f._defaultRulerConfig.size,Q="defaultMajorTickSpacing",a="maxPhysicalMajorTickSpacing",H="minPhysicalMajorTickSpacing",e="tickSpacingAdaptable",I="majorTickTextFont",b="majorTickTextColor",c="majorTickColor",R="minorTickColor",C="guideVisible",q="guideTipVisible",r="guideTipBorderColor",J="guideTipTextColor",l="guideTipTextFont",d="guideTipBackground",i="guideColor",g="guideWidth",y="minMajorTickSpacing",t=Z[Q],B=Z[a],V=Z[H],j=Z[e],N=Z[I],X=Z[b],A=Z[c],WK=Z[y],YK=Z[R],mK=Z[C],oK=Z[q],UK=Z[r],vK=Z[J],uK=Z[l],xK=Z[d],ZK=Z[i],MK=Z[g];function s(W,Y,m,o,U,v){var u,x,Z,M,K,s,D,k,F,w,n,p,L,G,$,E;Y.visible&&(W=W.getContext("2d"),u=Y[Q]||t,E=Y[e]!=KK?Y[e]:j,x=Y[I]||N,Z=Y[b]||X,M=Y.size!=KK?Y.size:h,K=Y[c]||A,s=Y[y]||WK,D=Y[R]||YK,k=Y[C]!=KK?Y[C]:mK,F=Y[q]!=KK?Y[q]:oK,w=Y[r]!=KK?Y[r]:UK,n=Y[J]!=KK?Y[J]:vK,p=Y[l]!=KK?Y[l]:uK,L=Y[d]!=KK?Y[d]:xK,G=Y[i]||ZK,$=Y[g]||MK,E&&(u=f[v]=f.$8k(f[v]||u,Y[a]||B,Y[H]||V,o?S:_,s)),m.call(f,W,T,z,O,P,M,o?S:_,u,x,Z,U,K,D),E=f.$16k,(k||F)&&E&&(o?f.$9k(W,E.x,M,G,$,k,F,w,n,p,L):f.$10k(W,E.y,M,G,$,k,F,w,n,p,L,U)))}f._view&&M&&(s(W,U,f.$11k,!0,!1,"_currentTopMajorTickSpacing"),s(Y,v,f.$12k,!1,!0,"_currentRightMajorTickSpacing"),s(m,u,f.$11k,!0,!0,"_currentBottomMajorTickSpacing"),s(o,x,f.$12k,!1,!1,"_currenLeftMajorTickSpacing"))},$8k:function(W,Y,m,o,U){return W*o<m?W=O(Y/o/U)*U:Y<W*o&&(W=u(m/o/U)*U),W},getHTipText:function(W){var Y=this._component,m=0,o=this._view.getBoundingClientRect();return Y.lp?m=P(Y.lp({x:W.x+o.left,y:W.y}).x):m-=P(this.getComponentView(Y).style.left)||0,m},$9k:function(W,Y,m,o,U,v,u,x,Z,M,K){var s=this._component,s=(W.save(),T.translateAndScale(W,0,0,1),Y-(P(this.getComponentView(s).style.left)||0));Y=this.getHTipText(this.$16k),v&&(W.beginPath(),W.fillStyle=o,W.rect(s,0,U,m),W.fill()),u&&(W.beginPath(),W.textAlign="center",W.textBaseline="middle",W.font=M,v=W.measureText(Y).width+6,W.fillStyle=K,W.rect(s-v/2,0,v,m),W.fill(),W.strokeStyle=x,W.stroke(),W.beginPath(),W.fillStyle=Z,W.fillText(Y,s,0+m/2)),W.restore()},getVTipText:function(W){var Y=this._component,m=0,o=this._view.getBoundingClientRect();return Y.lp?m=P(Y.lp({x:W.x,y:W.y+o.top}).y):m-=P(this.getComponentView(Y).style.top)||0,m},formatScaleText:function(W){return Math.round(W)},$10k:function(W,Y,m,o,U,v,u,x,Z,M,K,s){var D=this._component,k=(W.save(),T.translateAndScale(W,0,0,1),m/2),D=Y-(P(this.getComponentView(D).style.top)||0);Y=this.getVTipText(this.$16k),v&&(W.beginPath(),W.fillStyle=o,W.rect(k-m/2,D,m,U),W.fill()),u&&(W.translate(k,D),W.rotate((s?90:-90)*z/180),W.translate(-k,-D),W.beginPath(),W.textAlign="center",W.textBaseline="middle",W.font=M,v=W.measureText(Y).width+6,W.fillStyle=K,W.rect(k-v/2,D-m/2,v,m),W.fill(),W.strokeStyle=x,W.stroke(),W.fillStyle=Z,W.fillText(Y,k,D)),W.restore()},$11k:function(W,Y,m,o,U,v,u,x,Z,M,K,s,D){W.save();for(var k=Y,F=(k+o)/2,w=(o=o-k,T.translateAndScale(W,Y=0,0,1),0),n=0,p=P(v/2),L=v-p,G=K?0:p,$=x*u,E=$/10,p=(W.clearRect(Y-=$,0,(o+=$)-Y,v),W.beginPath(),W.fillStyle=D,O(F/E)*E-k),w=p;w<o;w+=E)W.rect(w,0+G,1,L);for(w=p;Y<w;w-=E)W.rect(w,0+G,1,L);for(W.fill(),G=K?0:1,W.beginPath(),W.fillStyle=s,w=p=O(F/$)*$-k;w<o;w+=$)W.rect(w,0+G,1,v-1);for(w=p;Y<w;w-=$)W.rect(w,0+G,1,v-1);W.fill();D=(P(/\d+px/.exec(Z)[0])||10)/2,W.textBaseline="middle",G=K?v-D-2:2+D,W.beginPath(),W.fillStyle=M,W.font=Z,s=O(F/$)*$/u;for(w=p,n=s;w<o;w+=$,n+=x){var f=this.getHScaleText?this.getHScaleText(w):n;W.fillText(this.formatScaleText(f),w+2,0+G)}for(w=p,n=s;Y<w;w-=$,n-=x){f=this.getHScaleText?this.getHScaleText(w):n;W.fillText(this.formatScaleText(f),w+2,0+G)}W.restore()},$12k:function(u,W,Y,m,o,U,v,x,Z,M,K,s,D){u.save();for(var k=Y,F=(k+o)/2,w=(o=o-k,T.translateAndScale(u,Y=0,0,1),0),n=this,p=0,L=P(U/2),G=U-L,$=K?0:L,E=x*v,f=E/10,L=(u.clearRect(0,0,U,(o+=E)-(Y-=E)),u.beginPath(),u.fillStyle=D,O(F/f)*f-k),w=L;w<o;w+=f)u.rect(0+$,w,G,1);for(w=L;Y<w;w-=f)u.rect(0+$,w,G,1);for(u.fill(),$=K?0:1,u.beginPath(),u.fillStyle=s,w=L=O(F/E)*E-k;w<o;w+=E)u.rect(0+$,w,U-1,1);for(w=L;Y<w;w-=E)u.rect(0+$,w,U-1,1);u.fill();var D=(P(/\d+px/.exec(Z)[0])||10)/2,S=90*z/180;function _(W,Y,m,o,U,v){U=n.getVScaleText?n.getVScaleText(m):U,U=n.formatScaleText(U),u.translate(W+Y,m),u.rotate(-o),u.translate(-W-Y,-m),u.fillText(U,W+Y+(v?2:1),m),u.translate(W+Y,m),u.rotate(o),u.translate(-W-Y,-m)}u.textBaseline="middle",$=K?U-D:2+D,S=K?-S:S,u.beginPath(),u.fillStyle=M,u.font=Z;s=O(F/E)*E/v;for(w=L,p=s;w<o;w+=E,p+=x)_(0,$,w,S,p,K);for(w=L,p=s;Y<w;w-=E,p-=x)_(0,$,w,S,p,K);u.restore()},onPropertyChanged:function(){this.iv()},dispose:function(){var W=this,Y=W._component,m=W._view;Y&&W.removeComponentPropertyChangeListener(Y,W.$14k),m&&(k(m,"mousemove",W.$15k),v(m.parentNode,m),W._view=null)}},U.def(ht.widget[W],Y,o)}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);