!function(d,v_,m){"use strict";var K,c=d.ht,v=c.<PERSON>,E=v.getInternal(),e=(v.clone,v.def),h=(c.<PERSON>,c.Math.Matrix4),y=c.Math.Vector4,Y=c.Math.Euler,i=c.Math.Quaternion,N=c.Math.Vector3,s=c.Math.angleMod;function L(e){if("undefined"!=typeof TextDecoder)return(new TextDecoder).decode(e);for(var J="",P=0,k=e.length;P<k;P++)J+=String.fromCharCode(e[P]);try{return decodeURIComponent(escape(J))}catch(e){return J}}function _(e,J){return s(e-J)}function M(e,J,P){return J===m&&(J=0),P===m&&(P=e.byteLength),L(new Uint8Array(e,J,P))}function D(e){return e/46186158e3}function U(e){return e*Math.PI/180}var t=["ZYX","YZX","XZY","ZXY","YXZ","XYZ"];function A(e){return 6<=(e=e||0)?t[0]:t[e]}function p(e,J,P){(J=J[P])!==m&&(e[P]=J)}function G(e,J){if(k="Kaydara FBX Binary  \0",(F=e).byteLength>=k.length&&k===M(F,0,k.length))K=(new kO)._zS(e);else{F=M(e);if(!function(P){var e=["K","a","y","d","a","r","a","\\","F","B","X","\\","B","i","n","a","r","y","\\","\\"],k=0;for(var J=0;J<e.length;++J)if(function(e){var J=P[e-1];return P=P.slice(k+e),k++,J}(1)===e[J])return;return 1}(F))throw new Error("Unknown format.");if(V(F)<7e3)throw new Error("FBX version not supported, FileVersion: "+V(F));K=(new PO)._zS(F)}var T,P,k=(new zO)._zS(J),e={model3d:!0},F=k.externalAssetURIs;F&&(e.externalAssetURIs=F),(F=k.sourceBufferMap)&&(e.sourceBufferMap=F);(F=k.materialMap)&&(T=e.matDef={},F.forEach(function(e){var J,P=e.name,k=T[P]={};for(J in e)"name"!==J&&"setValues"!==J&&(k[J]=e[J])})),Z(e,k,J,{});var z={},a={},S=!1;return v.traverse(e,null,function(e){if(z[e.name]){for(e.displayName=e.name;P=e.name+"_"+c.Math.generateIncreasingID(),z[P];);e.name=P}z[e.name]=e.ID,a[e.ID]=e.name,delete e.ID,"__undefined__"===e.mat&&(S=!0)},"comps"),S&&(e.matDef||(e.matDef={}),e.matDef.__undefined__||(e.matDef.__undefined__={type:(new Q).type})),!e.animations||!e.animations.length||e.animations.forEach(function(e){e.tracks.forEach(function(e){var J=e.name.split(".");J[0]&&a[J[0]]&&(e.name=a[J[0]]+"."+J.slice(1).join("."))})}),v.traverse(e,null,function(e){e.subMeshes&&f(e);var J=e.skeleton;if(J){for(var P=J[0],k=P.length,T=new Array(k),F=0;F<k;F++)T[F]=P[F].name;e.skeleton={bones:T,boneMatrixInverses:J[1],bindMatrix:J[2].toArray(),bindMatrixInverse:(new h).copy(J[2]).invert().toArray()}}},"comps"),v.traverse(e,null,function(e){var J=e.comps;if(J)for(var P=J.length-1;0<=P;P--){var k=J[P];k.mesh||k.isBone||k.comps&&k.comps.length||J.splice(P,1)}},"comps"),J.batchByMaterial&&E.batchModel3dByMaterial(e,J),E.completeCubeCenterOfModel3d(e,{scene:k,cube:J.cube,center:J.center,preferBox3:J.box3,rotationMode:J.rotationMode,t3:J.t3,r3:J.r3,s3:J.s3}),E.completeCommonPropertiesOfModel3d(e,v_.assign({},J,{flipY:!1!==J.flipY})),e}function I(){this.add=function(e,J){this[e]=J}}var P,k,T,$={position:"vs",normal:"ns"},R=new h,B=(P=new N,k=new i,T=new N,function(e,J){J.decompose(P,k,T),e.position=P.toArray(),e.quaternion=k.toArray(),e.scale=T.toArray()}),Z=function(e,J,P,k){var T=J.mat;if(T)if(T instanceof Array){for(var F=[],z=0,a=T.length;z<a;z++)F.push(T[z].name);e.mat=F}else e.mat=T.name;var S=J.mesh;if(S){e.mesh=v={},S.index&&(v.is=S.index.array);var v,K,c=S.attributes;for(K in c){var E=$[K]||K;P.ignoreNormal&&"ns"===E||(v[E]=c[K].array)}var S=S.groups;S&&(e.subMeshes=S),e.mat||(e.mat="__undefined__")}!(e.mat&&e.mat instanceof Array&&e.mat.length)||e.subMeshes&&e.subMeshes.length||(delete e.subMeshes,e.mat=e.mat[0],console.warn("Unable to locate group boundaries in the sub-meshes. This may lead to incorrect rendering of this mesh.")),J.matrix.equals(R)||B(e,J.matrix),J.userData&&(S=J.userData.transformData)&&(S.rotationOffset||S.rotationPivot||S.scalingOffset||S.scalingPivot||2===S.inheritType)&&((i=e.transformData={}).inheritType=S.inheritType||2,i.translation=S.translation||[0,0,0],S.rotation?((Y=S.rotation.map(U)).push(S.eulerOrder),i.rotation=Y):i.rotation=[0,0,0,1],i.scale=S.scale||[1,1,1],p(i,S,"rotationOffset"),p(i,S,"rotationPivot"),p(i,S,"scalingOffset"),p(i,S,"scalingPivot"));J.isBone&&(e.isBone=!0),e.name=J.name,e.ID=J.ID;var y,Y=J.skeleton,i=(Y&&(e.skeleton=Y),J.animations),g=(i&&(e.animations=i),J.getChildren().getArray());if(g){e.comps=[];for(z=0,a=g.length;z<a;z++)e.comps.push(y={}),Z(y,g[z],P,k)}},f=function(e){var J=e.subMeshes;if(J&&J.length){for(var P=[],k=0,T=0,F=J.length;T<F;T++){if((i=J[T]).start<k)return!1;k=i.start+i.count,-1===P.indexOf(i.materialIndex)&&P.push(i.materialIndex)}var z=e.mesh,a={},S=[],v=!1;if(z.is&&z.is.length)for(var K in v=!0,a.is=new Uint32Array(z.is.length),z)"is"!==K&&(a[K]=z[K]);else for(var K in z){var c=z[K];a[K]=new Float32Array(c.length)}for(var k=0,E=0;E<P.length;E++){var y=P[E],Y={start:k,count:0,materialIndex:y};S.push(Y);for(T=0,F=J.length;T<F;T++){var i=J[T];if(i.materialIndex===y){var g=i.start,p=i.count;for(K in z)if(!v||"is"===K)for(var c=z[K],h="is"===(h=K)?1:"uv"===h||"uv2"===h?2:"skinWeight"===h||"skinIndex"===h?4:3,N=h*p,U=g*h,m=k*h,_=0;_<N;_++)a[K][m+_]=c[U+_];k+=p,Y.count+=p}}}e.mesh=a,e.subMeshes=S}},j=(E.addMethod(v,{loadFbx:function(k,T){(T=T||{}).responseType="arraybuffer",v.xhrLoad(k,function(e){var J,P;e=e,J=T.finishFunc,P=T.shape3d,(e=e?G(e,T):null)?J&&J(e,k,P):J&&J(null)},T)},parseFbx:G}),new Y),H=new N;function w(e){var J=new h,P=new h,k=new h,T=new h,F=new h,z=new h,a=new h,S=new h,v=new h,K=new h,c=new h,E=new h,y=e.inheritType||0,Y=(e.translation&&J.setPosition(H.fromArray(e.translation)),e.preRotation&&((Y=e.preRotation.map(U)).push(e.eulerOrder),P.makeRotationFromEuler(j.fromArray(Y))),e.rotation&&((Y=e.rotation.map(U)).push(e.eulerOrder),k.makeRotationFromEuler(j.fromArray(Y))),e.postRotation&&((Y=e.postRotation.map(U)).push(e.eulerOrder),T.makeRotationFromEuler(j.fromArray(Y)),T.invert()),e.scale&&F.scale(H.fromArray(e.scale)),e.scalingOffset&&a.setPosition(H.fromArray(e.scalingOffset)),e.scalingPivot&&z.setPosition(H.fromArray(e.scalingPivot)),e.rotationOffset&&S.setPosition(H.fromArray(e.rotationOffset)),e.rotationPivot&&v.setPosition(H.fromArray(e.rotationPivot)),e.parentMatrixWorld&&(c.copy(e.parentMatrix),K.copy(e.parentMatrixWorld)),P.clone().multiply(k).multiply(T)),e=new h,i=(e.extractRotation(K),new h),i=(i.copyPosition(K),i.clone().invert().multiply(K)),i=e.clone().invert().multiply(i),g=F,p=new h,i=(0===y?p.copy(e).multiply(Y).multiply(i).multiply(g):1===y?p.copy(e).multiply(i).multiply(Y).multiply(g):(y=(new h).scale((new N).setFromMatrixScale(c)).clone().invert(),c=i.clone().multiply(y),p.copy(e).multiply(Y).multiply(c).multiply(g)),v.clone().invert()),y=z.clone().invert(),e=J.clone().multiply(S).multiply(v).multiply(P).multiply(k).multiply(T).multiply(i).multiply(a).multiply(z).multiply(F).multiply(y),Y=(new h).copyPosition(e),c=K.clone().multiply(Y);return E.copyPosition(c),(e=E.clone().multiply(p)).premultiply(K.invert()),e}var l=[];function x(e,J,P,k){var T;switch(k.mappingType){case"ByPolygonVertex":T=e;break;case"ByPolygon":T=J;break;case"ByVertice":T=P;break;case"AllSame":T=k.indices[0];break;default:console.warn("unknown attribute mapping type "+k.mappingType)}for(var F=(T="IndexToDirect"===k.referenceType?k.indices[T]:T)*k.dataSize,z=F+k.dataSize,a=l,S=k.buffer,v=z,K=F,c=0;K<v;K++,c++)a[c]=S[K];return a}function O(e,J,P){return e.slice(0,J).concat(P).concat(e.slice(J))}var r=new RegExp("[\\[\\]\\.:\\/]","g");function g(e){return e.replace(/\s/g,"_").replace(r,"")}function V(e){e=e.match(/FBXVersion: (\d+)/);if(e)return parseInt(e[1]);throw new Error("Cannot find the version number for the file given.")}function b(e){return e.split(",").map(function(e){return parseFloat(e)})}function X(){this.userData={},this.matrix=new h,this.matrixWorld=new h,X.superClass.constructor.apply(this,arguments)}function eO(e){for(var J in this.texture=!0,e)this[J]=e[J]}function z(e,J){this.mat=J,this.mesh=e,this.userData={},z.superClass.constructor.apply(this,arguments)}function JO(e,J){this.dv=new DataView(e),this.offset=0,this.littleEndian=J===m||J,this.getOffset=function(){return this.offset},this.size=function(){return this.dv.buffer.byteLength},this.skip=function(e){this.offset+=e},this._hS=function(){return 1==(1&this.getUint8())},this._iS=function(e){for(var J=[],P=0;P<e;P++)J.push(this._hS());return J},this.getUint8=function(){var e=this.dv.getUint8(this.offset);return this.offset+=1,e},this.getInt16=function(){var e=this.dv.getInt16(this.offset,this.littleEndian);return this.offset+=2,e},this.getInt32=function(){var e=this.dv.getInt32(this.offset,this.littleEndian);return this.offset+=4,e},this.getInt32Array=function(e){for(var J=[],P=0;P<e;P++)J.push(this.getInt32());return J},this.getUint32=function(){var e=this.dv.getUint32(this.offset,this.littleEndian);return this.offset+=4,e},this.getInt64=function(){var e,J;return this.littleEndian?(e=this.getUint32(),J=this.getUint32()):(J=this.getUint32(),e=this.getUint32()),2147483648&J?(J=4294967295&~J,-(4294967296*(J=4294967295===(e=4294967295&~e)?J+1&4294967295:J)+(e=e+1&4294967295))):4294967296*J+e},this.getInt64Array=function(e){for(var J=[],P=0;P<e;P++)J.push(this.getInt64());return J},this.getUint64=function(){var e,J;return this.littleEndian?(e=this.getUint32(),J=this.getUint32()):(J=this.getUint32(),e=this.getUint32()),4294967296*J+e},this.getFloat32=function(){var e=this.dv.getFloat32(this.offset,this.littleEndian);return this.offset+=4,e},this.getFloat32Array=function(e){for(var J=[],P=0;P<e;P++)J.push(this.getFloat32());return J},this.getFloat64=function(){var e=this.dv.getFloat64(this.offset,this.littleEndian);return this.offset+=8,e},this.getFloat64Array=function(e){for(var J=[],P=0;P<e;P++)J.push(this.getFloat64());return J},this._vS=function(e){var J=this.dv.buffer.slice(this.offset,this.offset+e);return this.offset+=e,J},this.getString=function(e){for(var J=[],P=0;P<e;P++)J[P]=this.getUint8();var k=J.indexOf(0);return 0<=k&&(J=J.slice(0,k)),L(new Uint8Array(J))}}function u(){this.uuid=c.Math.generateUUID(),this.name="",this.index=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null}e(X,c.Data,{add:function(e){this.addChild(e)},traverse:function(e){v.traverse(this,e)},applyMatrix4:function(e){this.matrix.premultiply(e),this.getParent()?this.matrixWorld.multiplyMatrices(this.getParent().matrixWorld,this.matrix):this.matrixWorld.copy(this.matrix)}});var n,S,F,a,Q=function(){this.type="pbr",this.setValues=function(e){if(e){for(var J in e)this[J]=e[J];return this}},this.clone=function(){var e,J=new Q;for(e in this)this[e]&&(J[e]=this[e]);return J}},PO=(e(z,X,{normalizeSkinWeights:function(){var e=new c.Math.Vector4,J=this.mesh.attributes.skinWeight;if(J)for(var P=0,k=J.count;P<k;P++){e.fromBufferAttribute(J,P);var T=1/e.manhattanLength();T!=1/0?e.multiplyScalar(T):e.set(1,0,0,0),J.setXYZW(P,e.x,e.y,e.z,e.w)}}}),function(){this._2S=function(){return this.nodeStack[this.currentIndent-2]},this._3S=function(){return this.nodeStack[this.currentIndent-1]},this._4S=function(){return this.currentProp},this._5S=function(e){this.nodeStack.push(e),this.currentIndent+=1},this._6S=function(){this.nodeStack.pop(),--this.currentIndent},this._7S=function(e,J){this.currentProp=e,this.currentPropName=J},this._zS=function(e){this.currentIndent=0,this.allNodes=new I,this.nodeStack=[],this.currentProp=[],this.currentPropName="";var F=this,z=e.split(/[\r\n]+/);return z.forEach(function(e,J){var P,k=e.match(/^[\s\t]*;/),T=e.match(/^[\s\t]*$/);k||T||(k=e.match("^\\t{"+F.currentIndent+"}(\\w+):(.*){",""),T=e.match("^\\t{"+F.currentIndent+"}(\\w+):[\\s\\t\\r\\n](.*)"),P=e.match("^\\t{"+(F.currentIndent-1)+"}}"),k?F._8S(e,k):T?F._aS(e,T,z[++J]):P?F._6S():e.match(/^[^\s\t}]/)&&F._bS(e))}),this.allNodes},this._8S=function(e,J){var P=J[1].trim().replace(/^"/,"").replace(/"$/,""),J=J[2].split(",").map(function(e){return e.trim().replace(/^"/,"").replace(/"$/,"")}),k={name:P},J=this._9S(J),T=this._3S();0===this.currentIndent?this.allNodes.add(P,k):P in T?("PoseNode"===P?T.PoseNode.push(k):T[P].id!==m&&(T[P]={},T[P][T[P].id]=T[P]),""!==J.id&&(T[P][J.id]=k)):"number"==typeof J.id?(T[P]={},T[P][J.id]=k):"Properties70"!==P&&(T[P]="PoseNode"===P?[k]:k),"number"==typeof J.id&&(k.id=J.id),""!==J.name&&(k.attrName=J.name),""!==J.type&&(k.attrType=J.type),this._5S(k)},this._9S=function(e){var J=e[0],P=(""!==e[0]&&(J=parseInt(e[0]),isNaN(J)&&(J=e[0])),""),k="";return 1<e.length&&(P=e[1].replace(/^(\w+)::/,""),k=e[2]),{id:J,name:P,type:k}},this._aS=function(e,J,P){var k=J[1].replace(/^"/,"").replace(/"$/,"").trim(),J=J[2].replace(/^"/,"").replace(/"$/,"").trim(),P=("Content"===k&&","===J&&(J=P.replace(/"/g,"").replace(/,$/,"").trim()),this._3S());if("Properties70"===P.name)this._cS(e,k,J);else{if("C"===k){for(var e=J.split(",").slice(1),T=parseInt(e[0]),e=parseInt(e[1]),F=(F=J.split(",").slice(3)).map(function(e){return e.trim().replace(/^"/,"")}),k="connections",z=J=[T,e],a=F,S=0,v=z.length,K=a.length;S<K;S++,v++)z[v]=a[S];P[k]===m&&(P[k]=[])}"Node"===k&&(P.id=J),k in P&&Array.isArray(P[k])?P[k].push(J):"a"!==k?P[k]=J:P.a=J,this._7S(P,k),"a"===k&&","!==J.slice(-1)&&(P.a=b(J))}},this._bS=function(e){var J=this._3S();J.a+=e,","!==e.slice(-1)&&(J.a=b(J.a))},this._cS=function(e,J,P){var P=P.split('",').map(function(e){return e.trim().replace(/^\"/,"").replace(/\s/,"_")}),k=P[0],T=P[1],F=P[2],z=P[3],a=P[4];switch(T){case"int":case"enum":case"bool":case"ULongLong":case"double":case"Number":case"FieldOfView":a=parseFloat(a);break;case"Color":case"ColorRGB":case"Vector3D":case"Lcl_Translation":case"Lcl_Rotation":case"Lcl_Scaling":a=b(a)}this._2S()[k]={type:T,type2:F,flag:z,value:a},this._7S(this._2S(),k)}}),kO=function(){this._zS=function(e){var J=new JO(e),P=(J.skip(23),J.getUint32());if(P<6400)throw new Error("FBX version not supported : "+P);for(var k=new I;!this._dS(J);){var T=this._eS(J,P);null!==T&&k.add(T.name,T)}return k},this._dS=function(e){return e.size()%16==0?(e.getOffset()+160+16&-16)>=e.size():e.getOffset()+160+16>=e.size()},this._eS=function(e,J){var P={},k=7500<=J?e.getUint64():e.getUint32(),T=7500<=J?e.getUint64():e.getUint32(),F=(7500<=J?e.getUint64():e.getUint32(),e.getUint8()),z=e.getString(F);if(0===k)return null;for(var a=[],S=0;S<T;S++)a.push(this._gS(e));var F=0<a.length?a[0]:"",v=1<a.length?a[1]:"",K=2<a.length?a[2]:"";for(P.singleProperty=1===T&&e.getOffset()===k;k>e.getOffset();){var c=this._eS(e,J);null!==c&&this._fS(z,P,c)}return P.propertyList=a,"number"==typeof F&&(P.id=F),""!==v&&(P.attrName=v),""!==K&&(P.attrType=K),""!==z&&(P.name=z),P},this._fS=function(e,J,P){var k,T,F,z,a;!0===P.singleProperty?(T=P.propertyList[0],Array.isArray(T)?(J[P.name]=P).a=T:J[P.name]=T):"Connections"===e&&"C"===P.name?(k=[],P.propertyList.forEach(function(e,J){0!==J&&k.push(e)}),J.connections===m&&(J.connections=[]),J.connections.push(k)):"Properties70"===P.name?v_.keys(P).forEach(function(e){J[e]=P[e]}):"Properties70"===e&&"P"===P.name?(T=P.propertyList[0],e=P.propertyList[1],F=P.propertyList[2],z=P.propertyList[3],0===T.indexOf("Lcl ")&&(T=T.replace("Lcl ","Lcl_")),a="Color"===(e=0===e.indexOf("Lcl ")?e.replace("Lcl ","Lcl_"):e)||"ColorRGB"===e||"Vector"===e||"Vector3D"===e||0===e.indexOf("Lcl_")?[P.propertyList[4],P.propertyList[5],P.propertyList[6]]:P.propertyList[4],J[T]={type:e,type2:F,flag:z,value:a}):J[P.name]===m?"number"==typeof P.id?(J[P.name]={},J[P.name][P.id]=P):J[P.name]=P:"PoseNode"===P.name?(Array.isArray(J[P.name])||(J[P.name]=[J[P.name]]),J[P.name].push(P)):J[P.name][P.id]===m&&(J[P.name][P.id]=P)},this._gS=function(e){var J=e.getString(1);switch(J){case"C":return e._hS();case"D":return e.getFloat64();case"F":return e.getFloat32();case"I":return e.getInt32();case"L":return e.getInt64();case"R":return k=e.getUint32(),e._vS(k);case"S":return k=e.getUint32(),e.getString(k);case"Y":return e.getInt16();case"b":case"c":case"d":case"f":case"i":case"l":var P=e.getUint32(),k=e.getUint32(),T=e.getUint32();if(0===k)switch(J){case"b":case"c":return e._iS(P);case"d":return e.getFloat64Array(P);case"f":return e.getFloat32Array(P);case"i":return e.getInt32Array(P);case"l":return e.getInt64Array(P)}void 0===TO&&console.error("Require External library fflate.min.js.");var k=TO.unzlibSync(new Uint8Array(e._vS(T))),F=new JO(k.buffer);switch(J){case"b":case"c":return F._iS(P);case"d":return F.getFloat64Array(P);case"f":return F.getFloat32Array(P);case"i":return F.getInt32Array(P);case"l":return F.getInt64Array(P)}default:throw new Error("Unknown property type "+J)}}},TO=function(){function y(e,J){for(var P=new B(31),k=0;k<31;++k)P[k]=J+=1<<e[k-1];var T=new Z(P[30]);for(k=1;k<30;++k)for(var F=P[k];F<P[k+1];++F)T[F]=F-P[k]<<5|k;return[P,T]}var e={},Y=("undefined"!=typeof module&&"object"==typeof exports?function(e){var a;try{a=require("worker_threads").Worker}catch(a){}return exports.default=a?function(e,J,P,k,T){var F=!1,z=new a(e+";var __w=require('worker_threads');__w.parentPort.on('message',function(m){onmessage({data:m})}),postMessage=function(m,t){__w.parentPort.postMessage(m,t)},close=process.exit;self=global",{eval:!0}).on("error",function(e){return T(e,null)}).on("message",function(e){return T(null,e)}).on("exit",function(e){e&&!F&&T(Error("exited with code "+e),null)});return z._wS(P,k),z.terminate=function(){return F=!0,a.prototype.terminate.call(z)},z}:function(e,J,P,k,T){setImmediate(function(){return T(Error("async operations unsupported - update to Node 12+ (or Node 10-11 with the --experimental-worker CLI flag)"),null)});function F(){}return{terminate:F,_wS:F}},e}:function(e){var F={},z=function(e){return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))},a=function(e){return new Worker(e)};try{URL.revokeObjectURL(z(""))}catch(F){z=function(e){return"data:application/javascript;charset=UTF-8,"+encodeURI(e)},a=function(e){return new Worker(e,{type:"module"})}}return e.default=function(e,J,P,k,T){J=a(F[J]||(F[J]=z(e)));return J._yS=function(e){return T(e.error,null)},J._xS=function(e){return T(null,e.data)},J._wS(P,k),J},e})({}),R=Uint8Array,B=Uint16Array,Z=Uint32Array,f=new R([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),w=new R([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),M=new R([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),J=y(f,2),I=J[0],r=J[1];I[28]=258,r[258]=28;for(var J=y(w,0),$=J[0],V=J[1],p=new B(32768),P=0;P<32768;++P){var k=(43690&P)>>>1|(21845&P)<<1;p[P]=((65280&(k=(61680&(k=(52428&k)>>>2|(13107&k)<<2))>>>4|(3855&k)<<4))>>>8|(255&k)<<8)>>>1}for(var D=function(e,J,P){for(var k=e.length,T=0,F=new B(J);T<k;++T)++F[e[T]-1];var z=new B(J);for(T=0;T<J;++T)z[T]=z[T-1]+F[T-1]<<1;if(P){for(var a=new B(1<<J),S=15-J,T=0;T<k;++T)if(e[T])for(var v=T<<4|e[T],K=J-e[T],c=z[e[T]-1]++<<K,E=c|(1<<K)-1;c<=E;++c)a[p[c]>>>S]=v}else for(a=new B(k),T=0;T<k;++T)e[T]&&(a[T]=p[z[e[T]-1]++]>>>15-e[T]);return a},d=new R(288),P=0;P<144;++P)d[P]=8;for(P=144;P<256;++P)d[P]=9;for(P=256;P<280;++P)d[P]=7;for(P=280;P<288;++P)d[P]=8;var s=new R(32);for(P=0;P<32;++P)s[P]=5;function eO(e){for(var J=e[0],P=1;P<e.length;++P)e[P]>J&&(J=e[P]);return J}function t(e,J,P){var k=J/8|0;return(e[k]|e[1+k]<<8)>>(7&J)&P}function JO(e,J){var P=J/8|0;return(e[P]|e[1+P]<<8|e[2+P]<<16)>>(7&J)}function h(J,e,P,k,T,F){var z=J.length,a=new R(k+z+5*(1+Math.ceil(z/7e3))+T),S=a.subarray(k,a.length-T),v=0;if(!e||z<8)for(var K=0;K<=z;K+=65535){var c=K+65535;v=c<z?vO(S,v,J.subarray(K,c)):(S[K]=F,vO(S,v,J.subarray(K,z)))}else{for(var e=cO[e-1],s=e>>>13,L=8191&e,E=(1<<P)-1,y=new B(32768),Y=new B(1+E),i=Math.ceil(P/3),M=2*i,g=function(e){return(J[e]^J[e+1]<<i^J[e+2]<<M)&E},p=new Z(25e3),h=new B(288),N=new B(32),U=0,m=0,_=K=0,j=0,H=0;K<z;++K){var x=g(K),b=32767&K,X=Y[x];if(y[b]=X,Y[x]=b,j<=K){var u=z-K;if((7e3<U||24576<_)&&423<u){v=KO(J,S,0,p,h,N,m,_,H,K-H,v),_=U=m=0,H=K;for(var n=0;n<286;++n)h[n]=0;for(n=0;n<30;++n)N[n]=0}var Q=2,q=0,D=L,o=b-X&32767;if(2<u&&x==g(K-o))for(var t=Math.min(s,u)-1,A=Math.min(32767,K),G=Math.min(258,u);o<=A&&--D&&b!=X;){if(J[K+Q]==J[K+Q-o]){for(var C=0;C<G&&J[K+C]==J[K+C-o];++C);if(Q<C){if(q=o,t<(Q=C))break;var I=Math.min(o,C-2),W=0;for(n=0;n<I;++n){var d=K-o+n+32768&32767,$=d-y[d]+32768&32767;W<$&&(W=$,X=d)}}}o+=(b=X)-(X=y[b])+32768&32767}q?(p[_++]=268435456|r[Q]<<18|V[q],x=31&r[Q],u=31&V[q],m+=f[x]+w[u],++h[257+x],++N[u],j=K+Q,++U):(p[_++]=J[K],++h[J[K]])}}v=KO(J,S,F,p,h,N,m,_,H,K-H,v),!F&&7&v&&(v=vO(S,v+1,O))}return l(a,0,k+zO(v)+T)}function T(){return[R,B,Z,f,w,M,I,$,kO,FO,p,D,eO,t,JO,zO,l,Q,i,z,n]}function F(){return[R,B,Z,f,w,M,r,V,PO,d,TO,s,p,cO,O,D,L,A,aO,q,SO,G,vO,KO,zO,l,h,K,bO,z]}function N(){return[pO,UO,g,x,o]}function _(){return[hO,NO]}function j(){return[mO,g,C]}function H(){return[_O]}function z(e){return postMessage(e,[e.buffer])}function n(e){return e&&e.size&&new R(e.size)}function a(e,J,P,k,T,F){var z=iO(P,k,T,function(e,J){z.terminate(),F(e,J)});return z._wS([e,J],J.consume?[e.buffer]:[]),function(){z.terminate()}}function S(J){return J.ondata=function(e,J){return postMessage([e,J],[e.buffer])},function(e){return J.push(e.data[0],e.data[1])}}function v(e,P,J,k,T){var F,z=iO(e,k,T,function(e,J){e?(z.terminate(),P.ondata.call(P,e)):(J[1]&&z.terminate(),P.ondata.call(P,e,J[0],J[1]))});z._wS(J),P.push=function(e,J){if(F)throw"stream finished";if(!P.ondata)throw"no stream handler";z._wS([e,F=J],[e.buffer])},P.terminate=function(){z.terminate()}}var PO=D(d,9,0),kO=D(d,9,1),TO=D(s,5,0),FO=D(s,5,1),zO=function(e){return(e/8|0)+(7&e&&1)},l=function(e,J,P){(null==J||J<0)&&(J=0),(null==P||P>e.length)&&(P=e.length);var k=new(e instanceof B?B:e instanceof Z?Z:R)(P-J);return k.set(e.subarray(J,P)),k},Q=function(e,P,J){var k=e.length;if(!k||J&&!J.l&&k<5)return P||new R(0);function T(e){var J=P.length;J<e&&((J=new R(Math.max(2*J,e))).set(P),P=J)}var F=!P||J,z=!J||J.i,a=(J=J||{},P=P||new R(3*k),J.f||0),S=J.p||0,v=J.b||0,K=J.l,c=J.d,E=J.m,y=J.n,Y=8*k;do{if(!K){J.f=a=t(e,S,1);var i=t(e,S+1,3);if(S+=3,!i){var g=e[(x=zO(S)+4)-4]|e[x-3]<<8,p=x+g;if(k<p){if(z)throw"unexpected EOF";break}F&&T(v+g),P.set(e.subarray(x,p),v),J.b=v+=g,J.p=S=8*p;continue}if(1==i)K=kO,c=FO,E=9,y=5;else{if(2!=i)throw"invalid block type";var g=t(e,S,31)+257,h=t(e,S+10,15)+4,N=g+t(e,S+5,31)+1;S+=14;for(var U=new R(N),m=new R(19),_=0;_<h;++_)m[M[_]]=t(e,S+3*_,7);S+=3*h;for(var p=eO(m),j=(1<<p)-1,H=D(m,p,1),_=0;_<N;){var x,b=H[t(e,S,j)];if(S+=15&b,(x=b>>>4)<16)U[_++]=x;else{var X=0,u=0;for(16==x?(u=3+t(e,S,3),S+=2,X=U[_-1]):17==x?(u=3+t(e,S,7),S+=3):18==x&&(u=11+t(e,S,127),S+=7);u--;)U[_++]=X}}var i=U.subarray(0,g),n=U.subarray(g),E=eO(i),y=eO(n),K=D(i,E,1),c=D(n,y,1)}if(Y<S){if(z)throw"unexpected EOF";break}}F&&T(v+131072);for(var s=(1<<E)-1,L=(1<<y)-1,Q=S;;Q=S){var q=(X=K[JO(e,S)&s])>>>4;if((S+=15&X)>Y){if(z)throw"unexpected EOF";break}if(!X)throw"invalid length/literal";if(q<256)P[v++]=q;else{if(256==q){Q=S,K=null;break}var o,C=q-254,q=(264<q&&(C=t(e,S,(1<<(o=f[_=q-257]))-1)+I[_],S+=o),c[JO(e,S)&L]),W=q>>>4;if(!q)throw"invalid distance";if(S+=15&q,n=$[W],3<W&&(o=w[W],n+=JO(e,S)&(1<<o)-1,S+=o),Y<S){if(z)throw"unexpected EOF";break}F&&T(v+131072);for(var d=v+C;v<d;v+=4)P[v]=P[v-n],P[v+1]=P[v+1-n],P[v+2]=P[v+2-n],P[v+3]=P[v+3-n];v=d}}J.l=K,J.p=Q,J.b=v,K&&(a=1,J.m=E,J.d=c,J.n=y)}while(!a);return v==P.length?P:l(P,0,v)},L=function(e,J,P){var k=J/8|0;e[k]|=P<<=7&J,e[1+k]|=P>>>8},A=function(e,J,P){var k=J/8|0;e[k]|=P<<=7&J,e[1+k]|=P>>>8,e[2+k]|=P>>>16},aO=function(e,J){for(var P=[],k=0;k<e.length;++k)e[k]&&P.push({s:k,f:e[k]});var T=P.length,F=P.slice();if(!T)return[O,0];if(1==T)return(i=new R(P[0].s+1))[P[0].s]=1,[i,1];P.sort(function(e,J){return e.f-J.f}),P.push({s:-1,f:25001});var z=P[0],a=P[1],S=0,v=1,K=2;for(P[0]={s:-1,f:z.f+a.f,l:z,r:a};v!=T-1;)z=P[P[S].f<P[K].f?S++:K++],a=P[S!=v&&P[S].f<P[K].f?S++:K++],P[v++]={s:-1,f:z.f+a.f,l:z,r:a};var c=F[0].s;for(k=1;k<T;++k)F[k].s>c&&(c=F[k].s);var E=new B(c+1),y=q(P[v-1],E,0);if(J<y){var k=0,Y=0,i=y-J,g=1<<i;for(F.sort(function(e,J){return E[J.s]-E[e.s]||e.f-J.f});k<T;++k){var p=F[k].s;if(!(E[p]>J))break;Y+=g-(1<<y-E[p]),E[p]=J}for(Y>>>=i;0<Y;){var h=F[k].s;E[h]<J?Y-=1<<J-E[h]++-1:++k}for(;0<=k&&Y;--k){var N=F[k].s;E[N]==J&&(--E[N],++Y)}y=J}return[new R(E),y]},q=function(e,J,P){return-1==e.s?Math.max(q(e.l,J,P+1),q(e.r,J,P+1)):J[e.s]=P},SO=function(e){for(var J=e.length;J&&!e[--J];);for(var P=new B(++J),k=0,T=e[0],F=1,z=function(e){P[k++]=e},a=1;a<=J;++a)if(e[a]==T&&a!=J)++F;else{if(!T&&2<F){for(;138<F;F-=138)z(32754);2<F&&(z(10<F?F-11<<5|28690:F-3<<5|12305),F=0)}else if(3<F){for(z(T),--F;6<F;F-=6)z(8304);2<F&&(z(F-3<<5|8208),F=0)}for(;F--;)z(T);F=1,T=e[a]}return[P.subarray(0,k),J]},G=function(e,J){for(var P=0,k=0;k<J.length;++k)P+=e[k]*J[k];return P},vO=function(e,J,P){var k=P.length,T=zO(J+2);e[T]=255&k,e[T+1]=k>>>8,e[T+2]=255^e[T],e[T+3]=255^e[T+1];for(var F=0;F<k;++F)e[T+F+4]=P[F];return 8*(T+4+k)},KO=function(e,J,P,k,T,F,z,a,S,v,K){L(J,K++,P),++T[256];for(var P=aO(T,15),c=P[0],P=P[1],E=aO(F,15),y=E[0],E=E[1],Y=SO(c),i=Y[0],Y=Y[1],g=SO(y),p=g[0],g=g[1],h=new B(19),N=0;N<i.length;++N)h[31&i[N]]++;for(N=0;N<p.length;++N)h[31&p[N]]++;for(var U=aO(h,7),m=U[0],U=U[1],_=19;4<_&&!m[M[_-1]];--_);var j,H,x=v+5<<3,b=G(T,d)+G(F,s)+z,T=G(T,c)+G(F,y)+z+14+3*_+G(h,m)+(2*h[16]+3*h[17]+7*h[18]);if(x<=b&&x<=T)return vO(J,K,e.subarray(S,S+v));if(L(J,K,1+(T<b)),K+=2,T<b){var X=D(c,P,0),u=c,n=D(y,E,0),Q=y,q=D(m,U,0);for(L(J,K,Y-257),L(J,K+5,g-1),L(J,K+10,_-4),K+=14,N=0;N<_;++N)L(J,K+3*N,m[M[N]]);K+=3*_;for(var o=[i,p],C=0;C<2;++C)for(var W=o[C],N=0;N<W.length;++N)L(J,K,q[j=31&W[N]]),K+=m[j],15<j&&(L(J,K,W[N]>>>5&127),K+=W[N]>>>12)}else X=PO,u=d,n=TO,Q=s;for(N=0;N<a;++N)255<k[N]?(A(J,K,X[257+(j=k[N]>>>18&31)]),K+=u[j+257],7<j&&(L(J,K,k[N]>>>23&31),K+=f[j]),H=31&k[N],A(J,K,n[H]),K+=Q[H],3<H&&(A(J,K,k[N]>>>5&8191),K+=w[H])):(A(J,K,X[k[N]]),K+=u[k[N]]);return A(J,K,X[256]),K+u[256]},cO=new Z([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),O=new R(0),o=function(){for(var e=new Z(256),J=0;J<256;++J){for(var P=J,k=9;--k;)P=(1&P&&3988292384)^P>>>1;e[J]=P}return e}(),x=function(){var k=-1;return{p:function(e){for(var J=k,P=0;P<e.length;++P)J=o[255&J^e[P]]^J>>>8;k=J},d:function(){return~k}}},C=function(){var z=1,a=0;return{p:function(e){for(var J=z,P=a,k=e.length,T=0;T!=k;){for(var F=Math.min(T+2655,k);T<F;++T)P+=J+=e[T];J=(65535&J)+15*(J>>16),P=(65535&P)+15*(P>>16)}z=J,a=P},d:function(){return(255&(z%=65521))<<24|z>>>8<<16|(255&(a%=65521))<<8|a>>>8}}},K=function(e,J,P,k,T){return h(e,null==J.level?6:J.level,null==J.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+J.mem,P,k,!T)},W=function(e,J){var P,k={};for(P in e)k[P]=e[P];for(P in J)k[P]=J[P];return k},EO=function(e,J,P){for(var k=e(),e=""+e,T=e.slice(e.indexOf("[")+1,e.lastIndexOf("]")).replace(/ /g,"").split(","),F=0;F<k.length;++F){var z=k[F],a=T[F];if("function"==typeof z){J+=";"+a+"=";var S=""+z;if(z.prototype)if(-1!=S.indexOf("[native code]")){var v=S.indexOf(" ",8)+1;J+=S.slice(v,S.indexOf("(",v))}else for(var K in J+=S,z.prototype)J+=";"+a+".prototype."+K+"="+z.prototype[K];else J+=S}else P[a]=z}return[J,P]},yO=[],YO=function(e){var J,P=[];for(J in e)(e[J]instanceof R||e[J]instanceof B||e[J]instanceof Z)&&P.push((e[J]=new e[J].varructor(e[J])).buffer);return P},iO=function(e,J,P,k){var T;if(!yO[P]){for(var F="",z={},a=e.length-1,S=0;S<a;++S)F=(T=EO(e[S],F,z))[0],z=T[1];yO[P]=EO(e[a],F,z)}var v=W({},yO[P][1]);return Y.default(yO[P][0]+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+J+"}",P,v,YO(v),k)},U=function(e,J){return e[J]|e[J+1]<<8},m=function(e,J){return(e[J]|e[J+1]<<8|e[J+2]<<16|e[J+3]<<24)>>>0},gO=function(e,J){return m(e,J)+4294967296*m(e,J+4)},g=function(e,J,P){for(;P;++J)e[J]=P,P>>>=8},pO=function(e,J){var P=J.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=J.level<2?4:9==J.level?2:0,e[9]=3,0!=J.mtime&&g(e,4,Math.floor(new Date(J.mtime||Date.now())/1e3)),P){e[3]=8;for(var k=0;k<=P.length;++k)e[k+10]=P.charCodeAt(k)}},hO=function(e){if(31!=e[0]||139!=e[1]||8!=e[2])throw"invalid gzip data";var J=e[3],P=10;4&J&&(P+=e[10]|2+(e[11]<<8));for(var k=(J>>3&1)+(J>>4&1);0<k;k-=!e[P++]);return P+(2&J)},NO=function(e){var J=e.length;return(e[J-4]|e[J-3]<<8|e[J-2]<<16|e[J-1]<<24)>>>0},UO=function(e){return 10+(e.filename&&e.filename.length+1||0)},mO=function(e,J){J=J.level,J=0==J?0:J<6?1:9==J?3:2;e[0]=120,e[1]=J<<6|(J?32-2*J:1)},_O=function(e){if(8!=(15&e[0])||7<e[0]>>>4||(e[0]<<8|e[1])%31)throw"invalid zlib data";if(32&e[1])throw"invalid zlib data: preset dictionaries not supported"};function jO(e,J){return J||"function"!=typeof e||(J=e,e={}),this.ondata=J,e}var c=function(){function e(e,J){J||"function"!=typeof e||(J=e,e={}),this.ondata=J,this.o=e||{}}return e.prototype.p=function(e,J){this.ondata(K(e,this.o,0,0,!J),J)},e.prototype.push=function(e,J){if(this.d)throw"stream finished";if(!this.ondata)throw"no stream handler";this.d=J,this.p(e,J||!1)},e}(),HO=(e.Deflate=c,function(e,J){v([F,function(){return[S,c]}],this,jO.call(this,e,J),function(e){e=new c(e.data);onmessage=S(e)},6)});function xO(e,J,P){if(P||(P=J,J={}),"function"!=typeof P)throw"no callback";return a(e,J,[F],function(e){return z(bO(e.data[0],e.data[1]))},0,P)}function bO(e,J){return K(e,J||{},0,0)}e.AsyncDeflate=HO,e.deflate=xO,e.deflateSync=bO;var E=function(){function e(e){this.s={},this.p=new R(0),this.ondata=e}return e.prototype.e=function(e){if(this.d)throw"stream finished";if(!this.ondata)throw"no stream handler";var J=this.p.length,P=new R(J+e.length);P.set(this.p),P.set(e,J),this.p=P},e.prototype.c=function(e){this.d=this.s.i=e||!1;var e=this.s.b,J=Q(this.p,this.o,this.s);this.ondata(l(J,e,this.s.b),this.d),this.o=l(J,this.s.b-32768),this.s.b=this.o.length,this.p=l(this.p,this.s.p/8|0),this.s.p&=7},e.prototype.push=function(e,J){this.e(e),this.c(J)},e}(),XO=(e.Inflate=E,function(e){this.ondata=e,v([T,function(){return[S,E]}],this,0,function(){var e=new E;onmessage=S(e)},7)});function uO(e,J,P){if(P||(P=J,J={}),"function"!=typeof P)throw"no callback";return a(e,J,[T],function(e){return z(i(e.data[0],n(e.data[1])))},1,P)}function i(e,J){return Q(e,J)}e.AsyncInflate=XO,e.inflate=uO,e.inflateSync=i;var nO=function(){function e(e,J){this.c=x(),this.l=0,this.v=1,c.call(this,e,J)}return e.prototype.push=function(e,J){c.prototype.push.call(this,e,J)},e.prototype.p=function(e,J){this.c.p(e),this.l+=e.length;e=K(e,this.o,this.v&&UO(this.o),J&&8,!J);this.v&&(pO(e,this.o),this.v=0),J&&(g(e,e.length-8,this.c.d()),g(e,e.length-4,this.l)),this.ondata(e,J)},e}(),J=(e.Gzip=nO,e.Compress=nO,function(e,J){v([F,N,function(){return[S,c,nO]}],this,jO.call(this,e,J),function(e){e=new nO(e.data);onmessage=S(e)},8)});function QO(e,J,P){if(P||(P=J,J={}),"function"!=typeof P)throw"no callback";return a(e,J,[F,N,function(){return[qO]}],function(e){return z(qO(e.data[0],e.data[1]))},2,P)}function qO(e,J){J=J||{};var P=x(),k=e.length,e=(P.p(e),K(e,J,UO(J),8)),T=e.length;return pO(e,J),g(e,T-8,P.d()),g(e,T-4,k),e}e.AsyncGzip=J,e.AsyncCompress=J,e.gzip=QO,e.compress=QO,e.gzipSync=qO,e.compressSync=qO;var oO=function(){function e(e){this.v=1,E.call(this,e)}return e.prototype.push=function(e,J){if(E.prototype.e.call(this,e),this.v){e=3<this.p.length?hO(this.p):4;if(e>=this.p.length&&!J)return;this.p=this.p.subarray(e),this.v=0}if(J){if(this.p.length<8)throw"invalid gzip stream";this.p=this.p.subarray(0,-8)}E.prototype.c.call(this,J)},e}(),CO=(e.Gunzip=oO,function(e){this.ondata=e,v([T,_,function(){return[S,E,oO]}],this,0,function(){var e=new oO;onmessage=S(e)},9)});function WO(e,J,P){if(P||(P=J,J={}),"function"!=typeof P)throw"no callback";return a(e,J,[T,_,function(){return[dO]}],function(e){return z(dO(e.data[0]))},3,P)}function dO(e,J){return Q(e.subarray(hO(e),-8),J||new R(NO(e)))}e.AsyncGunzip=CO,e.gunzip=WO,e.gunzipSync=dO;var sO=function(){function e(e,J){this.c=C(),this.v=1,c.call(this,e,J)}return e.prototype.push=function(e,J){c.prototype.push.call(this,e,J)},e.prototype.p=function(e,J){this.c.p(e);e=K(e,this.o,this.v&&2,J&&4,!J);this.v&&(mO(e,this.o),this.v=0),J&&g(e,e.length-4,this.c.d()),this.ondata(e,J)},e}(),J=(e.Zlib=sO,function(e,J){v([F,j,function(){return[S,c,sO]}],this,jO.call(this,e,J),function(e){e=new sO(e.data);onmessage=S(e)},10)});function LO(e,J,P){if(P||(P=J,J={}),"function"!=typeof P)throw"no callback";return a(e,J,[F,j,function(){return[MO]}],function(e){return z(MO(e.data[0],e.data[1]))},4,P)}function MO(e,J){J=J||{};var P=C(),e=(P.p(e),K(e,J,2,4));return mO(e,J),g(e,e.length-4,P.d()),e}e.AsyncZlib=J,e.zlib=LO,e.zlibSync=MO;var DO=function(){function e(e){this.v=1,E.call(this,e)}return e.prototype.push=function(e,J){if(E.prototype.e.call(this,e),this.v){if(this.p.length<2&&!J)return;this.p=this.p.subarray(2),this.v=0}if(J){if(this.p.length<4)throw"invalid zlib stream";this.p=this.p.subarray(0,-4)}E.prototype.c.call(this,J)},e}(),tO=(e.Unzlib=DO,function(e){this.ondata=e,v([T,H,function(){return[S,E,DO]}],this,0,function(){var e=new DO;onmessage=S(e)},11)});function AO(e,J,P){if(P||(P=J,J={}),"function"!=typeof P)throw"no callback";return a(e,J,[T,H,function(){return[GO]}],function(e){return z(GO(e.data[0],n(e.data[1])))},5,P)}function GO(e,J){return Q((_O(e),e.subarray(2,-4)),J)}e.AsyncUnzlib=tO,e.unzlib=AO,e.unzlibSync=GO;var IO=function(){function e(e){this.G=oO,this.I=E,this.Z=DO,this.ondata=e}return e.prototype.push=function(e,J){if(!this.ondata)throw"no stream handler";var P,k;this.s?this.s.push(e,J):(this.p&&this.p.length?((P=new R(this.p.length+e.length)).set(this.p),P.set(e,this.p.length)):this.p=e,2<this.p.length&&((k=this).s=new(31==this.p[0]&&139==this.p[1]&&8==this.p[2]?this.G:8!=(15&this.p[0])||7<this.p[0]>>4||(this.p[0]<<8|this.p[1])%31?this.I:this.Z)(function(){k.ondata.apply(k,arguments)}),this.s.push(this.p,J),this.p=null))},e}(),J=(e.Decompress=IO,function(){function e(e){this.G=CO,this.I=XO,this.Z=tO,this.ondata=e}return e.prototype.push=function(e,J){IO.prototype.push.call(this,e,J)},e}());function $O(e,J,P){if(P||(P=J,J={}),"function"!=typeof P)throw"no callback";return(31==e[0]&&139==e[1]&&8==e[2]?WO:8!=(15&e[0])||7<e[0]>>4||(e[0]<<8|e[1])%31?uO:AO)(e,J,P)}function RO(e,J){return(31==e[0]&&139==e[1]&&8==e[2]?dO:8!=(15&e[0])||7<e[0]>>4||(e[0]<<8|e[1])%31?i:GO)(e,J)}e.AsyncDecompress=J,e.decompress=$O,e.decompressSync=RO;var BO=function(e,J,P,k){for(var T in e){var F=e[T],T=J+T;F instanceof R?P[T]=[F,k]:Array.isArray(F)?P[T]=[F[0],W(k,F[1])]:BO(F,T+"/",P,k)}},ZO="undefined"!=typeof TextEncoder&&new TextEncoder,fO="undefined"!=typeof TextDecoder&&new TextDecoder,wO=0;try{fO.decode(O,{stream:!0}),wO=1}catch(Y){}function lO(e){for(var J="",P=0;;){var k=e[P++],T=(127<k)+(223<k)+(239<k);if(P+T>e.length)return[J,l(e,P-1)];T?3==T?(k=((15&k)<<18|(63&e[P++])<<12|(63&e[P++])<<6|63&e[P++])-65536,J+=String.fromCharCode(55296|k>>10,56320|1023&k)):J+=String.fromCharCode(1&T?(31&k)<<6|63&e[P++]:(15&k)<<12|(63&e[P++])<<6|63&e[P++]):J+=String.fromCharCode(k)}}J=function(){function e(e){this.ondata=e,wO?this.t=new TextDecoder:this.p=O}return e.prototype.push=function(e,J){if(!this.ondata)throw"no callback";if(J=!!J,this.t){if(this.ondata(this.t.decode(e,{stream:!0}),J),J){if(this.t.decode().length)throw"invalid utf-8 data";this.t=null}}else{if(!this.p)throw"stream finished";var P=new R(this.p.length+e.length),e=(P.set(this.p),P.set(e,this.p.length),lO(P)),P=e[0],e=e[1];if(J){if(e.length)throw"invalid utf-8 data";this.p=null}else this.p=e;this.ondata(P,J)}},e}(),e.DecodeUTF8=J,J=function(){function e(e){this.ondata=e}return e.prototype.push=function(e,J){if(!this.ondata)throw"no callback";if(this.d)throw"stream finished";this.ondata(b(e),this.d=J||!1)},e}();function b(e,J){if(J){for(var P=new R(e.length),k=0;k<e.length;++k)P[k]=e.charCodeAt(k);return P}if(ZO)return ZO.encode(e);for(var T=e.length,F=new R(e.length+(e.length>>1)),z=0,a=function(e){F[z++]=e},k=0;k<T;++k){z+5>F.length&&((S=new R(z+8+(T-k<<1))).set(F),F=S);var S=e.charCodeAt(k);S<128||J?a(S):(S<2048?a(192|S>>6):(55295<S&&S<57344?(a(240|(S=65536+(1047552&S)|1023&e.charCodeAt(++k))>>18),a(128|S>>12&63)):a(224|S>>12),a(128|S>>6&63)),a(128|63&S))}return l(F,0,z)}function OO(e,J){if(J){for(var P="",k=0;k<e.length;k+=16384)P+=String.fromCharCode.apply(null,e.subarray(k,k+16384));return P}if(fO)return fO.decode(e);J=lO(e);if(J[1].length)throw"invalid utf-8 data";return J[0]}e.EncodeUTF8=J,e.strToU8=b,e.strFromU8=OO;function rO(e){return 1==e?3:e<6?2:9==e?1:0}function VO(e,J){return J+30+U(e,J+26)+U(e,J+28)}function e_(e,J,P){var k=U(e,J+28),T=OO(e.subarray(J+46,J+46+k),!(2048&U(e,J+8))),k=J+46+k,F=m(e,J+20),P=P&&4294967295==F?P_(e,k):[F,m(e,J+24),m(e,J+42)],F=P[0],z=P[1],P=P[2];return[U(e,J+10),F,z,T,k+U(e,J+30)+U(e,J+32),P]}function X(e){var J=0;if(e)for(var P in e){P=e[P].length;if(65535<P)throw"extra field too long";J+=P+4}return J}function u(e,J,P,k,T,F,z,a){var S=k.length,v=P.extra,K=a&&a.length,c=X(v),T=(g(e,J,null!=z?33639248:67324752),J+=4,null!=z&&(e[J++]=20,e[J++]=P.os),e[J]=20,J+=2,e[J++]=P.flag<<1|(null==F&&8),e[J++]=T&&8,e[J++]=255&P.compression,e[J++]=P.compression>>8,new Date(null==P.mtime?Date.now():P.mtime)),E=T.getFullYear()-1980;if(E<0||119<E)throw"date not in range 1980-2099";if(g(e,J,E<<25|T.getMonth()+1<<21|T.getDate()<<16|T.getHours()<<11|T.getMinutes()<<5|T.getSeconds()>>>1),J+=4,null!=F&&(g(e,J,P.crc),g(e,J+4,F),g(e,J+8,P.size)),g(e,J+12,S),g(e,J+14,c),J+=16,null!=z&&(g(e,J,K),g(e,J+6,P.attrs),g(e,J+10,z),J+=14),e.set(k,J),J+=S,c)for(var y in v){var Y=v[y],i=Y.length;g(e,J,+y),g(e,J+2,i),e.set(Y,J+4),J+=4+i}K&&(e.set(a,J),J+=K)}function J_(e,J,P,k,T){g(e,J,101010256),g(e,J+8,P),g(e,J+10,P),g(e,J+12,k),g(e,J+16,T)}var P_=function(e,J){for(;1!=U(e,J);J+=4+U(e,J+2));return[gO(e,J+12),gO(e,J+4),gO(e,J+20)]},k_=function(){function e(e){this.filename=e,this.c=x(),this.size=0,this.compression=0}return e.prototype.process=function(e,J){this.ondata(null,e,J)},e.prototype.push=function(e,J){if(!this.ondata)throw"no callback - add to ZIP archive before pushing";this.c.p(e),this.size+=e.length,J&&(this.crc=this.c.d()),this.process(e,J||!1)},e}(),J=(e.ZipPassThrough=k_,function(){function e(e,J){var P=this;J=J||{},k_.call(this,e),this.d=new c(J,function(e,J){P.ondata(null,e,J)}),this.compression=8,this.flag=rO(J.level)}return e.prototype.process=function(e,J){try{this.d.push(e,J)}catch(e){this.ondata(e,null,J)}},e.prototype.push=function(e,J){k_.prototype.push.call(this,e,J)},e}()),J=(e.ZipDeflate=J,function(){function e(e,J){var k=this;J=J||{},k_.call(this,e),this.d=new HO(J,function(e,J,P){k.ondata(e,J,P)}),this.compression=8,this.flag=rO(J.level),this.terminate=this.d.terminate}return e.prototype.process=function(e,J){this.d.push(e,J)},e.prototype.push=function(e,J){k_.prototype.push.call(this,e,J)},e}()),J=(e.AsyncZipDeflate=J,function(){function e(e){this.ondata=e,this.u=[],this.d=1}return e.prototype.add=function(k){var T=this;if(2&this.d)throw"stream finished";var e=b(k.filename),J=e.length,P=k.comment,F=P&&b(P),P=J!=k.filename.length||F&&P.length!=F.length,z=J+X(k.extra)+30;if(65535<J)throw"filename too long";function a(){for(var e=0,J=S;e<J.length;e++)T.ondata(null,J[e],!1);S=[]}var J=new R(z),S=(u(J,0,k,e,P),[J]),v=this.d,K=(this.d=0,this.u.length),c=W(k,{f:e,u:P,o:F,t:function(){k.terminate&&k.terminate()},r:function(){var e;a(),v&&((e=T.u[K+1])?e.r():T.d=1),v=1}}),E=0;k.ondata=function(e,J,P){e?(T.ondata(e,J,P),T.terminate()):(E+=J.length,S.push(J),P?(e=new R(16),g(e,0,134695760),g(e,4,k.crc),g(e,8,E),g(e,12,k.size),S.push(e),c.c=E,c.b=z+E+16,c.crc=k.crc,c.size=k.size,v&&c.r(),v=1):v&&a())},this.u.push(c)},e.prototype.end=function(){var e=this;if(2&this.d){if(1&this.d)throw"stream finishing";throw"stream finished"}this.d?this.e():this.u.push({r:function(){1&e.d&&(e.u.splice(-1,1),e.e())},t:function(){}}),this.d=3},e.prototype.e=function(){for(var e=0,J=0,P=0,k=0,T=this.u;k<T.length;k++)P+=46+(F=T[k]).f.length+X(F.extra)+(F.o?F.o.length:0);for(var F,z=new R(P+22),a=0,S=this.u;a<S.length;a++)u(z,e,F=S[a],F.f,F.u,F.c,J,F.o),e+=46+F.f.length+X(F.extra)+(F.o?F.o.length:0),J+=F.b;J_(z,e,this.u.length,P,J),this.ondata(null,z,!0),this.d=2},e.prototype.terminate=function(){for(var e=0,J=this.u;e<J.length;e++)J[e].t();this.d=2},e}());function T_(e,J,i){if(i||(i=J,J={}),"function"!=typeof i)throw"no callback";function g(){for(var e=0;e<H.length;++e)H[e]()}function p(){for(var e=new R(_+22),J=m,P=_-m,k=_=0;k<S;++k){var T=j[k];try{var F=T.c.length,z=(u(e,_,T,T.f,T.u,F),30+T.f.length+X(T.extra)),a=_+z;e.set(T.c,a),u(e,m,T,T.f,T.u,F,_,T.m),m+=16+z+(T.m?T.m.length:0),_=a+F}catch(e){return i(e,null)}}J_(e,m,j.length,P,J),i(null,e)}var h={},N=(BO(e,"",h,J),v_.keys(h)),U=N.length,m=0,_=0,S=U,j=Array(U),H=[];U||p();for(var P=0;P<S;++P)!function(P){function e(e,J){e?(g(),i(e,null)):(e=J.length,j[P]=W(F,{size:a,crc:z.d(),c:J,f:S,m:c,u:v!=k.length||c&&K.length!=E,compression:Y}),m+=30+v+y+e,_+=76+2*(v+y)+(E||0)+e,--U||p())}var k=N[P],J=h[k],T=J[0],F=J[1],z=x(),a=T.length,S=(z.p(T),b(k)),v=S.length,K=F.comment,c=K&&b(K),E=c&&c.length,y=X(F.extra),Y=0==F.level?0:8;if(65535<v&&e("filename too long",null),Y)if(a<16e4)try{e(null,bO(T,F))}catch(P){e(P,null)}else H.push(xO(T,F,e));else e(null,T)}(P);return g}function F_(e,J){var P,k={},T=[],F=(BO(e,"",k,J=J||{}),0),z=0;for(P in k){var a=k[P],S=a[0],a=a[1],v=0==a.level?0:8,K=(U=b(P)).length,c=a.comment,E=c&&b(c),y=E&&E.length,Y=X(a.extra);if(65535<K)throw"filename too long";var i=v?bO(S,a):S,g=i.length,p=x();p.p(S),T.push(W(a,{size:S.length,crc:p.d(),c:i,f:U,m:E,u:K!=P.length||E&&c.length!=y,o:F,compression:v})),F+=30+K+Y+g,z+=76+2*(K+Y)+(y||0)+g}for(var h=new R(z+22),e=F,J=z-F,N=0;N<T.length;++N){u(h,(U=T[N]).o,U,U.f,U.u,U.c.length);var U,m=30+U.f.length+X(U.extra);h.set(U.c,U.o+m),u(h,F,U,U.f,U.u,U.c.length,U.o,U.m),F+=16+m+(U.m?U.m.length:0)}return J_(h,F,T.length,J,e),h}e.Zip=J,e.zip=T_,e.zipSync=F_;var z_=function(){function e(){}return e.prototype.push=function(e,J){this.ondata(null,e,J)},e.compression=0,e}(),J=(e.UnzipPassThrough=z_,function(){function e(){var P=this;this.i=new E(function(e,J){P.ondata(null,e,J)})}return e.prototype.push=function(J,P){try{this.i.push(J,P)}catch(e){this.ondata(e,J,P)}},e.compression=8,e}()),J=(e.UnzipInflate=J,function(){function e(e,J){var k=this;J<32e4?this.i=new E(function(e,J){k.ondata(null,e,J)}):(this.i=new XO(function(e,J,P){k.ondata(e,J,P)}),this.terminate=this.i.terminate)}return e.prototype.push=function(e,J){this.i.terminate&&(e=l(e,0)),this.i.push(e,J)},e.compression=8,e}()),J=(e.AsyncUnzipInflate=J,function(){function e(e){this.onfile=e,this.k=[],this.o={0:z_},this.p=O}return e.prototype.push=function(e,J){var E=this;if(!this.onfile)throw"no callback";if(!this.p)throw"stream finished";if(0<this.c){var P=Math.min(this.c,e.length),k=e.subarray(0,P);if(this.c-=P,this.d?this.d.push(k,!this.c):this.k[0].push(k),(e=e.subarray(P)).length)return this.push(e,J)}else{var y=0,Y=0,i=void 0,g=void 0;this.p.length?e.length?((g=new R(this.p.length+e.length)).set(this.p),g.set(e,this.p.length)):g=this.p:g=e;for(var p=g.length,h=this.c,k=h&&this.d,N=this;Y<p-4&&"break"!==function(){var k,e,J,P,T,F,z,a,S,v,K,c=m(g,Y);return 67324752==c?(y=1,i=Y,N.d=null,N.c=0,J=U(g,Y+6),k=U(g,Y+8),e=2048&J,J=8&J,P=U(g,Y+26),T=U(g,Y+28),Y+30+P+T<p&&(N.k.unshift(F=[]),y=2,a=m(g,Y+18),S=m(g,Y+22),v=OO(g.subarray(Y+30,Y+=30+P),!e),4294967295==a?(P=J?[-2]:P_(g,Y),a=P[0],S=P[1]):J&&(a=-1),Y+=T,N.c=a,K={name:v,compression:k,start:function(){if(!K.ondata)throw"no callback";if(a){var e=E.o[k];if(!e)throw"unknown compression type "+k;(z=a<0?new e(v):new e(v,a,S)).ondata=function(e,J,P){K.ondata(e,J,P)};for(var J=0,P=F;J<P.length;J++)z.push(P[J],!1);E.k[0]==F&&E.c?E.d=z:z.push(O,!0)}else K.ondata(null,O,!0)},terminate:function(){z&&z.terminate&&z.terminate()}},0<=a&&(K.size=a,K.originalSize=S),N.onfile(K)),"break"):h?134695760==c?(i=Y+=12+(-2==h&&8),y=3,N.c=0,"break"):33639248==c?(i=Y-=4,y=3,N.c=0,"break"):void 0:void 0}();++Y);if(this.p=O,h<0&&(P=g.subarray(0,y?i-12-(-2==h&&8)-(134695760==m(g,i-16)&&4):Y),k?k.push(P,!!y):this.k[+(2==y)].push(P)),2&y)return this.push(g.subarray(Y),J);this.p=g.subarray(Y)}if(J){if(this.c)throw"invalid zip file";this.p=null}},e.prototype.register=function(e){this.o[e.compression]=e},e}());function a_(a,S){if("function"!=typeof S)throw"no callback";for(var v=[],K=function(){for(var e=0;e<v.length;++e)v[e]()},c={},e=a.length-22;101010256!=m(a,e);--e)if(!e||65558<a.length-e)return void S("invalid zip file",null);var E=U(a,e+8),J=(E||S(null,{}),E),y=m(a,e+16),Y=4294967295==y;if(Y){if(e=m(a,e-12),101075792!=m(a,e))return void S("invalid zip file",null);J=E=m(a,e+32),y=m(a,e+48)}for(var P=0;P<J;++P)!function(){function J(e,J){e?(K(),S(e,null)):(c[F]=J,--E||S(null,c))}var e=e_(a,y,Y),P=e[0],k=e[1],T=e[2],F=e[3],z=e[4],e=VO(a,e[5]);y=z;if(P)if(8==P){z=a.subarray(e,e+k);if(k<32e4)try{J(null,i(z,new R(T)))}catch(e){J(e,null)}else v.push(uO(z,{size:T},J))}else J("unknown compression type "+P,null);else J(null,l(a,e,e+k))}();return K}function S_(e){for(var J={},P=e.length-22;101010256!=m(e,P);--P)if(!P||65558<e.length-P)throw"invalid zip file";var k=U(e,P+8);if(!k)return{};var T=4294967295==(E=m(e,P+16));if(T){if(P=m(e,P-12),101075792!=m(e,P))throw"invalid zip file";k=m(e,P+32),E=m(e,P+48)}for(var F=0;F<k;++F){var z=e_(e,E,T),a=z[0],S=z[1],v=z[2],K=z[3],c=z[4],z=VO(e,z[5]),E=c;if(a){if(8!=a)throw"unknown compression type "+a;J[K]=i(e.subarray(z,z+S),new R(v))}else J[K]=l(e,z,z+S)}return J}return e.Unzip=J,e.unzip=a_,e.unzipSync=S_,e}(),FO="binary:",zO=function(e,J){this.textureLoader=e,this.manager=J},aO=(e(zO,v_,{_zS:function(e){e=e||{},F=[],a={},n=this._AS();var J=this._BS(e),J=this._CS(J,e),J=this._DS(J),P=this._ES(),k=(new YO)._zS(P);return this._FS(P,k,J,e),S.externalAssetURIs=F,S.sourceBufferMap=a,S},_AS:function(){var T=new Map;return"Connections"in K&&K.Connections.connections.forEach(function(e){var J=e[0],P=e[1],e=e[2],k=(T.has(J)||T.set(J,{parents:[],children:[]}),{ID:P,relationship:e}),k=(T.get(J).parents.push(k),T.has(P)||T.set(P,{parents:[],children:[]}),{ID:J,relationship:e});T.get(P).children.push(k)}),T},_BS:function(e){var J={},P={};if("Video"in K.Objects){var k,T=K.Objects.Video;for(k in T){var F,z,a,S=T[k];J[F=parseInt(k)]=S.RelativeFilename||S.Filename,"Content"in S&&(a=S.Content instanceof ArrayBuffer&&0<S.Content.byteLength,z="string"==typeof S.Content&&""!==S.Content,(a||z)&&(a=this._lS(T[k],e.binaryImagePath),P[S.RelativeFilename||S.Filename]=a))}}for(F in J){var v=J[F];P[v]!==m?J[F]=P[v]:J[F]=J[F].split("\\").pop()}return J},_lS:function(e,J){var P,k=e.Content,T=e.RelativeFilename||e.Filename,F=T.slice(T.lastIndexOf(".")+1).toLowerCase();switch(F){case"bmp":P="image/bmp";break;case"jpg":case"jpeg":P="image/jpeg";break;case"png":P="image/png";break;case"tif":P="image/tiff";break;case"tga":null===this.manager.getHandler(".tga")&&console.warn("FBXLoader: TGA loader not found, skipping ",T),P="image/tga";break;default:return void console.warn('FBXLoader: Image type "'+F+'" is not supported.')}return"string"==typeof k?"data:"+P+";base64,"+k:(e=new Uint8Array(k),null==J&&URL?d.URL.createObjectURL(new Blob([e],{type:P})):(k=(J||"")+T.split("\\").pop(),a[k]=e,FO+k))},_CS:function(e,J){var P=new Map;if("Texture"in K.Objects){var k,T=K.Objects.Texture;for(k in T){var F=this.parseTexture(T[k],e,J);P.set(parseInt(k),F)}}return P},parseTexture:function(e,J,P){var k,T,e=n.get(e.id).children;if(e!==m&&0<e.length&&J[e[0].ID]!==m){if(0===(T=J[e[0].ID]).indexOf("blob:")||0===T.indexOf("data:"))return(J=new Image).src=T,k=new eO({image:J,dynamic:!0}),J.onload=function(){delete k.dynamic},k;if(0===T.indexOf(FO))return T.slice(FO.length)}return T&&(T=T.match(/[^\\/]*$/)[0],F.indexOf(T)<0&&F.push(T),P.assetsURIMap&&P.assetsURIMap[T]?T=P.assetsURIMap[T]:P.path&&(T=P.path+T)),T},_DS:function(e){var J=new Map;if("Material"in K.Objects){var P,k=K.Objects.Material;for(P in k){var T=this._kS(k[P],e);null!==T&&J.set(parseInt(P),T)}}return J},_kS:function(e,J){var P=e.id,k=e.attrName,T=e.ShadingModel;if("object"==typeof T&&T.value,!n.has(P))return null;T=this._gS(e,J,P),e=new Q;return e.setValues(T),e.name=k,e},_gS:function(e,J,P){var k,T,F={},z=(e.BumpFactor&&(F.bumpScale=e.BumpFactor.value),e.Diffuse?F.diffuse=e.Diffuse.value:!e.DiffuseColor||"Color"!==e.DiffuseColor.type&&"ColorRGB"!==e.DiffuseColor.type||(F.diffuse=e.DiffuseColor.value),e.DisplacementFactor&&(F.displacementScale=e.DisplacementFactor.value),e.Emissive?F.emissive=e.Emissive.value:!e.EmissiveColor||"Color"!==e.EmissiveColor.type&&"ColorRGB"!==e.EmissiveColor.type||(F.emissive=e.EmissiveColor.value),e.EmissiveFactor&&1!==(k=parseFloat(e.EmissiveFactor.value))&&(T=F.emissive)!==m&&(T=c.Default.getInternal().toColorArray(T),F.emissive=[T[0]*k,T[1]*k,T[2]*k]),e.Opacity&&(F.opacity=parseFloat(e.Opacity.value)),F.opacity<1&&(F.transparent=!0),e.ReflectionFactor&&(F.envMapIntensity=e.ReflectionFactor.value),e.Shininess&&(F.shininess=e.Shininess.value),e.Specular?F.specular=e.Specular.value:e.SpecularColor&&"Color"===e.SpecularColor.type&&(F.specular=e.SpecularColor.value),this);return n.get(P).children.forEach(function(e){switch(e.relationship){case"Bump":F.bumpMap=z._HS(J,e.ID);break;case"Maya|TEX_ao_map":F.aoMap=z._HS(J,e.ID);break;case"DiffuseColor":case"Maya|TEX_color_map":F.map=z._HS(J,e.ID);break;case"DisplacementColor":F.displacementMap=z._HS(J,e.ID);break;case"EmissiveColor":F.emissiveMap=z._HS(J,e.ID);break;case"NormalMap":case"Maya|TEX_normal_map":F.normalMap=z._HS(J,e.ID);break;case"ReflectionColor":F.envMap=z._HS(J,e.ID);break;case"SpecularColor":F.specularMap=z._HS(J,e.ID);break;case"TransparentColor":case"TransparencyFactor":F.alphaMap=z._HS(J,e.ID),F.transparent=!0}}),F},_HS:function(e,J){return"LayeredTexture"in K.Objects&&J in K.Objects.LayeredTexture&&(console.warn("layered textures are not supported."),J=n.get(J).children[0].ID),e.get(J)},_ES:function(){var e={},J={};if("Deformer"in K.Objects){var P,k=K.Objects.Deformer;for(P in k){var T,F=k[P],z=n.get(parseInt(P));"Skin"===F.attrType?((T=this._mS(z,k)).ID=P,1<z.parents.length&&console.warn("skeleton attached to more than one geometry is not supported."),T.geometryID=z.parents[0].ID,e[P]=T):"BlendShape"===F.attrType&&((T={id:P}).rawTargets=this._nS(z,k),T.id=P,1<z.parents.length&&console.warn("morph target attached to more than one geometry is not supported."),J[P]=T)}}return{skeletons:e,morphTargets:J}},_mS:function(e,P){var k=[];return e.children.forEach(function(e){var J=P[e.ID];"Cluster"===J.attrType&&(e={ID:e.ID,indices:[],weights:[],transformLink:(new h).fromArray(J.TransformLink.a)},"Indexes"in J&&(e.indices=J.Indexes.a,e.weights=J.Weights.a),k.push(e))}),{rawBones:k,bones:[]}},_nS:function(e,J){for(var P=[],k=0;k<e.children.length;k++){var T=e.children[k],F=J[T.ID],z={name:F.attrName,initialWeight:F.DeformPercent,id:F.id,fullWeights:F.FullWeights.a};if("BlendShapeChannel"!==F.attrType)return;z.geoID=n.get(parseInt(T.ID)).children.filter(function(e){return e.relationship===m})[0].ID,P.push(z)}return P},_FS:function(e,J,P,k){S=new X;var T=this._oS(e.skeletons,J,P),F=K.Objects.Model,z=this;T.forEach(function(J){var e=F[J.ID],e=(z._IS(J,e),n.get(J.ID));e&&(e.parents.forEach(function(e){e=T.get(e.ID);e!==m&&e.add(J)}),null==J.getParent()&&S.add(J))}),this._JS(e.skeletons,J,T),S.traverse(function(e){var J,P=e.userData.transformData;P&&((J=e.getParent())&&(P.parentMatrix=J.matrix,P.parentMatrixWorld=J.matrixWorld),J=w(P),e.applyMatrix4(J))}),S.materialMap=P,S.animations=(new iO)._zS(k)},_oS:function(e,J,P){var k,T=new Map,F=K.Objects.Model;for(k in F){var z=parseInt(k),a=F[k],S=n.get(z),v=S?this._pS(S,e,z,a.attrName):null;v||((v="Mesh"===a.attrType?this._sS(S,J,P):new X).name=a.attrName?g(a.attrName):"",a.uid||(a.uid=c.Math.generateIncreasingID()),v.uid=a.uid,v.ID=z),this._uS(v,a),T.set(z,v)}return T},_pS:function(e,J,T,F){var z=null;return e.parents.forEach(function(P){for(var e in J){var k=J[e];k.rawBones.forEach(function(e,J){e.ID===P.ID&&((z=new X).isBone=!0,z.matrixWorld.copy(e.transformLink),z.name=F?g(F):"",z.ID=T,k.bones[J]=z)})}}),z},_rS:function(e){var J,P;if(e.children.forEach(function(e){e=K.Objects.NodeAttribute[e.ID];e!==m&&(P=e)}),P===m)J=new Object3D;else{var e=P.LightType===m?0:P.LightType.value,k=16777215,T=(P.Color!==m&&(k=(new Color).fromArray(P.Color.value)),P.Intensity===m?1:P.Intensity.value/100),F=(P.CastLightOnObject!==m&&0===P.CastLightOnObject.value&&(T=0),0);P.FarAttenuationEnd!==m&&(F=P.EnableFarAttenuation!==m&&0===P.EnableFarAttenuation.value?0:P.FarAttenuationEnd.value);switch(e){case 0:J=new PointLight(k,T,F,1);break;case 1:J=new DirectionalLight(k,T);break;case 2:var z=Math.PI/3,a=(P.InnerAngle!==m&&(z=U(P.InnerAngle.value)),0);P.OuterAngle!==m&&(a=U(P.OuterAngle.value),a=Math.max(a,1)),J=new SpotLight(k,T,F,z,a,1);break;default:console.warn("Unknown light type "+P.LightType.value+", defaulting to a PointLight."),J=new PointLight(k,T)}P.CastShadows!==m&&1===P.CastShadows.value&&(J.castShadow=!0)}return J},_sS:function(e,J,P){var k=null,T=null,F=[];return e.children.forEach(function(e){J.has(e.ID)&&(k=J.get(e.ID)),P.has(e.ID)&&F.push(P.get(e.ID))}),T=1<F.length?F:1==F.length?F[0]:((e=P.has(-1))||(e=(new Q).setValues({name:"__missing__"}),P.set(-1,e)),e),k.FBX_Deformer,(e=new z(k,T)).normalizeSkinWeights(),e},_tS:function(e,J){},_uS:function(e,J){var P={};"InheritType"in J&&(P.inheritType=parseInt(J.InheritType.value)),"RotationOrder"in J?P.eulerOrder=A(J.RotationOrder.value):P.eulerOrder="ZYX","Lcl_Translation"in J&&(P.translation=J.Lcl_Translation.value),"PreRotation"in J&&(P.preRotation=J.PreRotation.value),"Lcl_Rotation"in J&&(P.rotation=J.Lcl_Rotation.value),"PostRotation"in J&&(P.postRotation=J.PostRotation.value),"Lcl_Scaling"in J&&(P.scale=J.Lcl_Scaling.value),"ScalingOffset"in J&&(P.scalingOffset=J.ScalingOffset.value),"ScalingPivot"in J&&(P.scalingPivot=J.ScalingPivot.value),"RotationOffset"in J&&(P.rotationOffset=J.RotationOffset.value),"RotationPivot"in J&&(P.rotationPivot=J.RotationPivot.value),e.userData.transformData=P},_IS:function(P,e){"LookAtProperty"in e&&n.get(P.ID).children.forEach(function(e){var J;"LookAtProperty"===e.relationship&&"Lcl_Translation"in(e=K.Objects.Model[e.ID])&&(e=e.Lcl_Translation.value,P.target!==m?(P.target.matrix.setPosition({x:e[0],y:e[1],z:e[2]}),S.add(P.target)):(J=P.matrix.getPosition(),P.matrix.lookAt({x:e[0],y:e[1],z:e[2]},J)))})},_JS:function(e,J,P){var k,T=this._KS();for(k in e){var F=e[k];n.get(parseInt(F.ID)).parents.forEach(function(e){J.has(e.ID)&&(e=e.ID,n.get(e).parents.forEach(function(e){P.has(e.ID)&&(P.get(e.ID).skeleton=[F.bones,function(e){for(var J=[],P=new h,k=0,T=e.length;k<T;k++)P.copy(e[k].matrixWorld).invert().toArray(J,16*k);return J}(F.bones),T[e.ID]||new h])}))})}},_KS:function(){var J={};if("Pose"in K.Objects){var e,P,k=K.Objects.Pose;for(e in k)"BindPose"===k[e].attrType&&(P=k[e].PoseNode,Array.isArray(P)?P.forEach(function(e){J[e.Node]=(new h).fromArray(e.Matrix.a)}):J[P.Node]=(new h).fromArray(P.Matrix.a))}return J},_LS:function(){var e,J,P;"GlobalSettings"in K&&"AmbientColor"in K.GlobalSettings&&(P=(J=K.GlobalSettings.AmbientColor.value)[0],e=J[1],J=J[2],0===P&&0===e&&0===J||(P=new Color(P,e,J),S.add(new AmbientLight(P,1))))}}),new c.Math.Matrix4);function q(e,J,P){this.name="",this.array=e,this.itemSize=J,this.count=e!==m?e.length/J:0,this.normalized=!0===P,this.usage=c.WebGLConstants.STATIC_DRAW,this.updateRange={offset:0,count:-1},this.version=0}e(u,v_,{_3s:function(e){return Array.isArray(e)?this.index=new(65535<arrayMax(e)?J:C)(e,1):this.index=e,this},_4s:function(e,J){return this.attributes[e]=J,this},_5s:function(e){return delete this.attributes[e],this},_6s:function(e){return this.attributes[e]!==m},applyMatrix4:function(e){var J=this.attributes.position,J=(J!==m&&(J.applyMatrix4(e),J.needsUpdate=!0),this.attributes.normal),P=(J!==m&&(P=(new c.Math.Matrix3).getNormalMatrix(e),J.applyNormalMatrix(P),J.needsUpdate=!0),this.attributes.tangent);return P!==m&&(P.transformDirection(e),P.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this},applyQuaternion:function(e){return aO.makeRotationFromQuaternion(e),this.applyMatrix4(aO),this},_MS:function(e,J,P){this.groups.push({start:e,count:J,materialIndex:P||0})},setFromPoints:function(e){for(var J=[],P=0,k=e.length;P<k;P++){var T=e[P];J.push(T.x,T.y,T.z||0)}return this._4s("position",new W(J,3)),this}}),v_.defineProperty(q.prototype,"needsUpdate",{set:function(e){!0===e&&this.version++}});var o=new N;function SO(e,J,P){q.call(this,new Int8Array(e),J,P)}function vO(e,J,P){q.call(this,new Uint8Array(e),J,P)}function KO(e,J,P){q.call(this,new Uint8ClampedArray(e),J,P)}function cO(e,J,P){q.call(this,new Int16Array(e),J,P)}function C(e,J,P){q.call(this,new Uint16Array(e),J,P)}function EO(e,J,P){q.call(this,new Int32Array(e),J,P)}function J(e,J,P){q.call(this,new Uint32Array(e),J,P)}function W(e,J,P){q.call(this,new Float32Array(e),J,P)}function yO(e,J,P){q.call(this,new Float64Array(e),J,P)}v_.assign(q.prototype,{isBufferAttribute:!0,onUploadCallback:function(){},setUsage:function(e){return this.usage=e,this},copy:function(e){return this.name=e.name,this.array=new e.array.constructor(e.array),this.itemSize=e.itemSize,this.count=e.count,this.normalized=e.normalized,this.usage=e.usage,this},_8s:function(e,J,P){e*=this.itemSize,P*=J.itemSize;for(var k=0,T=this.itemSize;k<T;k++)this.array[e+k]=J.array[P+k];return this},_9s:function(e){return this.array.set(e),this},copyColorsArray:function(e){for(var J=this.array,P=0,k=0,T=e.length;k<T;k++){var F=e[k];F===m?(J[P++]=1,J[P++]=1,J[P++]=1):(J[P++]=F.r,J[P++]=F.g,J[P++]=F.b)}return this},_7s:function(e){for(var J=this.array,P=0,k=0,T=e.length;k<T;k++){var F=e[k];F===m&&(F=new Vector2),J[P++]=F.x,J[P++]=F.y}return this},_as:function(e){for(var J=this.array,P=0,k=0,T=e.length;k<T;k++){var F=e[k];F===m&&(F=new N),J[P++]=F.x,J[P++]=F.y,J[P++]=F.z}return this},_bs:function(e){for(var J=this.array,P=0,k=0,T=e.length;k<T;k++){var F=e[k];F===m&&(F=new y),J[P++]=F.x,J[P++]=F.y,J[P++]=F.z,J[P++]=F.w}return this},applyMatrix3:function(e){for(var J=0,P=this.count;J<P;J++)o.x=this.getX(J),o.y=this.getY(J),o.z=this.getZ(J),o.applyMatrix3(e),this.setXYZ(J,o.x,o.y,o.z);return this},applyMatrix4:function(e){for(var J=0,P=this.count;J<P;J++)o.x=this.getX(J),o.y=this.getY(J),o.z=this.getZ(J),o.applyMatrix4(e),this.setXYZ(J,o.x,o.y,o.z);return this},applyNormalMatrix:function(e){for(var J=0,P=this.count;J<P;J++)o.x=this.getX(J),o.y=this.getY(J),o.z=this.getZ(J),o.applyNormalMatrix(e),this.setXYZ(J,o.x,o.y,o.z);return this},transformDirection:function(e){for(var J=0,P=this.count;J<P;J++)o.x=this.getX(J),o.y=this.getY(J),o.z=this.getZ(J),o.transformDirection(e),this.setXYZ(J,o.x,o.y,o.z);return this},set:function(e,J){return this.array.set(e,J=J===m?0:J),this},getX:function(e){return this.array[e*this.itemSize]},setX:function(e,J){return this.array[e*this.itemSize]=J,this},getY:function(e){return this.array[e*this.itemSize+1]},setY:function(e,J){return this.array[e*this.itemSize+1]=J,this},getZ:function(e){return this.array[e*this.itemSize+2]},setZ:function(e,J){return this.array[e*this.itemSize+2]=J,this},getW:function(e){return this.array[e*this.itemSize+3]},setW:function(e,J){return this.array[e*this.itemSize+3]=J,this},setXY:function(e,J,P){return e*=this.itemSize,this.array[e+0]=J,this.array[e+1]=P,this},setXYZ:function(e,J,P,k){return e*=this.itemSize,this.array[e+0]=J,this.array[e+1]=P,this.array[e+2]=k,this},setXYZW:function(e,J,P,k,T){return e*=this.itemSize,this.array[e+0]=J,this.array[e+1]=P,this.array[e+2]=k,this.array[e+3]=T,this},onUpload:function(e){return this.onUploadCallback=e,this},clone:function(){return new this.constructor(this.array,this.itemSize).copy(this)},toJSON:function(){return{itemSize:this.itemSize,type:this.array.constructor.name,array:Array.prototype.slice.call(this.array),normalized:this.normalized}}}),(SO.prototype=v_.create(q.prototype)).constructor=SO,(vO.prototype=v_.create(q.prototype)).constructor=vO,(KO.prototype=v_.create(q.prototype)).constructor=KO,(cO.prototype=v_.create(q.prototype)).constructor=cO,C.prototype=v_.create(q.prototype),C.prototype.constructor=C,(EO.prototype=v_.create(q.prototype)).constructor=EO,J.prototype=v_.create(q.prototype),J.prototype.constructor=J,(W.prototype=v_.create(q.prototype)).constructor=W,(yO.prototype=v_.create(q.prototype)).constructor=yO;var YO=function(){},iO=(e(YO,v_,{_zS:function(e){var J=new Map;if("Geometry"in K.Objects){var P,k=K.Objects.Geometry;for(P in k){var T=n.get(parseInt(P));T&&(T=this._2s(T,k[P],e),J.set(parseInt(P),T))}}return J},_2s:function(e,J,P){switch(J.attrType){case"Mesh":return this._1s(e,J,P);case"NurbsCurve":return this._PS(J)}},_1s:function(e,J,P){var k,T=P.skeletons,F=[],z=e.parents.map(function(e){return K.Objects.Model[e.ID]});if(0!==z.length)return k=e.children.reduce(function(e,J){return e=T[J.ID]!==m?T[J.ID]:e},null),e.children.forEach(function(e){P.morphTargets[e.ID]!==m&&F.push(P.morphTargets[e.ID])}),e=z[0],z={},"RotationOrder"in e&&(z.eulerOrder=A(e.RotationOrder.value)),"InheritType"in e&&(z.inheritType=parseInt(e.InheritType.value)),"GeometricTranslation"in e&&(z.translation=e.GeometricTranslation.value),"GeometricRotation"in e&&(z.rotation=e.GeometricRotation.value),"GeometricScaling"in e&&(z.scale=e.GeometricScaling.value),e=w(z),this._ZS(J,k,F,e)},_ZS:function(e,J,P,k){var T,F,z=new u,a=(e.attrName&&(z.name=e.attrName),this._YS(e,J)),S=this._XS(a),v=new W(S.vertex,3);return v.applyMatrix4(k),z._4s("position",v),0<S.colors.length&&z._4s("color",new W(S.colors,3)),J&&(z._4s("skinIndex",new C(S.weightsIndices,4)),z._4s("skinWeight",new W(S.vertexWeights,4)),z.FBX_Deformer=J),0<S.normal.length&&(v=(new c.Math.Matrix3).getNormalMatrix(k),(J=new W(S.normal,3)).applyNormalMatrix(v),z._4s("normal",J)),S.uvs.forEach(function(e,J){var P="uv"+(J+1).toString();z._4s(P=0===J?"uv":P,new W(S.uvs[J],2))}),a.material&&"AllSame"!==a.material.mappingType&&(T=S.materialIndex[0],F=0,S.materialIndex.forEach(function(e,J){e!==T&&(z._MS(F,J-F,T),T=e,F=J)}),0<z.groups.length&&((J=(v=z.groups[z.groups.length-1]).start+v.count)!==S.materialIndex.length&&z._MS(J,S.materialIndex.length-J,T)),0===z.groups.length&&z._MS(0,S.materialIndex.length,S.materialIndex[0])),this._VS(z,e,P,k),z},_YS:function(e,J){var T={};if(T.vertexPositions=e.Vertices!==m?e.Vertices.a:[],T.vertexIndices=e.PolygonVertexIndex!==m?e.PolygonVertexIndex.a:[],e.LayerElementColor&&(T.color=this._RS(e.LayerElementColor[0])),e.LayerElementMaterial&&(T.material=this._QS(e.LayerElementMaterial[0])),e.LayerElementNormal&&(T.normal=this._TS(e.LayerElementNormal[0])),e.LayerElementUV){T.uv=[];for(var P=0;e.LayerElementUV[P];)e.LayerElementUV[P].UV&&T.uv.push(this._SS(e.LayerElementUV[P])),P++}return T.weightTable={},null!==J&&(T.skeleton=J).rawBones.forEach(function(P,k){P.indices.forEach(function(e,J){T.weightTable[e]===m&&(T.weightTable[e]=[]),T.weightTable[e].push({id:k,weight:P.weights[J]})})}),T},_XS:function(K){var c={vertex:[],normal:[],colors:[],uvs:[],materialIndex:[],vertexWeights:[],weightsIndices:[]},E=0,y=0,Y=[],i=[],g=[],p=[],h=[],N=[],U=this;return K.vertexIndices.forEach(function(P,k){var e,F,z,J,T=!1,a=(P<0&&(P^=-1,T=!0),[]),S=[];if(Y.push(3*P,3*P+1,3*P+2),K.color&&(J=x(k,E,P,K.color),g.push(J[0],J[1],J[2])),K.skeleton){for(K.weightTable[P]!==m&&K.weightTable[P].forEach(function(e){S.push(e.weight),a.push(e.id)}),4<S.length&&(F=[0,0,0,0],z=[0,0,0,0],S.forEach(function(e,J){var k=e,T=a[J];z.forEach(function(e,J,P){e<k&&(P[J]=k,k=e,P=F[J],F[J]=T,T=P)})}),a=F,S=z);S.length<4;)S.push(0),a.push(0);for(var v=0;v<4;++v)h.push(S[v]),N.push(a[v])}K.normal&&(J=x(k,E,P,K.normal),i.push(J[0],J[1],J[2])),K.material&&"AllSame"!==K.material.mappingType&&(e=x(k,E,P,K.material)[0]),K.uv&&K.uv.forEach(function(e,J){e=x(k,E,P,e);p[J]===m&&(p[J]=[]),p[J].push(e[0]),p[J].push(e[1])}),y++,T&&(U._WS(c,K,Y,e,i,g,p,h,N,y),E++,y=0,Y=[],i=[],g=[],p=[],h=[],N=[])}),c},_WS:function(P,e,J,k,T,F,z,a,S,v){for(var K=2;K<v;K++)P.vertex.push(e.vertexPositions[J[0]]),P.vertex.push(e.vertexPositions[J[1]]),P.vertex.push(e.vertexPositions[J[2]]),P.vertex.push(e.vertexPositions[J[3*(K-1)]]),P.vertex.push(e.vertexPositions[J[3*(K-1)+1]]),P.vertex.push(e.vertexPositions[J[3*(K-1)+2]]),P.vertex.push(e.vertexPositions[J[3*K]]),P.vertex.push(e.vertexPositions[J[3*K+1]]),P.vertex.push(e.vertexPositions[J[3*K+2]]),e.skeleton&&(P.vertexWeights.push(a[0]),P.vertexWeights.push(a[1]),P.vertexWeights.push(a[2]),P.vertexWeights.push(a[3]),P.vertexWeights.push(a[4*(K-1)]),P.vertexWeights.push(a[4*(K-1)+1]),P.vertexWeights.push(a[4*(K-1)+2]),P.vertexWeights.push(a[4*(K-1)+3]),P.vertexWeights.push(a[4*K]),P.vertexWeights.push(a[4*K+1]),P.vertexWeights.push(a[4*K+2]),P.vertexWeights.push(a[4*K+3]),P.weightsIndices.push(S[0]),P.weightsIndices.push(S[1]),P.weightsIndices.push(S[2]),P.weightsIndices.push(S[3]),P.weightsIndices.push(S[4*(K-1)]),P.weightsIndices.push(S[4*(K-1)+1]),P.weightsIndices.push(S[4*(K-1)+2]),P.weightsIndices.push(S[4*(K-1)+3]),P.weightsIndices.push(S[4*K]),P.weightsIndices.push(S[4*K+1]),P.weightsIndices.push(S[4*K+2]),P.weightsIndices.push(S[4*K+3])),e.color&&(P.colors.push(F[0]),P.colors.push(F[1]),P.colors.push(F[2]),P.colors.push(F[3*(K-1)]),P.colors.push(F[3*(K-1)+1]),P.colors.push(F[3*(K-1)+2]),P.colors.push(F[3*K]),P.colors.push(F[3*K+1]),P.colors.push(F[3*K+2])),e.material&&"AllSame"!==e.material.mappingType&&(P.materialIndex.push(k),P.materialIndex.push(k),P.materialIndex.push(k)),e.normal&&(P.normal.push(T[0]),P.normal.push(T[1]),P.normal.push(T[2]),P.normal.push(T[3*(K-1)]),P.normal.push(T[3*(K-1)+1]),P.normal.push(T[3*(K-1)+2]),P.normal.push(T[3*K]),P.normal.push(T[3*K+1]),P.normal.push(T[3*K+2])),e.uv&&e.uv.forEach(function(e,J){P.uvs[J]===m&&(P.uvs[J]=[]),P.uvs[J].push(z[J][0]),P.uvs[J].push(z[J][1]),P.uvs[J].push(z[J][2*(K-1)]),P.uvs[J].push(z[J][2*(K-1)+1]),P.uvs[J].push(z[J][2*K]),P.uvs[J].push(z[J][2*K+1])})},_VS:function(P,k,e,T){var F;0!==e.length&&(P.morphTargetsRelative=!0,P.morphAttributes.position=[],F=this,e.forEach(function(e){e.rawTargets.forEach(function(e){var J=K.Objects.Geometry[e.geoID];J!==m&&F._US(P,k,J,T,e.name)})}))},_US:function(e,J,P,k,T){for(var J=J.PolygonVertexIndex!==m?J.PolygonVertexIndex.a:[],F=P.Vertices!==m?P.Vertices.a:[],z=P.Indexes!==m?P.Indexes.a:[],a=3*e.attributes.position.count,S=new Float32Array(a),v=0;v<z.length;v++){var K=3*z[v];S[K]=F[3*v],S[1+K]=F[3*v+1],S[2+K]=F[3*v+2]}a=new W(this._XS({vertexIndices:J,vertexPositions:S}).vertex,3);a.name=T||P.attrName,a.applyMatrix4(k),e.morphAttributes.position.push(a)},_TS:function(e){var J=e.MappingInformationType,P=e.ReferenceInformationType,k=e.Normals.a,T=[];return"IndexToDirect"===P&&("NormalIndex"in e?T=e.NormalIndex.a:"NormalsIndex"in e&&(T=e.NormalsIndex.a)),{dataSize:3,buffer:k,indices:T,mappingType:J,referenceType:P}},_SS:function(e){var J=e.MappingInformationType,P=e.ReferenceInformationType,k=[];return{dataSize:2,buffer:e.UV.a,indices:k="IndexToDirect"===P?e.UVIndex.a:k,mappingType:J,referenceType:P}},_RS:function(e){var J=e.MappingInformationType,P=e.ReferenceInformationType,k=[];return{dataSize:4,buffer:e.Colors.a,indices:k="IndexToDirect"===P?e.ColorIndex.a:k,mappingType:J,referenceType:P}},_QS:function(e){var J=e.MappingInformationType,P=e.ReferenceInformationType;if("NoMappingInformation"===J||!e.Materials)return{dataSize:1,buffer:[0],indices:[0],mappingType:"AllSame",referenceType:P};for(var k=e.Materials.a,T=[],F=0;F<k.length;++F)T.push(F);return{dataSize:1,buffer:k,indices:T,mappingType:J,referenceType:P}},_PS:function(e){if(pO===m)return console.error("The loader relies on NURBSCurve for any nurbs present in the model. Nurbs will show up as empty geometry."),new u;var J=parseInt(e.Order);if(isNaN(J))return console.error("Invalid Order %s given for geometry ID: %s",e.Order,e.id),new u;for(var P=J-1,J=e.KnotVector.a,k=[],T=e.Points.a,F=0,z=T.length;F<z;F+=4)k.push((new y).fromArray(T,F));if("Closed"===e.Form)k.push(k[0]);else if("Periodic"===e.Form)for(var a=P,S=J.length-1-a,F=0;F<P;++F)k.push(k[F]);e=new pO(P,J,k,a,S).getPoints(12*k.length);return(new u).setFromPoints(e)}}),function(){});function gO(e,J,P,k){for(var T=function(e,J,P){var k=P.length-e-1;if(J>=P[k])return k-1;if(J<=P[e])return e;for(var T=e,F=k,z=Math.floor((T+F)/2);J<P[z]||J>=P[z+1];)J<P[z]?F=z:T=z,z=Math.floor((T+F)/2);return z}(e,k,J),F=function(e,J,P,k){for(var T=[],F=[],z=[],a=T[0]=1;a<=P;++a){F[a]=J-k[e+1-a],z[a]=k[e+a]-J;for(var S=0,v=0;v<a;++v){var K=z[v+1],c=F[a-v],E=T[v]/(K+c);T[v]=S+K*E,S=c*E}T[a]=S}return T}(T,k,e,J),z=new y(0,0,0,0),a=0;a<=e;++a){var S=P[T-e+a],v=F[a],K=S.w*v;z.x+=S.x*K,z.y+=S.y*K,z.z+=S.z*K,z.w+=S.w*v}return z}e(iO,v_,{_zS:function(e){this.params=e;var J=[],P=this._qs();if(P!==m)for(var k in P){k=P[k],k=this._js(k);J.push(k)}return this.params=null,J},_qs:function(){if(K.Objects.AnimationCurve===m)return m;var e=this._ns(),e=(this._ms(e),this._ls(e));return this._ks(e)},_ns:function(){var e,J=K.Objects.AnimationCurveNode,P=new Map;for(e in J){var k=J[e];null!==k.attrName.match(/S|R|T|DeformPercent/)&&(k={id:k.id,attr:k.attrName,curves:{}},P.set(k.id,k))}return P},_ms:function(e){var J,P=K.Objects.AnimationCurve;for(J in P){var k,T,F={id:P[J].id,times:P[J].KeyTime.a.map(D),values:P[J].KeyValueFloat.a},z=n.get(F.id);z!==m&&(k=z.parents[0].ID,e.has(k)&&(T=e.get(k),(z=z.parents[0].relationship).match(/X/)?T.curves.x=F:z.match(/Y/)?T.curves.y=F:z.match(/Z/)?T.curves.z=F:z.match(/d|DeformPercent/)&&e.has(k)&&(T.curves.morph=F)))}},_ls:function(z){var e,J=K.Objects.AnimationLayer,P=new Map;for(e in J){var a=[],k=n.get(parseInt(e));k!==m&&(k.children.forEach(function(e,J){if(z.has(e.ID)){var P=z.get(e.ID);if(P.curves.x!==m||P.curves.y!==m||P.curves.z!==m){if(a[J]===m){var k=n.get(e.ID).parents.filter(function(e){return e.relationship!==m})[0].ID;if(k!==m){if((F=K.Objects.Model[k.toString()])===m)return;var T={modelName:F.attrName?g(F.attrName):"",ID:F.id,initialPosition:[0,0,0],initialRotation:[0,0,0],initialScale:[1,1,1]};S.traverse(function(e){e.ID===F.id&&(T.transform=e.matrix,e.userData.transformData&&(T.eulerOrder=e.userData.transformData.eulerOrder))}),T.transform||(T.transform=new h),"PreRotation"in F&&(T.preRotation=F.PreRotation.value),"PostRotation"in F&&(T.postRotation=F.PostRotation.value),a[J]=T}}a[J]&&(a[J][P.attr]=P)}else if(P.curves.morph!==m){if(a[J]===m){k=n.get(e.ID).parents.filter(function(e){return e.relationship!==m})[0].ID,e=n.get(k).parents[0].ID,e=n.get(e).parents[0].ID,e=n.get(e).parents[0];if(!e)return;var F,T={modelName:(F=K.Objects.Model[e.ID]).attrName?g(F.attrName):"",ID:F.id,morphName:K.Objects.Deformer[k].attrName};a[J]=T}a[J][P.attr]=P}}}),P.set(parseInt(e),a))}return P},_ks:function(e){var J,P=K.Objects.AnimationStack,k={};for(J in P){var T=n.get(parseInt(J)).children,T=e.get(T[0].ID);k[J]={name:P[J].attrName,layer:T}}return k},_js:function(e){var J=[],P=this,k=(e.layer.forEach(function(e){J=J.concat(P._ps(e))}),0);return J.forEach(function(e){k=Math.max(k,e.times[e.times.length-1])}),{name:e.name,duration:k,tracks:J}},_ps:function(e){var J=[],P=new N,k=new i,T=new N;return e.transform&&e.transform.decompose(P,k,T),P=P.toArray(),k=(new Y).setFromQuaternion(k,e.eulerOrder).toArray(),T=T.toArray(),e.T!==m&&0<v_.keys(e.T.curves).length&&((P=this._hs(e.ID,e.T.curves,P,"position"))!==m&&J.push(P)),e.R!==m&&0<v_.keys(e.R.curves).length&&((P=this._gs(e.ID,e.R.curves,k,e.preRotation,e.postRotation,e.eulerOrder))!==m&&J.push(P)),e.S!==m&&0<v_.keys(e.S.curves).length&&((k=this._hs(e.ID,e.S.curves,T,"scale"))!==m&&J.push(k)),J},_hs:function(e,J,P,k){var T=this._es(J);return{type:"linear",name:e+"."+k,times:T,values:this._ds(T,J,P)}},_gs:function(e,J,P,k,T,F){J.x!==m&&(this._cs(J.x),J.x.values=J.x.values.map(U)),J.y!==m&&(this._cs(J.y),J.y.values=J.y.values.map(U)),J.z!==m&&(this._cs(J.z),J.z.values=J.z.values.map(U));for(var z=this._es(J),a=this._ds(z,J,P,!0),S=(k!==m&&((k=k.map(U)).push(F),k=(new Y).fromArray(k),k=(new i).setFromEuler(k)),T!==m&&((T=T.map(U)).push(F),T=(new Y).fromArray(T),T=(new i).setFromEuler(T).inverse()),new i),v=new Y,K=[],c=0;c<a.length;c+=3)v.set(a[c],a[c+1],a[c+2],F),S.setFromEuler(v),k!==m&&S.premultiply(k),T!==m&&S.multiply(T),S.toArray(K,c/3*4);return{type:"quaternion",name:e+".quaternion",times:z,values:K}},_fs:function(e){var J=e.DeformPercent.curves.morph,P=J.values.map(function(e){return e/100}),k=S.getObjectByName(e.modelName).morphTargetDictionary[e.morphName];return{type:"number",name:e.ID+".morphTargetInfluences["+k+"]",times:J.times,values:P}},_es:function(e){var J=[];if(e.x!==m&&(J=J.concat(e.x.times)),e.y!==m&&(J=J.concat(e.y.times)),1<(J=(J=e.z!==m?J.concat(e.z.times):J).sort(function(e,J){return e-J})).length){for(var P=1,k=J[0],T=1;T<J.length;T++){var F=J[T];F!==k&&(k=J[P]=F,P++)}J=J.slice(0,P)}return J},getInterpolatedKeyframeValues:function(e,J,P,k){var T=[],F=["x","y","z"],z=e.length;if(k&&(J.x&&0!==J.x.times[0]||J.y&&0!==J.y.times[0]||J.z&&0!==J.z.times[0])){for(var k=P[3],a=[J.x?J.x.values[0]:m,J.y?J.y.values[0]:m,J.z?J.z.values[0]:m],S=P,v=Math.PI,K="X"===k[1]?[-S[0]+v,S[1]+v,S[2]+v]:"Y"===k[1]?[S[0]+v,-S[1]+v,S[2]+v]:[S[0]+v,S[1]+v,-S[2]+v],c=0,E=0,y=0;y<3;y++)a[y]!==m&&(c+=Math.abs(_(a[y],S[y])),E+=Math.abs(_(a[y],K[y])));1e-4<Math.abs(E-c)&&E<c&&(S=K);for(y=0;y<3;y++)a[y]!==m&&(S[y]=a[y]+_(S[y],a[y]));P=S}for(var Y=0;Y<3;Y++){var i=J[F[Y]],g=P[Y];if(i){var p,h=i.times,a=i.values,N=0;0!==h[0]&&(h=[0].concat(h),a=[g].concat(a));for(y=0;y<z;y++){for(var U=e[y];N<h.length-1&&h[N+1]<U;)N++;p=h[N+1]===h[N]||h[N+1]===m?a[N]:(p=(U-h[N])/(h[N+1]-h[N]),a[N]+(a[N+1]-a[N])*p),T[3*y+Y]=p}}else for(var y=0;y<z;y++)T[3*y+Y]=g}return T},_ds:function(e,J,P,k){if(this.params&&!1!==this.params.keyframeValuesInterpolation)return this.getInterpolatedKeyframeValues(e,J,P,k);var T=P,F=[],z=-1,a=-1,S=-1;return e.forEach(function(e){J.x&&(z=J.x.times.indexOf(e)),J.y&&(a=J.y.times.indexOf(e)),J.z&&(S=J.z.times.indexOf(e)),-1!==z?(e=J.x.values[z],F.push(e),T[0]=e):F.push(T[0]),-1!==a?(e=J.y.values[a],F.push(e),T[1]=e):F.push(T[1]),-1!==S?(e=J.z.values[S],F.push(e),T[2]=e):F.push(T[2])}),F},checkNeedInterpolate:function(P,e){function J(e){return Math.abs(P.values[e]-P.values[e-1])/(P.times[e]-P.times[e-1])}function k(e){var J=Math.abs(P.values[e]-P.values[e-1])%360;return(J=180<J?360-J:J)/(P.times[e]-P.times[e-1])}function T(e,J){return Math.max(e,J)/Math.min(e,J)}var F=1===e?2:e-1;return!(8<T(J(e),J(F))&&T(k(e),k(F))<1.2)},_cs:function(e){for(var J=1;J<e.values.length;J++){var P=e.values[J-1],k=e.values[J]-P,T=Math.abs(k);if(!(T<180)){var F=this.params?this.params.rotationInterpolation:m;if(!1!==F&&(!0===F||this.checkNeedInterpolate(e,J))){for(var F=T/179,z=k/F,a=P+z,T=e.times[J-1],S=(e.times[J]-T)/F,v=T+S,K=[],c=[];v<e.times[J];)K.push(v),v+=S,c.push(a),a+=z;e.times=O(e.times,J,K),e.values=O(e.values,J,c)}}}}});var pO=function(e,J,P,k,T){this.degree=e,this.knots=J,this._vs=[],this.startKnot=k||0,this.endKnot=T||this.knots.length-1;for(var F=0;F<P.length;++F){var z=P[F];this._vs[F]=new y(z.x,z.y,z.z,z.w)}this.getPoint=function(e,J){J=J||new N,e=this.knots[this.startKnot]+e*(this.knots[this.endKnot]-this.knots[this.startKnot]),e=gO(this.degree,this.knots,this._vs,e);return 1!==e.w&&e.divideScalar(e.w),J.set(e.x,e.y,e.z)},this.getPoints=function(e){e=e||5;for(var J=[],P=0;P<=e;P++)J.push(this.getPoint(P/e));return J}}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);