<!DOCTYPE html>
<html>
    <head>
        <title>Edge Arrow</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>    
        
        <script>
            htconfig = {                
                Style: {
                    'edge.width': 1,
                    'edge.color': 'black'
                }                       
            };
        </script>                                
        <script src="../../../../lib/core/ht.js"></script> 
        <script src="../../../../lib/plugin/ht-edgetype.js"></script>         
        <script src="edgeModel.js"></script> 
        <script>
            
            
            function init(){                                                                                 
                ht.Default.setImage('toArrow', {
                    width: 100,
                    height: 50,
                    comps: [
                        /*
                        {
                            type: 'rect',
                            rect: [0, 0, 100, 50],
                            borderWidth: 1,
                            borderColor: 'green'
                        },
                        */
                        {
                            type: 'shape',
                            points: [2, 25, 30, 25],
                            borderWidth: 4,
                            borderColor: 'rgba(255, 0, 0, 0.9)'
                        },
                        {
                            type: 'shape',
                            points: [30, 10, 30, 40, 50, 25, 30, 10],
                            background: 'rgba(255, 0, 0, 0.9)',
                            borderWidth: 1,
                            borderColor: 'red',
                            gradient: 'spread.vertical',
                            gradientColor: 'rgba(255, 255, 255, 0.9)'
                        }
                    ]
                }); 
                ht.Default.setImage('fromArrow', {
                    width: 100,
                    height: 50,
                    comps: [
                        {
                            type: 'image',
                            name: 'toArrow',
                            rect: [0, 0, 100, 50],
                            rotation: Math.PI
                        }
                    ]
                });                                 
                
                dataModel = new ht.DataModel();
                graphView = new ht.graph.GraphView(dataModel);    
                graphView.setEditable(true);
                graphView.setZoom(0.5);
                graphView.enableToolTip();
                graphView.getToolTip = function(e){
                    var data = this.getDataAt(e);
                    if(data){
                        return '<pre>' + JSON.stringify(data.getStyleMap(), null, 4) + '</pre>';
                    }
                    return null;
                };                
                        
                view = graphView.getView();
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    graphView.invalidate();
                }, false);   
                            
                createEdgeModel();
            }
            
            function createNode(x, y, style){
                var node = new ht.Node();
                node.s({
                    'shape': 'rect',
                    'opacity': 0.5
                });
                node.setPosition(x, y);
                node.setSize(20, 20);
                if(style){
                    node.s(style);
                }
                dataModel.add(node);
                return node;
            }

            function createEdge(sourceNode, targetNode, count, typeOrStyle){
                var edge;
                for(var i=0; i<count; i++){                    
                    edge = new ht.Edge(sourceNode, targetNode);                                               
                    if(typeof typeOrStyle === 'object'){
                        edge.s(typeOrStyle);
                    }else{
                        edge.s('edge.type', typeOrStyle);                        
                    }
                    edge.addStyleIcon("fromArrow", {
                        position: 15,
                        keepOrien: true,
                        width: 40,
                        height: 20,
                        names: ['fromArrow']
                    }); 
                    edge.addStyleIcon("toArrow", {
                        position: 19,
                        keepOrien: true,
                        width: 40,
                        height: 20,                        
                        names: ['toArrow']
                    }); 
                    dataModel.add(edge);
                }
                return edge;
            }            
            
        </script>
    </head>
    <body onload="init();">
    </body>
</html>
