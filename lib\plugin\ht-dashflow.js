!function(Z){"use strict";function q(Z){var D,R,j=Z.data,q=this.dm();j&&"add"===Z.kind&&(D=q.$1c,R=j instanceof h?"shape.":"edge.",D&&j.s(R+"dash.flow")&&D.indexOf(j)<0&&D.push(j)),"clear"===Z.kind&&(q.$1c=[])}function d(Z){var D=Z.property,R=Z.data,Z=Z.newValue,j=this.dm().$1c,q=R instanceof h?"s:shape.dash.flow":"s:edge.dash.flow";if(j&&D===q)if(Z)j.indexOf(R)<0&&j.push(R);else for(var d=j.length,B=0;B<d;B++)if(j[B]===R){j.splice(B,1);break}}function D(){var Z,D,R,j,q,d,B,J;this.$7c&&(delete this.$7c,D=(Z=this._data)instanceof h?"shape.":"edge.",R=Z.s(D+"dash.pattern"),j=Z.s(D+"dash.flow.reverse"),R&&Z.s(D+"dash")&&Z.s(D+"dash.flow")&&(q=this.s(D+"dash.offset")||0,d=Z.s(D+"dash.flow.step"),B=Z.getStyleMap(),J=0,R.forEach(function(Z){J+=Z}),q=(q-(d=j?-d:d))%J,B||(Z._styleMap=B={}),B[D+"dash.offset"]=q))}var R=Z.ht,j=R.Default,B=Math,h=(B.PI,B.sin,B.cos,B.atan2,B.sqrt,B.max,B.floor,B.round,B.ceil,R.Shape),B=(R.Edge,R.List,R.Style),J=R.graph,j=j.getInternal().ui(),X=null,H="prototype",_=Z.clearInterval,O=Z.setInterval,Z=J.GraphView[H],J=j.EdgeUI[H],j=j.ShapeUI[H],z=j.drawBody,I=J.drawBody,R=R.DataModel[H],n=R.prepareRemove,p=Z.setDataModel;B["edge.dash.flow.step"]==X&&(B["edge.dash.flow.step"]=3),B["shape.dash.flow.step"]==X&&(B["shape.dash.flow.step"]=3),R.prepareRemove=function(Z){n.call(this,Z);var D=Z._dataModel.$1c;if(D)for(var R=D.length,j=0;j<R;j++)if(D[j]===Z){D.splice(j,1);break}},Z.setDataModel=function(Z){var R,D=this,j=D._dataModel;j!==Z&&(j&&(j.umm(q,D),j.umd(d,D),j.$1c=[]),Z.mm(q,D),Z.md(d,D),R=Z.$1c=[],Z.each(function(Z){var D=Z instanceof h?"shape.":"edge.";Z.s(D+"dash.flow")&&R.indexOf(Z)<0&&R.push(Z)}),p.call(D,Z))},Z.setDashFlowInterval=function(Z){var D=this,R=D.$2c;D.$2c=Z,D.fp("dashFlowInterval",R,Z),D.$3c!=X&&(_(D.$3c),delete D.$3c,D.enableDashFlow(Z))},Z.getDashFlowInterval=function(){return this.$2c},Z.$4c=function(){var R=this,j=R._56I;R.dm().$1c.forEach(function(Z){var D;!j[Z.getId()]||(D=R.getDataUI(Z))&&(D.$7c=!0,Z.iv())})},Z.enableDashFlow=function(Z){var D=this;D.$3c==X&&(D.$3c=O(function(){D.$4c()},Z||D.$2c||50))},Z.disableDashFlow=function(){_(this.$3c),delete this.$3c};J.drawBody=function(Z){I.call(this,Z),D.call(this)},j.drawBody=function(Z){z.call(this,Z),D.call(this)}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);