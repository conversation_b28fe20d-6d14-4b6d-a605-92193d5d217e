var commentMap = {
    '2d.move.mode': '控制图元在2d的移动范围，可取值xy|x|y，其他非空值代表不可移动',
    '3d.move.mode': '控制图元在3d的移动范围，可取值xyz|xy|xz|yz|x|y|z，其他非空值代表不可移动',
    'batch': '3d模型所在的批量组，用于优化大数据量图元绘制性能，对应ht.Default.setBatchInfo(name, batchInfo)的name',
    'transparent.mask': '3d图元在该值为true时，界面看不到，但可响应点击等交互操作，参见GraphView#isTransparentMask(data)',
    'wf.visible': '默认为false代表不显示，可设置为selected值代表选中时才显示，或true值代表一直显示线框',
    'wf.color': '线框颜色',
    'wf.short': '默认值为`false`代表显示封闭的立体线框，设置为`true`则显示不封闭的短线框',
    'wf.width': '线框宽度，有些系统下只能显示`1`的效果，不同系统能显示的最大值也都有限制',
    'wf.mat': '线框变换矩阵',
    '2d.visible': '控制图元在GraphView上是否可见',
    '2d.selectable': '控制图元在GraphView上是否可选中',
    '2d.movable': '控制图元在GraphView上是否可移动',
    '2d.editable': '控制图元在GraphView上是否可编辑',
    '2d.attachable': '设置节点是否可吸附，该属性为编辑属性，只在编辑操作中有效，代码层级设置吸附关系不做处理',
    '2d.hostable': '设置节点是否可被吸附，该属性为编辑属性，只在编辑操作中有效，代码层级设置吸附关系不做处理',
    '3d.visible': '控制图元在Graph3dView上是否可见',
    '3d.selectable': '控制图元在Graph3dView上是否可选中',
    '3d.movable': '控制图元在Graph3dView上是否可移动',
    '3d.editable': '控制图元在Graph3dView上是否可编辑',
    'select.color': '选中颜色，一般会在选中图元呈现选中边框的效果',
    'select.width': '选中边框宽度，一般会在选中图元呈现选中边框的效果',
    'select.padding': '选中边框内边距，一般会在选中图元呈现选中边框的效果',
    'select.type': '选中图元的样式，shadow代表现实阴影,其他可设置样式参见入门手册的多边形类型章节',
    'select.brightness': '图元在3d下选中时的亮度，大于1代表更亮，小于1代表更暗，1则代表不变化',
    'shadow': '是否开启选中图元的阴影效果',
    'shadow.blur': '选中图元的阴影模糊级别',
    'shadow.offset.x': '选中图元的阴影水平偏移',
    'shadow.offset.y': '选中图元的阴影垂直偏移',
    'shadow2': '是否开启图元的阴影效果，该系列属性和shadow是互斥，优先级低于shadow',
    'shadow2.color': '图元的阴影颜色',
    'shadow2.blur': '图元的阴影模糊级别',
    'shadow2.offset.x': '图元的阴影水平偏移',
    'shadow2.offset.y': '图元的阴影垂直偏移',
    'icons': '设置多组icons图标，设置单组图标可通过Data的addStyleIcon和removeStyleIcon进行',
    'ingroup': '该属性决定图元是否被Group的父亲图元所包含',
    'image.stretch': '该属性决定图片的绘制方式，默认为填充满矩形区域，可设置为uniform和centerUniform的非失真方式',
    'bloom': 'fasle，是否开启节点辉光效果，前提是场景需开启辉光 g3d.enablePostProcessing("Bloom", true)，且开启辉光筛选 g3d.setPostProcessingValue("Bloom", "selective", true)',
    'body.color': '图元染色颜色，HT会自动在原有图元图片颜色上叠加染色效果，参见GraphView#getBodyColor(data)',
    'border.color': '图元边框颜色，HT会自动在原有图元上绘制一个边框，参见GraphView#getBorderColor(data)',
    'border.width': '图元边框宽度',
    'border.padding': '图元边框内边距',
    'border.type': '图元边框样式，可设置样式参见入门手册的多边形类型章节',
    'label': '图元文字内容，参见GraphView#getLabel(data)',
    'label.font': '图元文字字体',
    'label.color': '图元文字颜色，参见GraphView#getLabelColor(data)',
    'label.background': '图元文字背景，参见GraphView#getLabelBackground(data)',
    'label.position': '图元文字位置',
    'label.position.fixed': '为`true`时对`Edge`类型的文字起作用，保持文字始终在连线之上或之下的位置',
    'label.align': '图元文字多行时的水平对齐方式',
    'label.offset.x': '图元文字位置x轴偏移',
    'label.offset.y': '图元文字位置y轴偏移',
    'label.rotation': '图元文字旋转弧度',
    'label.max': '图元文字最大长度',
    'label.scale': '文字缩放',
    'label.selectable': '点击图元文字是否选中图元',
    'label2': '图元文字内容，参见GraphView#getLabel2(data)',
    'label2.font': '图元文字字体',
    'label2.color': '图元文字颜色，参见GraphView#getLabel2Color(data)',
    'label2.background': '图元文字背景，参见GraphView#getLabel2Background(data)',
    'label2.position': '图元文字位置',
    'label2.position.fixed': '为`true`时对`Edge`类型的文字起作用，保持文字始终在连线之上或之下的位置',
    'label2.align': '图元文字多行时的水平对齐方式',
    'label2.offset.x': '图元文字位置x轴偏移',
    'label2.offset.y': '图元文字位置y轴偏移',
    'label2.rotation': '图元文字旋转弧度',
    'label2.max': '图元文字最大长度',
    'label2.scale': '文字缩放',
    'label2.selectable': '点击图元文字是否选中图元',
    'note': '标注文字内容，参见GraphView#getNote(data)',
    'note.expanded': '标注是否展开状态',
    'note.font': '标注文字字体',
    'note.color': '标注文字颜色',
    'note.background': '标注背景，参见GraphView#getNoteBackground(data)',
    'note.position': '标注位置',
    'note.offset.x': '标注位置x轴偏移',
    'note.offset.y': '标注位置y轴偏移',
    'note.max': '标注文字最大长度',
    'note.align': '标注文字多行时的水平对齐方式',
    'note.toggleable': '标注是否允许双击进行展开合并交互方式',
    'note.border.width': '标注边框宽度',
    'note.border.color': '标注边框颜色，为空时HT会自动计算合适的边框颜色',
    'note.scale': '标注缩放',
    'note2': '标注文字内容，参见GraphView#getNote2(data)',
    'note2.expanded': '标注是否展开状态',
    'note2.font': '标注文字字体',
    'note2.color': '标注文字颜色',
    'note2.background': '标注背景，参见GraphView#getNote2Background(data)',
    'note2.position': '标注位置',
    'note2.offset.x': '标注位置x轴偏移',
    'note2.offset.y': '标注位置y轴偏移',
    'note2.max': '标注文字最大长度',
    'note2.align': '标注文字多行时的水平对齐方式',
    'note2.toggleable': '标注是否允许双击进行展开合并交互方式',
    'note2.border.width': '标注边框宽度',
    'note2.border.color': '标注边框颜色，为空时HT会自动计算合适的边框颜色',
    'note2.scale': '标注缩放',
    'group.title.font': 'Group类型图元标题字体',
    'group.title.color': 'Group类型图元标题文字颜色',
    'group.title.background': 'Group类型图元标题背景',
    'group.title.align': 'Group类型图元标题文字对齐，可取值left|center|right',
    'group.title.orientation': 'Group类型图元标题朝向，可取值left|top|right|bottom，仅在 group.type为空时有效',
    'group.border.radius': 'Group类型圆形圆角半径，仅在 group.type为空时有效',
    'group.splitLine': 'Group类型图元是否展示标题与内容的分割线，仅在 group.type为空且有标题时有效',
    'group.background': 'Group类型图元背景',
    'group.depth': 'Group类型图元边缘深度，正数为凸起效果，负数为凹陷效果，0为平的效果',
    'group.position': 'Group类型图元位置，该位置决定Group合并状态所处所有包含的children的unionRect区域位置',
    'group.toggleable': 'Group类型图元是否允许双击展开合并',
    'group.type': '组类型，默认为空，组张开时显示为上部分title文字标题，下部分为矩形填充，可设置为oval和rect等形状',
    'group.image': '用于显示组展开的呈现图片效果',
    'group.image.stretch': '组展开图片拉伸模式，默认值为fill，还可取值uniform和centerUniform',
    'group.repeat.image': '组展开时填充重复背景的图片，注意这里的图片不支持矢量',
    'group.padding': '组类型图元内容内边距',
    'group.padding.left': '组展开后左边缘与孩子图元的间距',
    'group.padding.right': '组展开后右边缘与孩子图元的间距',
    'group.padding.top': '组展开后上边缘与孩子图元的间距',
    'group.padding.bottom': '组展开后下边缘与孩子图元的间距',
    'group.border.width': '组展开后的边框宽度',
    'group.border.width.absolute': '组展开后的边框宽度，是否不跟随比例缩放，默认false',
    'group.border.color': '组展开后的边框颜色',
    'group.border.pattern': '组展开后的边框显示虚线样式，Array类型，例如[5, 5]',
    'group.border.cap': '组展开后的边框末端线帽的样式，可选参数为butt|round|square',
    'group.border.join': '组展开后的边框当两条线交汇时创建边角的类型，可选参数为bevel|round|miter',
    'group.border.repeat.image': '组展开时边框重复填充背景的图片，注意这里的图片不支持矢量',
    'group.gradient': '组展开后背景渐近类型',
    'group.gradient.color': '组展开后背景渐近颜色',
    'keepAspectRatio': '节点大小是否等比编辑，等同于按住 shift 编辑',
    'shape': '多边形类型图元，为空时显示为图片，可设置多边形类型参见入门手册',
    'shape.background': '多边形类型图元背景',
    'shape.repeat.image': '填充重复背景的图片，注意这里的图片不支持矢量',
    'shape.corner.radius': '该参数指定roundRect类型的圆角半径，默认为空系统自动调节，可设置正数值 ',
    'shape.border.width': '多边形类型图元边框宽度',
    'shape.border.color': '多边形类型图元边框颜色',
    'shape.border.3d': '多边形类型图元边框是否显示为3d效果',
    'shape.border.3d.color': '多边形类型图元边框3d颜色，为空采用默认白色，呈现3d效果时边缘的中间部分为该颜色',
    'shape.border.3d.accuracy': '多边形类型图元3d效果精确度，该值越小3d渐进效果越好但影响性能，一般情况无需修改',
    'shape.border.gradient.color': '连线渐进色，目前仅在`3D`上折线的非立体方式下支持，在折线末尾端呈现此渐进色',
    'shape.border.cap': '决定多边形类型图元线条末端线帽的样式，可选参数为：butt|round|square',
    'shape.border.join': '决定多边形类型图元当两条线交汇时创建边角的类型，可选参数为：bevel|round|miter',
    'shape.border.repeat.image': '边框重复填充背景的图片，注意这里的图片不支持矢量',
    'shape.gradient': '多边形类型图元渐进类似，可选参数参见入门手册',
    'shape.gradient.color': '多边形类型图元渐进颜色',
    'shape.polygon.side': '多边形类型的边数',
    'shape.arc.from': '圆弧类型起始弧度',
    'shape.arc.to': '圆弧类型结束弧度',
    'shape.arc.close': '圆弧类型是否闭合',
    'shape.arc.oval': '圆弧类型是否为椭圆',
    'shape.depth': '多边形类型图元深度，该属性目前只对`rect`类型起作用，正值代表凸起，负值代表凹陷，默认值为`0`',
    'shape.border.pattern': '多边形类型图元虚线样式参数，`Array`类型，例如`[5, 5]`',
    'shape.dash': '多边形类型图元是否显示虚线',
    'shape.dash.pattern': '多边形类型图元虚线样式',
    'shape.dash.offset': '多边形类型图元虚线偏移',
    'shape.dash.color': '多边形类型图元虚线颜色',
    'shape.dash.width': '多边形类型图元虚线宽度',
    'shape.dash.3d': '多边形类型图元虚线是否显示3d效果',
    'shape.dash.3d.color': '多边形类型图元虚线3d效果颜色，为空采用默认白色，呈现3d效果时连线的中间部分为该颜色',
    'shape.foreground': '多边形类型图元前景色',
    'shape.foreground.gradient': '多边形类型图元前景渐进类型',
    'shape.foreground.gradient.color': '多边形类型图元前景渐进颜色',
    'shape.foreground.clip.direction': '多边形类型图元前景裁切方向',
    'shape.foreground.clip.percentage': '多边形类型图元前景裁切比例',
    'shape.dash.3d.accuracy': '多边形类型图元虚线3d效果精确度，该值越小3d渐进效果越好但影响性能，一般情况无需修改',
    'edge.type': '决定连线走向样式，为空代表连接成直线，多条时自动成组，自环时绘制成圆形；为points时连线走向将由edge.points属性决定，用于绘制折线',
    'edge.independent': '决定连线是否独立显示，为true时即使连接节点不可见时，连线依然可见',
    'edge.points': '可设置类型为ht.List的{x:100, y:100}格式的点对象数组，当edge.type为points时起作用',
    'edge.segments': 'ht.List类型，用于描述点连接样式，数组元素为整型值',
    'edge.source.position': '起始点相对于起始图元的相对位置',
    'edge.source.offset.x': '起始点的水平偏移',
    'edge.source.offset.y': '起始点的垂直偏移',
    'edge.source.anchor.x': '起始点的锚点横向相对位置',
    'edge.source.anchor.y': '起始点的锚点纵向相对位置',
    'edge.source.anchor.elevation': '起始点的锚点垂直方向相对位置',
    'edge.target.position': '结束点相对于结束图元的相对位置',
    'edge.target.offset.x': '结束点的水平偏移',
    'edge.target.offset.y': '结束点的垂直偏移',
    'edge.target.anchor.x': '结束点的锚点横向相对位置',
    'edge.target.anchor.y': '结束点的锚点纵向相对位置',
    'edge.target.anchor.elevation': '结束点的锚点垂直方向相对位置',
    'edge.color': '连线颜色',
    'edge.width': '连线宽度',
    'edge.offset': '连线端点和图元中心点的距离',
    'edge.group': '对于成组的连线，可通过此属性归为不同的组，以达到独立展开合并的效果',
    'edge.expanded': '决定当前连续处于展开还是合并状态',
    'edge.gap': '起始和结束相同节点的连线成捆的显示，该参数为成捆连线之间的间距',
    'edge.toggleable': '决定连线是否响应双击，进行成捆连线展开合并的切换',
    'edge.pattern': 'Array类型，定义虚线样式，例如[5, 5]',
    'edge.cap': '连线末端线帽的样式，可选参数为：butt|round|square',
    'edge.join': '连线为折线交汇时创建边角的类型，可选参数为：bevel|round|miter',
    'edge.center': '决定连线是否聚集到中心点',
    'edge.3d': '决定连线是否显示为3d效果',
    'edge.3d.color': '连线3d颜色，为空采用默认白色，呈现3d效果时连线的中间部分为该颜色',
    'edge.3d.accuracy': '连线3d效果精确度，该值越小3d渐进效果越好但影响性能，一般情况无需修改',
    'edge.repeat.image': '连线重复填充背景的图片，注意这里的图片不支持矢量',
    'edge.dash': '连线是否显示虚线',
    'edge.dash.pattern': '连线虚线样式',
    'edge.dash.offset': '连线虚线偏移',
    'edge.dash.color': '连线虚线颜色',
    'edge.dash.width': '连线虚线宽度',
    'edge.dash.3d': '连线虚线是否显示3d效果',
    'edge.dash.3d.color': '连线虚线3d效果颜色，为空采用默认白色，呈现3d效果时连线的中间部分为该颜色',
    'edge.dash.3d.accuracy': '连线虚线3d效果精确度，该值越小3d渐进效果越好但影响性能，一般情况无需修改',
    'edge.gradient.color': '连线渐进色，目前仅在`3D`上连线的非立体方式下支持，会在连线的`target`端呈现此渐进色',
    'edge.source.t3': '连线source端偏移，[tx, ty, tz]格式，默认为空',
    'edge.target.t3': '连线target端偏移，[tx, ty, tz]格式，默认为空',
    'attach.row.index': '图元host到Grid类型图元的行索引',
    'attach.column.index': '图元host到Grid类型图元的列索引',
    'attach.row.span': '图元host到Grid类型图元的行跨越数',
    'attach.column.span': '图元host到Grid类型图元的列跨越数',
    'attach.padding': '图元host到Grid类型图元的内边距',
    'attach.padding.left': '图元host到Grid类型图元的左边距',
    'attach.padding.right': '图元host到Grid类型图元的右边距',
    'attach.padding.top': '图元host到Grid类型图元的顶边距',
    'attach.padding.bottom': '图元host到Grid类型图元的底边距',
    'attach.index': '用于CSGNode吸附到Shape图元时使用，代表所在Shape的线段索引',
    'attach.offset': '代表吸附在CSGShape图元所在线段的偏移位置',
    'attach.offset.relative': '如果为`true`则偏移量代表所在线段长度乘以attach.offset值',
    'attach.offset.opposite': '代表所在线段的正方向还是反方向偏移',
    'attach.thickness': 'CSGNode的height属性值由Shape的thickness乘以该值决定',
    'attach.gap': '代表与线段垂直方向的偏移',
    'attach.gap.relative': '如果为true则与线段垂直方向的偏移为所Shape的thickness值乘以attach.gap',
    'attach.cull': '决定是否对CSGNode相交的部分进行剔除',
    'attach.operation': '与CSGNode图元模型操作，subtract代表裁剪，union代表融合，intersect代表取相交，其实值代表不做处理',
    'grid.row.count': 'Grid类型图元的行数',
    'grid.column.count': 'Grid类型图元的列数',
    'grid.row.percents': 'Grid类型图元的每行高度百分比，默认为null代表均分',
    'grid.column.percents': 'Grid类型图元的每列宽度百分比，默认为null代表均分',
    'grid.border': 'Grid类型图元的内边缘宽度',
    'grid.border.left': 'Grid类型图元的左边缘宽度',
    'grid.border.right': 'Grid类型图元的右边缘宽度',
    'grid.border.top': 'Grid类型图元的顶边缘宽度',
    'grid.border.bottom': 'Grid类型图元的底边缘宽度',
    'grid.gap': 'Grid类型图元的单元格的间隙',
    'grid.background': 'Grid类型图元的背景',
    'grid.depth': 'Grid类型图元的内边缘的深度，0代表平面效果，正值代表凸起效果，负值代表凹陷效果',
    'grid.cell.depth': 'Grid类型图元的单元格四边缘的深度，0代表平面效果，正值代表凸起效果，负值代表凹陷效果',
    'grid.cell.border.color': 'Grid类型图元设置单元格边框颜色，该属性在grid.cell.depth值为0时起效',
    'grid.block': 'Grid类型图元是否显示块边框，默认值为undefined代表不绘制，v代表绘制列块，h代表绘制行块',
    'grid.block.padding': 'Grid类型图元块边框距离单元格内容间距',
    'grid.block.width': 'Grid类型图元块边框绘制宽度',
    'grid.block.color': 'Grid类型图元块边框绘制颜色',
    'alphaTest': '当图形是不透明时（即没有设置 transparent 属性），贴图`alpha`透明度低于这个值则直接不显示',
    'brightness': '图元在3d下的亮度，大于1代表更亮，小于1代表更暗，1则代表不变化',
    'opacity': '图元在3d下的透明度，值范围0~1',
    'pixelPerfect': '图元在 2d 下，透明区域是否忽略选中图元，默认为 true，表示忽略选中',
    'label.face': '文字在3d下的朝向，可取值left|right|top|bottom|front|back|center',
    'label.t3': '文字在3d下的偏移，格式为[x,y,z]',
    'label.r3': '文字在3d下的旋转，格式为[rotationX,rotationY,rotationZ]',
    'label.rotationMode': '文字在3d下的沿三个轴旋转先后顺序，可取值xyz|xzy|yxz|yzx|zxy|zyx',
    'label.light': '文字在3d下是否受光线影响',
    'label.blend': '文字在3d下染色颜色',
    'label.opacity': '文字在3d下的透明度，值范围0~1',
    'label.reverse.flip': '文字在3d下反面是否显示正面的内容',
    'label.reverse.color': '文字在3d下反面的颜色',
    'label.reverse.cull': '文字在3d下反面是否显示，隐藏背面可提高性能',
    'label.transparent': '文字在3d下是否透明',
    'label.autorotate': '文字在3d下是否自动朝向眼睛的方向',
    'label.texture.scale': '该值代表内存实际生成贴图的倍数，不宜设置过大否则影响性能',
    'label2.face': '文字在3d下的朝向，可取值left|right|top|bottom|front|back|center',
    'label2.t3': '文字在3d下的偏移，格式为[x,y,z]',
    'label2.r3': '文字在3d下的旋转，格式为[rotationX,rotationY,rotationZ]',
    'label2.rotationMode': '文字在3d下的沿三个轴旋转先后顺序，可取值xyz|xzy|yxz|yzx|zxy|zyx',
    'label2.light': '文字在3d下是否受光线影响',
    'label2.blend': '文字在3d下染色颜色',
    'label2.opacity': '文字在3d下的透明度，值范围0~1',
    'label2.reverse.flip': '文字在3d下反面是否显示正面的内容',
    'label2.reverse.color': '文字在3d下反面的颜色',
    'label2.reverse.cull': '文字在3d下反面是否显示，隐藏背面可提高性能',
    'label2.transparent': '文字在3d下是否透明',
    'label2.autorotate': '文字在3d下是否自动朝向眼睛的方向',
    'label2.texture.scale': '该值代表内存实际生成贴图的倍数，不宜设置过大否则影响性能',
    'note.face': '标注在3d下的朝向，可取值left|right|top|bottom|front|back|center',
    'note.t3': '标注在3d下的偏移，格式为[x,y,z]',
    'note.r3': '标注在3d下的旋转，格式为[rotationX,rotationY,rotationZ]',
    'note.rotationMode': '标注在3d下的沿三个轴旋转先后顺序，可取值xyz|xzy|yxz|yzx|zxy|zyx',
    'note.light': '标注在3d下是否受光线影响',
    'note.blend': '标注在3d下染色颜色',
    'note.opacity': '标注在3d下的透明度，值范围0~1',
    'note.reverse.flip': '标注在3d下反面是否显示正面的内容',
    'note.reverse.color': '标注在3d下反面的颜色',
    'note.reverse.cull': '标注在3d下反面是否显示，隐藏背面可提高性能',
    'note.transparent': '标注在3d下是否透明',
    'note.autorotate': '标注在3d下是否自动朝向眼睛的方向',
    'note.texture.scale': '该值代表内存实际生成贴图的倍数，不宜设置过大否则影响性能',
    'note2.face': '标注在3d下的朝向，可取值left|right|top|bottom|front|back|center',
    'note2.t3': '标注在3d下的偏移，格式为[x,y,z]',
    'note2.r3': '标注在3d下的旋转，格式为[rotationX,rotationY,rotationZ]',
    'note2.rotationMode': '标注在3d下的沿三个轴旋转先后顺序，可取值xyz|xzy|yxz|yzx|zxy|zyx',
    'note2.light': '标注在3d下是否受光线影响',
    'note2.blend': '标注在3d下染色颜色',
    'note2.opacity': '标注在3d下的透明度，值范围0~1',
    'note2.reverse.flip': '标注在3d下反面是否显示正面的内容',
    'note2.reverse.color': '标注在3d下反面的颜色',
    'note2.reverse.cull': '标注在3d下反面是否显示',
    'note2.transparent': '标注在3d下是否透明',
    'note2.autorotate': '标注在3d下是否自动朝向眼睛的方向',
    'note2.texture.scale': '该值代表内存实际生成贴图的倍数，不宜设置过大否则影响性能',
    'shape3d': '为空时显示为六面立方体，其他可选值为box|sphere|cylinder|cone|torus|star|rect|roundRect|triangle|rightTriangle|parallelogram|trapezoid',
    'shape3d.scaleable': '模型的显示大小是否根据图元的s3参数变化',
    'shape3d.top.cap': 'ht.Edge和ht.Polyline都通过该参数控制起始部分形状，可取值flat|round|null',
    'shape3d.bottom.cap': 'ht.Edge和ht.Polyline都通过该参数控制结束部分形状，可取值flat|round|null',
    'shape3d.start.angle': 'ht.Edge和ht.Polyline都通过该参数控制管道截面起始边的角度',
    'shape3d.sweep.angle': 'ht.Edge和ht.Polyline都通过该参数控制管道截面的跨越角度',
    'shape3d.color': '3d图形整体颜色',
    'shape3d.top.color': '3d图形顶面颜色',
    'shape3d.bottom.color': '3d图形底面颜色',
    'shape3d.from.color': '3d图形起始面颜色',
    'shape3d.to.color': '3d图形结束面颜色',
    'shape3d.image': '3d图形整体贴图',
    'shape3d.top.image': '3d图形顶面贴图',
    'shape3d.bottom.image': '3d图形底面贴图',
    'shape3d.from.image': '3d图形起始面贴图',
    'shape3d.to.image': '3d图形结束面贴图',
    'shape3d.light': '3d图形是否受光线影响',
    'shape3d.side': '决定3d图形显示为几边型，为0时显示为平滑的曲面效果',
    'shape3d.side.from': '决定3d图形起始边位置',
    'shape3d.side.to': '决定3d图形结束边位置',
    'shape3d.visible': '决定3d图形是否可见，该参数不影响label,note和icons等其他部分元素',
    'shape3d.from.visible': '决定3d图形起始面是否可见',
    'shape3d.to.visible': '决定3d图形结束面是否可见',
    'shape3d.top.visible': '决定3d图形顶面是否可见',
    'shape3d.bottom.visible': '决定3d图形底面是否可见',
    'shape3d.torus.radius': '决定3d圆环形管半径',
    'shape3d.resolution': '决定3d图形微分分段数，和side类似但决定不同的方向的分段，数值越大越均匀但影响性能',
    'shape3d.blend': '决定3d图形的染色',
    'shape3d.opacity': '决定3d图形的透明度，值范围0~1',
    'shape3d.reverse.flip': '决定3d图形的反面是否显示正面的内容',
    'shape3d.reverse.color': '决定3d图形的反面颜色',
    'shape3d.reverse.cull': '决定3d图形的反面是否显示，隐藏背面可提高性能',
    'shape3d.transparent': '决定3d图形是否透明',
    'shape3d.uv.offset': '决定3d图形整体贴图的uv偏移，格式为[0.5,0.5]',
    'shape3d.uv.scale': '决定3d图形整体贴图的uv缩放，格式为[3,2]',
    'shape3d.uv.rotation': '决定3d图形贴图整体旋转角度，格式为数字',
    'shape3d.top.uv.offset': '决定3d图形顶面贴图的uv偏移，格式为[0.5,0.5]',
    'shape3d.top.uv.scale': '决定3d图形顶面贴图的uv缩放，格式为[3,2]',
    'shape3d.top.uv.rotation': '决定3d图形顶面贴图旋转角度，格式为数字',
    'shape3d.bottom.uv.offset': '决定3d图形底面贴图的uv偏移，格式为[0.5,0.5]',
    'shape3d.bottom.uv.scale': '决定3d图形底面贴图的uv缩放，格式为[3,2]',
    'shape3d.bottom.uv.rotation': '决定3d图形底面贴图旋转角度，格式为数字',
    'shape3d.from.uv.offset': '决定3d图形起始面贴图的uv偏移，格式为[0.5,0.5]',
    'shape3d.from.uv.scale': '决定3d图形起始面贴图的uv缩放，格式为[3,2]',
    'shape3d.from.uv.rotation': '决定3d图形起始面贴图旋转角度，格式为数字',
    'shape3d.to.uv.offset': '决定3d图形结束面贴图的uv偏移，格式为[0.5,0.5]',
    'shape3d.to.uv.scale': '决定3d图形结束面贴图的uv缩放，格式为[3,2]',
    'shape3d.to.uv.rotation': '决定3d图形结束面贴图旋转角度，格式为数字',
    'shape3d.discard.selectable': '决定贴图透明度低到被剔除的部分是否可选中',
    'shape3d.top.discard.selectable': '决定顶部贴图透明度低到被剔除的部分是否可选中',
    'shape3d.bottom.discard.selectable': '决定底部贴图透明度低到被剔除的部分是否可选中',
    'shape3d.from.discard.selectable': '决定起始面贴图透明度低到被剔除的部分是否可选中',
    'shape3d.to.discard.selectable': '决定结束面贴图透明度低到被剔除的部分是否可选中',
    'shape3d.smooth': '决定shape3d是sphere/cylinder/torus类别时生成的模型是否是平滑的',
    'shape3d.point.epsilon': 'shape生成3D几何体的时候，过于接近的点将被剔除以便更正确的三角形化',
    '3d.reflectable': '设置节点是否镜面可见，设置为 false 时，镜子里看不到该节点',
    'shape3d.reflector': '是否开启作为镜面反射，仅在 shape3d 为 billboard / plane 时生效',
    'shape3d.reflector.color': '镜面反射的混合色',
    'shape3d.reflector.blur': '镜面反射的模糊级别',
    'shape3d.reflector.blur.ratio': '镜面反射的模糊范围',
    'shape3d.reflector.background': '背景色',
    'shape3d.reflector.clip': '镜面之上需要clip，判定单位是深度，裁切需要bias偏差以表现更好，0.003向上稍突出以去除缝隙',
    'repeat.uv.length': '如果设置了长度值，则贴图会根据模型长度自动调节横向贴图倍数',
    'light.type': '默认为`point`点光灯，可设置为`spot`聚光灯，以及`directional`的方向光类型',
    'light.color': '灯颜色',
    'light.intensity': '灯强度，默认为`1`，大于`1`增强，小于`1`减弱',
    'light.disabled': '关闭灯光效果，默认为false，可设置为true关闭灯效',
    'light.center': '光源照射方向的中心点位置',
    'light.range': '灯影响范围，默认为`0`代表可照射到无穷远处，如果设置了值则光照射效果随物体远离光影而衰',
    'light.angle': '灯照射的张角弧度，该张角照射范围内物体才受此光源影响',
    'light.exponent': '灯光照射衰减指数，默认值为`0`，值越大离中心方向越远的物体受光照影响效果越弱',
    'highlight.visible': '3D中图元是否受悬浮高亮影响，默认true，即开启悬浮高亮后，该图元可以显示高亮',
    'highlight.width': '3D中悬浮高亮的厚度，全局设置',
    'highlight.color': '3D中悬浮高亮的颜色，全局设置',
    'highlight.mode': '高亮的模式，包括：`disabled`关闭高亮，`selected`选中高亮，`hover`悬浮高亮，`style`风格设置高亮',
    'color.empty': '3D中贴图无法找到等因素，将会使用空颜色，该值设置空颜色具体采用哪个颜色',
    'mat': '3d整体图形矩阵变化',
    'left.mat': '六面体的左面进行矩阵变化',
    'right.mat': '六面体的右面进行矩阵变化',
    'top.mat': '六面体的顶面进行矩阵变化',
    'bottom.mat': '六面体的底面进行矩阵变化',
    'front.mat': '六面体的前面进行矩阵变化',
    'back.mat': '六面体的后面进行矩阵变化',
    'dw.flip': '对DoorWindow图元显示进行翻转，相当于沿z轴旋转180度',
    'dw.s3': '对DoorWindow图元显示尺寸在原有基础上再进行缩放，[0.999, 0.999, 0.5]代表x和y收缩了0.001，z收缩了0.5',
    'dw.t3': '对DoorWindow图元显示位置在原有基础上再进行绝对位置偏移',
    'dw.toggleable': '对DoorWindow图元是否允许双击进行展开和关闭的操作',
    'dw.expanded': 'DoorWindow图元当前图元是否处于展开状态',
    'dw.angle': 'DoorWindow图元当前状态旋转角度',
    'dw.start': 'DoorWindow图元关闭状态的起始旋转弧度',
    'dw.end': 'DoorWindow图元展开状态的结束旋转弧度',
    'dw.axis': 'DoorWindow图元展开和关闭操作的旋转轴，可取值left|right|top|bottom|v|h',
    'all.light': '六面是否受光线影响',
    'all.visible': '六面是否可见',
    'all.color': '六面颜色',
    'all.image': '六面贴图',
    'all.blend': '六面染色颜色',
    'all.opacity': '六面透明度，值范围0~1',
    'all.reverse.flip': '六面的反面是否显示正面的内容',
    'all.reverse.color': '六面的反面颜色',
    'all.reverse.cull': '六面的背面是否可见',
    'all.transparent': '六面是否透明',
    'all.uv': '六面uv自定义，为空采用默认值[0,0, 0,1, 1,1, 1,0]，左旋转90度[1,0,  0,0, 0,1, 1,1]，右旋转90度[0,1, 1,1, 1,0, 0,0]，右旋转180度[1,1, 1,0, 0,0, 0,1]，左旋转180度：[0,1, 0,0, 1,0, 1,1]',
    'all.uv.offset': '六面贴图的uv偏移，格式为[0.5,0.5]',
    'all.uv.scale': '六面贴图的uv缩放，格式为[3,2]',

    'all.discard.selectable': '六面贴图透明度低到被剔除的部分是否可选中',
    'text': '文本内容',
    'text.align': '文本水平对齐方式，可取值 left|center|right',
    'text.vAlign': '文本在多行情况下的垂直对齐方式，可取值 top|middle|bottom',
    'text.color': '文本颜色',
    'text.gradient': '文本渐近类型',
    'text.gradient.color': '文本渐进颜色',
    'text.gradient.pack': '文本渐进颜色集，可以是颜色值，也可以数组，如 ["linear", 0, 0, 0, 1, 0, "red", 1, "green"]或["radial", 0.5, 0.5, 0.2, 0, "red", 1, "green"]',
    'text.font': '文本字体',
    'text.shadow': '文本是否开启阴影效果',
    'text.shadow.color': '文本阴影颜色',
    'text.shadow.blur': '文本阴影模糊级别',
    'text.shadow.offset.x': '文本阴影水平偏移',
    'text.shadow.offset.y': '文本阴影垂直偏移',
    'text.scale.x': '文本水平缩放值',
    'text.scale.y': '文本垂直缩放值',
    'text.gradient.pack': '文本渐变颜色',
    'interactive': '是否可交互，默认false',
    'clip.direction': '裁切方向。top: 从上到下，right: 从左到右，bottom: 从下到上，left: 从右到左。默认top',
    'clip.percentage': '裁切比例，取值 0 ~ 1',
    'shape.foreground.clip.gradient': '1、大于 1，保持固定宽度的渐变区域；2、0 ~ 1，保持图元方向长度百分比的宽度 3、0，默认值，不处理',
    '3d.clip.direction': '裁切方向。top: 从上到下，right: 从左到右，bottom: 从下到上，left: 从右到左。front：从后到前，back：从前到后',
    '3d.clip.percentage': '裁切比例，取值 0 ~ 1',
    'shape.border.width.absolute': '多边形边框宽度，是否不跟随比例缩放，默认false',
    'shape.fill.rule': '多边形背景填充规则。nonzero: 非零环境，evenodd: 奇偶环境',
    'shape.fill.clip.direction': '多边形背景裁切方向，默认top。top: 从上到下，right: 从左到右，bottom: 从下到上，left: 从右到左。',
    'shape.fill.clip.percentage': '多边形背景裁切比例，取值 0 ~ 1',
    'autolayout.gap': '节点布局间距',
    'autolayout.hgap': '节点横向布局间距',
    'autolayout.vgap': '节点纵向布局间距',
    'edge.width.absolute': '连线线宽度，是否不跟随比例缩放，默认false',
    'wf.geometry': '是否显示几何体的线条',
    'wf.loadQuadWireframe': '是否载入四边面的线框',
    'wf.combineTriangle': '线框模型是否合并三角',
    'autorotate': '是否自动朝向相机',
    'fixSizeOnScreen': '是否无论缩放远近，在屏幕内呈现固定大小，值可为true（使用图片或矢量自身大小）/false, 也可以是[100, 200]（对应宽高）',
    'texture.cache': '如果贴图是矢量，是否缓存（缓存后性能会得到提升）',
    'alwaysOnTop': '是否总是在最前',
    'layout.h': '横向布局方式，可取值left|center|right|scale|leftright',
    'layout.v': '纵向布局方式，可取值top|center|bottom|scale|topbottom',
    'preventDefaultWhenInteractive': '节点可交互时，是否阻止拓扑图的交互（如缩放平移）',
    '2d.gray': '是否灰化，即变为黑白色',
    'fullscreen': '是否占满全屏，可取值： fill 填充，uniform 等比',
    'fullscreen.gap': '全屏时，预留边距',
    'fullscreen.lock': '全屏时是否保持宽高不变，用拓扑图的缩放来适配屏幕，h：横向锁定，宽度不变，v：纵向锁定，高度不变',
    'attach.points': '吸附点，用于辅助连线操作，格式为对象数组[{x, y}, {x, y} ...]',
    'attach.style': '吸附方式，close：接近时吸附，远离时可在其它位置吸附；strict：严格，只能吸附到吸附点上，无法在其它位置上吸附',
    'attach.close.threshold': '吸附点判定距离',
    'edge.source.index': '起始点是另外一条连线时，连接的拐点下标，优先于 edge.source.percent 的设置',
    'edge.source.percent': '起始点是另外一条连线时，连接的百分比位置，取值0~1',
    'edge.target.index': '结束点是另外一条连线时，连接的拐点下标，优先于 edge.target.percent 的设置',
    'edge.target.percent': '结束点是另外一条连线时，连接的百分比位置，取值0~1',
    'renderTextureSamples': '开启后处理后，抗锯齿采样数量，小于2则关闭抗锯齿，取值正整数，但是不建议太大会影响性能',
    'hide.overlapping.group': 'billboard开启autorotate后，同名组的billboard被遮挡会隐藏掉，以避免太多billboard显示，默认undefined不因遮挡隐藏',
    'vector.dynamic': 'billboard开启autorotate后，是否动态根据在屏幕的大小来决定3D贴图大小，以呈现最清晰的状态，注意对性能有一定影响',
    'vector.dynamic.maxsize': '开启vector.dynamic后，动态贴图的大小上限保护，避免太大的贴图对性能影响，建议不超过4096',
    'texture.scale': '矢量贴图在 3D 中的大小比例，例如设置该值为 2 的时候，矢量申明是 64*64，则生成的 3D 贴图是 128*128',
    'geometry.cache': '针对3D中的polyline/edge，是否锁定形状，即使属性变化仍然不进行形状的重建计算，以提升性能。例如此时只是进行uv流动动画，可以将该值设置为true',
    'texture.mipmap.max': '3D 环境下，图片开启 mipmap 的话，设置 mipmap 最高等级，默认 undefined 不特殊设置干预',
    'texture.filter': '3D 环境下，贴图绘制的像素采样方式，可设置字符串 nearest 或者 linear，默认为 undefined 不特殊设置干预',
    'texture.filter.min': '3D 环境下，大贴图绘制到小区域的像素采样方式，可设置字符串 nearest 或者 linear，默认为 undefined，取 texture.filter 样式值',
    'texture.filter.mag': '3D 环境下，小贴图绘制到大区域的像素采样方式，可设置字符串 nearest 或者 linear，默认为 undefined，取 texture.filter 样式值',
    'envmap': '决定节点受环境光影响的程度，取值范围为 0 ~ 1',
    'csg.cull.color': '被裁切位置显示的颜色值',
    'csg.cull.box': '是否用模型的包围盒进行裁切，表示采用 Node 包围盒裁切，可设置为 false，表示采用 Node 设置的 Shape3dModel 进行裁切。注意，如果 Shape3dModel 存在凹陷的面，不建议将该属性设置为 false',
    'points.image': '点云节点上，展示的贴图，包括，rect / circle / 图片 / 矢量，该样式与 points.color 互斥，并且优先级高于 points.color 样式',
    'points.color': '点云节点上，展示的颜色值，只有当未指定 points.image 样式时才生效',
    'points.transparent': '点云节点是否透明',
    'points.size': '点云节点展示的大小，这边要求点云设置的贴图大小，必须是正方形比例的贴图，如果贴图是非正方形，会被拉伸变形',
    'points.opacity': '点云节点的透明度，取值范围 0 ~ 1',
    'keepPosition': '修改block下子元素的大小位置，block 的位置 position 保持不变',
    'text.decoration': '文本装饰，可设置为：underline、overline 或 strikethrough',
    'text.vertical': '文本是否垂直绘制',
    'text.vertical.gap': '文本垂直绘制间隔',
};

[
    {k: 'all', n: '六'},
    {k: 'left', n: '左'},
    {k: 'right', n: '右'},
    {k: 'top', n: '顶'},
    {k: 'bottom', n: '底'},
    {k: 'front', n: '前'},
    {k: 'back', n: '后'}
].forEach(function(face){
    commentMap[face.k + '.light'] = face.n + '面是否受光线影响';
    commentMap[face.k + '.visible'] = face.n + '面是否可见';
    commentMap[face.k + '.color'] = face.n + '面颜色';
    commentMap[face.k + '.image'] = face.n + '面贴图';
    commentMap[face.k + '.blend'] = face.n + '面染色颜色';
    commentMap[face.k + '.opacity'] = face.n + '面透明度，值范围0~1';
    commentMap[face.k + '.reverse.flip'] = face.n + '面的反面是否显示正面的内容';
    commentMap[face.k + '.reverse.color'] = face.n + '面的反面颜色';
    commentMap[face.k + '.reverse.cull'] = face.n + '面的背面是否可见';
    commentMap[face.k + '.transparent'] = face.n + '面是否透明';
    commentMap[face.k + '.uv'] = face.n + '面uv自定义，为空采用默认值[0,0, 0,1, 1,1, 1,0]，左旋转90度[1,0,  0,0, 0,1, 1,1]，右旋转90度[0,1, 1,1, 1,0, 0,0]，右旋转180度[1,1, 1,0, 0,0, 0,1]，左旋转180度：[0,1, 0,0, 1,0, 1,1]';
    commentMap[face.k + '.uv.offset'] = face.n + '面贴图的uv偏移，格式为[0.5,0.5]';
    commentMap[face.k + '.uv.scale'] = face.n + '面贴图的uv缩放，格式为[3,2]';
    commentMap[face.k + '.discard.selectable'] = face.n + '贴图透明度低到被剔除的部分是否可选中';
    commentMap[face.k + '.toggleable'] = 'CSGBox' + face.n + '面是否允许双击面进行展开和关闭的操作';
    commentMap[face.k + '.expanded'] = 'CSGBox' + face.n + '面当前是否处于展开状态';
    commentMap[face.k + '.angle'] = 'CSGBox' + face.n + '面当前状态旋转角度';
    commentMap[face.k + '.start'] = 'CSGBox' + face.n + '面关闭状态的起始旋转弧度';
    commentMap[face.k + '.end'] = 'CSGBox' + face.n + '面展开状态的结束旋转弧度';
    commentMap[face.k + '.axis'] = 'CSGBox' + face.n + '面展开和关闭操作的旋转轴，可取值left|right|top|bottom|v|h';
    commentMap[face.k + '.uv.rotation'] = face.n + '面贴图uv旋转角度，格式为数字';
});
