<!DOCTYPE html>
<html>
    <head>
        <title>FormPane</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>    
                                       
        <script src="../../../../lib/core/ht.js"></script>          
        <script src="../../../../lib/plugin/ht-form.js"></script>  
        <script>
            
            function createText(text){
                return {
                    element: text,
                    background: 'yellow',
                    align: 'center'
                };
            }
            
            function init(){                                
                formPane1 = new ht.widget.FormPane();  
                formPane2 = new ht.widget.FormPane();                  
                formPane3 = new ht.widget.FormPane();  
                formPane4 = new ht.widget.FormPane();  
                
                split1 = new ht.widget.SplitView(formPane1, formPane2); 
                split2 = new ht.widget.SplitView(formPane3, formPane4); 
                split3 = new ht.widget.SplitView(split1, split2, 'v');         
                                                      
                view = split3.getView();
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    split3.invalidate();
                }, false);                                                   
                  
                var w = 50;  
                formPane1.addRow(['key1', createText('value1'), 'key2', createText('value2')], [w, 0.1, w, 0.1]);
                formPane1.addRow(['key3', createText('value3'), 'key4', createText('value4')], [w, 0.1, w, 0.1]);
                formPane1.addRow(['key5', createText('value5'), 'key6', createText('value6')], [w, 0.1, w, 0.1]);
                formPane1.addRow(['key7', createText('value7'), 'key8', createText('value8')], [w, 0.1, w, 0.1]);
                formPane1.addRow([{element: 'key9', vAlign: 'top'}, createText('value9')], [w, 0.1], 0.1);
   
                formPane2.addRow(['key1', createText('value1'), 'key2', createText('value2')], [w, 0.1, w, 0.1]);
                formPane2.addRow(['key3', createText('value3'), 'key4', createText('value4')], [w, 0.1, w, 0.1]);
                formPane2.addRow(['key5', createText('value5'), 'key6', createText('value6')], [w, 0.1, w, 0.1], 0.1);
                formPane2.addRow(['key7', createText('value7'), 'key8', createText('value8')], [w, 0.1, w, 0.1], 0.1);
   
                var ww = '100+0.1';
                formPane3.addRow(['key1', createText('value1'), 'key2', createText('value2')], [w, ww, w, ww]);
                formPane3.addRow(['key3', createText('value3'), 'key4', createText('value4')], [w, ww, w, ww]);
                formPane3.addRow(['key5', createText('value5'), 'key6', createText('value6')], [w, ww, w, ww]);
                formPane3.addRow(['key7', createText('value7'), 'key8', createText('value8')], [w, ww, w, ww]);
                formPane3.addRow(['key9', createText('value9'), 'key10', createText('value10')], [w, ww, w, ww], 0.1);                
                
                formPane4.addRow(['key1', createText('value1'), 'key2', createText('value2')], [w, ww, w, ww]);
                formPane4.addRow(['key3', createText('value3'), 'key4', createText('value4')], [w, ww, w, ww], w);
                formPane4.addRow(['key5', createText('value5'), 'key6', createText('value6')], [w, ww, w, ww], ww);
                formPane4.addRow(['key7', createText('value7'), 'key8', createText('value8')], [w, ww, w, ww], ww);                  
            }
            
                    
            
        </script>
    </head>
    <body onload="init();">
    </body>
</html>
