<!doctype html>
<html>
    <head>
        <title>HT for Web Beginners Manual</title>
        <meta charset="UTF-8">    
        <meta name="viewport" content="user-scalable=yes, width=1024">
        <style type="text/css">
            .sideNav{
                width:195px;
                background:white;
                position:fixed;
                top:10px;
                left:5px;
                bottom:10px;
                border:1px solid rgb(230,230,230);
            }
            .sideNav .header,.contactable{
                background:rgb(61,74,94);
                color:white;
                line-height: 28px;
                height: 28px;
                padding:0 5px;
                margin:0;
                position:relative;
            }
            .sideNav .navContainer{
                width:100%;
                position: absolute;
                top:28px;
                bottom:0;
                overflow:auto;
            }
            .sideNav ul{
                padding-left:15px;
                list-style-position:inside;
            }
            .navContainer>ul{
                margin-top: 10px;
                background-position: 0px 100px;
                background-size: 0 0;

                background-image: -moz-linear-gradient(top, rgb(105,187,156), rgb(105,187,156)); 
                background-image: -webkit-linear-gradient(left, rgb(105,187,156), rgb(105,187,156));
                background-image: linear-gradient(left, rgb(105,187,156), rgb(105,187,156));
                background-image: -ms-linear-gradient(left, rgb(105,187,156), rgb(105,187,156));
                background-repeat: no-repeat;
            }
            .contactable{
                -webkit-transform: rotate(90deg);
                -webkit-transform-origin:0 100%;
                -moz-transform: rotate(90deg);
                -moz-transform-origin:0 100%;
                -ms-transform: rotate(90deg);
                -ms-transform-origin:0 100%;
                transform: rotate(90deg);
                transform-origin:0 100%;

                position:fixed;
                left:0;
                top:10px;
                cursor: pointer;
            }
            .anim{
                -webkit-transition: all .3s;
                -moz-transition: all .3s;
                -o-transition: all .3s;
                transition: all .3s;
            }
            h1, h2, h3, h4, h5, h6, p, blockquote {
                margin: 0;
                padding: 0;
            }
            body {
                font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif;
                font-size: 13px;
                line-height: 18px;
                color: #737373;
                background-color: white; 
                margin: 10px 13px 10px 13px;
            }
            table {
                margin: 10px 0 15px 0;
                border-collapse: collapse;
            }
            td,th {	
                border: 1px solid #ddd;
                padding: 3px 10px;
            }
            th {
                padding: 5px 10px;	
            }
            a {
                color: #0069d6;
            }
            a:hover {
                color: #0050a3;
                text-decoration: none;
            }
            a img {
                border: none;
            }
            p {
                margin-bottom: 9px;
            }
            h1, h2, h3, h4, h5, h6 {
                color: #404040;
                line-height: 36px;
            }
            h1 {
                margin-bottom: 18px;
                font-size: 30px;
            }
            h2 {
                font-size: 24px;
            }
            h3 {
                font-size: 18px;
            }
            h4 {
                font-size: 16px;
            }
            h5 {
                font-size: 14px;
            }
            h6 {
                font-size: 13px;
            }
            hr {
                margin: 0 0 19px;
                border: 0;
                border-bottom: 1px solid #ccc;
            }
            blockquote {
                padding: 13px 13px 21px 15px;
                margin-bottom: 18px;
                font-family:georgia,serif;
                font-style: italic;
            }
            blockquote:before {
                content:"\201C";
                font-size:40px;
                margin-left:-10px;
                font-family:georgia,serif;
                color:#eee;
            }
            blockquote p {
                font-size: 14px;
                font-weight: 300;
                line-height: 18px;
                margin-bottom: 0;
                font-style: italic;
            }
            code, pre {
                font-family: Monaco, Andale Mono, Courier New, monospace;
            }
            code {
                background-color: #fee9cc;
                color: rgba(0, 0, 0, 0.75);
                padding: 1px 3px;
                font-size: 12px;
                -webkit-border-radius: 3px;
                -moz-border-radius: 3px;
                border-radius: 3px;
            }
            pre {
                display: block;
                padding: 14px;
                margin: 0 0 18px;
                line-height: 16px;
                font-size: 11px;
                border: 1px solid #d9d9d9;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            pre code {
                background-color: #fff;
                color:#737373;
                font-size: 11px;
                padding: 0;
            }
            sup {
                font-size: 0.83em;
                vertical-align: super;
                line-height: 0;
            }
            * {
                -webkit-print-color-adjust: exact;
            }
            @media screen and (min-width: 914px) {
                body {
                    width: 854px;
                    margin:10px auto;
                }
            }
            @media print {
                body,code,pre code,h1,h2,h3,h4,h5,h6 {
                    color: black;
                }
                table, pre {
                    page-break-inside: avoid;
                }
            }                        
            iframe{
                width: 100%;
                border: 1px solid #34495E;
                margin: 0;                
            }            
            .logo{
                vertical-align: middle;
            }

        </style>        
        <script>
            var sideNav, contactable, sideNavUL, navContainer;
            if (/MSIE (\d+\.\d+);/.test(navigator.userAgent)) { //test for MSIE x.x;
                var ieversion = new Number(RegExp.$1);
            }
            document.addEventListener("click",function (e){
                if(sideNav){
                    var bounds=sideNav.getBoundingClientRect();
                    if(sideNav.style.opacity==1&&(e.clientX<bounds.left||e.clientX>bounds.left+bounds.width||e.clientY<bounds.top||e.clientY>bounds.top+bounds.height)){
                        toggleNav();
                    };
                }
            });
            document.addEventListener("DOMContentLoaded",function (e){
                sideNav = document.getElementById("sideNav");
                contactable = document.getElementById("contactable");
                sideNavUL = document.getElementById("sideNavUL");
                navContainer = document.getElementById("navContainer");
                sideNav.addEventListener("webkitTransitionEnd", handleTransitionEnd, false);
                sideNav.addEventListener("transitionend", handleTransitionEnd, false);
                
                //wheel mouse to imitate scrolling
                navContainer.addEventListener("mousewheel", function (e) {
                    var scrollTo = null;
                    scrollTo = (e.wheelDelta * -1);
                    if (scrollTo) {
                        e.preventDefault();
                        this.scrollTop += scrollTo;
                    }
                });
                //wheel mouse to imitate scrolling(firefox)
                navContainer.addEventListener("DOMMouseScroll", function (e) {
                    var scrollTo = null;
                    scrollTo = 40 * e.detail;
                    if (scrollTo) {
                        e.preventDefault();
                        this.scrollTop += scrollTo;
                    }
                });
                
                //draw background of sideNav
                var navMap = {}, refMap = {}, anchors = ["overview", 'what', 'characteristic', 'sdk', 'tool', 
                    'environment', 'abbreviation', 'model', 'designpattern', 
                    'classes', 'util', 'list', 'notifier', 'default', 'style', 'color', 'jsonserializer',
                    'datatype', 'data', 'node', 'edge', 'group', 'shape', 'polyline', 'grid', 'subgraph', 'tab', 'column', 'property', 'datamodel', 'selectionmodel', 
                    'view', 'viewstructure', 'config', 'image', 'animation', 'graphview', 'graphviewzoom', 'graphviewinteraction',
                    'filter', 'graphviewstyle', 'styleshape', 'styleposition', 'styleselect', 'stylelabel', 'stylenote','styleicon',
                     'styleborder', 'stylegroup', 'styleedge', 'propertyview', 'listview',
                    'treeview', 'tableview', 'treetableview', 'toolbar', 'splitview', 'borderpane', 'accordionview',
                    'tabview', 'recommendation', 'book', 'subscribe'];
                anchors.forEach(function (element) {
                    refMap[element] = document.getElementById("ref_" + element);
                    navMap[element] = document.getElementById("nav_" + element);
                });
                var visibleAnchor = "overview";
                window.addEventListener("scroll", function (e) {
                    //ignore scroll event which is delivered from navContainer                    
                    if (e.target == navContainer || e.srcElement == navContainer) {
                        return;
                    }
                    for (var i = 0; i < anchors.length; i++) {
                        var refEle = refMap[anchors[i]];
                        var top = refEle.getBoundingClientRect().top;
                        if (top > -5 && top < 200) {
                            if (ieversion == 9) {
                                navMap[visibleAnchor].style.background = "white";
                            }
                            navMap[visibleAnchor].style.color = "rgb(0, 105, 214)";
                            visibleAnchor = anchors[i];
                            break;
                        }
                    }
                    var aEle = navMap[visibleAnchor];
                    var aTop = aEle.getBoundingClientRect().top;
                    var aHeight = aEle.getBoundingClientRect().height;
                    var aBottom = aEle.getBoundingClientRect().bottom;
                    var navULTop = sideNavUL.getBoundingClientRect().top;
                    var aToULGap = aTop - navULTop;
                    if (aTop < navContainer.scrollTop + navULTop) {
                        navContainer.scrollTop -= (navContainer.scrollTop + navULTop - aTop);
                    } else if (aBottom > document.documentElement.clientHeight) {
                        navContainer.scrollTop += (aBottom - document.documentElement.clientHeight) + aHeight;
                    }
                    if (ieversion == 9) {
                        aEle.style.background = "rgb(105,187,156)";
                    }
                    aEle.style.color = "white";
                    sideNavUL.style.setProperty("background-size", "100% 16px", null);
                    sideNavUL.style.setProperty("background-position", "0 " + aToULGap + "px", null);
                }, true);
            });
            
            function toggleNav() {
                if (sideNav.style.opacity == 1) {
                    sideNav.style.opacity = 0;
                    sideNav.style.left = "-200px";
                    if (ieversion == 9) {
                        contactable.style.display = "block";
                    }
                } else {
                    contactable.style.display = "none";
                    sideNav.style.opacity = 1;
                    sideNav.style.left = "5px";
                }
                
            }
            function handleTransitionEnd(e) {
                if(e.propertyName!="opacity")return;
                if (sideNav.style.opacity < 1)
                    contactable.style.display = "block";
            }

            function init() {
                var logoSrc = 'data:image/png;base64,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',
                        logos = document.querySelectorAll('.logo'),
                        i = 0;
                for (; i < logos.length; i++) {
                    logos[i].src = logoSrc;
                }

                var iframes = document.querySelectorAll('iframe'),
                        func = function () {
                };
                for (i = 0; i < iframes.length; i++) {
                    var iframe = iframes[i];

                    // a small hack to make it work on android
                    iframe.ontouchstart = func;

                    var div = document.createElement('div');
                    div.innerHTML = '<a href="' + iframe.src + '" target="_blank">Run→</a>';
                    iframe.parentNode.insertBefore(div, iframe);
                }
            }
        </script>    
    </head>
    <body onload="init();">
        <a href="http://www.hightopo.com"><img class="logo"></a>HT for Web Beginners Manual
        <hr style="margin: 1px 0 20px 0">
        <div class="anim sideNav" id="sideNav" style="opacity: 0;left:-200px;">
            <p class="header">Index<span style="position:absolute;right:5px;display:inline-block;width:20px;height:20px;text-align: center;cursor:pointer" onclick="toggleNav()"><</span></p>
            <div class="navContainer" id="navContainer">
                <ul id="sideNavUL">
                    <li>
                        <a id="nav_overview" href="#ref_overview">Overview</a>
                        <ul>
                            <li><a id="nav_what" href="#ref_what">What is HT for Web</a></li>
                            <li><a id="nav_characteristic" href="#ref_characteristic">Product Characteristic</a></li>
                            <li><a id="nav_sdk" href="#ref_sdk">Development of Class Library</a></li>
                            <li><a id="nav_tool" href="#ref_tool">Development Tools</a></li>
                            <li><a id="nav_environment" href="#ref_environment">Runtime Environment</a></li>
                            <li><a id="nav_abbreviation" href="#ref_abbreviation">Function Abbreviation</a></li>
                        </ul>
                    </li>
                    <li>
                        <a id="nav_model" href="#ref_model">Model</a>
                        <ul>
                            <li><a id="nav_designpattern" href="#ref_designpattern">Design Mode</a></li> 
                            <li><a id="nav_classes" href="#ref_classes">Package Hierarchy</a></li>                            
                            <li>
                                <a id="nav_util" href="#ref_util">Tool Class</a>
                                <ul>                            
                                    <li><a id="nav_list" href="#ref_list">List</a></li>
                                    <li><a id="nav_notifier" href="#ref_notifier">Notifier</a></li>
                                    <li><a id="nav_default" href="#ref_default">Default</a></li>
                                    <li><a id="nav_style" href="#ref_style">Style</a></li>
                                    <li><a id="nav_color" href="#ref_color">Color</a></li>
                                    <li><a id="nav_jsonserializer" href="#ref_jsonserializer">JSONSerializer</a></li>
                                </ul>
                            </li>                                                        
                            <li>
                                <a id="nav_datatype" href="#ref_datatype">Data Type</a>
                                <ul>
                                    <li><a id="nav_data" href="#ref_data">Data</a></li>
                                    <li><a id="nav_node" href="#ref_node">Node</a></li>
                                    <li><a id="nav_edge" href="#ref_edge">Edge</a></li>
                                    <li><a id="nav_group" href="#ref_group">Group</a></li>
                                    <li><a id="nav_shape" href="#ref_shape">Shape</a></li>
                                    <li><a id="nav_polyline" href="#ref_polyline">Polyline</a></li>
                                    <li><a id="nav_grid" href="#ref_grid">Grid</a></li>
                                    <li><a id="nav_subgraph" href="#ref_subgraph">SubGraph</a></li>
                                    <li><a id="nav_tab" href="#ref_tab">Tab</a></li>
                                    <li><a id="nav_column" href="#ref_column">Column</a></li>
                                    <li><a id="nav_property" href="#ref_property">Property</a></li>
                                </ul>
                            </li>
                            <li><a id="nav_datamodel" href="#ref_datamodel">Data Container</a></li>
                            <li><a id="nav_selectionmodel" href="#ref_selectionmodel">Selection Model</a></li>
                        </ul>
                    </li>
                    <li>
                        <a id="nav_view" href="#ref_view">Components</a>
                        <ul>
                            <li><a id="nav_viewstructure" href="#ref_viewstructure">View Structure</a></li>
                            <li><a id="nav_config" href="#ref_config">Config</a></li>
                            <li><a id="nav_image" href="#ref_image">Image</a></li>
                            <li><a id="nav_animation" href="#ref_animation">Animation</a></li>
                            <li><a id="nav_propertyview" href="#ref_propertyview">PropertyView Component</a></li>
                            <li><a id="nav_listview" href="#ref_listview">ListView Component</a></li>
                            <li><a id="nav_treeview" href="#ref_treeview">TreeView Component</a></li>
                            <li><a id="nav_tableview" href="#ref_tableview">TableView Component</a></li>
                            <li><a id="nav_treetableview" href="#ref_treetableview">TreeTableView Component</a></li>  
                            <li><a id="nav_toolbar" href="#ref_toolbar">Toolbar Component</a></li>
                            <li><a id="nav_splitview" href="#ref_splitview">SplitView Component</a></li>
                            <li><a id="nav_borderpane" href="#ref_borderpane">BorderPane Component</a></li>
                            <li><a id="nav_accordionview" href="#ref_accordionview">AccordionView Component</a></li>
                            <li><a id="nav_tabview" href="#ref_tabview">TabView Component</a></li>                              
                            <li>
                                <a id="nav_graphview" href="#ref_graphview">GraphView Component</a>
                                <ul>
                                    <li><a id="nav_graphviewzoom" href="#ref_graphviewzoom">Zoom</a></li>
                                    <li><a id="nav_graphviewinteraction" href="#ref_graphviewinteraction">Interaction</a></li>                                    
                                    <li><a id="nav_filter" href="#ref_filter">Filter</a></li>
                                    <li><a id="nav_graphviewstyle" href="#ref_graphviewstyle">Style</a></li>
                                    <li><a id="nav_styleselect" href="#ref_styleselect">Select</a></li>
                                    <li><a id="nav_styleborder" href="#ref_styleborder">Border</a></li>
                                    <li><a id="nav_styleshape" href="#ref_styleshape">Shape</a></li>
                                    <li><a id="nav_styleposition" href="#ref_styleposition">Position</a></li>                                    
                                    <li><a id="nav_stylelabel" href="#ref_stylelabel">Label</a></li>
                                    <li><a id="nav_stylenote" href="#ref_stylenote">Nnote</a></li>
                                    <li><a id="nav_styleicon" href="#ref_styleicon">Icon</a></li>                                                                        
                                    <li><a id="nav_stylegroup" href="#ref_stylegroup">Group</a></li>
                                    <li><a id="nav_styleedge" href="#ref_styleedge">Edge</a></li>
                                </ul>
                            </li>                                                                                  
                        </ul>
                    </li>
                    <li>
                        <a id="nav_recommendation" href="#ref_recommendation">Recommendation</a>
                        <ul>
                            <li><a id="nav_book" href="#ref_book">Book</a></li>
                            <li><a id="nav_subscribe" href="#ref_subscribe">Subscribe</a></li>
                        </ul>
                    </li>
                </ul>
                <p style="height:100px;"></p>
            </div>
        </div>

        <div id="contactable" class="contactable" style="display: block;" onmouseover="toggleNav()">Index</div>
<div id="ref_overview"></div>

<h2>Summary</h2>

<div id="ref_what"></div>

<h3>What is HT?</h3>

<p><code>HT</code> is a one-stop solution based on the <a href="http://dev.W3.org/html5/spec/">HTML5</a> standard enterprise application graphical user interface (GUI), which includes a rich graphical user interface (GUI) development class library for generic components, topologies, and 3D rendering engines, providing a fully based on <code>HTML5</code> vector graphics editors, topology editors and 3D scene editors, and many other visual design tools, as well as a complete class library development manuals, tool manuals, and the <code>HTML5</code> technology for large-scale team development of customer depth training manuals.</p>

<p><iframe src="examples/example_overview.html" style="height:410px"></iframe></p>

<div id="ref_characteristic"></div>

<h3>Product Features</h3>

<blockquote><p>Ease to use, lightweight, high-performance and cross-platform four principles are our never-ending pursuits.</p></blockquote>

<ul><li>Ease to use: Easy to learn to use, a few hours of study can be used, a few days of use can be proficient</li><li>Lightweight: Core packages containing generic components, 2D topologies, and 3D engines are only about 300k</li><li>High performance: All components can load more than 10,000 of the data, and can maintain smooth operation</li><li>Cross-platform: All supported HTML5 browsers that can run on desktop platforms and mobile terminals</li></ul>

<div id="ref_sdk"></div>

<h3>Developing Class libraries</h3>

<pre><code>&lt;script src=&quot;ht.js&quot;&gt;&lt;/script&gt; </code></pre>

<p>The <code>HT</code> core development class library has only one <code>ht.js</code> <code>JavaScript</code>(hereinafter referred to briefly as <code>JS</code>) file, calling <code>ht.Default.getVersion()</code> can get the current version number.
We will ensure that all versions are downward compatibility, which means that when upgrading a product, you only need to update the <code>ht.js</code> file, which can be upgraded without any code modification.</p>

<p>The core <code>ht.js</code> class library contains the data model, generic components such as tree table, 2D topology components, vector and 3D rendering engines and other core functional components, while <code>HT</code> provides many extensions, such as dialog box, menu, and form, to meet other requirements of the class library or component for users to choose from.</p>

<div id="ref_tool"></div>

<h3>Development Tools</h3>

<p>Unrestricted, any text editor available.</p>

<div id="ref_environment"></div>

<h3>Operating Environment</h3>

<p>Any browser that supports the <code>HTML5</code> standard. Years ago, <code>Chrome</code>, <code>Firefox</code>, <code>Safari</code> and <code>Opera</code> versions have all supported <code>HTML5</code>, <code>iOS</code> and <code>Android</code> browsers have also supported <code>HTML5</code>, <code>IE</code> needs <code>IE9</code> and above versions , if you are using <a href="../3d/ht-3d-guide.html">HT for Web 3D</a> is supported by <code>IE11</code> and above, and it is recommended to use the latest version of the browser as much as possible.</p>

<p>If the project environment must use an old IE browser such as <code>IE6</code>, <code>IE7</code> and <code>IE8</code>, or by using <a href="../3d/ht-3d-guide.html">HT for Web 3D</a>, and the onsite environment cannot be upgraded to <code>IE11</code>, you can consider installing <a href="http://www.Chromium.org/developers/how-tos/ chrome-frame-getting-started/chrome-frame-faq#toc-for-developers">Google Chrome Frame</a> plug-in, embedded in the following <code>Tag</code> code fragment, the page will use <code>Chrome</code> to render.</p>

<pre><code>&lt;meta http-equiv=&quot;X-UA-Compatible&quot; content=&quot;chrome=1&quot;&gt; </code></pre>

<p>Pay attention to the following points while using the <code>Google Chrome Frame</code>:</p>

<ul><li><code>Google Chrome</code> does not support opening a page from <code>local file</code>, and must deploy the page to the <code>Web</code> server to open</li><li><code>Google Chrome Frame</code> does not support 64-bit browsers: <a href="http://www.chromium.org/developers/how-tos/chrome-frame-getting-started/chrome-frame-faq#TOC-Does-Google-Chrome-Frame-work-on-64-bit-IE-">Currently, 64-bit versions of IE are not supported. It&#39;s worth pointing out that 32-bit IE is the default on 64-bit Windows 7.</a></li><li><code>Google Chrome Frame</code> does not support <code>iframe</code>: <a href="http://stackoverflow.com/questions/2465322/opening-an-iframe-for-ie-while-using-chrome-frame">At this point ChromeFrame only supports the meta tag detection on top level URLs.</a></li></ul>

<p>The <a href="http://src.chromium.org/viewvc/chrome/trunk/src/chrome_frame/host.html">solution</a> with embedded <code>OBJECT</code> element, bypassing <code>Google Chrome Frame</code> does not support <code>iframe</code>  </p>

<pre><code>&lt;OBJECT ID=&quot;ChromeFrame&quot; WIDTH=500 HEIGHT=500 CODEBASE=&quot;http://www.google.com&quot;
        CLASSID=&quot;CLSID:E0A900DF-9611-4446-86BD-4B1D47E7DB2A&quot;&gt;
    &lt;PARAM NAME=&quot;src&quot; VALUE=&quot;http://www.google.com&quot;&gt;
    &lt;embed ID=&quot;ChromeFrameplug-in&quot; WIDTH=500 HEIGHT=500 NAME=&quot;ChromeFrame&quot;
        SRC=&quot;http://www.google.com&quot; TYPE=&quot;application/chromeframe&quot;&gt;
    &lt;/embed&gt;
&lt;/OBJECT&gt;</code></pre>

<p><code>Google Chrome Frame</code> has stopped supporting and updating in January <code>2014</code>, and <code>Google Chrome Frame</code> is now developing to <code>31</code>, which has met the canvas capabilities of <code>HT</code> <code>2D</code> and <code>3D</code>, so <code>HT</code> customers can use <code>Google Chrome Frame</code> solves the problem of compatible <code>IE</code> old versions. Other questions refer to <code>Google Chrome Frame&#39;s</code> <a href="http://www.chromium.org/developers/how-tos/chrome-frame-getting-started">Developer Guide</a> and <a href="http://www.chromium.org/developers/diagnostics/gcf_troubleshooting">Troubleshooting</a></p>

<p>Part of the old version of the <code>Android</code> terminal system, there are <code>bugs</code> that not erased clean and have a residual shadow for the <code>canvas</code> support, the following is a section <code>workaround</code> code: </p>

<pre><code>ht.Default.viewListener = function (view, kind){
    var canvas = view._canvas;
    if(canvas &amp;&amp; kind === &#39;beginValidate&#39;){
        canvas.width = 0;
        canvas.height = 0;
        canvas.style.width = 0;
        canvas.style.height = 0;                    
    }
};</code></pre>

<div id="ref_abbreviation"></div>

<h3>Shorthand</h3>

<p><code>HT</code> based on the <code>JavaScript</code> language, the development tools are weaker in spelling and error cues because of the flexibility of the <code>JavaScript</code> dynamic language, so <code>HT</code> provides shorthand naming methods for some commonly used functions, memorizing the following common function abbreviations can improve coding efficiency.</p>

<p>Several letters of the <code>HT</code> abbreviation have the following meanings:</p>

<ul><li><code>m</code>: <code>monitor</code>、<code>model</code></li><li><code>um</code>: <code>unmonitor</code></li><li><code>f</code>: <code>fire</code></li></ul>

<p>The list of <code>HT</code> shorthand functions is as follows:</p>

<ul><li><code>GraphView#addinteractorListener</code> = <code>GraphView#mi</code></li><li><code>GraphView#removeinteractorListener</code> = <code>GraphView#umi</code></li><li><code>GraphView#fireinteractorEvent</code> = <code>GraphView#fi</code></li><li><code>Graph3dView#addinteractorListener</code> = <code>Graph3dView#mi</code></li><li><code>Graph3dView#removeinteractorListener</code> = <code>Graph3dView#umi</code></li><li><code>Graph3dView#fireinteractorEvent</code> = <code>Graph3dView#fi</code></li><li><code>SelectionModel#addSelectionChangeListener</code> = <code>SelectionModel#ms</code></li><li><code>SelectionModel#removeSelectionChangeListener</code> = <code>SelectionModel#ums</code></li><li><code>SelectionModel#getFirstData</code> = <code>SelectionModel#fd</code></li><li><code>SelectionModel#getLastData</code> = <code>SelectionModel#ld</code></li><li><code>SelectionModel#contains</code> = <code>SelectionModel#co</code></li><li><code>SelectionModel#setSelection</code> = <code>SelectionModel#ss</code></li><li><code>SelectionModel#appendSelection</code> = <code>SelectionModel#as</code></li><li><code>SelectionModel#selectAll</code> = <code>SelectionModel#sa</code></li><li><code>SelectionModel#removeSelection</code> = <code>SelectionModel#rs</code>  </li><li><code>SelectionModel#clearSelection</code> = <code>SelectionModel#cs</code>  </li><li><code>DataModel#getSelectionModel</code> = <code>DataModel#sm</code></li><li><code>DataModel#addDataModelChangeListener</code> = <code>DataModel#mm</code></li><li><code>DataModel#removeDataModelChangeListener</code> = <code>DataModel#umm</code>  </li><li><code>DataModel#addDataPropertyChangeListener</code> = <code>DataModel#md</code></li><li><code>DataModel#removeDataPropertyChangeListener</code> = <code>DataModel#umd</code></li><li><code>DataModel#addHierarchyChangeListener</code> = <code>DataModel#mh</code> </li><li><code>DataModel#removeHierarchyChangeListener</code> = <code>DataModel#umh</code> </li><li><code>firePropertyChange</code> = <code>fp</code>  </li><li><code>addPropertyChangeListener</code> = <code>mp</code></li><li><code>removePropertyChangeListener</code> = <code>ump</code></li><li><code>getPosition</code> = <code>p</code></li><li><code>setPosition</code> = <code>p</code></li><li><code>getTranslateX</code> = <code>tx</code></li><li><code>setTranslateX</code> = <code>tx</code></li><li><code>getTranslateY</code> = <code>ty</code></li><li><code>setTranslateY</code> = <code>ty</code></li><li><code>getStyle</code> = <code>s</code></li><li><code>setStyle</code> = <code>s</code></li><li><code>getAttr</code> = <code>a</code></li><li><code>setAttr</code> = <code>a</code></li><li><code>invalidate</code> = <code>iv</code></li><li><code>invalidateModel</code> = <code>ivm</code></li><li><code>getSelectionModel</code> = <code>sm</code></li><li><code>getLogicalPoint</code> = <code>lp</code></li><li><code>Toolbar#setValue</code> = <code>v</code></li><li><code>Toolbar#getValue</code> = <code>v</code></li><li><code>FormPane#setValue</code> = <code>v</code></li><li><code>FormPane#getValue</code> = <code>v</code></li></ul>

<p>The following is some common shorthand examples: </p>

<p><code>graphView.getDataModel().getSelectionModel().setSelection(data)</code> = <code>graphView.dm().sm().ss(data)</code>
<code>graphView.getDataModel().getSelectionModel().addSelectionChangeListener(func)</code> = <code>graphView.dm().sm().ms(func)</code>
<code>dataModel.getSelectionModel().getLastData().setAttr(&#39;age&#39;, 35)</code> = <code>dataModel.sm().ld.a(&#39;age&#39;, 35)</code></p>

<div id="ref_model"></div>

<h2>Model</h2>

<div id="ref_designpattern"></div>

<h3>Design Pattern</h3>

<p>Refer to <a href="../datamodel/ht-datamodel-guide.html#ref_designpattern">DataModel Manual</a></p>

<div id="ref_classes"></div>

<h3>Class Package Hierarchy</h3>

<p><code>HT</code> follows the conventional object-oriented language design style, with <a href="http://en.wikipedia.org/wiki/camelcase">Camel-Case</a> for classes and packages, class names beginning with uppercase, and packet names beginning with lowercase. The entire framework occupies only the global variable <code>HT</code>, which means that the development of the regular front-end page will take up the <code>window.ht</code>, if <code>ht.js</code> runs in the webwork environment will occupy the <code>self.ht</code> variable, if <code>ht.js</code> runs in <code>node.js</code> environment will occupy the <code>module.exports.ht</code> variable.</p>

<p><code>HT</code> overall frame hierarchy is very &quot;flat&quot;, <code>ht.*</code> includes models and system-related classes, <code>ht.widget.*</code> contains generic component-related classes, <code>ht.graph.*</code> contains 2D graphics component-related classes, <code>ht.graph3d.*</code> contains the class of the 3D graphics component-related classes, considering <code>js</code> language and <code>HTML5</code> application particularity, as far as possible to reduce the hierarchy of class package, short package function naming is the characteristics of the API design in HT framework, which can reduce the user learning index, reduce the coding workload, contribute to minify the overall development kit.</p>

<p>The <code>JavaScript</code> language does not provide rigorous language-level support for object-oriented classes, for which the <code>HT</code> designs a package system for users to choose from, refer to <a href="../serialization/ht-serialization-guide.html#ref_classbasic">Serialization Manual</a></p>

<p><iframe src="examples/example_classes.html" style="height:400px"></iframe></p>

<div id="ref_util"></div>

<h3>Tools Class</h3>

<div id="ref_list"></div>

<h4>List</h4>

<p><code>HT</code> provides <code>ht.List</code> array class that encapsulates an <code>Array</code> array and provides a more easily remember and use function interface:</p>

<ul><li><code>new ht.List(array/list/object)</code> Constructor can input <code>array</code> and <code>ht.List</code> array, or you can add a single <code>object</code> element</li><li><code>size()</code> Returns the total number of the array elements</li><li><code>isEmpty()</code> Determines whether an array is empty or not</li><li><code>add(item, index)</code> Adds elements, <code>index</code> specifies the insert position and the NULL representative to insert into the last</li><li><code>addAll(array)</code> Adds all elements in <code>array</code>, support <code>Array</code> and <code>ht.List</code> type</li><li><code>get(index)</code> Returns <code>index</code> position of element</li><li><code>slice(start, end)</code> Returns the new <code>ht.List</code> object array from <code>start</code> to <code>end</code>, if <code>end</code> is NULL, represents to the last element</li><li><code>remove(item)</code> Deletes the specified element and returns the index in which the element is located</li><li><code>removeAt(index)</code> Deletes the element of the index, and returns the deleted element object</li><li><code>set(index, item)</code> Inserts an element at the specified index location</li><li><code>clear()</code> Clears all elements of the array</li><li><code>contains(item)</code> Determines whether an element is in an array</li><li><code>indexOf(item)</code> Returns the index in the array, if it does not exist then return <code>-1</code></li><li><code>each(func, scope)</code> Sequence iterates through all the elements, specifying functions to invoke <code>scope</code> fields</li><li><code>reverseEach(func, scope)</code> To iterate through all elements, you can specify the function to invoke <code>scope</code> field</li><li><code>toArray(matchFunc, scope)</code> Returns all new <code>array</code> arrays of matching elements according to <code>matchfunc</code>, which can be specified by the function to invoke <code>scope</code> field</li><li><code>toList(matchFunc, scope)</code> Returns the new <code>ht.List</code> array of all matching elements according to <code>matchFunc</code>, you can specify a function to invoke <code>scope</code> field</li><li><code>sort(sortFunc)</code> Rearrange array element positions according to <code>sortFunc</code> comparison logic</li><li><code>reverse()</code> The order of the array elements is reversed</li></ul>

<div id="ref_notifier"></div>

<h4>Notifier</h4>

<p><code>HT</code> provides event notification manager <code>ht.Notifier</code> class, you can add listener functions, remove listener functions, and distribute events to all listener functions: </p>

<ul><li><code>add(listener, scope)</code> Adds listener function for <code>scope</code> parameter optional</li><li><code>remove(listener, scope)</code> Deletes listener function, <code>scope</code> field parameter have to be consistent with the parameters of <code>add</code> </li><li><code>contains(listener, scope)</code> Determines whether the specified listener function is included, the <code>scope</code> field parameter have to be consistent with the parameters of <code>add</code></li><li><code>fire(event)</code> Distributes events to all listening functions</li></ul>

<div id="ref_default"></div>

<h4>Default</h4>

<p><code>ht.Default</code> defines the default values for all components and a series of tool class functions, refer to <a href="../theme/ht-theme-guide.html#ref_default">Style Manual</a> </p>

<div id="ref_style"></div>

<h4>Style</h4>

<p><code>ht.Style</code> object defines the default <code>style</code> attribute value with <code>ht.Data</code> type, refer to <a href="../theme/ht-theme-guide.html#ref_style">Style Manual</a> </p>

<div id="ref_color"></div>

<h4>Color</h4>

<p><code>ht.Color</code> object defines all the default colors for the <code>HT</code> system, refer to <a href="../theme/ht-theme-guide.html#ref_color">Style Manual</a></p>

<div id="ref_jsonserializer"></div>

<h4>JSONSerializer</h4>

<p><code>ht.JSONSerializer</code> serialization class that provides serialization and deserialization of the <code>JSON</code> format for <code>DataModel</code> data model, refer to <a href="../serialization/ht-serialization-guide.html">Serialization Manual</a></p>

<div id="ref_datatype"></div>

<h3>Data Type</h3>

<p><code>ht.Data</code> (referred to briefly as <code>Data</code>, the following is a description of how to omit package header <code>ht.</code>) is described as the most basic data type, the user can store business information in the <code>Data</code> object properties, the current <code>HT</code> provides <code>Node</code>, <code>Edge</code>, <code>Tab</code>, <code>Column</code>, these types have different display effects in different view components: <code>Data</code> on the <code>TreeView</code> tree component represents a tree node; the <code>Data</code> on the <code>TableView</code> table component represents a row of records, and the column represents the properties of the <code>Data</code> object; <code>Graphview</code> <code>Node</code> on the graphics component represents a graphical element; Defining a page sign <code>TabView</code> uses the <code>Tab</code> type to store the page signing information; When defining a table column, <code>TableView</code> uses <code>column</code> to store column information.</p>

<div id="ref_data"></div>

<h4>Data</h4>

<p>Refer to <a href="../datamodel/ht-datamodel-guide.html#ref_data">DataModel Manual</a></p>

<div id="ref_node"></div>

<h4>Node</h4>

<p><code>ht.Node</code> type is the base class of the <code>GraphView</code> and <code>Graph3dView</code> rendering data nodes, inheriting from the <code>Data</code> class.
The following is <code>Node</code> of the <code>GraphView</code> topological map related function properties, and <code>Graph3dView</code> related function properties refer to <a href="../3d/ht-3d-guide.html">3D Manual</a>.
In addition to displaying pictures, <code>Node</code> can display a variety of predefined graphics, refer to <a href="#ref_styleshape">Shape Section</a>.</p>

<ul><li><code>getPosition()</code> and <code>setPosition({x:100, y:200})</code> Gets and sets the central point coordinates of datas</li><li><code>getImage()</code> and <code>setImage(image)</code> Gets and sets the picture information, in the <code>GraphView</code> topology picture in general with <code>position</code> as the center drawing</li><li><code>getWidth()</code> and <code>setWidth(width)</code> Gets and sets the width of the data, if not set to <code>image</code> corresponding picture width</li><li><code>getHeight()</code> and <code>setHeight(height)</code> Gets and sets the height of the data, if not set to <code>image</code> corresponding picture height</li><li><code>getSize()</code> and <code>setSize(10, 20 | {width:10, height:20})</code> Gets and sets the data&#39;s width and height size information</li><li><code>getRect()</code> and <code>setRect(x,y,width,height | {x:10,y:20,width:30,height:40})</code> Gets and sets the data rectangular area</li><li><code>getRotation()</code> and <code>setRotation(Math.PI/2)</code> Gets and sets the rotation angle of the data, rotate clockwise around the center point</li><li><code>getAttaches()</code> Gets the <code>ht.List</code> type array that absorbs all of its nodes.</li><li><code>getEdges()</code> Gets all edges to the node model associated with <code>ht.List</code> type array</li><li><code>getSourceEdges()</code> Gets all the edge <code>ht.List</code> type array that starts at the node on all models</li><li><code>getTargetEdges()</code> Gets all the edge <code>ht.List</code> type array that ends at the node on all models </li><li><code>getAgentEdges()</code> Gets the edge <code>ht.List</code> type array on the current node graph that is associated with the agent</li><li><code>getSourceAgentEdges()</code> Gets all graphics on which the agent starts from the node of the edge <code>ht.List</code> type array</li><li><code>getTargetAgentEdges()</code> Gets all graphics on which the agent ends from the node of the edge <code>ht.List</code> type array</li><li><code>hasAgentEdges()</code> Determines whether the current node has an agent edge on the graph, returns <code>true</code>, if no return <code>false</code></li><li><code>getHost()</code> and <code>setHost(host)</code> Gets and sets the adsorption host object, when the node is adsorbed on the host data, when the host moves or rotates, it drives all the adsorbents.</li><li><code>onHostChanged(oldHost, newHost)</code> Callbacks the function when the adsorbed host object changes and can be overloaded for subsequent processing</li><li><code>handleHostPropertyChange(event)</code> Callbacks the function when the adsorbed host object changes and can be overloaded for subsequent processing</li><li><code>isHostOn(node)</code> Determines whether the data is adsorbed onto the specified data object or not</li><li><code>isLoopedHostOn(node)</code> Determines whether a ring adsorption is formed with the specified data, such as <code>A</code> adsorption <code>B</code>, <code>B</code> adsorption <code>C</code>, <code>C</code> adsorption back <code>A</code>, then <code>A</code>, <code>B</code> and <code>C</code> datas are mutually annular adsorption</li></ul>

<p><iframe src="examples/example_node.html" style="height:150px"></iframe>  </p>

<p>In the above example, the <code>air11</code> data was created to adsorb the <code>air13</code> data, <code>air11</code> changed the size, <code>air13</code> set the rotation:</p>

<pre><code>ht.Default.setImage(&#39;mac&#39;, &#39;res/mac-air.png&#39;);            

air11 = new ht.Node();
air11.setName(&#39;11-inch MacBook Air&#39;);
air11.setImage(&#39;mac&#39;);
air11.setSize(80, 43);
air11.setPosition(100, 70);                
dataModel.add(air11);

air13 = new ht.Node();
air13.setName(&#39;13-inch MacBook Air&#39;);                
air13.setImage(&#39;res/mac-air.png&#39;);
air13.setPosition(260, 70);
air13.setRotation(Math.PI/2);
dataModel.add(air13);

air11.setHost(air13);   </code></pre>

<p>The code sets <code>GraphView</code> to editable, allowing only <code>air11</code> to edit the size, <code>air13</code> can only rotate, which is implemented by setting <a href="#ref_filter">Filter</a></p>

<pre><code>graphView.setEditable(true);
graphView.setRectEditableFunc(function (data){
    return data === air11;
});
graphView.setRotationEditableFunc(function (data){
    return data === air13;
});</code></pre>

<div id="ref_edge"></div>

<h4>Edge</h4>

<p><code>ht.Edge</code> type is used to connect the source and target two <code>Node</code> nodes, and the two nodes can have multiple <code>Edge</code>, and also allow the source and target to be the same node.
The <code>agent</code> of the line refers to the node in the current graph where the true agent connects the edge, and when the node is in the closed <code>Group</code>, the <code>Group</code> connects the internal nodes of the agent.
More style attributes of <code>Edge</code> refer to <a href="#ref_styleedge">Edge Style</a>.</p>

<ul><li><code>getSource()</code> and <code>setSource(node)</code> Gets and sets the source node</li><li><code>getTarget()</code> and <code>setTarget(node)</code> Gets and sets the target node</li><li><code>isLooped()</code> Determines whether the source and target of the edge are the same node </li><li><code>getSourceAgent()</code> Gets the source node of a edge on a graph</li><li><code>getTargetAgent()</code> Gets the target node of a edge on a graph</li><li><code>getEdgeGroup()</code> Gets <code>ht.EdgeGroup</code> type object with multiple edges between the source and target nodes</li><li><code>toggle()</code> Realizes the switch the multiple lines between the current source and target node and affect the <code>style</code> attribute of <code>edge.expanded</code></li><li><code>isEdgeGroupHidden()</code> Determines whether the current edge is hidden in the edge group or not</li><li><code>getEdgeGroupSize()</code> Returns the number of edges for the edge group where the current edge is located</li><li><code>getEdgeGroupIndex()</code> Returns the index of the edge group where the current edge is located</li><li><code>isEdgeGroupAgent()</code> Determines whether the current edge is an agent of the edge group or not</li></ul>

<p><iframe src="examples/example_edge.html" style="height:150px"></iframe></p>

<p>Can insert into the <code>source</code> and <code>target</code> node objects in the constructor by <code>new ht.Edge(source, target)</code>, it can also build the <code>Edge</code> object and then set separately, the example overloads the <code>graphView.getLabel</code> function, customized the text label of the data, and when the multiple edges in the same group are merged, the agent text in the edge can show the edge group information:</p>

<pre><code>var edge = new ht.Edge();
edge.setSource(source);
edge.setTarget(target);
dataModel.add(edge);               

edge = new ht.Edge(source, target);
edge.toggle();
dataModel.add(edge);                               

edge = new ht.Edge(source, source);
dataModel.add(edge);                 

graphView.getLabel = function (data){
    if(data instanceof ht.Edge){
        if(data.isEdgeGroupAgent()){
            return data.getEdgeGroupSize() + &#39; become 1&#39;;
        }
    }
    return data.getName();
};</code></pre>

<div id="ref_group"></div>

<h4>Group</h4>

<p><code>ht.Group</code> type is used as a parent container to contain child datas, which can be expanded and merged by double-clicking on the <code>GraphView</code> topology, and when merged, the hidden descendant data node is automatically set, and the merged <code>Group</code> connects the agent if there is a child node connected to the outside. The movement of the <code>Group</code> will drive the child&#39;s nodes to follow, and the child&#39;s position and size changes will also affect the <code>Group&#39;s</code> expanded graphics and <code>position</code>. Refer to <a href="#ref_stylegroup">Group Style Properties</a>.</p>

<ul><li><code>isExpanded()</code> and <code>setExpanded(true/false)</code> The expanded merge state of an object </li><li><code>toggle()</code> Can toggle the expanded merge state</li><li>Child data <code>style</code> <code>ingroup</code> property determines whether it can be contained by <code>Group</code> and defaults to <code>true</code></li></ul>

<p><iframe src="examples/example_group.html" style="height:230px"></iframe></p>

<p>In the example, the <code>Group</code> object is created, and the <code>Group</code> position is automatically affected by the location of the child nodes that are subsequently added, instead of being assigned a position on the <code>group.setExpanded(true)</code>. Adding children through <code>addChild</code> or <code>setParent</code> is optional:</p>

<pre><code>var group = new ht.Group();
group.setName(&#39;Double click on me&#39;); 
group.setExpanded(true);
dataModel.add(group);

var node1 = new ht.Node();
node1.setName(&#39;Node1&#39;);
node1.setPosition(80, 80);
group.addChild(node1);
dataModel.add(node1);

var node2 = new ht.Node();
node2.setName(&#39;Node2&#39;);              
node2.setPosition(180, 80);
node2.setParent(group);
dataModel.add(node2);</code></pre>

<p>Constructs a <code>style</code> of the <code>ingroup</code> attribute to <code>false</code>, the data will be free from the <code>group</code>, the <code>group</code> move and it will follow, but the node position or size change will not affect the <code>Group</code> object:</p>

<pre><code>var node4 = new ht.Node();
node4.setName(&#39;The Special One&#39;);   
node4.setStyle(&#39;ingroup&#39;, false);
node4.setPosition(290, 100);
group.addChild(node4);
dataModel.add(node4);</code></pre>

<p>The following code constructs a <code>label</code> label that does not affect the <code>Group&#39;s</code> child data, which overloads the <code>Graphview.getBoundsForGroup</code> function for this function, and only returns its <code>node3. getRect()</code> size for the <code>node3</code> data, other datas continue to maintain the logic of the original function, which first caches the default function <code>var oldfunc = Graphview.getBoundsForGroup</code>, and then called by <code>oldFunc.call(this, child)</code> in the overloaded function, which use the <code>HT</code> common overload technique, such the method can be overloaded without defining a new class and can invoke the original function logic as needed:</p>

<pre><code>var node3 = new ht.Node();
node3.setPosition(130, 140);
node3.s({
    &#39;label.font&#39;: &#39;bold 21px arial&#39;,
    &#39;label.color&#39;: &#39;white&#39;,
    &#39;label.offset.y&#39;: 8,
    &#39;label.background&#39;: &#39;#E74C3C&#39;
});                
node3.setName(&#39;HT for Web&#39;);
node3.setParent(group);
dataModel.add(node3);

var oldFunc = graphView.getBoundsForGroup;
graphView.getBoundsForGroup = function (child){
    if(child === node3){
        return node3.getRect();
    }
    return oldFunc.call(this, child);
};</code></pre>

<div id="ref_shape"></div>

<h4>Shape</h4>

<p>Refer to <a href="../shape/ht-shape-guide.html">Shape Manual</a></p>

<div id="ref_polyline"></div>

<h4>Polyline</h4>

<p>Refer to <a href="../shape/ht-shape-guide.html">Shape Manual</a></p>

<div id="ref_grid"></div>

<h4>Grid</h4>

<p><code>ht.Grid</code> type is generally used as a container for grid layout of the subordinate nodes (<code>attachNode.setHost(grid)</code>), and the subordinate nodes can be <code>Grid</code> types, to achieve nested layouts.</p>

<ul><li><code>setStyle(&#39;grid.row.count&#39;, 1)</code> Sets number of rows</li><li><code>setStyle(&#39;grid.column.count&#39;, 1)</code> Sets number of column</li><li><code>setStyle(&#39;grid.row.percents&#39;, [0.1, 0.2, 0.3, 0.4])</code> Sets the height per line percentage, and the default is <code>null</code> represents an equalization</li><li><code>setStyle(&#39;grid.column.percents&#39;, [0.1, 0.2, 0.3, 0.4])</code> Sets the height per column percentage, and the default is <code>null</code> represents an equalization</li><li><code>setStyle(&#39;grid.border&#39;, 1)</code> Sets the thickness of the four edges of the container, if the uneven thickness of the quadrilateral can be controlled by the following four parameters</li><li><code>setStyle(&#39;grid.border.left&#39;, 0)</code> Sets the thickness of the left edge of the container</li><li><code>setStyle(&#39;grid.border.right&#39;, 0)</code> Sets the thickness of the right edge of the container</li><li><code>setStyle(&#39;grid.border.top&#39;, 0)</code> Sets the thickness of the top edge of the container</li><li><code>setStyle(&#39;grid.border.bottom&#39;, 0)</code> Sets the thickness of the bottom edge of the container</li><li><code>setStyle(&#39;grid.gap&#39;, 1)</code> Sets the gap between cells in a container</li><li><code>setStyle(&#39;grid.background&#39;, &#39;#E5BB77&#39;)</code> Sets the background between cells in a container</li><li><code>setStyle(&#39;grid.depth&#39;, 1)</code> Sets the depth of the four edges of the container, <code>0</code> represents a flat effect, a positive value representing a raised effect, and a negative value represents a sunken effect.</li><li><code>setStyle(&#39;grid.cell.depth&#39;, -1)</code> Sets the depth of the four edges of a cell, <code>0</code> represents a flat effect, a positive value representing a raised effect, and a negative value represents a sunken effect.</li><li><code>setStyle(&#39;grid.cell.border.color&#39;, &#39;#868686&#39;)</code> Sets the cell border color that is effective when the <code>grid.cell.depth</code> value is <code>0</code></li><li><code>setStyle(&#39;grid.block&#39;, &#39;undefined&#39;)</code> Sets whether to display a block border or not, the default is <code>undefined</code> represents does not draw, <code>v</code> represents to draw column block, <code>h</code> represents to draw row block</li><li><code>setStyle(&#39;grid.block.padding&#39;, 3)</code> Sets block border padding between cell contents</li><li><code>setStyle(&#39;grid.block.width&#39;, 1)</code> Sets block border drawing width </li><li><code>setStyle(&#39;grid.block.color&#39;, &#39;#868686&#39;)</code> Sets block border drawing color</li><li>As the <code>Node</code> type of the satellite node, you can set the following <code>attach</code> related parameters: <ul><li><code>setStyle(&#39;attach.row.index&#39;, 0)</code> Sets the location of the cell row where the node is located</li><li><code>setStyle(&#39;attach.column.index&#39;, 0)</code> Sets the location of the cell column where the node is located</li><li><code>setStyle(&#39;attach.row.span&#39;, 1)</code> Sets the number of node rows across</li><li><code>setStyle(&#39;attach.column.span&#39;, 1)</code> Sets the number of node column across</li><li><code>setStyle(&#39;attach.padding&#39;, 0)</code> Sets the distance between four edges and cells, positive values are out of cells, and negative values are less than cells</li><li><code>setStyle(&#39;attach.padding.left&#39;, 0)</code> Sets the distance between left edges and cells, positive values are out of cells, and negative values are less than cells</li><li><code>setStyle(&#39;attach.padding.right&#39;, 0)</code> Sets the distance between right edges and cells, positive values are out of cells, and negative values are less than cells</li><li><code>setStyle(&#39;attach.padding.top&#39;, 0)</code> Sets the distance between top edges and cells, positive values are out of cells, and negative values are less than cells</li><li><code>setStyle(&#39;attach.padding.bottom&#39;, 0)</code> Sets the distance between bottom edges and cells, positive values are out of cells, and negative values are less than cells</li></ul></li></ul>

<p><iframe src="examples/example_grid.html" style="height:290px"></iframe></p>

<div id="ref_subgraph"></div>

<h4>SubGraph</h4>

<p><code>ht.SubGraph</code> types have similarities with the <code>Group</code> type, they will affect the way the child is rendered, different with the <code>Group</code> type with the child node in the same layer interface display, <code>SubGraph</code> type to present its children including descendant nodes in the next layer of interface, in <code>GraphView</code> component is shown as double-clicking <code>SubGraph</code> data will enter the new interface content, under the new interface content double-click the background can return the <code>SubGraph</code> interface of the data, <code>SubGraph</code> can be unlimited layer nesting.</p>

<p>The <code>GraphView</code> is related to the <code>SubGraph</code> function as follow: </p>

<ul><li><code>getCurrentSubGraph()</code> and <code>setCurrentSubGraph(subGraph)</code> Gets and sets the current subgraph, the default is empty representation at the top layer</li><li><code>upSubGraph()</code> Enter the upper subgraph of the current subgraph</li></ul>

<p><iframe src="examples/example_subgraph.html" style="height:150px"></iframe></p>

<div id="ref_tab"></div>

<h4>Tab</h4>

<p>Refer to <a href="../tabview/ht-tabview-guide.html">TabView component Manual</a> </p>

<div id="ref_column"></div>

<h4>Column</h4>

<p>Refer to <a href="../tableview/ht-tableview-guide.html">TableView Component Manual</a></p>

<div id="ref_property"></div>

<h4>Property</h4>

<p>Refer to <a href="../propertyview/ht-propertyview-guide.html">PropertyView Components Manual</a></p>

<div id="ref_datamodel"></div>

<h3>Data Model</h3>

<p>Refer to <a href="../datamodel/ht-datamodel-guide.html#ref_datamodel">DataModel Manual</a></p>

<div id="ref_selectionmodel"></div>

<h3>Selection Model</h3>

<p>Refer to <a href="../datamodel/ht-datamodel-guide.html#ref_selectionmodel">DataModel Manual#selectionmodel</a></p>

<div id="ref_view"></div>

<h2>Components</h2>

<div id="ref_viewstructure"></div>

<h3>Constitutes</h3>

<p>The components of the <code>HT</code> framework refer to visually interactive view controls, the <code>HT</code> framework is based on <code>HTML5</code> technology, so the visual part of the <code>HT</code> component is essentially an <code>HTML</code> element, most of the <code>HT</code> components are bound to the <code>DataModel</code> data model, and the user can drive visual components by manipulating the pure <code>JS</code> language, to shield the complexity of the <code>HTML</code> underlying graphics technology.
The aim that <code>HT</code> encapsulate <code>HTML5</code> technology is to improve development efficiency and maintainability, but does not mean that users are not allowed to manipulate <code>HTML</code> native elements, programmers with <code>HTML5</code> development experience can use a variety of <code>HTML5</code> technologies to customize the <code>HT</code> component, in the context of understanding the <code>HT</code> system mechanism.</p>

<p>All <code>HT</code> components are at the root of a <a href="http://www.W3.Org/wiki/html/elements/div">div</a> component that can be obtained through the component&#39;s <code>getView()</code> function.
The default and custom interaction event listeners are generally added to the <code>div</code> (<code>getView().addEventListener(type, func, false)</code>), the render layer is generally provided by <a href="http://www.W3.Org/wiki/html/elements/canvas">canvas</a>.
The user can set the <a href="http://www.W3.Org/wiki/css">CSS</a> background and other styles to the root <code>div</code> and <code>canvas</code> layers, you can also add a new <code>HTML</code> component to the root <code>div</code> layer and present it as a sibling component of <code>canvas</code>.
<code>HT</code> components are generally set to <code>position</code> as the <code>absolute</code> absolute positioning mode, <code>box-sizing</code> properties are set to <code>border-box</code>.</p>

<p>Most of the <code>HT</code> components provide the <code>isDisabled()</code> and <code>setDisabled(true/false, iconUrl)</code> functions to make the entire component unusable, typically used to remotely load the data process to temporarily leave the component in an unusable state, <code>iconUrl</code> in which case the <code>gif</code> picture path is generally set to represent the being loaded state.
Components in the <code>disabled</code> state generate a <code>div</code> that blocks the entire component through <code>ht.Default.disabledBackground</code> can modify the shaded component background color.</p>

<p>The rendering of <code>HT</code> components is largely implemented by the inner <code>canvas</code> component, which provides the appropriate extension function for customization, for example, <code>listView</code> provides <code>drawRow</code> for function custom line drawing, <code>GraphView</code> by vector mechanism to customize data <code>image</code> on topology rendering, etc., components that are rendered based on <code>canvas</code> such as <a href="#ref_listview">ListView</a>, <a href="#ref_propertyview">PropertyView</a>, <a href="#ref_treeview">TreeView</a>,
<a href="#ref_tableview">TableView</a>, <a href="#ref_treetableview">Treetableview</a>, <a href="#ref_graphview">GraphView</a>, etc., components, provides a brush interface for drawing <code>canvas</code> at the bottom and topmost levels:</p>

<ul><li><code>addTopPainter(function (g){})</code> and <code>removeTopPainter(func)</code> Adds and removes brushes that are drawn at the top level</li><li><code>addBottomPainter(function (g){})</code> and <code>removeBottomPainter(func)</code> Adds and removes brushed that are drawn at the bottom level</li></ul>

<p>All components of <code>HT</code> do not take the <code>HTML</code> feature of the scroll bar, completely from the <code>HT</code> component, the scroll bar can be automatically hidden, dynamically appearing when the component is translated or the mouse slides over the boundary, and the default transparent scrollbar <code>cover</code> is above the content and does not affect the layout of the component interface:</p>

<ul><li><code>getScrollBarColor()</code> and <code>setScrollBarColor(color)</code> Gets and sets the color of the scrollbar</li><li><code>getScrollBarSize()</code> and <code>setScrollBarSize(6)</code> Gets and sets the width of the scrollbar</li><li><code>isAutoHideScrollBar()</code> and <code>setAutoHideScrollBar(true/false)</code> Gets and sets whether scrollbar are automatically hidden or not, the default value is <code>true</code></li></ul>

<p>The <code>HT</code> component&#39;s coordinate origin defaults to the upper left corner, most components have the translation function, the translation visualization effect is the scrolling, divides into the horizontal ordinate translation <code>translateX</code> attribute, and the vertical coordinate translation <code>translateY</code> attribute, both default values are <code>0</code>.</p>

<p>Some components can only be translated horizontally, such as <code>tabView</code> and <code>tableHeader</code>; some components can only be translated vertically, such as <code>ListView</code> and <code>TreeView</code>, while <code>GraphView</code> and <code>TableView</code> are both horizontal and vertical; can be overloaded <code>adjustTranslateX</code> and <code>adjustTranslateY</code> functions to change the translation logic:</p>

<ul><li><code>setTranslate(x, y, anim)</code> Sets a new horizontal translation and vertical translation value, <code>anim</code> represents whether to animate it or not, refer to <a href="#ref_animation">Animation Manual</a></li><li><code>getTranslateX()</code> and <code>setTranslateX(10)</code> Gets and sets the current horizontal translation value, abbreviated to <code>tx()</code> and <code>tx(10)</code></li><li><code>getTranslateY()</code> and <code>setTranslateY(10)</code> Gets and sets the current vertical translation value, abbreviated to <code>ty()</code> and <code>ty(10)</code></li><li><code>translate(tx, ty)</code> Increases horizontal and vertical translation values based on current value</li><li><code>adjustTranslateX(value)</code> This function passed the horizontal translation value that is about to be set, returns the final setting value, and can overload the restricted horizontal translation range</li><li><code>adjustTranslateY(value)</code> This function passed the vertical translation value that is about to be set, returns the final setting value, and can overload the restricted horizontal translation range</li></ul>

<p>The <code>HT</code> components are typically embedded in containers such as <code>BorderPane</code>, <code>SplitView</code> and <code>TabView</code>, and the outermost <code>HT</code> component requires the user to manually add <code>getView()</code> back to the bottom <code>div</code> element to the page&#39;s <code>DOM</code> element , it should be noted that when the parent container size changes, if the parent container is <code>BorderPane</code> and <code>SplitView</code> and so on these <code>HT</code> predefined container components, then the <code>HT</code> container automatically recursively invokes the child component <code>invalidate</code> function to notify the update. However, if the parent container is native to the <code>HTML</code> element, then the <code>HT</code> component is not known whether need to be updated, so the outermost <code>HT</code> component generally needs to listen for window size change events and invoke the outermost component <code>invalidate</code> function to update.</p>

<p>In order for the outermost component to fill the window with convenience, <code>HT</code> all components have a <code>addToDOM</code> function, its implementation logic is as follows, where <code>iv</code> is the <code>invalidate&#39;s</code> shorthand:</p>

<pre><code>addToDOM = function (){   
    var self = this,
        view = self.getView(),   
        style = view.style;
    document.body.appendChild(view);            
    style.left = &#39;0&#39;;
    style.right = &#39;0&#39;;
    style.top = &#39;0&#39;;
    style.bottom = &#39;0&#39;;      
    window.addEventListener(&#39;resize&#39;, function () { self.iv(); }, false);            
}</code></pre>

<div id="ref_config"></div>

<h3>Config</h3>

<p>Changing the <code>HT</code> system default properties requires that the <code>HT</code> system only read <code>htConfig</code> configuration information at initialization time through a global <code>htConfig</code> variable name, so the attribute must be initialized before introducing the <code>ht.js</code> package, modifying <code>htConfig</code> when running the variable does not work again, the sample code is as follows:</p>

<pre><code>&lt;script&gt;
    htconfig = {
        Color: {       
            label: &#39;#000&#39;,
            labelSelect: &#39;#FFF&#39;          
        },
        Default: {
            toolTipDelay: 100,
            toolTipContinual: true
        },                
        Style: {
            &#39;select.color&#39;: &#39;#E74C3C&#39;,
            &#39;select.width&#39;: 3
        }
    };
&lt;/script&gt;           
&lt;script src=&quot;ht.js&quot;&gt;&lt;/script&gt;   </code></pre>

<p>Configurable parameters are grouped into three broad categories, and specific attribute descriptions refer to <a href="../theme/ht-theme-guide.html">Style Manual</a></p>

<ul><li>Configure the global default color theme and eventually set it to <code>ht.Color</code> object properties</li><li>Configure global, component default parameters, and eventually set it to <code>ht.Default</code> object properties</li><li>Configure the default parameters for the data and eventually set it to <code>ht.Style</code> object properties</li></ul>

<div id="ref_image"></div>

<h3>Image</h3>

<p>Images are an important resource for graphics components, icons on trees, topological datas, and so on can be drawn with images, <code>HT</code> supports regular picture formats such as <code>PNG</code> and <code>JPG</code>, as shown in the <a href="#ref_node">Node</a> chapter example, there are two ways to use a image:</p>

<ul><li>Sets the image relative or absolute path to the corresponding properties of the data model, <code>air13.setImage(res/mac-air.png)</code></li><li>Registered by <code>ht.Default.setImage(&#39;mac&#39;, &#39;res/mac-air.png&#39;)</code>, and then sets the registration name to the model <code>air11.setImage(&#39;mac&#39;)</code></li></ul>

<p>Directly setting path in development is convenient, without registering images in advance, but when data model <a href="../serialization/ht-serialization-guide.html">serialization</a> the image path will occupy more memory, image path is not conducive to management maintenance in the future, both ways are the correct way to use, according to the project situation selected different ways or mixed use.
If you use the <code>url</code> path, the <code>HT</code> interior automatically loads the picture, and automatically updates the corresponding view component after <code>onload</code>.</p>

<p>The <code>HT</code> frame of the image was given a broader meaning, <code>HT</code> provides a custom <code>JSON</code> format vector description in the <code>HT</code> standard defined <code>JSON</code> vector format, can also be used as a image for registration and use, <code>HT</code> vector are more space-saving than traditional picture format, zoom without distortion, the most powerful thing is that all the graphics parameters of the vector can be dynamically bound to data on the <code>Data</code> model, as specified in the <a href="../vector/ht-vector-guide.html">Vector Manual</a>.</p>

<p><code>ht.Default.setImage</code> function has several methods of invocation: </p>

<ul><li><code>setImage(&#39;hightopo&#39;, &#39;www.hightopo.com/logo.png&#39;)</code> Registers by url</li><li><code>setImage(&#39;hightopo&#39;, &#39;data:image/png;base64,iVBORw...Jggg==&#39;)</code> Registers by <code>base64</code></li><li><code>setImage(&#39;www.hightopo.com/logo.png&#39;)</code> Only use <code>url</code> parameter, picture&#39;s <code>name</code> and <code>url</code> path are the same in this way</li><li><code>setImage(&#39;hightopo&#39;, 200, 80, &#39;www.hightopo.com/logo.png&#39;)</code> Registers by url and specified the image&#39;s width and height</li><li><code>setImage(&#39;hightopo&#39;, 200, 80, &#39;data:image/png;base64,iVBORw...Jggg==&#39;)</code> Registers by <code>base64</code> and specified the image&#39;s width and height</li><li><code>setImage(&#39;hightopo&#39;, img|canvas)</code> Registers <code>img</code> and <code>canvas</code> can draw <code>html</code> element</li></ul>

<p>Using registration <code>img</code> of the <code>HTML</code> element, the user needs to ensure that the image resource is loaded, and that the <code>HT</code> does not listen to its <code>onload</code> event, so the view component updates are not automatically notified.</p>

<p><code>ht.Default.getImage(name, color)</code> can obtain the corresponding picture element, in the picture loading process, this function returns empty, only after <code>onload</code> can get the corresponding data element, <code>color</code> is the color parameter, generally is NULL, if has the color value then <code>HT</code> interior will build a new picture of <code>color</code> dyed</p>

<p>The <code>HT</code> also provides several drawing functions for the picture, the <code>image</code> in the following parameters can be plotted <code>img</code> or <code>canvas</code> elements, or vector <code>json</code> format, because the vector can dynamically bind the data model has the function of dyeing, so the vector can be drawn into the <code>data</code> <code>view</code> components and <code>color</code> dyeing parameters:</p>

<ul><li><code>ht.Default.drawImage(g, image, x, y, width, height, data, view, color)</code> Draws the <code>image</code> in a filled way within the specified rectangle area</li><li><code>ht.Default.drawCenterImage(g, image, x, y, data, view, color)</code> Draws <code>image</code> in the center of <code>x</code> and <code>y</code> </li><li><code>ht.Default.drawStretchImage(g, image, stretch, x, y, w, h, data, view, color)</code> Draws the picture in the rectangular position, the <code>strech</code> type is as follows:<ul><li><code>fill</code> Picture fills the entire rectangular area, if the picture width and height scale and the rectangular inconsistency will cause the picture stretch distortion</li><li><code>uniform</code> The picture always retains the original width and height ratio unchanged and fills the rectangular area as far as possible</li><li><code>centerUniform</code> When the rectangular area is larger than the picture size, the picture is drawn in the center position in the original size, and the <code>uniform</code> is used when the space is not enough.</li></ul></li></ul>

<div id="ref_animation"></div>

<h3>Animation</h3>

<p>Under the design architecture of the <code>HT</code> data model driven graphics component, the animation can be understood as the process of gradually changing some attributes from the starting value to the target value, <code>HT</code> provides for <code>ht.Default.startanim</code> animation function, whose sample code is as follows:</p>

<pre><code>ht.Default.startAnim({
    frames: 12, // Animation frame
    interval: 10, // Interval between frames
    easing: function (t){ return t * t; }, // Animation easing function, use `ht.Default.animEasing`, by default
    finishFunc: function (){ console.log(&#39;Done!&#39;) }, // The call function after animation finished
    action: function (v, t){ // Necessary, properties changed while on animation
        node.setPosition( // `node` moves from `p1` to `p2` in this example
            p1.x + (p2.x - p1.x) * v,
            p1.y + (p2.y - p1.y) * v
        );
    }
});</code></pre>

<p><code>ht.Default.startAnim</code> support <code>frame-based</code> and <code>time-based</code> two ways of animation, the above code is <code>frame-based</code> way, in this way the user by specifying the <code>frames</code> animation frame number, and <code>interval</code> animation frame interval parameter controls the animation effect.</p>

<p>The following code is <code>time-based</code>, in which the user only needs to specify the number of milliseconds the animation cycle of <code>duration</code> can be, <code>HT</code> will complete the animation in the specified time period, different from <code>frame-based</code> way has a definite fixed number of frames, that is how many times <code>action</code> function been called, the number of <code>time-based</code> mode frames or <code>action</code> functions are called depending on the system environment, generally the system configuration is better, the more efficient browsers call the number of frames and the animation process is smoother. Because <code>JS</code> language can not accurately control the <code>interval</code> time interval, the use of <code>frame-based</code> can not accurately control the animation time period, even if the same <code>frames</code> and <code>interval</code> parameters in different environments, there may be a big difference in the animation cycle, so <code>HT</code> defaults to <code>time-based</code> mode, if the <code>duration</code> and <code>frames</code> parameters are not be set, the <code>duration</code> parameter will be automatically set to <code>ht.Default.animDuration</code> value.</p>

<pre><code>ht.Default.startAnim({
    duration: 500, // Animation-duration, use `ht.Default.animDuration`, by default
    action: function (v, t){ 
        ... 
    }
});</code></pre>

<p>The <code>startAnim</code> function returns an <code>anim</code> object that can be called <code>anim.stop(true)</code> to terminate the animation, where the parameter <code>shouldBeFinished</code> represents a completely outstanding target change, if <code>true</code>, it will invoke <code>anim.action(anim.easing(1))</code>. At the same time <code>anim</code> also has <code>anim.pause()</code> and <code>anim.resume()</code> interruptible and continue animation functions, as well as the <code>anim.isRunning()</code> function to determine whether the animation is in progress.</p>

<p>The <code>action</code> function&#39;s first parameter <code>v</code> represents the value after the operation of the <code>easing (t)</code> function, <code>t</code> represents the progress of the current animation form <code>0</code> to <code>1</code>, and the general properties vary according to the <code>v</code> parameter.</p>

<p><iframe src="examples/example_animation.html" style="height:280px"></iframe></p>

<p>The above example shows the animation effect of clicking on a background graphic element to move to a click position, clicking on the data itself to rotate and zoom, <code>ht.Default.startAnim</code> <code>easing</code> parameter is used to allow user-defined functions to control animations through mathematical formulas, such as constant-speed changes, first-slow, and then fast-after effects, refer to <a href="http://easings.net/">http://easings.net/</a>, the sample code <a href="examples/easing.js">easing.js</a> defines a series of animation functions to choose from:</p>

<pre><code>var Easing = {
    swing: function (t) {
        return ( -Math.cos(t * PI) / 2 ) + 0.5;
    },
    /**
    * Begins slowly and accelerates towards end. (quadratic)
    */
    easeIn: function (t) {
        return t * t;
    },
    /**
    * Begins quickly and decelerates towards end.  (quadratic)
    */
    easeOut: function (t) {
        return ( 2 - t) * t;
    },
    // ... 
}</code></pre>

<p>In the example, the build uses <code>graphView.setinteractors</code> to remove all the default interactivity, adding listeners through <code>view.addEventListener</code>, while building the <code>select</code> element for <code>HTML</code> to select different <code>easing</code> effect, add it to the <code>div</code> component of <code>graphView.getView()</code>, so you need to do a filter on the <code>select</code> element in a custom interaction event, where <code>graphview.getLogicalPoint(e)</code> returns the logical coordinate position based on the interaction event.</p>

<p>In this example, the triggering data rotates around its center by clicking on the data, and the data is then restored to the original size by a large variable, which sets the <code>frames</code> for <code>30</code> frame and <code>interval</code> for <code>16</code> millisecond intervals.
The <code>frame-based</code> way completes the animation; click on the background area trigger data to move to the specified click position, which completes the animation by setting <code>duration</code> for <code>500</code> cycle <code>time-based</code>.</p>

<pre><code>var select = document.createElement(&#39;select&#39;);
select.style.position = &#39;absolute&#39;;
select.style.top = &#39;10px&#39;;
select.style.right = &#39;10px&#39;;
view.appendChild(select);
for(var name in Easing){
    var option = document.createElement(&#39;option&#39;);
    option.innerHTML = name;
    if(name === &#39;easeOut&#39;){
        option.setAttribute(&#39;selected&#39;, &#39;true&#39;);
    }
    select.appendChild(option);
}                  

graphView.setinteractors(null);
var type = &quot;ontouchend&quot; in document ? &#39;touchstart&#39; : &#39;mousedown&#39;;                                

isAnimating = false;
view.addEventListener(type, function (e){
    e.preventDefault();
    if(isAnimating || e.target === select || !ht.Default.isLeftButton(e)){
        return;
    }
    isAnimating = true;
    var data = graphView.getDataAt(e);
    var easing = Easing[select.value];
    var finishFunc = function (){
        isAnimating = false;
    };
    if(data === toy){
        var size = toy.getSize();
        ht.Default.startAnim({
            frames: 30, 
            interval: 16,
            easing: easing,
            finishFunc: finishFunc,                            
            action: function (v){
                toy.setRotation(Math.PI * v);
                var r = Math.abs(v - 0.5) * 2;
                toy.setSize(size.width * r, size.height * r);
            }
        });                        
    }else{
        var p2 = graphView.getLogicalPoint(e);
        var p1 = toy.getPosition();
        anim = ht.Default.startAnim({  
            duration: 500,
            easing: easing,
            finishFunc: finishFunc,
            action: function (v){
                toy.setPosition(
                    p1.x + (p2.x - p1.x) * v,
                    p1.y + (p2.y - p1.y) * v
                );
            }
        });                        
    }
}, false);</code></pre>

<p>The example modifies the style background color of the underlying <code>div</code> component, while adding the top and bottom brushes, drawing <code>Click anywhere you want ...</code> text information, by moving the data can be found that <code>topPainter</code> drawn content is rendered above the data, <code>bottomPainter</code> drawn content is rendered under the data.</p>

<pre><code>view.style.background = &#39;#FCFCFC&#39;;
graphView.addTopPainter(function (g){
    ht.Default.drawText(g, &#39;click anywhere you want ..&#39;, &#39;24px Arial&#39;, &#39;lightgray&#39;, 50, 100, 0, 0, &#39;left&#39;);
});      
graphView.addBottomPainter(function (g){
    ht.Default.drawText(g, &#39;click anywhere you want ..&#39;, &#39;24px Arial&#39;, &#39;lightblue&#39;, 200, 180, 0, 0, &#39;left&#39;);
}); </code></pre>

<p>Functions of many components of <code>HT</code> are also animated, such as <code>setTranslate(x, y, anim)</code>, <code>zoomIn(anim)</code>, <code>rotate(leftRight, upDown, anim)</code>, and so on, with <code>anim</code> parameter options, there are two types of arguments that can be passed in:</p>

<ul><li>the <code>boolean</code> type of <code>true</code> and <code>false</code>, <code>true</code> means the animation is started, and the default animation effect is used, <code>false</code> means that the animation is not started</li><li><code>JSON</code> object structure that takes the type parameter to represent the start animation, while the <code>JSON</code> structure attributes with <code>ht.Default.startAnim</code> type can be descendants of <code>duration</code>, <code>frames</code>, <code>interval</code>, <code>easing</code> and <code>finishFunc</code> and other animation control parameters.</li></ul>

<blockquote><p>Except through <code>ht.Default.startAnim</code> to invoke startup animation, <code>DataModel</code> also has functions to start scheduling tasks, which can be extended to flow, flicker, size changes and other animation effects, as detailed in the <a href="../../core/schedule/ht-schedule-guide.html">Dispatch Manual</a>, for more robust descriptive animation controls refer to <a href="../../plug-in/animation/ht-animation-guide.html">Animation plug-in</a>.</p></blockquote>

<div id="ref_propertyview"></div>

<h3>Property Component</h3>

<p>Refer to <a href="../propertyview/ht-propertyview-guide.html">Property Component Manual</a> and <a href="../../plug-in/propertypane/ht-propertypane-guide.html">PropertyPane plug-in</a></p>

<div id="ref_listview"></div>

<h3>Listview Component</h3>

<p>Refer to <a href="../listview/ht-listview-guide.html">ListView Component Manual</a></p>

<div id="ref_treeview"></div>

<h3>TreeView Component</h3>

<p>Refer to <a href="../treeview/ht-treeview-guide.html">TreeView Component Manual</a></p>

<div id="ref_tableview"></div>

<h3>TableView Component</h3>

<p>Refer to <a href="../tableview/ht-tableview-guide.html">TableView Component Manual</a></p>

<div id="ref_treetableview"></div>

<h3>TreeTableView Component</h3>

<p>Refer to <a href="../treetableview/ht-treetableview-guide.html">TreeTableView Component Manual</a></p>

<div id="ref_toolbar"></div>

<h3>Toolbar Component</h3>

<p>Refer to <a href="../toolbar/ht-toolbar-guide.html">Toolbar Manual</a></p>

<div id="ref_splitview"></div>

<h3>SplitView Component</h3>

<p>Refer to <a href="../splitview/ht-splitview-guide.html">SplitView Component Manual</a></p>

<div id="ref_borderpane"></div>

<h3>BorderPane Component</h3>

<p>Refer to <a href="../borderpane/ht-borderpane-guide.html">BorderPane Component Manual</a></p>

<div id="ref_accordionview"></div>

<h3>AccordionView Component</h3>

<p>Refer to <a href="../accordionview/ht-accordionview-guide.html">AccordionView Component Manual</a></p>

<div id="ref_tabview"></div>

<h3>TabView Component</h3>

<p>Refer to <a href="../tabview/ht-tabview-guide.html">TabView Component Manual</a></p>

<div id="ref_graphview"></div>

<h3>GraphView Component</h3>

<p>The topological graphics component <code>ht.graph.GraphView</code>(hereinafter referred to as <code>GraphView</code>) is the most abundant component in the <code>HT</code> framework <code>2D</code> and its related class libraries are under the <code>ht.graph</code> package.
<code>GraphView</code> has basic graphics rendering and editing functions, topology node connection and automatic layout function, power and telecommunications industry predefined objects, with animation rendering and other special effects, so its application is very wide, can be used as a monitoring field of drawing tools and Man-machine interface, can be used as a general graphical editing tools, can be extended into business applications such as workflows and organization charts.</p>

<div id="ref_graphviewzoom"></div>

<h4>Zoom In</h4>

<p>Changing the <code>zoom</code> attribute value (default to <code>1</code>) enables the <code>GraphView</code> zooming function to view the full picture or detail of the topological graphics component in an enlarged or reduced manner.
The mouse wheel on the peace plate and the double finger <code>pinch</code> gesture can change the <code>zoom</code> value in default. Press SPACEBAR to reset <code>zoom</code> to default <code>1</code> (the operation also resets <code>translateX</code> and <code>translateY</code> to <code>0</code>):</p>

<ul><li><code>ht.Default.zoomIncrement = 1.3</code> Calls the zooming stepping of the <code>zoomIn</code> and <code>zoomOut</code> functions</li><li><code>ht.Default.scrollZoomIncrement = 1.05</code> Mouse wheel zoom step</li><li><code>ht.Default.pinchZoomIncrement = 1.08</code> Touch screen double finger zoom step</li><li><code>ht.Default.zoomMax = 20</code> Maximum magnification</li><li><code>ht.Default.zoomMin = 0.01</code> Minimum reduction value </li></ul>

<p>The following is the <code>GraphView</code> scaling correlation function in which the <code>point</code> parameter in the following function represents the center data for zooming, which is typically input the <code>graphView.getLogicalPoint(event)&#39;s</code> return value, that is the center of the current mouse click, when this parameter is empty, it is zooming in the center of the currently visible rectangular area.</p>

<ul><li><code>zoomIn(anim, point)</code> Zoom in</li><li><code>zoomOut(anim, point)</code> Zoom out </li><li><code>zoomReset(anim, point)</code> Sets the scale value to <code>1</code> </li><li><code>scrollZoomIn(point)</code> Calls when the wheel is enlarged</li><li><code>scrollZoomOut(point)</code> Calls when the wheel is shrink </li><li><code>pinchZoomIn(point)</code> The touch-screen double finger is magnified when called</li><li><code>pinchZoomOut(point)</code> Calls when touch double finger shrinks</li><li><code>getZoom()</code> and <code>setZoom(value, anim, point)</code> Gets and sets the value of scaling, the final setting will invoke <code>adjustZoom</code> for control</li><li><code>adjustZoom(value)</code> Input the zoom value that is about to be modified, return the scaling value of the final run setting, and can be overloaded for customization</li></ul>

<p>The <code>adjustZoom</code> function defaults to the following, for <code>ht.Default</code> configured maximum and minimum values are limited:</p>

<pre><code>adjustZoom = function (value){
    if(value &lt; ht.Default.zoomMin){
        return ht.Default.zoomMin;
    }
    if(value &gt; ht.Default.zoomMax){
        return ht.Default.zoomMax;
    }
    return value;                
}; </code></pre>

<div id="ref_graphviewinteraction"></div>

<h4>Interaction</h4>

<p><code>GraphView</code> has some internal interactor for basic selection, single double click, zoom, panning, and editing in default, here are the internal interactors:</p>

<ul><li><code>interactor</code> interactor class, provides basis functions, such as distributed interactive events, listening function add and remove, encapsulate drag and drop operation, automatic pan scrolling function</li><li><code>Defaultinteractor</code> To achieve the <code>Group</code>, <code>Edge</code> and <code>SubGraph</code> the double-click response, hand grab picture panning, mouse wheel zooming, keyboard response and other functions in default</li><li><code>Selectinteractor</code> Enables the data to be single and box-selected. Drag background is panning in default, and press <code>Ctrl</code> to make a box selection (<code>Command</code> key in <code>Mac</code>)</li><li><code>Moveinteractor</code> To achieve the move function of the selected data</li><li><code>Editinteractor</code> To achieve the size change and angle rotation of datas, as well as the <code>Shape</code> and <code>Edge</code> type of the multiple-point editing of datas</li><li><code>Touchinteractor</code> To achieve <code>touch</code> interaction on mobile devices</li><li><code>Scrollbarinteractor</code> To achieve scroll bar display and interactive function</li></ul>

<p>These interactions can be combined with <code>GraphView#setinteractors(list)</code>, and the user can also extend their own interactivity based on <code>interactor</code>, the following code is the implementation of <code>Graphview#setEditable(false/true)</code>, the <code>GraphView</code> constructor called <code>setEditable(false)</code>, so by default only the basic operation function does not have the editing function, and the edit function can be achieved by calling <code>setEditable(true)</code>.                 </p>

<pre><code>setEditable: function (editable) {
    var self = this;
    if (editable) {
        self.setinteractors([
            new ScrollBarinteractor(self),
            new Selectinteractor(self),
            new Editinteractor(self),
            new Moveinteractor(self),
              new Defaultinteractor(self),
            new Touchinteractor(self)
        ]);
    } else {
        self.setinteractors([
            new ScrollBarinteractor(self),
            new Selectinteractor(self),
            new Moveinteractor(self),
            new Defaultinteractor(self),
            new Touchinteractor(self, {editable: false})
        ]);
    }
},</code></pre>

<p><code>Touchinteractor</code> the second parameter of the class can be passed into the <code>JSON</code> object control function to open and close, the default is open</p>

<ul><li><code>selectable</code> Allowed to be selected</li><li><code>movable</code> Allowed to move</li><li><code>pannable</code> Allowed to translate</li><li><code>pinchable</code> Allowed to scale</li><li><code>editable</code> Allowed to edit</li></ul>

<p>The internal <code>interactor</code> sends events in the interactive process, which can be monitored by <code>GraphView#addinteractorListener</code>, referred to briefly as <code>mi</code></p>

<pre><code>graphView.addinteractorListener(function (e) {
    if(e.kind === &#39;clickData&#39;){
        console.log(e.data + &#39;clicked&#39;);
    }
    else if(e.kind === &#39;doubleClickData&#39;){
        console.log(e.data + &#39;double clicked&#39;);
    }            
    else if(e.kind === &#39;clickBackground&#39;){
        console.log(&#39;clicked background&#39;);
    }  
    else if(e.kind === &#39;doubleClickBackground&#39;){
        console.log(&#39;double clicked&#39;);
    }     
    else if(e.kind === &#39;beginRectSelect&#39;){
        console.log(&#39;start to select the rect&#39;);
    }              
    else if(e.kind === &#39;betweenRectSelect&#39;){
        console.log(&#39;selecting the rect&#39;);
    }             
    else if(e.kind === &#39;endRectSelect&#39;){
        console.log(&#39;stop to select the rect&#39;);
    }           
    else if(e.kind === &#39;beginMove&#39;){
        console.log(&#39;start to move the data&#39;);
    }              
    else if(e.kind === &#39;betweenMove&#39;){
        console.log(&#39;moving the data&#39;);
    }             
    else if(e.kind === &#39;endMove&#39;){
        console.log(&#39;stop to move the data&#39;);
    } 
    else if(e.kind === &#39;beginPan&#39;){
        console.log(&#39;start to pinch pan&#39;);
    }              
    else if(e.kind === &#39;betweenPan&#39;){
        console.log(&#39;pinching pan&#39;);
    }             
    else if(e.kind === &#39;endPan&#39;){
        console.log(&#39;stop to pinch pan&#39;);
    }     
    else if(e.kind === &#39;beginEditRect&#39;){
        console.log(&#39;start to edit the data size and position&#39;);
    }              
    else if(e.kind === &#39;betweenEditRect&#39;){
        console.log(&#39;editing the data size and position&#39;);
    }             
    else if(e.kind === &#39;endEditRect&#39;){
        console.log(&#39;stop to edit the data size and position&#39;);
    } 
    else if(e.kind === &#39;beginEditPoint&#39;){
        console.log(&#39;start to edit the points of multi-sided Shape or multi-point Edge&#39;);
    }              
    else if(e.kind === &#39;betweenEditPoint&#39;){
        console.log(&#39;editing the points of multi-sided Shape or multi-point Edge&#39;);
    }             
    else if(e.kind === &#39;endEditPoint&#39;){
        console.log(&#39;stop to edit the points of multi-sided Shape or multi-point Edge&#39;);
    } 
    else if(e.kind === &#39;beginEditRotation&#39;){
        console.log(&#39;start to rotate the data&#39;);
    }              
    else if(e.kind === &#39;betweenEditRotation&#39;){
        console.log(&#39;rotating the data&#39;);
    }             
    else if(e.kind === &#39;endEditRotation&#39;){
        console.log(&#39;stop to rotate the data&#39;);
    }               
    else if(e.kind === &#39;moveLeft&#39;){
        console.log(&#39;leftarrow move the data 1 pixel to the left&#39;);
    }       
    else if(e.kind === &#39;moveRight&#39;){
        console.log(&#39;rightarrow move the data 1 pixel to the right &#39;);
    } 
    else if(e.kind === &#39;moveUp&#39;){
        console.log(&#39;uparrow move the data up 1 pixel&#39;);
    } 
    else if(e.kind === &#39;moveDown&#39;){
        console.log(&#39;down-arrow move the data down 1 pixel&#39;);
    } 
    else if(e.kind === &#39;toggleNote&#39;){
        console.log(&#39;toggle note to expand and merge&#39;);
    }             
    else if(e.kind === &#39;toggleNote2&#39;){
        console.log(&#39;toggle note2 to expand and merge&#39;);
    }             
});</code></pre>

<p>There is also a logical coordinate point (<code>LogicalPoint</code>) concept about interaction, the translated and scaled components have <code>getLogicalPoint</code> function in general, according to the interactive event to return the corresponding coordinate point information, simple understanding of logical coordinates point is same as user set <code>model</code> coordinates, and the actual display at the screen point of coordinates, need to be through <code>zoom</code> and <code>translate</code> conversion, in the <code>GraphView</code> to change the <code>zoom</code> and <code>translate</code> process, the data model of the data has not changed, that is, the logical coordinate value is unchanged, only the rendering effect of the view component changes, the logical coordinate information of all datas in <code>DataModel</code>, such as <code>position</code>, <code>points</code>, <code>width</code> and <code>height</code>, remains unchanged, so when customizing the interaction event processing, it is necessary to call the following interactive screen coordinates and logical coordinate conversion functions:</p>

<ul><li><code>getLogicalPoint(event)</code> Input interaction <code>event</code> parameter, return corresponding logical coordinate point, referred to briefly as <code>lp</code></li><li><code>getDataAt(pointorevent, filter)</code> Input logical coordinate point or interactive <code>event</code> event parameter, returns the data under the current point, <code>filter</code> can be filtered</li><li><code>getSelectedDataAt(pointOrEvent)</code> Input the logical coordinate point or interaction <code>event</code> parameter, which returns the selected data under the current point.</li><li><code>getDatasInRect(rect, intersects, selectable)</code> Gets the datas within the logical coordinate region.<ul><li><code>rect</code> Represents the logical coordinate area.</li><li><code>intersects</code> Specifies whether to intersect or include select or not, <code>true</code> means intersect select, <code>false</code> means include select.</li><li><code>selectable</code> Specifies whether the data is required to be selected or not, for the NULL represents does not require, whether selectable is judging by <code>GraphView.isSelectable</code> function</li></ul></li><li><code>moveSelection(xOffset, yOffset)</code> Move the selected data horizontally <code>xOffset</code>, move <code>yOffset</code> vertically  </li></ul>

<p><code>Graphview</code> has a series of <code>on*</code> type callback functions that can be overloaded for subsequent processing or change the default implementation logic:</p>

<ul><li><code>onDataClicked(data, event)</code> Called when the data is clicked</li><li><code>onDataDoubleClicked(data, event)</code> Called when the data is double clicked</li><li><code>onEdgeDoubleClicked(edge, event)</code> Called when the edge is double clicked, default to call <code>edge.toggle()</code></li><li><code>onGroupDoubleClicked(group, event)</code> Called when the group is double clicked, default to call <code>group.toggle()</code></li><li><code>onSubGraphDoubleClicked(subGraph, event)</code> Called when the subgraph is double clicked, default to call <code>graphView.setCurrentSubGraph(subGraph)</code> into the subgraph</li><li><code>onBackgroundClicked(event)</code> Called when the background is clicked</li><li><code>onBackgroundDoubleClicked(event)</code> Called when the background is double clicked, default to call <code>upSubGraph()</code> into the upper subgraph</li><li><code>onCurrentSubGraphChanged(event)</code> Called when the current subgraph is changed, default to call <code>reset()</code> to  recovery the default zoom and the default translate value</li><li><code>onAutoLayoutEnded()</code> Called when the autolayout animation is finished</li><li><code>onMoveEnded()</code> Called when moving the position of data is finished</li><li><code>onPanEnded()</code> Called when grabbing translate topological graph is finished</li><li><code>onRectSelectEnded()</code> Called when the frame selection is finished</li><li><code>onTranslateEnded()</code> Called when translating animation is finished</li><li><code>onZoomEnded()</code> Called when zooming topological graph is finished</li><li><code>onPinchEnded()</code> Called when touchscreen pinching is finished</li><li><code>onSelectionChanged()</code> Called when the selection is changed, the selected data will show in the visible scale in the topological graph</li></ul>

<p>In addition to calling the <code>GraphView</code> encapsulated functions, users can also add native <code>HTML</code> component listener events, such as the <a href="#ref_node">Node</a> chapter example, through <code>graphView.getView().addEventListener</code> to add listening to the bottom of the <code>div</code>, the following code has a few points to note:</p>

<ul><li>Through <code>ht.Default.isTouchable? &#39; Touchend &#39;: &#39; MouseUp &#39;</code> judgment for the desktop and touchscreen terminals to do a comprehensive event type considerations</li><li>Find datas under the event through <code>Graphview.getDataAt(e)</code></li><li>Through <code>ht.Default.isDoubleClick(e)</code> judge click and double-click event distinction</li></ul>

<p>In the aforementioned <code>HT</code> package of <code>onDataClicked</code> and <code>on**Clicked</code> events, are triggered in <code>mousedown</code> and <code>touchstart</code>, if needed to listen in <code>mouseup</code> and <code>touchend</code> after letting go of the event, you can listen to the <code>HTML</code> raw events application is more in the case of click on the data to open the dialog box, if in the <code>HT</code> package of the <code>on*clicked</code> event processing directly invoke pop-up dialog, such operations, will affect the <code>HT</code> subsequent interactive event processing , so interactive events affect the function of the interface, you can choose to listen in <code>mouseup</code> and <code>touchend</code> inside, in some cases even need to call <code>ht.Default.callLater(function (){})</code> is handled in the following ways:</p>

<pre><code>var eventType = ht.Default.isTouchable ? &#39;touchend&#39; : &#39;mouseup&#39;;
graphView.getView().addEventListener(eventType, function (e){
    var data = graphView.getDataAt(e);
    if(data &amp;&amp; ht.Default.isDoubleClick(e)){
        alert(data.getName() + &#39; is double clicked.&#39;);
    }
});</code></pre>

<p>The following example customized to create <code>Node</code>, <code>Edge</code> and <code>Shape</code> interactors:</p>

<p><iframe src="examples/example_editor.html" style="height:400px"></iframe></p>

<div id="ref_filter"></div>

<h4>Filter</h4>

<p>Filtering mechanism through the whole <code>HT</code> framework, and the use of filtering mechanism is particularly concentrated in <code>GraphView</code>, the rational use of these filters can flexibly control whether the data visible, movable, editable, etc.</p>

<pre><code>isMovable: function (data) {
    if(data instanceof ht.Edge){
        return false;                
    }
    return this._movableFunc ? this._movableFunc(data) : true;
},</code></pre>

<p>The above code is a simplified version of the <code>GraphView.isMovable(data)</code> function, which is called by the <code>moveinteractor</code> class to determine whether the data movable, <code>this._movableFunc</code> property is set by <code>GraphView.setMovableFunc(func)</code>, by the code is known <code>Edge</code> is not allowed to drag by default, and then determine whether the <code>movableFunc</code> function property is set, if the logic is set then whether the function movable is determined by this, finally returns <code>true</code>.</p>

<p>So there are two ways to customize whether movable logic:</p>

<ul><li>Set the <code>GraphView.setMovableFunc(func)</code> function property.</li><li>Overload <code>GraphView.isMovable(data)</code>, in this way customers need to consider the original <code>isMovable</code> implementation logic.</li></ul>

<p>The following list is a common filter function:</p>

<ul><li>Whether the data is visible or not:<ul><li><code>GraphView.isVisible(data)</code></li><li><code>GraphView.setVisibleFunc(func)</code></li></ul></li><li>Whether the data is selectable or not:<ul><li><code>GraphView.isSelectable(data)</code> Returns <code>GraphView.getSelectionModel().isSelectable(data)</code> in default</li><li><code>GraphView.getSelectionModel().setFilterFunc(func)</code></li><li><code>GraphView.setSelectableFunc(func)</code> Internal called <code>GraphView.getSelectionModel().setFilterFunc(func)</code></li></ul></li><li>Whether the data is editable or not, this is the main switch of editing<ul><li><code>GraphView.isEditable(data)</code></li><li><code>GraphView.setEditableFunc(func)</code></li><li>Whether the data size is editable or not<ul><li><code>GraphView.isRectEditable(data)</code></li><li><code>GraphView.setRectEditableFunc(func)</code></li></ul></li><li>Whether the data is rotatable or not<ul><li><code>GraphView.isRotationEditable(data)</code></li><li><code>GraphView.setRotationEditableFunc(func)</code></li></ul></li><li>Whether the data anchor is editable or not<ul><li><code>GraphView.isAnchorEditable(data)</code></li><li><code>GraphView.setAnchorEditableFunc(func)</code></li></ul></li><li>Whether the data point is editable or not<ul><li><code>GraphView.isPointEditable(data)</code></li><li><code>GraphView.setPointEditableFunc(func)</code></li></ul></li></ul></li></ul>

<p>Many users who have just started using <code>HT</code> are puzzled by the filter set on the view component rather than the data model. If you control the filter on the model, all components that share the same model can only have the same filtering logic, and the <code>HT</code> design idea enables different components to have different filtering logic.</p>

<p>In addition to setting filters on the view components, the <code>GraphView</code> internal filtering mechanism also refers to the following <code>style</code> attributes, which can change the control effect of the following <code>style</code> on a single data: </p>

<ul><li><code>2d.visible</code>: The default value is <code>true</code>, control data is visible on <code>GraphView</code></li><li><code>2d.selectable</code>: The default value is <code>true</code>, control data is selectable on <code>GraphView</code></li><li><code>2d.movable</code>: The default value is <code>true</code>, control data is movable on <code>GraphView</code></li><li><code>2d.editable</code>: The default value is <code>true</code>, control data is editable on <code>GraphView</code></li><li><code>2d.move.mode</code>: The default value is NULL, to control data move range, can be set to the following parameters:<ul><li><code>xy</code>: Can move in <code>xy</code> plane </li><li><code>x</code>: Moves only along the <code>x</code>-axis </li><li><code>y</code>: Moves only along the <code>y</code>-axis </li><li>Any other not-null value represents the non-removable</li></ul></li></ul>

<p>Similarly, there are similar control parameters for <code>Graph3dView</code>:</p>

<ul><li><code>3d.visible</code>: The default value is <code>true</code>, controls whether data is visible on <code>Graph3dView</code></li><li><code>3d.selectable</code>: The default value is <code>true</code>, controls whether data is selectable on <code>Graph3dView</code></li><li><code>3d.movable</code>: The default value is <code>true</code>, controls whether data is movable on <code>Graph3dView</code></li><li><code>3d.editable</code>: The default value is <code>true</code>, controls whether data is editable on <code>Graph3dView</code></li><li><code>3d.move.mode</code>: The default value is null, controls data movement range, can be set as the following parameters:<ul><li><code>xyz</code>: Can move in 3d space</li><li><code>xy</code>: Moves only in <code>xy</code> plane </li><li><code>xz</code>: Moves only in <code>xz</code> plane</li><li><code>yz</code>: Moves only in <code>yz</code> plane</li><li><code>x</code>: Moves only in <code>x</code>-axis </li><li><code>y</code>: Moves only in <code>y</code>-axis </li><li><code>z</code>: Moves only in <code>z</code>-axis </li><li>Any other not-null value represents the non-removable</li></ul></li></ul>

<div id="ref_graphviewstyle"></div>

<h4>Style</h4>

<p><code>HT</code> <code>Data</code> can be classified into three types of properties:</p>

<ul><li><code>get/set</code> or <code>is/set</code> type, such as <code>getName()</code>, <code>setName(&#39;ht&#39;)</code> and <code>isExpaned()</code>, for common property operations</li><li><code>attr</code> type, gets and sets by <code>getAttr(name)</code> and <code>setAttr(key, value)</code>, this type is <code>HT</code> reserved for users to store business data</li><li><code>style</code> type, operate by <code>getStyle(name)</code> and <code>setStyle(name, value)</code>, the data style on <code>GraphView</code> is controlled by this type property</li></ul>

<p>The following code is the implementation of the <code>style</code> related function in <code>HT</code>, the default data <code>_stylemap</code> is NULL, query the value will refer to <code>ht.Style</code> global default value:</p>

<pre><code>getStyleMap: function (){
    return this._styleMap;
},
getStyle: function (name, checkDefault) {
    if (checkDefault === undefined) {
        checkDefault = true;
    }
    var value = this._styleMap ? this._styleMap[name] : undefined;
    if (value === undefined &amp;&amp; checkDefault) {
        return ht.Style[name];
    } else {
        return value;
    }
},
setStyle: function (name, newValue) {        
    if (!this._styleMap) {
        this._styleMap = {};
    }
    var oldValue = this._styleMap[name];
    if(newValue === undefined){
        delete this._styleMap[name];               
    }else{
        this._styleMap[name] = newValue;               
    }        
    if (this.fp(&#39;s:&#39; + name, oldValue, newValue)) {
        this.onStyleChanged(name, oldValue, newValue);
    }
},</code></pre>

<p>The following is the description of partial <code>style</code> attribute, and more attributes are described in subsequent chapters:</p>

<ul><li><code>image.stretch</code> Draws the drawing type of the image, the default value is <code>fill</code>, can be set to <code>uniform</code> or <code>centerUniform</code>, refer to <a href="#ref_image">Image</a></li><li><code>ingroup</code> Determines whether the data is included within the expanded <code>Group</code> or not and the default value is <code>true</code></li><li><code>opacity</code> Used to control the opacity of the entire data and the value range is <code>0~1</code></li><li><code>body.color</code> Setting this color will change the rendering of the center of the data, and can be customized by overloading the <code>GraphView.getBodyColor(data)</code> function: <ul><li>For the display <code>image</code> of the <code>Node</code>, it will automatically be drawn to the <code>body.color</code> after the image is dyed</li><li>For the vector with <code>shape</code>, if the background is to be filled, <code>shape.background</code> is replaced by <code>body.color</code></li><li>For the vector with <code>shape</code>, if no background is required, <code>shape.border.color</code> is replaced by <code>body.color</code></li><li>For the <code>edge</code> connection type, <code>edge.color</code> will be replaced by <code>body.color</code></li></ul></li></ul>

<p><iframe src="examples/example_bodycolor.html" style="height:200px"></iframe></p>

<div id="ref_styleselect"></div>

<h4>Select</h4>

<p>The <code>GraphView</code> data is selected by default to display a selected border, and the effect of the selected border can be controlled by the <code>select.*</code> related properties on the style.</p>

<ul><li><code>select.color</code> The color of selected border</li><li><code>select.width</code> The width of selected border, the default value is 1, if the value is <code>0</code> represents there were no border</li><li><code>select.padding</code> The gap between selected border and <code>Node.getRect()</code>, the default value is <code>2</code>, can be negative value, this attribute is meaningless in <code>ht.Shape</code> and <code>ht.Edge</code></li><li><code>select.type</code> The shape of selected border, the default value is <code>rect</code>, refer to <a href="#ref_styleshape">Shape Manual</a>, and set to <code>shadow</code> to appear as a shadow effect<ul><li><code>shadow.offset.x</code> Shadow horizontal offset, the default value is <code>3</code></li><li><code>shadow.offset.y</code> Shadow vertical offset, the default value is <code>3</code></li><li><code>shadow.blur</code> Shadow blur level, the default value is <code>6</code></li></ul></li></ul>

<div id="ref_styleborder"></div>

<h4>Border</h4>

<p>The <code>boder</code> style draws a border effect at the edge of the data, which is used for alarms or prompts, and can be customized by overloading the <code>GraphView.getBorderColor(data)</code>.</p>

<ul><li><code>border.color</code> The color of border, the default value is null for not drawing</li><li><code>border.width</code> The width of border, the default value is <code>2</code></li><li><code>border.padding</code> The gap between border and <code>Node.getRect()</code>, the default value is <code>2</code>, can be set as negative value, this attribute is meaningless of <code>ht.Shape</code> and <code>ht.Edge</code>, etc.</li><li><code>border.type</code> The shape of border, the default value is <code>rect</code>, refer to <a href="#ref_styleshape">Shape Manual</a></li></ul>

<p><iframe src="examples/example_bordercolor.html" style="height:300px"></iframe></p>

<div id="ref_styleshape"></div>

<h4>Shape</h4>

<p>The <code>Node</code> data on <code>GraphView</code>, in addition to being displayed as a picture set by <code>image</code> property, can also be set to vector for various polygonal types built into the <code>HT</code> frame.
To be displayed in vector format can be achieved by setting the <code>style</code> property of the <code>Node</code> type data, the related <code>style</code> property name is described as follows:</p>

<ul><li><code>shape</code> String type, determines the shape of <code>shape</code>, the default value is NULL, represents drawing by <code>image</code>, the support type is as follows:<ul><li><code>rect</code> Rectangle</li><li><code>circle</code> Circle</li><li><code>oval</code> Oval</li><li><code>roundRect</code> Round rectangle</li><li><code>star</code> Star</li><li><code>triangle</code> Triangle</li><li><code>hexagon</code> Hexagon</li><li><code>pentagon</code> Pentagon</li><li><code>diamond</code> Diamond</li><li><code>rightTriangle</code> Right Triangle</li><li><code>parallelogram</code> Parallelogram</li><li><code>trapezoid</code> Trapezoid</li><li><code>polygon</code> Polygon</li><li><code>arc</code> Arc</li></ul></li><li><code>shape.border.width</code> The width of border, the default value is <code>0</code> represents for not drawing the border</li><li><code>shape.border.color</code> The color of border</li><li><code>shape.border.cap</code> The type of border cap, selectable parameters are <code>butt|round|square</code>
<img src="data:image/png;base64,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"></li><li><code>shape.border.join</code> Border when two lines intersect create a type of cornor, optional parameters are <code>bevel|round|miter</code>
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAcYAAACFCAMAAAD2B6htAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAEXUExURXt5/7W2972+/3t59//PzrW2tdbX1r2+93Nxc//Hxu/v7wgICM7PzikoKefn55yenO++vVpZWsbHxs6mpRAQEDEoKRAICDEwMb2+vZx5e2NJSqWGhN7f3lJRUnt5e2NhY2tpa6WmpTk4OUpJSoSG/5SWlDEgIVJBQiEYGK2urYyOjISGhGNRUhgYGEIwMffHxu/HxikgIXNZWlpJSq2OjIRpa4xxc8aenLWOjJRxc9aurb2WlNampXthY+e2td62td6urb2enIRhY2tZWmtRUiEgITkwMUI4OZR5e0o4ObWWlEpBQtbPzsbH9+/n53Nx/72mpdbX95ye/62WlMaurZyGhMa+vaWenKWOjHtZWnNhYwAAAP///4IyFQcAABKmSURBVHjaYmBmGIVDHgYBBBADQ8woGPKAGSCAGJhHA2HoAwaAAGJQGQ2EYRCNAAE0mhuHRTQCBNBo3Tgs6kaAABrNjcMiNwIEEDXrRpnR8ByoaAQIICrmRh5u/tEAHaBoBAgg6tWNXNzR0TyjITowdSNAAFEtN3LxRkdHc4+WqwOTGwECiGp1o2A0CPByjYbpQEQjQABRKzfKRUOA8miYDkQ0AgQQlepG/mgYUBgN1AGoGwECiDq5kYcbHo3Ro83VAciNAAFElbqRCykWB1dzlUdOiTeaW0mQf3jX2QwAAUSN3AhupCLA4Gmu8usiXCU4nBvRDAABRI26UTAaFQyS5qqMEqqz5IZx3QgQQFTIjXLR6GBQNFd5uNGdJTh8cyNAAFFeN/JHYwKFwRiLgyIeuSSwAIpLLwaAAKI4N2ILrkHQXOXC6qwBT15ozQhqlV4MAAFEad2IPbgGvLmKPbgGPnkJYncWpcmLGSCAKMyNuIJroJurOIJroJOXHC5nUZi8GAACiMK6EWdwDWxzFWdwDWzy4sfpLAqTFwNAAFGWG3EH14A2V/EE10AmLx7uaBolLwaAAKKobkQJLg1pRwu1QdGeQAkuEUlzR/VBkbxQmxGm1uZGVEtezAABREluRAkuMQFOFk4Ww0HQnkANLmGgqzjVB0HyQm1GSINCy4xayYsBIIAoqBtRg8uIBeguFouBb0+gBpceC8hdwoOguYrSjNDgBLuLWs1VBoAAIj83ojVSbUGpnsV64NsTqK0uKU6wuwa+uYrajNADu4qTWs1VBoAAIr9uRGuk6oDTl+SAtyfQWl2ioDICLXUNRPJCa3WJ6IPcJUCt5iozQACRnRvRG6mS4PTlNdDNVfRGqhQovDitxAY4eaE3Ug05QaElIEql5MUAEEDk1o0YwcXC4mFrasciObDDX+jBJcrJImkaacdiPrDJC32sS8OA09rJVJjFXoQ6yYsBIIDIzI3owQUs650gNZHxQA5/YQSXADhdGQpwSg9k8kIf6xIT5hQGlg8iDmhtL7KTFwNAAJFXN6IHl5o+CyScTFlY9AZu+As9uESEWYTB6V2dhUVnAJMX+liXBae+BrisEKBS6cUMEEBk5UYcqQvSHxIwHLD2BHpwmbAIQEstFxbOgUte6M0IS3j/Wp2TxZkayYsBIIDIqhvRg8ucxQDWhhCzYrEZqPYEenCZIbUhJKnXnqC0GQFs00vBe9ucLOpUSF4MAAFETm5EDy5gDlRDHs0ZoPYEenAZs3BKIY/mCIsMSPLC0uoywxF2ZCcvBoAAIqNuxEhdLCxS2OpJOrcn0INLjYXFFLmedGBxHIjkhaXVZYLMt+G0ozx5MQMEEOm5EaORysliiycX0Ks9gRlcaK1TqrUnqNPqgpde9pyUN1cZAAKI5LoRPbiwhI4tC4s7vdsTGK0uzDpanZOF/skLS6tLAy0ADSjvDTEABBCpuZFg6hqg9gRmm15ADN1ZwPYEvZurmK0uTlF0Z3mzcBpRmLwYAAKI1LoRPbgc4W16/HFL4/YERpuexUANc25WmlOfvskLy1iXDqazjNBm0khPXswAAURibpQjmO8g8SiAPhpN2/YERnBhjiZBmqvw/i1dkhdmq4vTEpuzKC69GAACiLS6ETN1cepgXZNA3/YERpueBa3VBU9edvTsDWG0uvTR5mORekMOFJVeDAABRFJuRA8uNxZOUxxrS+g5uopjJBUbUKPj6CqesS6M5EXh6CoDQACRUjdiNFIx21hIa03o1p4gqtUFb65ilh+0Sl4YrS4WAw1czqKw9GIGCCASciMJqYt64xMUjaRiA870Gl3FMpKqhttZwN6QC/nJiwEggEioGzFGUjn18QRXtIgNpxU9mqv4RlJp0p6gylgXluYqBaOrDAABRHxuxDKSaojPXaDRVToMf+EfScXenqBDbwj/SCqO0ovsuSEGgAAium7EN04P20BIuO5UoHlwYba65NDrTnva94bQmxGuaCOpoO1daNsvo61Y7MjtDTEDBBCxuZHgSGp0tFaMBOGcwU/b4NLAbIkKYkY1zXtDRLS6eDHbGuSPrjIABBCRdSMRI6mCWLKsEwsnTUdXMYILS30MVCaB0Vyl8egqllaXK6qILheW0QGy54YYAAKIuNxIVOrC5n4atycwggtjJFWXC1uNYMTJ4kbD5EV4JJUbbB168vIkd3SVASCAiKsb0YPLGqNNrwstxpXoObqK2abXxxpcmB6QxhhzpWLyImKsSxO7Smcym6vMAAFEVG4kPJIKCy7MfEvD0VUiRlI1YWqV6De6irEmFXMkVQtX/iCz9GIACCBi6kYixuk1cTcdadaewNKmd8IVXJjtCZqtXcWyJtUC9xkEGMmLrLWrDAABRERuxLEmFXtwYRb4tBpdJWIkVRBfHqHR6CoRY128+FodZI2uMgAEEOG6kYtwe0oQb+alzdpVoltduJIXjdau4lqTitmMoGLpxQwQQARzI4mpC2t7ghZrVwmPpOpy4a8baLJ2FfeaVIxmBK7kRcbaVQaAACJYN+JZk4ojuDAKfFqsXSU8ksrNQ8grNOgNETHWpUlIDxlrVxkAAohQbpQjPG/BQzgHU33tKhEjqZqYupRoPbpKxEiqFuGcQvrcEANAABGoG4kYp9ckwjvUXrtKYE0qjuDC1p6g6uA9oTWp0TgOykJPXiSvXWUGCCD8uZGokVRsgLajq0SNpBKVW6jaGyK51YWz9CJ1dJUBIIDw1o1EjqQSk42puXaViDWpvDi0YmlPUC95EV6TqstFZGFM4twQA0AA4cuNZKcuGrcnCK9J1eUitpag4tpVYkdSiUleJK5dZQAIIHx1I+E1qbp4imyaja4SXpPKzUO8p6i2dpWksS7CyYuktavMAAGEJzeSMJJKVF6m0ugqKSOpxLQnqDS6SsSaVC1SihiSSi8GgADCXTcSP05PbOubKu0JItakapHWEKHK2lUi1qQSOM1ViYK1qwwAAYQzNxKxJlWLgNdoMbpK4kgqUfmGCqOrZIx1EdEbIrq5ygAQQLjqRiLWpAqSXP5RvnaVolYX7tFViteuEl6TqstFcjlDfOnFDBBAOHIjFVIXdcYnKB5JJSZ5Ubx2lfCaVG4iDJQge+0qA0AA4agbCa9J1SWqHaBE3bWr5Iyk0qE3ROZYFxHNVSJHVxkAAgh7biS8JpW44MI2ukrB8Bd5I6nEtSco6A2RN5JKZOlF1NwQA0AAYa0byRmnJ9aLFKxdJWJNqha5dSwFa1eJWZNKrFlkrl1lBgggbLmR7JFU2o6ukj2SStveEFVaXRSOrjIABBCWupGCkVRiSkJy164SuSaV3ORF9tpV4takkj2KQNTcEANAAGHJjcpUTF1EtifIad6YkDCSSuToKvraVS4yjCFlJJWY5IVl7aoEZjQCBBCWupGHl8g1qWQW+Bjpgjh/cikRuSaV3OSFsXaVnNxI8lgXEb0htOYqlru2mAECCFvdiBJgkmS26YkfXeWVISPcSR1JJX10lWhv8hO5JpU6pRfW69wZAAIIe79RkMxxenLaE8pcZCRUvGtSyW5PIK9dJTpxActBbuLWpJKdvJDWrmJPXAwAAYRjFIefuDWp5Bb4SHmJpOFLWIBpUNbqwpW8kNauKpNSccCqITLHukgYXcXRm2UACCBcY6qQACO4JpXcAh++dpXEwUtIgFHc6sLTXNUhZ2wcWg0RWpNKcekliMM4ZoAAwj3DwUu11IV7fILkahYSYCYUt7pwJS/o2lV+svxHeE0q2ckLsnYV50WiDAABhHu+ERhgxKxJJbPAB6+fIaH+QQ4wM8pbXXjbE2SZxk/JWBcxvSF1btyJiwEggPCtxQklZk0q2e0JAc4I8tJEONkjqcS1J0xkyMtBAuSOpBJXetnjCXsGgADCu041hIrBhTk+EUCuSWFR1AsuzPZEsB+ZJvl4UKUZgSN5efHhUcwMEED416lKcFMtuDAKfArWqaINUFB21y1a8qJknaoSVZoRWJOXIN6SiwEggAjs4UAJMEqvBuanTvGMHmC8FDpLgnqbqgSp0ozAkrwI3JLOABBAhPZwIAUYpcGF7EuyGjfYjdKleJcwP5USF3WNQkpe3IQSFwNAABHe3yhIteBCFPjKlJvFT7XgQniR4sSFVA1pxtDRi8wAAUTEbmN+qgUXrMCnyrZeaIBpUsMsJWolLng1pEUNZwkSuw6BASCAiNn7Dw4wqgQXpMCn0hk04ACjSnBBkheVjiQAV0OC1DFLiWDjBhqNAAFE1EkcwACjTnCBCnxuqp1AAwwwKgUXKHlR74AjQSo0I+DJS44YdQwAAUTcuThcWlTzo6ZMDPUA9ZzFQ83jjTSpdvQPD3GFIDNAAFF20/goGByAASCAKLjbeBQMnmgECKDR3DgsohEggMi/23gUDB7ADBBAo7lxWORGgAAarRuHRTQCBBCe3MjKSpaRbKxstHMvH6sQWfrA2sj0EHFAiJUP5Hc+Eh1FrofQohEggPDUjdHR2EOSA2QxG073skYjhZYQB5o6RQ6K3MsWTZ5+sDYUD2E4hEORIpdxRLOB/M5GoqNQPYTuBkhYE1E3AgQQntwoL48jJFmhBI5kGS2EHKds6L6lLN1FK5IfjSgewnBINGUJTDE6hpxoRPUQuhvwBDNqbgQIIDx1IwcHvlzBiluKhoVqTDQr+dHIwUFDh7GSG43UKOkZAAJwWsZIAIMgEEwD5RX+Ia/K//8hHKCOM7GwsQkyCze58/A3cmq4VdC/IWnj5qeQ71XVSCuJQPBK2LdFRmSNJ0emhlfxaHdJxand0aNzKyjoX3ty7zJqkQ38iIk57rWMbVyv3snG7ay72GU0JkzkCcWFH18cTxeAMyvGARgEgZOJEwOLkzEdnHjS/f8fFURs0rRJ62gEjoscJLz0RiMiIxUAjbLuTLoXYpqLMNMSPTZAgDsGMtrtUG00OqsLSGhZwlHHRaHvbAlb/lzVMzUYtl0Hd/esT4oxdvk4jt4TSbtgPd0fI47MyCUiBzVrWhDQ4qJHROEtY5iiKgaZQ/TQetg89MZTAM6rXQdgEAROmk4MLEyGMHTyk+z/f0flTKSNaZq4OEDk5I5H/Ps3Hq30eK4PWKMRenajOU63Y71LsXeLUT7dQ6LwW/Asyc+6O1ml1WR0qefKoouMEZ6hMi9LJ2uDt/ZKr41DxpFu2p2seBHK4EGNFLDhCANxAKyPggUXswpNGYP8r268BRChfiO0JQBJn0Lw1g20buSDJBE+UAyxYksu4NBVhQUxHzycwUlQNprsaGSDOwdqGmo0IoyXF8fRfgGrlIXIgoMZGo3wACcvGsGuUAX6FDloIAEAdpasLMRGeayOAolALWcD6YflRiTfYo9GgAAinBuRgoENPRqBRoNqGXAqwlq9gwWhTgc3YTmi0bIO2Q0WSGjB0hJqNMLZ0MDEEY0wWbBBSNEYQ2E0gkIcOWgg9oDSDB+4FIOWBdijEdbSB4UaG1KuxddqZQAIIEJjqvijkRW26AdvNEKdC9ZDzWiE6UeyHd14mN+xRyNMFuFK6kQjUvMBUt3Jgy1ThNR/8HYF9miEhSPCgYSjkRkggCjLjchRNxqNaNHIhtyXZoVkeZS4oF40MgAEELF1I85ClYhohNZPrNQuVBVhhaoimYVqDLSRpR0dQ9VoBPkUtSoD1r4Qu5CbdbgKVVZ49Ul0NAIEENm5ETL4IC4ObZPii0ZogxvcnqBmNLJBwkQb2Q504yGNGD4c0QhudUFlqRiNYEuRggaU4lQhVR4HNJT4cEYjtF3LisgtREQjQACRWTcCHSnEBm5biwNrckVZVnzRCAxcVjYh+WhWKkcjMAK12di0wZHJB2zqs4H6fGjGAxv/QmyK4jiiEawL2JPjo1o0cgiBfCoUgxI0IIuiIe08PvFoRTY2VnkO3NHICnIyK1g90XUjQACRmRshvVgQLQvugbPhjUbw2IE4awy1oxHSw4aUCyAHyQphGg9SIiuEIxpj2EDOl2WLoV40wnruyEEDGVeAjXaDRGVZcUdjDHgEAjyIQnRuBAgg8ucb2eBDTGwESxn8iiia8mDDxkRVwocnfcJGyGjiIhy+ZsNpJSxi2Uh0FANAANF49l82OmZwAj5yBtxoDVjJnL+JYQAIIBquxWGT5ZCNVhyMcQismsSjhQaZozg4OMgaZQbXjQABRMPcyKfNoS00KLMiGweHKtugS1scHIrklu8MAAE0uhZnOAAGgAAaXRk3LKIRIIBG16kOB8AMEECjuXFY5EaAABqtG4dFNAIE0GhuHBbRCBBAo3XjsKgbAQJoNDcOi9wIEECjdeOwiEaAABrNjcMiGgECaLRuHBZ1I0AAjebGYZEbAQKIwZ+diZFpFA9tzAAQQAy+7IxMjKN4aONAgAADAP+ya9dsALXrAAAAAElFTkSuQmCC"></li><li><code>shape.border.pattern</code> The type of dashed line, <code>Array</code> type, such as <code>[5, 5]</code></li><li><code>shape.depth</code> Only make an effect in type <code>rect</code>, the positive value means bulge, the negative value means depress, the default value is <code>0</code></li><li><code>shape.background</code> The fulfill color of background, <code>null</code> means do not fulfill the background</li><li><code>shape.gradient</code> The gradient type:<ul><li>The value is null means do not draw the gradient effect, only use <code>shape.background</code> to fulfill the background</li><li>Supported type: <code>&#39;linear.southwest&#39;, &#39;linear.southeast&#39;, &#39;linear.northwest&#39;, &#39;linear.northeast&#39;, 
&#39;linear.north&#39;, &#39;linear.south&#39;, &#39;linear.west&#39;, &#39;linear.east&#39;, 
&#39;radial.center&#39;, &#39;radial.southwest&#39;, &#39;radial.southeast&#39;, &#39;radial.northwest&#39;, &#39;radial.northeast&#39;, 
&#39;radial.north&#39;, &#39;radial.south&#39;, &#39;radial.west&#39;, &#39;radial.east&#39;, 
&#39;spread.horizontal&#39;, &#39;spread.vertical&#39;, &#39;spread.diagonal&#39;, &#39;spread.antidiagonal&#39;, 
&#39;spread.north&#39;, &#39;spread.south&#39;, &#39;spread.west&#39;, &#39;spread.east&#39;</code></li></ul></li><li><code>shape.gradient.color</code> The gradient color of background </li><li><code>shape.repeat.image</code> Fulfill the image in repeat background, take notice the image in there do not support vector</li><li><code>shape.dash</code> Whether display dashed line or not, the default value is <code>false</code> </li><li><code>shape.dash.pattern</code> The type of the dashed line, the default value is <code>[16, 16]</code></li><li><code>shape.dash.offset</code> The offset of the dashed line, the default value is <code>0</code></li><li><code>shape.dash.color</code> The color of the dashed line</li><li><code>shape.dash.width</code> The width of the dashed line, the default value is NULL means set its value is the same as <code>shape.border.width&#39;s</code></li><li><code>shape.dash.3d</code> Whether display the <code>3d</code> effect of dashed line, the default value is <code>false</code></li><li><code>shape.dash.3d.color</code> The <code>3d</code> effect colors of dashed line, default to white for null, the middle part of the line is the color when the <code>3d</code> effect is rendered</li><li><code>shape.dash.3d.accuracy</code> Dashed <code>3d</code> effect precision, the lower the value <code>3d</code> progressive effect the better but affect performance, general situation without modification</li><li><code>shape.corner.radius</code> This parameter specifies the fillet radius of the <code>roundrect</code> type, which is automatically adjusted by default and can set positive values</li><li><code>shape.polygon.side</code> The side of polygon, the parameter specified the side of <code>polygon</code>, the default value is <code>6</code></li><li><code>shape.arc.from</code> The begin of the arc, the default value is <code>Math.PI</code></li><li><code>shape.arc.to</code> The end of the arc, the default value is <code>2*Math.PI</code></li><li><code>shape.arc.close</code> Whether the arc closed or not, the default value is <code>true</code></li><li><code>shape.arc.oval</code> Whether the arc is oval or not, the default value is <code>false</code></li></ul>

<p><iframe src="examples/example_styleshape.html" style="height:690px"></iframe></p>

<p><iframe src="examples/example_gradient.html" style="height:320px"></iframe></p>

<div id="ref_styleposition"></div>

<h4>Position</h4>

<p>Refer to <a href="../position/ht-position-guide.html">Position Manual</a></p>

<div id="ref_stylelabel"></div>

<h4>Label</h4>

<p><code>GraphView</code> datas can add text descriptions, such as the data <code>setName(&#39; Hello ht &#39;)</code>, the <code>Hello HT</code> text appears below the data.
The chapter <code>position</code> <a href="#ref_styleposition">Example</a> text message is not set by <code>setName</code>, but by <code>setStyle(&#39; label &#39;, &#39; Hello HT &#39;);</code> <code>GraphView.getLabel</code> function decides the final display of label:</p>

<pre><code>getLabel: function (data) {
    var label = data.getStyle(&#39;label&#39;);
    return label === undefined ? data.getName() : label;        
},</code></pre>

<p>Through the above code, <code>style</code> on the <code>label</code> property priority is higher than <code>name</code> attribute, can overload <code>GraphView.getLabel</code> function to change the text acquisition logic.</p>

<p><code>*View.getLabel</code> is designed to run through all of the <code>HT</code> components in a similar way to <code>ListView</code>, <code>TreeView</code>, <code>TabView</code>, etc., what is the different is that the default implementation logic for other components is to return the <code>data.toLabel()</code> value, which is the default implementation of <code>Data#toLabel()</code>.</p>

<pre><code>toLabel: function (){
    return this._displayName || this._name;
}  </code></pre>

<p>The above code shows that the <code>displayName</code> attribute has a higher precedence than <code>name</code> and <code>style</code> attributes are generally used only <code>GraphView</code> components, and other components do not consider <code>label</code> attributes on style, when <code>tree</code> and When <code>Graphview</code> share the same <code>DataModel</code> data model, you need to display different text on <code>Tree</code> and <code>GraphView</code>, in which case you can set the <code>label</code> attribute on <code>style</code>, or set the <code>displayName</code> property can achieve a different effect, or directly overload the component&#39;s <code>getLabel</code> function custom logic.</p>

<p>In addition to provide the <code>label.*</code> properties, <code>HT</code> also provide the <code>label2.*</code> property to meet the need for an data to display double label, the <code>label2</code> and <code>label</code> attribute meaning is one-to-one correspondence:</p>

<ul><li><code>label</code> Label, is NULL in default</li><li><code>label.font</code> The font of label, such as: <code>10px sans-serif</code></li><li><code>label.color</code> The color of label, can be customized by <code>GraphView.getLabelColor(data)</code></li><li><code>label.background</code> The background color of label, is empty in default, can be customized by <code>GraphView.getLabelBackground(data)</code></li><li><code>label.opacity</code> The opacity of label, the value range from 0 to 1</li><li><code>label.position</code> The position of label, reference <a href="#ref_styleposition">position</a></li><li><code>label.offset.x</code> The horizontal offset of label means horizontal offset along the edge direction for <code>Edge</code></li><li><code>label.offset.y</code> The vertical offset of label means horizontal offset along the edge direction for <code>Edge</code></li><li><code>label.rotation</code> The rotation of label, such as <code>setStyle(&#39;label.rotation&#39;, Math.PI/2)</code></li><li><code>label.max</code> The default value is NULL means do not limit the max width, if it is opposite value, then the text max display width will not beyond this value</li><li><code>label.align</code> The align of label, can be set to <code>left</code>, <code>center</code> or <code>right</code></li><li><code>label.position.fixed</code> The default value is <code>false</code>, it make an effect in the label of <code>Edge</code> while it set as <code>true</code>, to keep label in upper position or lower position of edge direction</li><li><code>label.scale</code> The scale of label, the default value is <code>1</code>, refer to <a href="../../core/position/ht-position-guide.html#ref_offsetrotation">Position Manual</a></li></ul>

<p><iframe src="examples/example_label.html" style="height:300px"></iframe></p>

<div id="ref_stylenote"></div>

<h4>note</h4>

<p><code>note</code> generally as the annotation of the data, there are hints and warnings, in the form of bubbles, can also be reduced to merge into a small callout.
The <code>note</code> parameter controlled by the <code>style</code> of the <code>note.*</code> related property, and <a href="#ref_stylelabel">Label</a>, in order to meet the requirements of an data double annotation, provides a <code>note2.*</code> second callout parameter:</p>

<ul><li><code>note</code> The content of note, can be customized by <code>GraphView.getNote(data)</code></li><li><code>note.expanded</code> Whether to expanded the note or not, the default value is <code>true</code>, set to <code>false</code> to merge indent small callouts</li><li><code>note.font</code> The font of note, such as: <code>10px sans-serif</code></li><li><code>note.color</code> The color of note</li><li><code>note.background</code> The background of note, can be customized by <code>GraphView.getNoteBackground(data)</code></li><li><code>note.border.width</code> The width of note&#39;s border, the default value is <code>1</code></li><li><code>note.border.color</code> The note&#39;s border color</li><li><code>note.opacity</code> The opacity of note, the value range from <code>0</code> to <code>1</code></li><li><code>note.position</code> The position of note, reference <a href="#ref_styleposition">position</a></li><li><code>note.offset.x</code> The horizontal offset of note</li><li><code>note.offset.y</code> The vertical offset of note</li><li><code>note.max</code> The default value is NULL means do not limit the max width, if it is positive value, then the label&#39;s max display width should not beyond this value</li><li><code>note.align</code> The align of note, can be set as <code>left</code>, <code>center</code> or <code>right</code></li><li><code>note.toggleable</code> The default value is <code>true</code> means it allowed double click to expand and merge, if it&#39;s <code>false</code>, then it won&#39;t response double click</li><li><code>note.scale</code> The scale of note, the default value is <code>1</code>, refer to <a href="../../core/position/ht-position-guide.html#ref_offsetrotation">Position</a></li></ul>

<div id="ref_styleicon"></div>

<h4>icon</h4>

<p> <code>icon</code> and <code>note</code> are similar, show around the data, there are hints and warnings of the role, the different is the <code>notes</code> shows the text, and <code>icon</code> shows the picture or 
<a href="../vector/ht-vector-guide.html">Vector</a>.</p>

<ul><li><code>addStyleIcon(name, icons)</code> Adds a set of <code>icon</code>, <code>name</code> parameter to specify the name of this set <code>icon</code>, <code>icons</code> parameter describes <code>icon</code> content</li><li><code>removeStyleIcon(name)</code> Removes <code>icons</code> corresponding to <code>name</code> parameter</li></ul>

<p>Above is <code>ht.Data</code> provides the operation <code>icon</code> function, the two methods actually modify the <code>style</code> of the <code>icons</code> attribute, the user can also set and get <code>icons</code> through <code>setStyle(&#39;icons&#39;, icons)</code> and <code>getStyle(&#39;icons&#39;)</code>.</p>

<p><code>addStyleIcon</code> method the second parameter <code>icons</code> is a <code>JSON</code> format object whose properties are as follows:</p>

<ul><li><code>names</code> An array that contains multiple strings, each corresponding to a picture or vector</li><li><code>visible</code> Whether the group picture is displayed</li><li><code>for3d</code> Components that represent this group of pictures for <code>Graph3dView</code> display and are not displayed in the <code>GraphView</code> component</li><li><code>position</code> Specified the position of <code>icons</code>, refer to <a href="#ref_styleposition">Position</a></li><li><code>offsetX</code> The <code>icons</code> position on the <code>position</code> basis, the horizontal offset <code>offsetX</code></li><li><code>offsetY</code> The <code>icons</code> position on the <code>position</code> basis, the vertical offset <code>offsetY</code></li><li><code>direction</code> The preferred value is <code>west</code>, <code>east</code>, <code>north</code>, <code>south</code>, which specifies the direction of <code>icons</code></li><li><code>keepOrien</code> When the line direction changes, <code>icons</code> automatically adjusts the direction to maintain the best reading effect(such as text), and this property is <code>true</code> to prevent automatic direction adjustment</li><li><code>gap</code> Specified the gap of <code>icon</code></li><li><code>rotation</code> Specified the rotation of <code>icon</code></li><li><code>rotationFixed</code> This parameter allows the icon to rotate regardless of the <code>Edge</code> connection angle, only according to <code>rotation</code>, the default value is <code>false</code></li><li><code>width</code> Specified each icon&#39;s width</li><li><code>height</code> Specified each icon&#39;s height</li><li><code>opacity</code> The range of value from <code>0</code> to <code>1</code></li><li><code>stretch</code> Icon drawing stretch mode, the default value is <code>fill</code>, can be set as <code>uniform</code> and <code>centerUniform</code></li><li><code>positionFixed</code> The default value is <code>false</code>, if it&#39;s <code>true</code>, it has an effect on type of <code>Edge</code>, keep the icon in the upper position or lower position</li></ul>

<p><iframe src="examples/example_styleicon.html" style="height:200px"></iframe></p>

<pre><code>edge.addStyleIcon(&quot;flags&quot;, {
    position: 17,
    direction: &#39;east&#39;,
    offsetX: -26,
    gap: 10,
    names: [&#39;China&#39;, &#39;Spain&#39;, &#39;USA&#39;]
});

edge.addStyleIcon(&quot;arrow1&quot;, {
    position: 2,
    width: 50,
    height: 25,
    keepOrien: true,
    names: [&#39;arrow&#39;]
});

edge.addStyleIcon(&quot;arrow2&quot;, {
    position: 4,
    width: 50,
    height: 25,
    positionFixed: true,
    names: [&#39;arrow&#39;]
}); </code></pre>

<p>In the above example, the <code>Edge</code> object is set by the <code>addStyleIcon</code> way, with a row of three flag icons of <code>[&#39; China &#39;, &#39; Spain &#39;, &#39; USA &#39;]</code>, and two <code>arrow</code> vector icons, which can be replaced by the following code: </p>

<pre><code>edge.setStyle(&#39;icons&#39;, {
    flags: {
        position: 17,
        direction: &#39;east&#39;,
        offsetX: -26,
        gap: 10,
        names: [&#39;China&#39;, &#39;Spain&#39;, &#39;USA&#39;]
    },
    arrow1: {
        position: 2,
        width: 50,
        height: 25,
        keepOrien: true,
        names: [&#39;arrow&#39;]
    },
    arrow2: {
        position: 4,
        width: 50,
        height: 25,
        positionFixed: true,
        names: [&#39;arrow&#39;]
    }
});</code></pre>

<p>In the example <code>node1</code> <code>node1.setStyle(&quot;icons&quot;, ...)</code> registers a three-vector orb, by overloading <code>graphView.onDataClicked</code> and combining <code>graphView.getIconInfoAt</code> functions to get clicked on the specific <code>icon</code> icons to change the line arrow color</p>

<pre><code>graphView.onDataClicked = function (data, e) {
    if(data === node1){
        var info = this.getIconInfoAt(e, data);
        if(info){
            edge.a(&#39;arrow.color&#39;, info.name.comps[0].background);
            node1.a(&#39;select.index&#39;, info.index);
        } 
    }                                       
};</code></pre>

<div id="ref_stylegroup"></div>

<h4>group</h4>

<p>Group type datas can be set to the following styles:</p>

<ul><li><code>group.type</code> The default is NULL, the top section <code>title</code> text title while group expanded, the lower part is divided into rectangular fill, also can be set to <code>oval</code> and <code>rect</code> and other shapes, optional values refer to <a href="#ref_styleshape">Shape</a>, the <code>label</code> is no longer displayed as <code>title</code> when set to non-empty type and is displayed and controlled using the normal <code>label</code> style attribute</li><li><code>group.image</code> The default value is NULL, rendering picture effects for displaying group expansion</li><li><code>group.image.stretch</code> Group expand picture stretch mode, the default value is <code>fill</code>, it can also be <code>uniform</code> or <code>centerUniform</code></li><li><code>group.repeat.image</code> Fill the repeat image to the background, notice that the image in there does not supported vector</li><li><code>group.position</code> The position of group, the default value is <code>17</code> means in the center</li><li><code>group.toggleable</code> Decided whether allow double click to switch the group to expand or merge or not, the default is <code>true</code></li><li><code>group.padding</code> The padding between the four boundary and the child data after expand the group, the default value is <code>8</code></li><li><code>group.padding.left</code> The padding between the left boundary and the child data after expand the group, the default value is <code>0</code></li><li><code>group.padding.right</code> The padding between the right boundary and the child data after expand the group, the default value is <code>0</code></li><li><code>group.padding.top</code> The padding between the top boundary and the child data after expand the group, the default value is <code>0</code></li><li><code>group.padding.bottom</code> The padding between the bottom boundary and the child data after expand the group, the default value is <code>0</code></li><li><code>group.title.font</code> The <code>title&#39;s</code> font after expand the group, only works while <code>group.type</code> is empty</li><li><code>group.title.color</code> The color of <code>title</code> after expand the group, only works while <code>group.type</code> is empty</li><li><code>group.title.background</code> The background of <code>title</code> after expand the group, only works while <code>group.type</code> is empty</li><li><code>group.title.align</code> The align of <code>title</code> after the group expand, the default value is <code>left</code>, and can be set as <code>center</code> or <code>right</code></li><li><code>group.depth</code> The depth of border after expand the group, <code>0</code> means plane effect, the positive value means the convex effect, the negative value means the dent effect, the default value is <code>1</code></li><li><code>group.background</code> The background after expand the group   </li><li><code>group.gradient</code> The gradient after expand the group, selectable value refer to <a href="#ref_styleshape">Shape</a></li><li><code>group.gradient.color</code> The gradient color after expand the group</li><li><code>group.border.color</code> The border color after expand the group</li><li><code>group.border.width</code> The border width after expand the group</li><li><code>group.border.pattern</code> The border pattern is dash after expand the group, type <code>Array</code>, such as <code>[5, 5]</code></li><li><code>group.border.cap</code> The border cap style after expand the group, selectable value <code>butt|round|square</code></li><li><code>group.border.join</code> Group expanded border creates a corner type when two lines intersect, optional parameters are <code>bevel|round|miter</code></li></ul>

<p><iframe src="examples/example_stylegroup.html" style="height:370px"></iframe></p>

<div id="ref_styleedge"></div>

<h4>edge</h4>

<ul><li><code>edge.type</code> the type of string, decided the style of edge: <ul><li><code>undefined</code> The default value, represents the connection into a straight line, many times automatically into groups, from the ring to draw a circle</li><li>The direction of the <code>points</code> type will be determined by the <code>edge.points</code> property to draw a polyline or curve</li><li>More line types and how to customize the connection type, refer to <a href="../../plug-in/edgetype/ht-edgetype-guide.html">Edgetype</a>. </li></ul></li><li><code>edge.points</code> The default value is NULL, can be set as type <code>ht.List</code> or <code>Array</code> <code>{x:10, y:20}</code></li><li><code>edge.segments</code> Describe the type of <code>ht.List</code> or <code>Array</code>, should be considered between the start point and the end point, the array element is integer: <ul><li>1: <code>moveTo</code>, occupied <code>1</code> point, represents the start of a new path</li><li>2: <code>lineTo</code>, occupied <code>1</code> point, represents link the last point to this point</li><li>3: <code>quadraticCurveTo</code>, occupied <code>2</code> points, the first point is the control point of the carve line, the second point is the end of the carve line</li><li>4: <code>bezierCurveTo</code>, occupied <code>3</code> points, the first and the second are the control points of the curve line, and the last point is the end of the curve line</li><li>5: <code>closePath</code>, do not occupied any point, represents this path drawing is done, and close the shape to the begin point</li></ul></li></ul>

<p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAL4AAAC+CAMAAAC8qkWvAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABgUExURczMzLu7u3d3d9vb2z09PaysrO84AENDQ11dXZqamomJiePj49LS0vHx8SoqKu7u7sTExOnp6REREcnJyfT09P////7+/vf39/j4+P39/fr6+vb29vv7+/z8/Pn5+TJfoSWZtLYAAAXNSURBVHja7N0Lc+IqFAfwwIELeZHw2KxW5X7/b3khsa9d7VbAJNzlTCdBq83v/CXV6ZRJ9SPrqgq/8Au/8Au/8DPlWwldtevqQNp7fAMgzWnXZSSAuc23IE8ZlAR7ky/hlEWBvMnPI3wf/01+Z/Lgm+4mvzplUlXhF37hF36m/H9cFf6W/E0+aNzh/3ysZn6lQf5cudLwAfy2X7+DJPxe96+jlTtIwl/C36KDFPz38FfvIAX/U/jrdpCA/1v4K3aQgP85/BHVrtA4tmt0EM9/Dx9qrpQg3BUTwg05ruG5HcTzX8OnTHH68YUYxhpzoXijn9dBNP8aPhKM3vx+S7FQrKHP6SCaP4cvOdFfPMa1QBTD9Zi8g1j+HP4o6j8+cEBzCyhtB7F8Hz4V3fceLClXBNOEHUTyffg1Gx44IMJC8DpVB5F8F37N+weP2TVuGjWd3JzvwkekDzhsWzM3jUa5LR+gE6GEwZ0IIraDKH6vpahijo5iO4jiA/A69uVHOKaDGH6va57i18foOuBhHdzhH75TMKr2kKauHTz8vAi+0Qwd0tXcAZL/ulqFDw0/pC3t3tLW4ptRwSF9rcUH3hzy5Rsq+sPmFcwHRg/58k3DDhnzQXQZ8w3Gh4z5lZIZ881zfmmuxR/VkDF/N+GH8dEe3rGC+YbVh4z5VJid8+0XZQi1e6kAPhU2Y/6ewg/g7yn8x/lJwyer8ymxT+Frq9Xz+Yag5/CJ7+Dp/PDwMcHc1tp/zUPJSU0sxo0fYK2IrS3wZWrOe+yeYzmjfi8p4TKeHx4+xhYpi5H7WobuJ2Hit9eBewGkAOv5y97fY5W0HIAhLjWP5fu/AsTME7Lwl+Hb9n2A8PLYZU+ud2vOERaEqC35HFmpbOPmhF6GQlo0894GBNzGT5FlT6xkS29uXjXW0i35khAm3LQg7DrUgvAl3deBUwoyBz/vGyHQzEe1P3NEvSU/ya/5FJ95/lp+nTd/tc88L5lU4Rd+4Rd+4Rd+4Rd+4Rd+4T/OP2dShV/4hV/4ufL9kstnP6PwC//DM4a98KENOPRQDfvgm8qet/En4QeFn8Sfgh8Yfgr/Hf7lkYL2ElrOf4mpBHxTvVy28ifgfyP8Smu/ildr3ab1x/O/Ch8oZkIpJZZVvOJ1FW+Xyh/Pvxe+X3zsqFT3n+62mjbcL4HtUvij+bfD7/zKRHp/Vkm/ihfraH80/0b4shECd398ZtuQ68PC/bH838PXXH3Dfu0dC0Zj/LH8X8NHhNTmEQBigob7I/m/hO/w6GFCxwUK9UfyP4UPLAA/N8AYhPnj+B/DN9jPgsCiAssQfxz/Q/hIYHMJL9MIFOCP4r+Hf8IELnEFonncH8V/C78TzSW6TpihYUX+W/hUdJcURRUdkvCP3ylo590L5uaYplpR9w89IYJvqrPfDaw5Jque+f+XW4W/hN8KdExYdC3+Ej6I7pi01uLP4XcCjlny5/Ar0R43rWC+Dx+EPObJ9+G3auPsw/kufJn6rF2P78I3RB9z5bvweX3Mle/Cb/AxWz60iL1kyzcViOGYLR9ago7Z8k21j4kfyAdEfu6bP31Rp1HAtJMK4ANupnz5JyrOGfOBjFO+/BPmCQ6L8Eb8Sg3P4KtqFf6J19Mz+NU66aM05y3inNBp8JuKEIKn+noDE//i+vW6k+sQT369rtsPlPAhnn9yPzYJX8DAEEMTd4caCExkmm8g4r87uDcW6u5yX26qLut1h4rH8v1fAdKccn7yIKwIEW4gqhnqbyyTatnOfLepXtfr7ojvWLh2E6WCaT6bHNTfWODgvju4uwbm+RMhU+3eKemO+Mzn7qf90LjYfcrzjesp7dfrTn697sxHrj+/Xnc//A0/8xR+4Rd+4Rf+/4+f4YVFC7/wC7/wC/+v5XfnPPTn2xeohT4Pfn/78sCyzYPf3r44s4UhB/1w59LYPwy0/c7n/7lv712YPPfLwmdYhV/4hV/4hV/4GdV/AgwAgILrVTD67DAAAAAASUVORK5CYII="></p>

<ul><li><code>edge.color</code> The color of the line</li><li><code>edge.width</code> The width of the line, the default value is <code>2</code></li><li><code>edge.offset</code> The distance between the top of the edge and the center of the data, the default value is <code>20</code></li><li><code>edge.group</code> The line group can be classified by this attribute, to achieve the independence expand effect, the default value is <code>0</code></li><li><code>edge.gap</code> The gap between the group line, the default value is <code>12</code></li><li><code>edge.toggleable</code> Decided whether double click can switch expand and merge or not, the default value is <code>true</code></li><li><code>edge.center</code> Decided whether the edge come together to the center or not, the default value is <code>false</code>  </li><li><code>edge.pattern</code> Display dashed edge style, type <code>Array</code>, such as <code>[5, 5]</code></li><li><code>edge.expanded</code> Judging the status is expand or merge, only can be read on common, can call <code>Edge.toggle()</code> Synchronous changing other line parameter</li><li><code>edge.cap</code> The style of the edge&#39;s cap, selectable parameter <code>butt | round | square</code>
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAcYAAABMCAMAAADA4OcRAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAD5UExURXt5/3t593t5e9bX1rW2tf/Pzv/HxnNxc8aenEIwMefn50I4OYSG/8bHxr2WlDEwMb2enGNhY1JRUqWmpYSGhJSWlDk4Ob2+ve/v74Rpa5yenFpZWggICBgYGBAQEM7PznthYyEYGO++vf/X1lJBQs6mpYyOjJx5e0pJSvfHxhAICN7f3oRhY//v762urTEgIWNRUnNZWrWOjPfn5ykoKWNJSpRxc1pJSpye//f39yEgIYxxc/fPztampf/3996ure/Hxr2+9ykgIWtpazkwMfe+vWtZWpyGhJR5e/fX1lpBQue+vffv762OjM6urdaurf/n5wAAAP///95+UUgAAAluSURBVHjaYmBkGIVDHloABBADQ9AoGPKAESCAGBhHA2HoAwaAAGLgGQ2EYRCNAAE0mhuHRTQCBNBo3Tgs6kaAABrNjcMiNwIE0GjdOCyiESCARnPjsIhGgAAarRuHRd0IEECjuXFY5EaAABqtG4dFNAIE0GhuHBbRCBBAo3XjsKgbAQJoNDcOi9wIEEB460axQJoCchys5ywmG0hjIKahpk2ca5gCBwgwoUUjQADhzY2DLRolmGToFU5i4kMqGgECCG/dOMiiUVyWniGlITF0opERIICGUG6UonNQmUgMndwIEEBDp26UontYEY7HQRONAAE0ZHKj/AAEltiQiUaAABoydaPsQISW2lCpGwECaKjkRqkBCS0ZiSGSGwECaKjUjbIDE1xSQyQaAQJoiORG+QEKLo0hEo0AAYSnbtRV9lO3oplD5BQ4lHWJjkY1hEZ9IwEjfdoFkCQfhw7RSQ0pGuXUOXxVaOcsFQ4+STncdSNAAOHOjbpsrGys9nK0chcf0HS2AGKjURquT1EJqE9JkVbOMgP52hrBlyc2GlVB/qFZPJqDTFfFnRsBAgh33agM1MnKasshwMFHA8wBNBtoPrHRiCjeJcH6vPlo5C42sLsQiVecyGg0BetT4qCRuzTB7uLGGY0AAYQ7NyqD0iWtIenRqADWp0kj97hCaE6So1GSHqHlgjMaAQIId93oCU5fCtycNIFe4LyuTHo0GoLTpQcnjdwFSh+sDoEkRyMn2D8cNHIVpz+4DFLEWTcCBBDu3GhpA9SpTrM6G5SrWH2IjUZEeGmpgmoJLVo5yxSU6s0RfG1i60Z1UJlKsyrbCdQiMMNdNwIEEJ5+o6WxOw2bXoGcksZ2ZPX+DRQMaOgsKx1JpMiQIb7DwSnpJkc7Z2kZ6nDj6XAABNAQ6TdqD1D/THqI9BsBAmiojKmKDUxwiQ+RMVWAABoqY6riAxJaYkFDJDcCBNCQmW/UGISZcfBEI0AADZn5RgmZAQ+sQRyNAAE0dNbiyNM9HqWHzlocgAAaQmtx5GUHWywOntwIEEBDaZ2qhDQdA0pGbygtcAQIoCG1TjVInF4RKasmETSUohEggPDWjVJMNAVkrXMXZ6I9kJIfRI7BCtCa0IwAATS6h2NY7OEACKDR/Y3DIhoBAmg0Nw6LaAQIoNH9jcMBMAIE0GhuHBa5ESCACNeNLCxAgpeFGNOYWZhp7WAuFl6y9IG1sbDQwklU8TW5/oJGI0AAEc6N4P4dO45OHjMXxBEQj7AEstA6GpkD2cnSB9ZG3s5YgimEGr4m11/QaAQIIMJ1o6AgnmiEWs4SCI5H3kBempcfgcLkRyPYK1QH1PE1mf6C1o0AAUQ4N7KzEx2NzIHMtI9GFvKjkZ2dNgUE88D5C5obAQKIcN0Ii0YuFmjxDS1IQRRzoCAzM3MQl2ggCzOQj+YhXqgOLiCDBa6JF8amwLtgu5khdQqsUoE4C1y8gwheeEUIVoIlGrlgrmJmgRkC1IgwkMRoZIZ7EmwE2DnQyoaLGdUaJPfDwggtGrEHHdBg7CmGASAAKVa0AjAIAh8KAiGich9W//8xm+dstT1ssKckyjtPtPC9GiFHaF5GefmqQHfgOh3wuYAldApTgmKSzUR2quISZ7F/dCGOoBBLE/UYGPHkY33Bt1oUGz1PTJBevjhKejOj0C3M778TiZqyuVsANcPoZfA8uPokDzWVAceTJrp9qDRJ11tmQ3imcReA9CpaARgEgYwFgRCHZB/W/v9j1hljLWIM9iIVoqd2Zh//jbFqc5Pd4F3GmY3PF0PJCeX97mE6e2vb7KjyswOhWsjSABGEQuYyIlBaxyGUcz9V6kuO1xDryj3Mss7Ve4JYHzc3OhzLmBKPAc8dUEI5BOpoJ2wCGksWxtS1+cyE6xUFtlMAEdlvhNaNQvyo0YhWNyIDsFKUaGWBhzUvpW27QH5IiQpziih6NIL9KgRytCA/ciWO6hzUaEXzJhl1L7JmjGiEAGGQCDM4pkHpBUyJoDU8RNFCkxcpqLnAyQ0jNwIEEJFjqlCHCIPsJSoauZBTNLBWYBdEisYgrG4hPdBgbUQhIfRohIcezB3o0SiKVCAAKyBRdv5ANG+SBoQCYRUjsoWo0cjLIswuBIlGSDAIgcoyZmb0aAN5ByPoYD4QxJbEGAACiLTcCHYWUdHIjEjt4JIeLRrZqRKNMGtBrsMRjbhsRMkgwJoRJRpZSG97cgHrOn52dC+iFKpgFSjRCJ8+ZMaRs5GCDiaKtc/AABBARNeN5EcjP6RxOVijkQVSwrFTFo3AaBJmBzXzcEUjF6R1w4ISjewECmikoMMbjYwAAURabgSX4sTVjfBykxnS0EKORi6K+rrIhSokOPgFSS5UEeUm1GvI0ShC7mgPeyAXrkIVWl+jRKMg9joYLowcdDAf8GNrRjMABBBJdSOkSQFubEGHoKBtcxbM9hM7JJy4uKBuQW7iiFDaYYZFCr8Q3CnQul8ELRqhLQ4u9GhkhiYzZljUITVxuMgoLJjhDRhkC6HjdEKIaBRFjkbYMB4X9tE9lKCD+hn7yB8DQABOrG0FYBCEDhSCQGJmH9b//8yOLVhbPWx7k5K8pZ3T624sQkDLsXliJBgg3LLGBCAdAdRJHhgnYSsnjwtCqWcZFczEfgD6dRnZ7XLVDjKos8nbk+QqOU2VMXBP4hCgYBC0fx1fYX70aEdg7K0yGvRJSp0ithSFOpYRdwe2ZWp+rQWHKY+pAzrPWFxz2u0QQETXjeywLjKkvywKSSLgOpgZ0kxA7/2AO8Sgvg5IjQg0Nwryw7qz1IjGIFAQBUIGHUDW8LOgF6rQFgxmvzFIGKQVGFQQBSzQQhXJmyQBUfBgh0gQtJ8vBB3qBrVrhHgRTRxhYZRoBGYJ0JAARqoGd/RBXW5E0AUFCglhBjKsbgQIIOLnG7kQ/XskZhAzbBCMGYsOZpgapIYPM3XHXZGGHXCZzMyFXyvCO6AxR2YyhyaQQoCZC5HSkByF1X3M2O2DCSP0gEbJcLmNASCA6LsWh5n2M1kUAXbqzWQFUnsYHp+BDAABRN/Z/9FopFE0AgQQfdficLHzDupoFKZe2LMLUzuJ4TGQESCARtfiDAfAABBAo+tUh0U0AgTQaG4cFtEIEECj61SHA2AECKDR3DgsciNAAI3WjcMiGgECaDQ3DotoBAig0bpxWNSNAAE0mhuHRW4ECCAGCwYGRp5RPLSxI0CAAQAszN0jZYiEZQAAAABJRU5ErkJggg=="></li><li><code>edge.join</code> The type of the corner while two lines join together, selectable parameter <code>bevel|round|miter</code>
<img src="data:image/png;base64,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"></li><li><code>edge.source.position</code> The default value is <code>17</code>, the begin point is the related position of the begin data</li><li><code>edge.source.offset.x</code> The default value is <code>0</code>, the horizontal offset of the begin point</li><li><code>edge.source.offset.y</code> The default value is <code>0</code>, the vertical offset of the begin point</li><li><code>edge.target.position</code> The default value is <code>17</code>, the end point is the related position of the end data</li><li><code>edge.target.offset.x</code> The default value is <code>0</code>, the horizontal offset of the end point</li><li><code>edge.target.offset.y</code> The default value is <code>0</code>, the vertical offset of the end point </li><li><code>edge.dash</code> Whether display the dash line or not, the default value is <code>false</code></li><li><code>edge.dash.pattern</code> The type of dash line, the default value is <code>[16, 16]</code></li><li><code>edge.dash.offset</code> The offset of the dash line, the default value is <code>0</code></li><li><code>edge.dash.color</code> The color of the dash line </li><li><code>edge.dash.width</code> The width of the dash line, the default value is empty means use the value of the <code>edge.width</code> </li><li><code>edge.dash.3d</code> Whether to display the dash line&#39;s <code>3d</code> effect, the default value is <code>false</code></li><li><code>edge.dash.3d.color</code> The color of the dash line&#39;s <code>3d</code> effect, if it&#39;s empty and the default value is white, the edge&#39;s middle part is this color</li><li><code>edge.dash.3d.accuracy</code> The accuracy of the <code>3d</code> dash line, the value is more small the <code>3d</code> gradient effect is more good but influence the performance, it do not need to change in common</li></ul>

<blockquote><p>For the line of the <code>edge.type</code> is <code>points</code>, while <code>edge.center</code> is <code>false</code>, and the <code>edge.offset</code> is <code>0</code>, the begin of the line and the end of the line will cut by the relative node&#39;s rectangle boundary.</p></blockquote>

<p><iframe src="examples/example_styleedge.html" style="height:200px"></iframe></p>

<div id="ref_recommendation"></div>

<h2>Recommand</h2>

<div id="ref_book"></div>

<h3>Books</h3>

<p><a href="http://www.amazon.com/Object-Oriented-JavaScript-high-quality-applications-libraries/dp/1847194141">Object-Oriented JavaScript</a><br/>The development of <code>HTML5</code> applications, especially enterprise applications, <code>JavaScript</code> almost occupies the bulk of the code, so the need to master the <code>JS</code> syntax, internal classes and functions, and other object-oriented language different class inheritance patterns, as well as <code>DOM</code> basic operations, this book is a good choice for this knowledge.</p>

<p><img src="data:image/jpeg;base64,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"></p>

<p><a href="http://www.amazon.com/Pro-JavaScript-Design-Patterns-Object-Oriented/dp/159059908X/">Pro JavaScript Design Patterns</a> <br/>Design patterns have been widely used in a variety of software development, but many familiar design patterns, with the implementation of the JS language and <code>Java</code> and <code>C#</code> and other traditional object language implementation of the way there are many differences, resulting in many beginners often don&#39;t know how to do, or the design of the model is not the best <code>JS</code> practice, the book on the interface, single class, inheritance, factories and other modes of the <code>JS</code> language can be selected to achieve the way.</p>

<p><img src="data:image/jpeg;base64,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"></p>

<p><a href="http://www.amazon.com/CSS3-Missing-David-Sawyer-McFarland/dp/1449325947/">CSS3: The Missing Manual</a> <br/>HT products provide a wealth of enterprise application components, to make a routine case only with JS is enough, but the more beautiful interface effect can not be separated from the <code>CSS</code> technology to grasp and use of HT product depth customization expansion is often inseparable from the <code>CSS</code> technology. The book&#39;s 12th edition on the rave, the third version of the <code>HTML5</code> has been revised, deleted previously for the old version of the <code>IE</code> browser chapters, added to the <code>CSS3</code> new features of the chapter space, is a classic reading that worth front-end programmers to read.
<img src="data:image/jpeg;base64,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"></p>

<p><a href="http://www.amazon.com/Responsive-Web-Design-HTML5-CSS3/dp/1849693188/">Responsive Web Design with HTML5 and CSS3</a> <br/><code>Responsive Web Design(RWD)</code> is a new area of front-end technology, the first originator of the concept of &quot;responsive Web designs&quot; is <a href="http://alistapart.com/article/">Responsive Web Designer</a> responsive-web-design).
A good page not only in the <code>PC</code> browser, but also need to consider the user&#39;s use of mobile phones or tablets to browse the rendering effect, so programmers and designers need to consider the different screen size and other complex situations, to maintain the desktop mouse and mobile device gesture operation has a better user experience, this book has played a role, can let you understand the current common <code>RWD</code> design skills, began to focus on this aspect of technology and use.  </p>

<p><img src="data:image/jpeg;base64,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"></p>

<div id="ref_subscribe"></div>

<h3>Describe</h3>

<p>We recommend reading books to system learning a technology, but it takes months or even years to write an excellent classic book, and it is highly recommended that three weekly subscriptions for front-end technology be developed for the latest technical information in order to keep abreast of the front-line programmers, and just look at the three weekly emails, and will not miss the interesting events that are taking place on earth&#39;s front-end development field:</p>

<ul><li><a href="http://javascriptweekly.com/">JavaScript Weekly</a>   </li><li><a href="http://html5weekly.com/">HTML5 Weekly</a>     </li><li><a href="http://appendto.com/modern-web-observer/">The Modern Web Observer</a></li></ul>    <hr id="contact" style="margin: 20px 0 1px 0">
    <a href="http://www.hightopo.com"><img class="logo"></a>Welcome to contact us <a href="mailto:<EMAIL>"><EMAIL></a>
</body>
</html>
