<!DOCTYPE html>
<html>
    <head>
        <title>Shape Type</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script>   
        <script>
            function init() {
                dataModel = new ht.DataModel();
                graphView = new ht.graph.GraphView(dataModel);
                view = graphView.getView();

                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function(e) {
                    graphView.invalidate();
                }, false);
                
                ht.Default.setImage('shape', {
                    width: 100,
                    height: 50,
                    comps: [
                        {
                            type: 'shape',
                            borderWidth: 2,
                            borderColor: '#34495E',
                            background: '#40ACFF',
                            gradient: 'spread.vertical',
                            gradientColor: 'white',
                            points: [5, 25, 70, 25, 70, 5, 95, 25, 70, 45],
                            segments: [
                                1, // moveTo [5, 25]
                                2, // lineTo [70, 25]
                                1, // moveTo [70, 5]
                                2, // lineTo [95, 25]
                                2, // lineTo [70, 45]
                                5] // closePath to [70, 5]
                        }                          
                    ]
                });

                var node = new ht.Node();
                node.setPosition(80, 80);
                node.setImage('shape');
                dataModel.add(node);                 

                ht.Default.setImage('dash', {
                    width: 220,
                    height: 150,
                    comps: [
                        {
                            type: 'shape',
                            points: [10, 110, 10, 10, 210, 10, 210, 110],
                            segments: [1, 4],
                            background: 'yellow',
                            gradient: 'linear.north'
                        },
                        {
                            type: 'shape',
                            shadow: true,
                            points: [
                                30, 10,
                                30, 110,
                                30, 60,
                                90, 60,
                                90, 10,
                                90, 110,
                                130, 10,
                                190, 10,
                                160, 10,
                                160, 110
                            ],
                            segments: [
                                1, 2, 1, 2, 1, 2,
                                1, 2, 1, 2
                            ],
                            borderWidth: 10,
                            borderColor: '#1ABC9C',
                            borderCap: 'butt',
                            dash: true,
                            dashPattern: [8]
                        },
                        {
                            type: 'shape',
                            points: [
                                10, 130,
                                35, 120,
                                60, 130,
                                85, 140,
                                110, 130,
                                135, 120,
                                160, 130,
                                185, 140,
                                210, 130
                            ],
                            segments: [
                                1, 3, 3, 3, 3
                            ],
                            borderWidth: 10,
                            borderColor: '#3498DB',                            
                            dash: true,
                            dashPattern: [8, 4, 4, 4],
                            dash3d: true,
                            dash3dColor: 'red'
                        }
                    ]
                });

                var node = new ht.Node();
                node.setPosition(270, 90);
                node.setImage('dash');
                dataModel.add(node);

                graphView.setEditable(true);
            }


        </script>
    </head>
    <body onload="init();">
    </body>
</html>
