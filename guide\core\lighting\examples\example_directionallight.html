<!DOCTYPE html>
<html>
    <head>
        <title>Directinallight</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0;
                margin: 0;                
            }            
            .main {
                background: black;
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;                
            }
            .formpane {
                top: 10px;
                right: 10px;   
                background: rgba(230, 230, 230, 0.5);
            } 
        </style>    
                                       
        <script src="../../../../lib/core/ht.js"></script> 
        <script src="../../../../lib/plugin/ht-modeling.js"></script> 
        <script src="../../../../lib/plugin/ht-obj.js"></script> 
        <script src="../../../../lib/plugin/ht-form.js"></script>  
        
        <script src="util.js"></script> 
        <script src="scooter_obj.js"></script> 
        <script src="scooter_mtl.js"></script>         
                
        <script>
                                    
            function init(){                                                                                                                                              
                dataModel = new ht.DataModel();
                g3d = new ht.graph3d.Graph3dView(dataModel);    
                g3d.setEye([300, 700, 700]);
                g3d.setHeadlightRange(2000);
                
                createModel();
                
                redLight = new ht.Light();
                redLight.s({
                    'light.type': 'directional',
                    'light.color': 'red',
                    'light.range': 1500
                });
                redLight.p3(-250, 250, 250);
                dataModel.add(redLight);                 
                
                center = new ht.Node();
                center.s3(5, 5, 5);
                center.s({
                    '3d.selectable': false,
                    'shape3d': 'sphere',
                    'shape3d.color': 'white'
                });
                dataModel.add(center);  
                
                ray = new ht.Edge(redLight, center);
                ray.s({
                    'edge.width': 1,
                    'edge.color': 'red',
                    'edge.offset': 0,
                    'edge.gradient.color': 'white'
                });
                dataModel.add(ray);  
                
                view = g3d.getView();
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    g3d.iv();
                }, false);                                
                
                createFormPane();
                formPane.v('floor', true);
                formPane.getView().className = 'formpane';
                document.body.appendChild(formPane.getView());                                                 
            }            
            
            function createFormPane(){                                                
                formPane = new ht.widget.FormPane();
                formPane.setWidth(200);
                formPane.setHeight(218);
                         
                createTexturePane();                
                createHeadlightPane();

                newLine('Directinallight');
                formPane.addRow([
                    {
                        checkBox: {
                            label: 'Disable red light',                            
                            onValueChanged: function(){
                                redLight.s('light.disabled', this.getValue());
                            }
                        }
                    }
                ], [0.1]);                
                formPane.addRow([
                    'Intensity',
                    {
                        slider: {                    
                            min: 0,
                            max: 3,                            
                            step: 0.1,
                            leftBackground: 'red',
                            value: redLight.s('light.intensity'), 
                            onValueChanged: function(){     
                                redLight.s('light.intensity', this.getValue());                                
                            }
                        }
                    }                        
                ], [0.1, 0.15]);                  
            }
            

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
