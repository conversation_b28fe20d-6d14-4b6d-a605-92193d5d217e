<!doctype html>
<html>
    <head>
        <title>HT for Web Shape Manual</title>
        <meta charset="UTF-8">    
        <meta name="viewport" content="user-scalable=yes, width=1024">
        <style type="text/css"> 
            h1, h2, h3, h4, h5, h6, p, blockquote {
                margin: 0;
                padding: 0;
            }
            body {
                font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif;
                font-size: 13px;
                line-height: 18px;
                color: #737373;
                background-color: white; 
                margin: 10px 13px 10px 13px;
            }
            table {
                margin: 10px 0 15px 0;
                border-collapse: collapse;
            }
            td,th {	
                border: 1px solid #ddd;
                padding: 3px 10px;
            }
            th {
                padding: 5px 10px;	
            }
            a {
                color: #0069d6;
            }
            a:hover {
                color: #0050a3;
                text-decoration: none;
            }
            a img {
                border: none;
            }
            p {
                margin-bottom: 9px;
            }
            h1, h2, h3, h4, h5, h6 {
                color: #404040;
                line-height: 36px;
            }
            h1 {
                margin-bottom: 18px;
                font-size: 30px;
            }
            h2 {
                font-size: 24px;
            }
            h3 {
                font-size: 18px;
            }
            h4 {
                font-size: 16px;
            }
            h5 {
                font-size: 14px;
            }
            h6 {
                font-size: 13px;
            }
            hr {
                margin: 0 0 19px;
                border: 0;
                border-bottom: 1px solid #ccc;
            }
            blockquote {
                padding: 13px 13px 21px 15px;
                margin-bottom: 18px;
                font-family:georgia,serif;
                font-style: italic;
            }
            blockquote:before {
                content:"\201C";
                font-size:40px;
                margin-left:-10px;
                font-family:georgia,serif;
                color:#eee;
            }
            blockquote p {
                font-size: 14px;
                font-weight: 300;
                line-height: 18px;
                margin-bottom: 0;
                font-style: italic;
            }
            code, pre {
                font-family: Monaco, Andale Mono, Courier New, monospace;
            }
            code {
                background-color: #fee9cc;
                color: rgba(0, 0, 0, 0.75);
                padding: 1px 3px;
                font-size: 12px;
                -webkit-border-radius: 3px;
                -moz-border-radius: 3px;
                border-radius: 3px;
            }
            pre {
                display: block;
                padding: 14px;
                margin: 0 0 18px;
                line-height: 16px;
                font-size: 11px;
                border: 1px solid #d9d9d9;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            pre code {
                background-color: #fff;
                color:#737373;
                font-size: 11px;
                padding: 0;
            }
            sup {
                font-size: 0.83em;
                vertical-align: super;
                line-height: 0;
            }
            * {
                -webkit-print-color-adjust: exact;
            }
            @media screen and (min-width: 914px) {
                body {
                    width: 854px;
                    margin:10px auto;
                }
            }
            @media print {
                body,code,pre code,h1,h2,h3,h4,h5,h6 {
                    color: black;
                }
                table, pre {
                    page-break-inside: avoid;
                }
            }                        
            iframe{
                width: 100%;
                border: 1px solid #34495E;
                margin: 0;                
            }            
            .logo{
                vertical-align: middle;
            }

        </style>        
        <script>
            function init() {
                var logoSrc = 'data:image/png;base64,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',
                    logos = document.querySelectorAll('.logo'),
                    i = 0;
                for (; i < logos.length; i++) {
                    logos[i].src = logoSrc;
                }
                
                var iframes = document.querySelectorAll('iframe'),
                    func = function (){};
                for (i=0; i < iframes.length; i++) {
                    var iframe = iframes[i];
                    
                    // a small hack to make it work on android
                    iframe.ontouchstart = func;
                    
                    var div = document.createElement('div');
                    div.innerHTML = '<a href="' + iframe.src + '" target="_blank">Run→</a>';
                    iframe.parentNode.insertBefore(div, iframe);
                }   
            }
        </script>    
    </head>
    <body onload="init();">
        <a href="http://www.hightopo.com"><img class="logo"></a>HT for Web Shape Manual
        <hr style="margin: 1px 0 20px 0">
<p>Index</p>

<ul><li><a href="#ref_overview">Overview</a></li><li><a href="#ref_2d">2D</a><ul><li><a href="#ref_basic">Basic Attribute</a></li><li><a href="#ref_style">Style Attribute</a> </li><li><a href="#ref_different">Pipeline difference</a>  </li><li><a href="#ref_host">Host</a>  </li></ul></li><li><a href="#ref_3d">3D</a><ul><li><a href="#ref_floor">Floor Type</a></li><li><a href="#ref_wall">Wall Type</a></li><li><a href="#ref_pipeline">Pipeline Type</a></li><li><a href="#ref_polyline">Spatial Pipeline</a></li><li><a href="#ref_csgshape">CSG Shape</a></li><li><a href="#ref_rotation">Rotation</a></li></ul></li></ul>

<hr/>

<div id="ref_overview"></div>

<h2>Overview</h2>

<p><code>ht.Shape</code> provided by <code>HT</code> is an extremely powerful type of data that can be displayed on both the <code>GraphView</code> and <code>Graph3dView</code> components, with its expanded subclass <code>ht.Polyline</code> can realize the function of 3D spatial pipeline, and the expand subclass <code>ht.CSGShape</code> can realize hollow function effect, this manual will be introduced in detail.</p>

<div id="ref_2d"></div>

<h2>2D</h2>

<div id="ref_basic"></div>

<h3>Basic Attribute</h3>

<p><code>ht.Shape</code> type is used on the <code>GraphView</code> component to render a polygon data, whose shape is mainly described by the properties of <code>points</code> and <code>segments</code>:
<code>points</code> is the vertex information in <code>ht.List</code> type array, vertex is the object whose format is like<code>{x:100, y:200}</code>; <code>segments</code> is the segment array information in <code>ht.List</code> type, the line segment is integer with scale of <code>1</code> to <code>5</code>, representing different vertex connection methods.</p>

<p>If <code>segments</code> is empty, the vertices in the <code>points</code> array are connected sequentially in array order, so the type of the line segment does not need to set the <code>segments</code> argument.
<code>segments</code> is mainly used to draw a curve, or there is a jump breakpoint, its <code>1~5</code> value meaning is described as follows:</p>

<ul><li>1: <code>moveTo</code> which occupies <code>1</code> point information, represents the starting point of a new path</li><li>2: <code>lineTo</code> which occupies <code>1</code> point information, represents the connection from the last point to this point</li><li>3: <code>quadraticCurveTo</code> occupies <code>2</code> point information, the first point as a curve control point, the second point as the end of the curve</li><li>4: <code>bezierCurveTo</code> occupies <code>3</code> point information, the first and second points as the curve control points, and the third point as the end point of a curve</li><li>5: <code>closePath</code> which does not occupy point information, represents the end of the path drawing and closes to the starting point of the path</li></ul>

<p><img src="data:image/png;base64,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"></p>

<p><code>ht.Shape&#39;s</code> main property function:</p>

<ul><li><code>getPoints()</code> and <code>setPoints(points)</code> Gets and sets the array of points, and the parameter is <code>Array</code> type to be internally converted into <code>ht.List</code>, clear all points for null</li><li><code>addPoint(point, index)</code> Inserts new points at the specified index position, if the <code>index</code> is empty then inserts into the last</li><li><code>setPoints(index, point)</code> Replaces new points at the specified index location</li><li><code>removePointAt(index)</code> Deletes the point at the specified index location</li><li><code>toPoints()</code> Copy a new <code>ht.List</code> type vertex array</li><li><code>segments&#39;s</code> Attribute is an <code>Array</code> or <code>ht.List</code> type that describes the point join style and the array element is an integer value:<ul><li>1: <code>moveTo</code> which occupies <code>1</code> point information, represents the starting point of a new path</li><li>2: <code>lineTo</code> which occupies <code>1</code> point information, represents the connection from the last point to this point</li><li>3: <code>quadraticCurveTo</code> occupies <code>2</code> point information, the first point as a curve control point, the second point as the end of the curve</li><li>4: <code>bezierCurveTo</code> occupies <code>3</code> point information, the first and second points as the curve control points, and the third point as the end point of a curve</li><li>5: <code>closePath</code> which does not occupy point information, represents the end of the path drawing and closes to the starting point of the path</li></ul></li><li><code>isClosePath()</code> and <code>setClosePath(true)</code> Gets and sets whether the polygon is closed, default to <code>false</code>, <a href="#ref_wall">Wall type</a> by setting the closure to seamlessly connect with the starting point</li><li><code>getLength(resolution)</code> Gets the polygon length, <code>resolution</code> for the number of curved differential slices, and if not input this parameter <code>HT</code> will adopt the system default value</li></ul>

<blockquote><p><code>ht.Shape</code> type of <code>points</code> value and <code>width</code>, <code>height</code> and <code>position</code> these three size and position information exist correlation, that means modifying <code>points</code> will affect <code>width</code>, <code>height</code> and <code>position</code> corresponding value, the same changes to <code>width</code>, <code>height</code> and <code>position</code> values also affect <code>points</code> information.</p></blockquote>

<div id="ref_style"></div>

<h3>Style Attribute</h3>

<p><code>ht.Shape</code> related <code>style</code> properties: </p>

<ul><li><code>shape.border.width</code> Border width, the default value of <code>0</code> means no borders are drawn</li><li><code>shape.border.color</code> Border color</li><li><code>shape.border.cap</code> Border terminal line cap style, optional parameter is <code>butt|round|square</code>
<img src="data:image/png;base64,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"></li><li><code>shape.border.join</code> Border when two lines intersect it will create a type of corner, optional parameter is <code>bevel|round|miter</code>
<img src="data:image/png;base64,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"></li><li><code>shape.border.pattern</code> Shows dashed style, <code>Array</code> type, for example <code>[5, 5]</code></li><li><code>shape.background</code> Background fill color, for <code>null</code> represents no filling background</li><li><code>shape.gradient</code> Gradient color type: <ul><li>The null representation does not draw an gradient color effect, and only fills the background with <code>shape.background</code> solid color.</li><li>Support type <code>&#39;linear.southwest&#39;,&#39;linear.southeast&#39;,&#39;linear.northwest&#39;,&#39;linear.northeast&#39;,
&#39;linear.north&#39;,&#39;linear.south&#39;,&#39;linear.west&#39;,&#39;linear.east&#39;,
&#39;radial.center&#39;,&#39;radial.southwest&#39;,&#39;radial.southeast&#39;,&#39;radial.northwest&#39;,&#39;radial.northeast&#39;,
&#39;radial.north&#39;,&#39;radial.south&#39;,&#39;radial.west&#39;,&#39;radial.east&#39;,
&#39;spread.horizontal&#39;,&#39;spread.vertical&#39;,&#39;spread.diagonal&#39;,&#39;spread.antidiagonal&#39;,
&#39;spread.north&#39;,&#39;spread.south&#39;,&#39;spread.west&#39;,&#39;spread.east&#39;</code></li></ul></li><li><code>shape.gradient.color</code> Background gradient color</li><li><code>shape.repeat.image</code> Fills a picture of a repeating background, noting that the image here does not support vectors</li><li><code>shape.dash</code> Shows dashed lines, the default is <code>false</code> </li><li><code>shape.dash.pattern</code> Dashed line style, the default is <code>[16, 16]</code></li><li><code>shape.dash.offset</code> Dashed line offset, the default is <code>0</code></li><li><code>shape.dash.color</code> Dashed line color</li><li><code>shape.dash.width</code> Dashed line width, default to null representative using the value of <code>shape.border.width</code> </li><li><code>shape.dash.3d</code> Whether the dashed line displayed the <code>3d</code> effect, the default is <code>false</code></li><li><code>shape.dash.3d.color</code> The colors of <code>3d</code> dashed line effect, default white for empty, the middle part of the line is the color when the <code>3d</code> effect is rendered</li><li><code>shape.dash.3d.accuracy</code> The accuracy of <code>3d</code> dashed line effect, the lower the value of <code>3d</code> gradient effect the better but affect performance, do not need to modify in general</li></ul>

<p><iframe src="examples/example_shape.html" style="height:250px"></iframe>   </p>

<div id="ref_different"></div>

<h3>管线差异</h3>

<p><code>ht.Polyline</code> type inherits from <code>ht.Shape</code>, its express more similar line features with <code>ht.Edge</code>, so the <code>label</code>, <code>note</code> and <code>icons</code>, etc., attached parts position and direction similar to line type will consider the angle of polyline, from the following examples can only see the differences between the two accessories placed.</p>

<p><iframe src="examples/example_shapepolyline.html" style="height:270px"></iframe>  </p>

<p>From the example above we can find <code>ht.Polyline</code> turn off the rotation function <code>setRotation</code>, see more differences in <a href="#ref_polyline">Spatial Pipeline</a> chapter</p>

<div id="ref_host"></div>

<h3>Host</h3>

<p><code>ht.Node</code> datas can be adsorbed onto another data through <code>setHost(host)</code>, so that the <code>host</code> datas move and rotate and the <code>attach</code> adsorption datas will be driven.
When <code>host</code> data is <code>ht.Shape</code> type, if the <code>attach</code> data is set to <code>attach.*</code> related properties, the data can be adsorbed to the <code>ht.Shape</code> at the specified segment position.</p>

<ul><li><code>attach.index</code>: The default value is <code>-1</code>, which is specified to adsorb in <code>ht.Shape</code> data segment index</li><li><code>attach.offset</code>: The default value is <code>0</code>, which is specified to adsorb in <code>ht.Shape</code> data offset position of the segment where the data is located</li><li><code>attach.offset.relative</code>: The default value is <code>false</code>, if <code>true</code> the offset represents the length of the line segment multiplied by <code>attach.offset</code> value</li><li><code>attach.offset.opposite</code>: The default value is <code>false</code>, which is specified to adsorb in <code>ht.Shape</code> whether the data is in the positive or reverse direction of the segment</li><li><code>attach.gap</code>: The default value is <code>0</code>, which is specified to adsorb in <code>ht.Shape</code> offset of the segment in the vertical direction.</li><li><code>attach.gap.relative</code>: The default value is <code>false</code> and, if <code>true</code>, offsets the vertical direction of the line to <code>ht.Shape</code> <code>thickness</code> value of shape multiplied by <code>attach.gap</code></li><li><code>attach.thickness</code>: The default value is null, <code>CSGNode</code> defaults to <code>1.001</code>, and the <code>height</code> property value of the adsorbed data when the positive value is determined by the <code>thickness</code> of the <code>host</code></li></ul>

<blockquote><p>Adsorbed to <code>ht.Shape</code> segment function does not currently support <code>segments</code> be set to curve, nor does it consider <code>ht.Shape</code> sets the factor of rotation value <code>rotation</code>, when the data is adsorbed to <code>ht.Shape</code> segment, its <code>rotation</code> and <code>position</code> positions will be based on <code>ht.Shape</code> automatically changes in sync; if the <code>attach.thickness</code> value is set, the <code>height</code> of the data is automatically adjusted according to the <code>host</code> <code>thickness</code> value, which does not require user settings in common.</p></blockquote>

<p><iframe src="examples/example_host.html" style="height:270px"></iframe></p>

<div id="ref_3d"></div>

<h2>3D</h2>

<div id="ref_floor"></div>

<h3>Floor Type</h3>

<p><code>ht.Shape</code> datas when <code>thickness</code> properties are less than <code>0</code> often used to render the floor effect of the polygon model, this type of <code>tall</code> property determines the thickness of the floor, generally through <code>floor.setElevation(floor.getTall()/2);</code> to set the floor on the sea level.</p>

<p>The floor type is controlled the top parameters by the <code>shape3d.top.*</code>, <code>shape3d.bottom.*</code> control of the bottom surface parameters, <code>shape3d.*</code> control of the thickness around the parameters.
For situations where only needs the plane and does not requires a stereo effect, you can set the bottom and surround visible parameters of <code>shape3d.bottom.visible</code> and <code>shape3d.visible</code> to <code>false</code> implementations.</p>

<p><code>GraphView</code> on the 2D <code>ht.Shape</code> realizes tile map can be set through <code>shape.repeat.image</code> property, <code>Graph3dView</code> 3D tiles are controlled by <code>shape3d.top.uv.scale</code> and <code>shape3d.top.bottom.scale</code>, but in this way if the floor size changes, it is generally necessary to set the parameters again so it is not convenient, <code>HT</code> provides the <code>repeat.uv.length</code> parameter to resolve this problem, and by setting this parameter, the tile will automatically adjusts the number of tiles based on the size of the graphic, and also with <code>shape3d.top.uv.scale</code> and <code>shape3d.top.bottom.scale</code> parameters are superimposed, and of course, in most cases, if <code>repeat.uv.length</code> is set, you do not need to set <code>shape3d.top.uv.scale</code> and <code>shape3d.top.bottom.scale</code> parameter.</p>

<p><iframe src="examples/example_floor.html" style="height:450px"></iframe></p>

<div id="ref_wall"></div>

<h3>Floor Type</h3>

<p><code>ht.Shape</code> datas when the value of <code>thickness</code> property is more than <code>0</code> often used to render a wall effect of the polygon model, this type of <code>tall</code> attribute determines the height of the wall, <code>thickness</code> represents wall thickness, usually set the wall on the sea level through <code>wall.setElevation(wall.getTall()/2)</code>.</p>

<p>In a wall type, the model is equivalent to a linear of hexahedron, so it can be controlled by the <code>all.*</code>, <code>left.*</code> or <code>right.*</code>, etc. parameters of hexahedron, refer to <a href="../../core/3d/ht-3d-guide.html#ref_cube">3D Manual Cube Chapeter</a></p>

<p><iframe src="examples/example_wall.html" style="height:500px"></iframe></p>

<div id="ref_pipeline"></div>

<h3>Pipeline Type</h3>

<p><code>ht.Shape</code> data is presented as the model effect of the cylinder pipe when the <code>shape3d</code> attribute is <code>cylinder</code>, <code>tall</code> and <code>thickness</code> determine the width and height value of the cylinder section, and when the <code>tall</code> and <code>thickness</code> values are presented as rounded slices, the direction of the pipeline is determined by <code>points</code> and <code>segments</code>.</p>

<p>The pipeline is controlled the top parameters by <code>shape3d.top.*</code>, <code>shape3d.bottom.*</code> controls the bottom surface parameters, <code>shape3d.*</code> to control the middle part of the pipeline&#39;s direction.
The effect of a hollow pipe can be achieved by setting <code>shape3d.top.visible</code> and <code>shape3d.bottom.visible</code> to <code>false</code>.</p>

<p><iframe src="examples/example_pipeline.html" style="height:500px"></iframe></p>

<div id="ref_polyline"></div>

<h3>Spatial Pipeline</h3>

<p>The types described above are based on the 2D point information of <code>{x: 10, y: 20}</code> to describe the graphic trend, <code>ht.Polyline</code> inherits from <code>ht.Shape</code>, support <code>{x: 10, y: 20, e: 30}</code> format of 3D space point description, if <code>e</code> value is empty then take <code>elevation</code> elevation value, modify <code>ht.Polyline</code> <code>elevation</code> and <code>tall</code> values will automatically adjust the <code>e</code> value in the <code>points</code> of vertex, and the same <code>points</code> vertex information changes are synchronized with the <code>ht.Polyline</code> <code>elevation</code> and <code>tall</code> values.</p>

<ul><li><code>x</code> Represents the 3D <code>x</code> axis coordinates</li><li><code>y</code> Represents the 3D <code>z</code> axis coordinates, refer to <a href="../../core/3d/ht-3d-guide.html#ref_2drelation">3D Manual</a></li><li><code>e</code> Represents the 3D <code>y</code> axis coordinates, can be referred to briefly as <code>elevation</code>, represents the altitude of the 3D <code>y</code> axis</li></ul>

<p><code>ht.Polyline</code> also supports the <code>segments</code> parameters introduced in the <a href="#ref_basic">Basic Properties</a> section, which extends from 2D planar curves to 3D spatial curve effects.
<code>ht.Polyline</code> closes the function of <code>setRotationX</code>, <code>setRotationY</code>, <code>setRotationZ</code> and <code>setClosePath</code>.</p>

<p><code>ht.Edge</code> and <code>ht.Polyline</code> is very similar in showing effect, but <code>ht.Edge</code> start and end points come from the <code>source</code> and <code>target</code> nodes, while <code>ht.Edge</code> <code>edge.type</code> attribute is <code>points</code>, <code>edge.points</code> and <code>edge.segments</code> correspond to <code>ht.Polyline</code> <code>points</code> and <code>segments</code> attributes, the vertex information of <code>edge.points</code> can also be a 3D vertex format for <code>{x: 10, y: *, e: 30}</code>, where the <code>e</code> value is null to represent <code>0</code>.</p>

<p><code>ht.Edge</code> and <code>ht.Polyline</code> has two ways of showing, the default display as a normal wireframe effect, when <code>shape3d</code> set to <code>cylinder</code> is displayed as the effect of the 3D pipeline, <code>HT</code> through the differential segment to achieve the curve, so to achieve a higher equalization curve effect, both ways can be passed <code>shape3d.resolution</code> control curve differential segment number.</p>

<ul><li><p>Normal wireframe effect:</p><ul><li><code>ht.Edge</code> Controls the width through <code>edge.width</code></li><li><code>ht.Edge</code> Controls the color through <code>edge.color</code></li><li><code>ht.Edge</code> Controls the color through <code>edge.gradient.color</code>    </li><li><code>ht.Polyline</code> Controls the width through <code>shape.border.width</code></li><li><code>ht.Polyline</code> Controls the color through <code>shape.border.color</code>    </li><li><p><code>ht.Polyline</code> Controls the gradient through <code>shape.border.gradient.color</code></p></li></ul></li><li><p>The effect of the spatial pipeline</p><ul><li><code>ht.Edge</code> Controls the width through <code>edge.width</code></li><li><code>ht.Polyline</code> Controls the width through <code>thickness</code> </li><li><code>ht.Edge</code> and <code>ht.Polyline</code> Control the color, tile and etc. style parameters through <code>shape3d.*</code></li><li><code>ht.Edge</code> and<code>ht.Polyline</code> Can be controlled by <code>shape3d.side</code> to control the number of pipe sections, as set to <code>6</code>, then shown as cube cross-section</li><li><code>ht.Edge</code> and <code>ht.Polyline</code> Control the angle of the starting edge of the pipe section through <code>shape3d.angle</code></li><li><code>ht.Edge</code> and <code>ht.Polyline</code> Control the cross angle of the pipe cross-section through <code>shape3d.sweep.angle</code>, the default is 2 PI</li><li><code>ht.Edge</code> and <code>ht.Polyline</code> Control the number of times the tile is automatically tiled in the direction of the pipeline by <code>repeat.uv.length</code>  </li><li><code>ht.Edge</code> and <code>ht.Polyline</code> Control the start and end parts by <code>shape3d.top.cap</code> and <code>shape3d.bottom.cap</code>, the value is as follows:<ul><li><code>undefined</code>: The default value, which means that the hollow is not closed</li><li><code>flat</code>: Represents a plane to be closed</li><li><code>round</code>: Closed on behalf of a circular body</li></ul></li></ul></li></ul>

<p><iframe src="examples/example_polyline.html" style="height:600px"></iframe></p>

<p><code>Edge</code> and <code>Polyline</code> in the ordinary wireframe effect can realize the function of dashed line, there are two kinds of dashed effect:</p>

<ul><li><p>Alternating between hollow and solid:</p><ul><li><code>ht.Edge</code> Controls dashed mode by <code>edge.pattern</code>, the default is NULL, sets to <code>[20, 10]</code> represents a length of <code>20</code> of the solid line, a length of <code>10</code> of the hollow, repeated alternating</li><li><p><code>ht.Polyline</code> Controls dashed mode by <code>shape.border.pattern</code>, the default is empty, set to <code>[20, 10]</code> Represents a length of <code>20</code> of the solid line, a length of <code>10</code> of the hollow, repeated alternating</p></li></ul></li><li><p>Alternating colors:  </p><ul><li><code>ht.Edge</code> Controls whether to enable color alternating dashes by the <code>edge.dash</code>, the default is `false</li><li><code>ht.Edge</code> Controls dashed mode by <code>edge.dash.pattern</code>, the default is <code>[16, 16]</code> represents length <code>16</code> of <code>edge.dash.color</code> color, and length <code>16</code> of <code>edge.color</code> colors repeat alternate</li><li><code>ht.Polyline</code> Controls whether to turn on color alternating dashes by <code>shape.dash</code>, the default is <code>false</code></li><li><code>ht.Polyline</code> Controls the dash mode by <code>shape.dash.pattern</code>, the default is <code>[16, 16]</code> represents the length <code>16</code> of the <code>shape.dash.color</code> color, and the length <code>16</code> of <code>shape.border.color</code> alternating colors</li></ul></li></ul>

<blockquote><p><code>Graph3dView</code> closes the dashed line by default, if you to start the dashed line drawing function through <code>graph3dView.setDashDisabled(false)</code>.</p></blockquote>

<div id="ref_csgshape"></div>

<h3>Hollow Wall</h3>

<p><a href="#ref_host">Host</a> section describes <code>ht.Node</code> can be adsorbed to <code>ht.Shape</code> type segment, when introducing <a href="../../plug-in/modeling/ht-modeling-guide.html#ref_csgshape">ht-modeling.js</a> after the modeling expansion package is inherited, through the <code>ht.Node</code> <code>CSGNode</code>, and inherits from <code>ht.Shape</code> <code>ht.CSGShape</code>, can realize the effect that the polygon been hollowed.</p>

<p><iframe src="examples/example_cull.html" style="height:600px"></iframe></p>

<blockquote><p>The above example will <code>attach.thickness</code> set to <code>1.001</code>, the height is set to <code>setTall(80.001)</code>, where the mantissa <code>0.001</code> is the size reserved for avoiding the error of the <code>js</code> floating-point operation.</p></blockquote>

<p><code>CSGNode</code> <code>attach.operation</code> property defaults to <code>subtract</code>, which represents the hollow of the <code>CSGNode</code> and <code>CSGShape</code> type of <code>host</code> datas, in the example by setting <code>3d.visible</code> to <code>false</code> hides the <code>CSGNode</code> for hollowing out, but for the type of doors and windows, in addition to the use of windows and doors model hollowed out, but also need to retain the model of doors and windows, and doors and windows are generally thicker than the wall, but if <code>attach.thickness</code> is less than <code>1</code>, you cannot chisel through the wall thickness, for this <a href="../../plug-in/modeling/ht-modeling-guide.html#ref_ext">Modeling Expansion Pack</a> also provides an <code>DoorWindow</code> type of data that facilitates users to build models of door and window types, and <code>DoorWindow</code> type datas provide <code>dw.s3</code> parameter, which defaults to <code>[0.999, 0.999, 0.5]</code>, represents a model that is actually displayed on the <code>3D</code> interface and then zooms in on the size of the <code>dw.s3</code> again, so that <code>attach.thickness</code> continues to keep the <code>1.001</code> that can be worn.</p>

<div id="ref_rotation"></div>

<h3>Rotation</h3>

<p>The above mentioned <a href="#ref_floor">Floor Type</a>, <a href="#ref_wall">Wall Type</a> and <a href="#ref_pipeline">Pipe Type</a> three types are described by the 2D point information of <code>{x: 10, y: 20}</code>, and 2D <code>y</code> coordinates are mapped to 3D <code>z</code> coordinates the conversion relationship can refer to <a href="../../core/3d/ht-3d-guide.html#ref_2drelation">3D Manual</a>.
But all three types support <code>ht.Shape</code> <code>rotationX</code>, <code>rotationY</code> and <code>rotationZ</code> space rotation function, by setting the rotation parameter can achieve the erection effect.</p>

<p><iframe src="examples/example_rotation.html" style="height:500px"></iframe></p>

<blockquote><p>The above example can realize the special effect of the horizontal flow of the pipe and the rotation of cross-section direction by changing the <code>shape3d.uv.offset</code> parameter.</p></blockquote>    <hr id="contact" style="margin: 20px 0 1px 0">
    <a href="http://www.hightopo.com"><img class="logo"></a>Welcome to contact us <a href="mailto:<EMAIL>"><EMAIL></a>
</body>
</html>
