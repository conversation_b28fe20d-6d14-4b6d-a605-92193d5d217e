<!DOCTYPE html>
<html>
    <head>
        <title>Editor</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <script src="../../../../lib/core/ht.js"></script>
        <script src="../../../../lib/plugin/ht-cssanimation.js"></script>
        <script src="../../../../lib/plugin/ht-palette.js"></script>
        <script src="CreateNodeInteractor.js"></script>
        <script type="text/javascript">
            function init() {
                ht.Default.setImage("res/music.png", "data:image/png;base64,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");
                ht.Default.setImage("res/music2.png", "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAj/ElEQVR4Xu1dC5RdVXk+k/c7OyEQAgnZAcIzwhHkIUY5PBTrA67Lqn0pU621S9syVAVsaxlaV1dtFQYVl2Ir11r7wHZxwUcFEU4QUBDMjjxCCDA75Eles5NMZibP6f+v7L32Wd/a55zcuXMzuXT+tc7Mveece85+/Pt/Pnb0/wlGYRRGYRRGYRRGYRRGYRRGYRRGoS062uH6B2fSX0FHTIehI6GjSoe05xkUX7PndMRw25U7opaBUQTASb/UTrSio4sOkZ1g+B/ZayrzPaWjYs+lFnmUfSZD1V131+x3A8/l99YyyCfsdZNBQGnvkfYwmbYz6Mz/SuYZGq4L3ybbHt+Xdvtcd1+nPZfaQxHCr2lVBMDJPzczCLGfYAI38B4UIIFxE5oZ6BgmTvABz2cQ7npm4BOHFJnnSpgMbJPJnNfuffB89+zUt8+22YOeNXlcPGPSOMFf1vQMmMzvBIyLtkdHISK0ArmnYzkdgzM/l/IxOPfzj9yj1u+856ertvX8+Pkt3S9u3t293gzctaV3z/KNOwfuGRwc7KHjLjpupqN7++69/L3n5a19Pa/t2vMw38u/oXPXrnytd7n7LV//pTbddy/f1MPnbn/k1W7+zvfe9cR6ftZyvr/2zObl/LzubX2D/Hz+//Dq7fz5YW7TU6/uuOv+F7be8+grPcvpWfy9h9vAbeZ2ufbyPfxb2+a76DO/457Hu81t/Bv+zO3i/3Rct7V3b3emP8uf3bjrYX4mn+c2faD6m56T/+7RHhqrbjp67HFbS1MA24GOa5bMiW695jTzas+ASk6dFQOvj2DF1eyqkBGAX4X+N7A6kUqkTt6A1RwC41YwsAQD96kMe1BAfTS3HdiYAJZjf+9ZYJYiPvji9vjRbmNuub+br2miAItaCQFQ2Ev54z1/eE5cecOxUQloT/LDAPwcACcYWUIYgN1IeEb4Hv8d7xVFbS18tgfzvyu3Ru/61gqHYAkLwK2IAAt5NV/3tgVxV+U0vBqeuFEwdAi9vT9a9IXHHUVjBFgRHSaMiY4eqNARi8njtF8tYShZcSaXGuC9AOXUppwq4Hf8Ld47lDah4CpnT9bvPOOYCNhXayGA44WnHDNZIZ8LDTbweAVSPk5AmuHPNVDpEFB+qIXeC2peCu+tZdqFWkGKrMddQ6keZBh/b2AxbOnd654nWxIBXEdJ7ZGgqnlp75ktEf/74s90dMMPVivifeaBVdvUvzyxIXqs20T/9vSm6M5frOcB6vrHh9boHzy3pUb3GTov6TCd97/S9dymXlF9coNMX+pJ7f+IBCnDBz1P8/ev/Hwt/+/6vnqNn2HueHSdvvfZLfqVbf18TW/dvVfTf/HdpzbW6JmajoTep+jdET2/g9ojb01fTe17BX1X9C5Bz++i5wi6T3Bfnl67s0p9kZ+5bzW3JaZz8ksPr6nRcxU9P6Lnqhc27zZ0LSLo7Fr2apxHOd5z9hxAgNaTAXp40kkAjEgA5BUkgZyZ5I6nxbKXjROWcHUIe1QzWoGAFeVA2sOt7or9LJyl0Z13v0U7AZJbuE9kjVUg4aPNAW0IKtN2Nw7OGCSXf/rCKD5xOspAjISR1QRqJAO8LzpMGBcdZfDS1r4aGDscmN49B1CdQwsdHx0ByqJhwjQYerzl0FsJI2QrmcmMoX0KEIsB1TaZ+c4Q58goSWbiHXTyn2OmjFemf39I8xEnzJgIp1qEBSCcffzUOE/Iu0TONHmCUEgAgomPYbJiuO4oQAX4sVuN7n+ClMVRK7QSgg1AZO6VBepq6hDFrvrEflbb+vbJk2ZNCo2PIKshIFJrUgBToObFZA71QhWS4zDIgJSd5aNxBiEq2cmDVa8LJw0hjJAC24nIAuzDUyt/3uw9cNAbnTLQvX0A2tN6FEDwsWpzn8zBYkUWLwH8tN7nS5TKgTqEEStsoJFF/cBzdaizMVgUZQZB9ISxY0LvVefNn+4pVSuzgL0HBlXegE8cNyb1g9QIhAUwmEBROnGNg4GJl4F36KzTq6dvX+j98eTxY2DRtBQL8KbY046dkmeHN4T97VGjUD6p4ohSvWLQWSp0+rFTDKnJJvTbSeOAMrQoC4geWr3dYbyAa4ZkgIDV73ULyOtrq7b06TzE2bXngHhdIMDli2cHpWSnBnmS6H/zekIEHA/wZiY7B/aHJpotgQbuby0EcJOqNuwK2c7ltt37xNzpE9yqTzDI43WKBAZsHtqxAITdew+0sinYC3ZLFwkRsnkPRoOGOuksfMgmzFFKDXTUAICgqokCRuPHtsnQe0h4dv1OW9oXQKoeT2wFBcCevv3CygBpwJADVrsRA/QAyuFkCWQIil/a2h9CcD1vxgRn1k5aWg1cICYFyfmBwUFz7NTxKQRvxhARLOqarMbBwMRDHOBwgX/eqXMmh4xJyctb+9vd95ZCABzMVZt3BwdgYN/BeAvJAc5U6tyuYIJt1Levc9zNCBoseBook8iJD9ANIKBhIXhg/0HFnzEwdc/+g/57K7OAMW1twclk4Yfs3RLCsMFuX/r8oBoF1kUDOrjESczE96WZcxV7HqOPUWtBf7+pgwVEu/ccUIE+qJOPmezb2IqGIDfQs6eMD9rdDxwcjMgQpMBNWyz1hmUDkRe6jVZC+J5mLJC1zLPb3UTnIpU/VMDEa8r74oXAY6aObw+xgFlTxpsWZgF+UK0lUKBBhKXf13r3xiD8BYMtZ04aFxG1YO+hgkE3J8+exNfF1WfPUXOnTUjp0LOnjItsSJXiz+xZ42eQ5U1duXhWxM9ZOGtScuPlCyMSttI/OP/46Jx506ILT5pRoUnRH71wniLeHFlvpbn0FKHOmjuV35vSM9h+Yd5z1pwqnWM5hp8fQzSTKA1etUDrAFmJsH4S09KmYOeDN/37IgiK4MMQ79PI95B68KSlnzqPAyZwpSd0KAjlUt6e4EO2Icw8BpYg/+E9p7ZD4okAtuCu89ER8EyK1WTRm0kISoJbOqYtqvTvOyjSl3s4oMP3O8ftTZQwpF1IkpGi14MvIPmF3mHaLzxB4iBwx2klMR/EiXf3qR0D+01WDYKY+xjIcjtQER06D6HnOJkSBL8Ynh8Cs5hs+vzhuGkTHIKoWVPG6a/+fG28vW+/KZBTnB0A2CSks7USAmAHf+vMOUHsnzphbEKTnw2+jEN2czIWKbiGg5LkyAiVPPWrTsdRfBgqXYyJI1PGjxU0+brg94aohSEkr4V0/Unjx1hq17oygLRqoEFLoAOmAMGIGq8jk0FkYpFKKArMrOUQltzVMFhABa9s7l8B/5ekBjOihCKCpKBgWve5pbWAftJnQyvvV6/uTNkSVkRByEqm+vcdgMCPcvcvIAOmdOkAqY0zen9SRNFyk0cB9h0Y1Hg9pDpSRFCIMgnKDYha2hnkBjQ+YXpohasLTprhsml1mD96QRLUr8PRPrQNtjAcFr5p154ahYMrDgWn0O1OCg+XFKZdpdBz+cSaHeITd6+sUmi4QzRFjipthVf3bpVpq4Tzon4rpUdm0x/0Bur1O/akrS4DiEMr4WAw5u/FLRAtHObVej8Ji3BfcNVt7t2bvutOpc84bkry45XbBKt+r2wfEF4esKTeh4h3RR7a7/zlBpPRJAypeClF5cTEo8XFC2eqJcdPTa464xhB6qMhHZ1/21GUaMqm7nFj2jyFyZEdiMWZUN9o3JJWNwSZTHAjClOCLITcQVNE2knH1tMnjkuCvnQLvMK//ti69PsrNvO55Ol1uyKGnv79WetfimQbjUrACpLnX9vtJkAQK5L84aYfvRwxnHvCtM5vfuAMfdHCmVmWgxE9Mdk5iuIMDdkoDMUDyFDc4unHTWn5oFB5SDUaH7Lt8wrFCUDkUSQkxTYwQtFRQwMLZ9hc9vVfRzT5SeZ81cbdJ3aVSltoYZE9F2f8De10/jJLIdy5Lvs94c/2vSorLK7Y0Gsuvv0pwdlKOaZrwTZ+SHFDYC2BWUAaWjzb+vaDFtKiLED3DOCqdWqOAg0AQbL1zalDqNYxf++8v1uAzNGeU1FjBf+Ba3nn7408LMOM5wySxDf+8CX5lkUz9VsWAf56Aw/aNhAEUZM4dP3Zjb2i1YVA44TA0Arf0rsvKonD10SGY6sm4SrQtOqjTExhJ6/kZpdT4efTscxSGAbx3KbdIR4vuH8ouyCweZtljJCQSHKHaHl3sBXOgmoSCViSTb0ZDxyyD2lDo0IIJCnlTDtEoUm5ZSTC3Rh+vHJrKOKJLXx4HsG0tUWGNI7Q+LAQmLakOxhhLEnCSMask0iQoFQr0LsFGYJc2DQOYnrfs1t91a8jAEgJHBKs3tIf5OE2rDsuHJu2Nk36fqh/goTZuCVZAMJWKwmjEEiClLDewDQHy9V28peToUTD9cD3EYGapVAJ2RAihKkTx5aFtGm2BD6zsTdE4bhARMuHhcsiG/wZc6fUoKgSgqBYgsimThmIKGYSKb2OPyIQu3Jv1qUrMOIpf/J9RrIkd3YIfrOxF9TnlqQArM9OFSEMX2/2JKQHF4Z+sf7NlcVw8CiIQrCRx6t9IwJVp8uPH9MWB2SA1KuBYQ2JYxkYeUJC4HNeC1AtHRJmTaoIkmQD1oMlOGOQghgWFnHwSLBUYMAZKRCDg5Egy6BGV/Rk0l4IOURRHKIhdzfmBtr75dnzppkWzQvA6N9IBWLbWbjjaJoySVdNI16KvyW/O1TXHDkWZyt/xuCAkk7IK7B1SDYEsakb5SNrSWxNNRA7smtgf1zkzy+yA7i4AfztUaXltLloZrRz7DUk5KEdAN3hHEmECIB9lC1tB6AycXmTxvwPSTgKjTF5xcDjBmxlhIEqn+pA4osgR1DZCtYHBwfzon40s4ZWVwNjWx/ABBvaFnE4WGGwB/FQfeLMiTFeEJPHA6UYERCWxcUh5Lem7lrR78lhxWwiKCTbOAqAFkwO7aMkx6JJLnCWaBawDA0SgpWgIWx6ZCgcqXHO0IUFsBi5kzI2Z2MC8Xxy9typrw87AK1ggxcca4Dy6ggycx8OUA3Cv0YCZJGvg3k78/goH1gINtZrqJGykGYRtbQQ6PXhMUFfPzlRSqNpiEQCBfAbMUwn6ftoYAG9ew+EKEDM8QDE4oqKQwsWEsnOEfD7u0Ca1jcEIaCtXJSqkaAmWeBoGxjckUGA6RPHBtvx2q69GsrUh9LEFflEQpbMdPXW/qjVnUEKJPawrbwEWE8OCUnzxUQooDAC4GUAESF5nzaBz6VFMg4dsfUGIsSUqQQ+lBbVAkiPNwFzpmASSe7gGDuI/nIeyBD5JB+BGWFnkDkUHjY9pOsLimZWsMcAguD+sVkb+4AqZUvbAbggM0EcIpEUDFEkB6iefmcq9ZApsDjSBiHB8Qy2fSbgBhcu1KxAxuExqAUQxJB2oFodAURRWLSd/LgAediW7liAwWtnHDd1RNQ/zOzhLF7XVwwKPYx08dh6DRGElZFa3hJozl8wA5EhpaODauTKMvsAO4vYJQwgXOrUCMsAmlZwR7ZOAQIjcFkiCxXKCrVfrduxR7dIrWC/GSTE2yc2tMlnyPj98apYwCEU9mUzZ0IxA+lkWmFHcvsb137IZRSUaCIIyU0EwOorO3vKkIgCSnTAFhJTWLiXIY4iBMBJ74ANFiuYuLFh597srh5Vi9Gcyy85+YICP+3vgwUUjHMGIQvo8fYB0eSJ73TIHPBAGlvQUYR4OMc7IBIghbQsAFRGNhJNsM8d6dxAnHQ/GF3Z/DrKmOGqX+qf3nuq/vkrRn7xvadGT63dJd5x+mwDVTeES9wgR09SQOJMQbo0l5dpZv9Si9ydWdMzGbXU1UvmUNLqFLVATEwo89mo9btC6q8hC2jCqeElYWGSEEgBheN7OZ0NaguMAALA4FzrJt1NAgdtvvusOfFb5Exz6amzUvLT83n5mcsWxnyTFdSSQBEkygxiSdlNbHjy2ZJGJDIN2dQzNXTEMPXvGjvpmvvpWND586enX7p6cTt5/bhNAszPrngF1hsSVCwiOPE40Xa/ACyUoc8+fprkzyOFALjvb5frNKs+yakiejOlRd14hcwWUUryhDnYKNHxPhbyipwl0u0qEhoIsg+4lSMb3trW909T/9rbL5wX/c+KzV1PdFwgTpg5sb00MxkLT7gKKOUr2GzcuUcTMkvcuobUy5GqD4CD44sp3XzVokh//pKo9tFzefJd48rCn0M+b8nBnhwRVBA7L4KFG3xyqXunbpDkdzm2du0F8+KX/+oSTfscdq29eWk7Tz62BSEvNM1GMpUJcYoMQVHoPsoZ4ERU/ihsO5tOAXBwbnY8kIokRdXfPStyOev1WqmKSBls94rnNKlJSCXcRszaUYAGkLvK76X0rIgmPbJknqGjwX6JKYdiAoOagEN65+sILSByhRuXOm77uOKIUQDe8JkHwVbIohV/Dkx+XbaAFL5bEj5eEhkPhT1pe6S2kERoFcmdvpy6GUL/rrOTEDNLIx5vaPKjOqVuLCypoNhzQAPw/XKIk7NjiKb0cm1DxjUfzWQBuNv3zS5797ZrFov0U+fbCJxyYL5FerHhvf54/z3eq48+x1SQQdnCCyajJ7OkK1G9coPpzKjWlIxgqA6Au799iDw/4pXf/deXmCtPm11XSVoqKqG56ATvCUiFJjhKWdN+gIiMLC8JWAiOZRp7PbGGLgQxZ+qEmHZUgaDSZrMAL+HKv3nHoqjj0pOiMtDb++kYiF7e1q8+e99qmdHP1S1Rt7cL/OuzHAAhbrpC1ihYIqEdRbmCFq58K9h5idiVmMHXPta9w6/A+iT9Kn+kPY2Z7B8uGzM00ea6e14UHORB2onK1jim82z1E1wXmZCpxpL9vOlOj49SoCxOYK5xFTTKfpIkB6A2JHgxkb+kbo9gW4PSPg+OJGEv7bzq5ELMYzdv1yNro688stbQpDvMxl3AVaDSZhzA6hTPucH95w+dGX3sIiwzFyku7UI5+nzeUK7erDoEvnYqCKlJwi9dVbSzZ0Qp6BS9O87YRBQFu5MJbC/sfaCgCqrI3scxj/d97FxD6eUy8Cy1+O8fl7Y4RcwbSDeDAuDgSOb5NPmlNvb4S09yWhRKyIgIEmv55eyekeTVAX682whCAA2lWIWVsmWdmTyVa5bMYS2mcNVz1ZGO2otcCCJioJWaFVQNVjPPaEox1CisANnXuOFkKOjVUQyygsojVSgycZIo8XyLxfkkv/0/nufJL5SIod5uJSfuHzuP+wcoYhf8PcEqYMdPh/Jx5UasCgt8v33u3ELEIcseVx0pV2fDCIwOKgPjEGc+a+bx1KZKHhsaStTTmKHYux1fvOt3zrSNz18ZvPJpv99S92+ejaBwV1DPIoRrE1UaDZeSxT4UgBP62CxNdYFlEcl/45efrGsTqJIkWJGzzaw6pMqOlyztZ85jTIFpelSwq3bFtu5rlhybVbkMSvgfv3slBzDUs4efKESU8JawaUZQMiQNh4owKTtw7popWv0ueOPOD56Rt5p4p3J9/b2r6yr3XoedALepd/Z+uWb7ACCRv4+FQOhf4wiAK8eRr7uvfQOHIufG6b/9G8s5W9dOVoMDE6YU2m8Y4U3IJCVDahlAiZrk5I5OsmKyOhvqG6mp5sPfe847bpoTFJOCzZ+dRTZyKMgeEwiJbwIC2FXHq3/BrEmMmSrE67gaNlWsSIdz6xQYbLcqJGzYoOMTpwVjCin3DpAoV+dPePWzfd8hL9yvvvjQGo94ww8KtADH84WNmcQF5yihaboM4PjsW0+ZJciTJ3GLVSf0EenPy3IxDa4O3IzJOGnalXknl3JIF5ZWCGQQZav/ggXTDa3+NNA/cxNV+iLkroBa1zCANhCB2Vo6c7CtoYT+EERq0RQEcA36UHycV08Cgt+zG3crWD0qJPgNkfTjPgBpdqXs6A/61A3LALbIlC6LTO5858k6Z4Xrbz+xQQWQsAEITl4MSO/URm3V2SQUCbyVKqE0TQ1k8uheRCFNuTtjUdlVLuqcJdVx7ibN9QGmQOtQyZgttoYv/pbcxHGZbcP9jiKPgjLCgy9u54rdsPIaBNwp3APaAVJbSqZU12+CEOgbRwEPXHDBrw6Q/B9a3ZOVtBFT1RAFJ4wFVHlu5UWzJ4fi5nn1G0g9R5A2UomFW50j/IXItRjObeFQ9QVyL2xImQ5N+Ni2JsYEupW29GSR2+EfPr+VVz9OvMkccaMrpSTBky1ldtVgiZnD27KGzL55FMqQPQODWVB1E8Mo7Gp4Jrq/cWyOTHJogaqh/5sKMAPJd/xLDNee/yXXq7SCQTV1pVgjzRW6yigcJ5CE+D957zQJYBJC1jTELjbMEnDywTGkMkmgSH3JSBQJV5ewGQgQ2wwER8px8HmAvU7uI3srpRPXuCGl5hwpnCSJE2iNKBoKNwahsuTYIHJToclsRXETtEs0TgEMUDkJ281Vntm4OzhebJNhl3kz1UB+sQv0kAHJNVlrBpx5Ns1JczKoWzcwaOgSjq3PXgRUpIiCJuMSd7DJ5CYafE/fvkO8F1Z/jKop9M0MTdDFeAffvzfMC5bRMys27GJ3sWlmiRh+MIdm58XlGUpdtgYSWPUgNDWyWkC1RHKtaUcueLbbaWOf168LgCKHQiXdE/LdG1RHwRcByadD6yNuJgnx/7UzwxqK3n9g0MpdzRMCjVWz8mLrxKrNfe3QCbT1p560DRkMDow7rMcs9hPjgYNQSrZXF65UbYjF0XP5elC9hQkbrjR0GfB4JlBl1IFcubnPnm8SBXCDImdNas/pnOYaeECaNez3Hw+TRVCEnEMcQbR00cygR9EiQCn5pVq8IfVV7OdSs7gqfb8UTHyjsoDAoBh7CJv6pvF+W3gCoAl2ALWhN9eaRsYWlRmQGKpzS0xoaFxq9tKy2zEkR0sRsKlTCLQt4SID/SPeO82A40kHnhUPZ6IsbErJ4WWKkmdChi5NNgqLNE2mABt27MkrYCA/e9lJMsNn05yomGiYjCjoOq2wCpgTkGqqv9pYpnUYF3wa6p8t067cAaofsMSGEVsExkdfvniWyRuHOeSbcRSjmQhgKMFCULnzoBA4mSVorwJKiOEX4MVrFNAZFHW8bUHeytY8saEVgnxWre8NlqnhQEwKDsEdweOMXQDVOD3MHkJ1/PQJeZtmmp+s3KaaKQOw7syhNlXeAKknnLwg/vytCwxZ0hKIZuHvqlFLYGh1AUWpXX0oQAVB0n5/LARC3kGYwrEqZfP08FkxbQMXc5ZyQYSSgM+NgW+vpIDQ+ENvnBukANQ/s+pQ5pNqBgLgIImHX+rJUzck7ZeHZt8USORwGYREhuRp2g5eUs6cCKzqLg4Jt5FJCoxAiOBddAjeXyigCgoODzt+xoQKsB6VQcYULHiNgMkuoPPnz1AUDYzPdLuqeqGU+tE0BHAd/Pdfb8ojNRREeVxW5RP5endYfRqqi5iydVD17HKyASMsTFahnEPJKdG2HNcqvccht0YtB3h3I6VpDYaEX3/pAlCf/fM5FN2CbooWACZUTU4RLuYUqtglKAde/sO7T+GGorqIDVdD1ZcxwfISOTMiA5Ab+K5Mvn5C7RQ/eWGbBKTIg5orOElbxwbVOUrkiN5OyRxAgRxImLghyTvoXLr7I0tSHteQTEbIKh11o6PzSASFdmVco0Ge9EcXn2g4+CIHFAiGDenJnJL2o4+fGztybxGv4vgnRfD4yQXyH2IDbhV97+lNBvvnvj/wJ2+sUF0+HdrJFFK6GilSLVzVE4q/iEOU77lNvfqGH7zk30vtbzoCOCcPbYsqKPxLhhrOtey+9v7TNa74jHYQ5/oH6gBW+yhjh8O3Ou0zO9zA2Zw8SfxcBFZ/mVtYPbi6J0o96zC4R/G9Hzun8vvnzUXPJ9YgMPVqPihUrvrcmwXINu6z+umq7Ukm6rqraYYgXCUudq5ItyaBibOEFWFwtgx6AiRSY4frEAw1Zeh2Wtt/ZyCbJvoLDts+BNXDTZWy9wk+PnPfakOrLA2ZXtkpRkgu//jiE5y8IyHhQ0EyS12OIqKgkvIuOA9QhhCDWTCFpQN1ax4C4CDdyw3pWrY24szXnLg2SXkDYtmfnmeuXDyrAhMEXrVgyndu4eT5Myd2rb95aUo1CFxBJpQLDG39bh7XO6xqVN/qcM+k4E/xrV9uSPKER6I85psfPLNGWdHtPGGOFNtDouk74EQLlnYjjUZxYY32C0/IrYTywe88kz3XGQ0R2hqskVPjEKonr78ghShhJP0RpX/LW+7vViDkoJaAgiFqB9WHP3ker/yirdg5zVxTulac2Sb29qHUBHCI89T1F2D9QhRGU6IUvCk19xHlGwNlcdznOOAlVfQuTe8qjKH4xN0rDW1f762QQN2aSQGQCnRyzt87vrE8Aa8crqaYEkj1szdcJCiTWBCGeyuaXwFVPxD+Oa7oBE18NHjrFRUm+wWDYygngRM13apN7OTXDfw72ybDSS61Z7ZkhTyU1hMu0kR9NFQ/ICG2UOM2W/ZXdYYkMI8zcmneA4Cf8eWrF1epf5Imv71o8rmWwn+pzQJZ25GkALhS7mLBiTos/vMjS2ocf49CD64YThyhpEpBUbYRuVnN3Wqzov1wBEfcskePzqkl86ZVyMPV+a4zj6kGSWE4apcnS2WQqoNllgb71+2qeP7tO0/Rn1o6PwHPJkJWZpDffWpjunHnXknb4te+9ug6Sf1q/7Ol86ufptoId7z/dC5ta6iANFDC/FzLyrd/w0atlPvmJn+kEACTRWMuA7f6Ly8pCnVGwEjfLOg6d/lMkzue5kRUw6vL7todDWMJHJmpPyByXdPlqq8MsIVSIHXWUEaScZqURYA1I4kAiAQ1lzb22HVvSokSAFaXwxCzWzSRxfQT33/BUR02h74PbxouSkeUwNx4uTQ3XL4Qg1xESZ/C8kM5mIu6fmWefHWnoyxqOHc/bxvm+oDt9ohuvHyhuemKheyeBXI5JIQIFnii+kK1d39rhdtU2lieeHsTq55WXP1fZnmfTk6qvvfsY+N69Pw6w8fNA6u2pVd9UznDUjus+qMFAbByiCfdpMsKro5Jhw6kU4kcfqohA0hC1I16061PGlLTHIVJ6+X3DWk/3u7AtRDNdW9dIFwZeDQBY4BqMKA23PeUSr4oyrC2JnXQZo5CBMAc+w43sVzg8ZNL50uucPV7583lHH70dmlI90KbOq92/fXH1sVcAeS2ZWsrEGCaFK2MZveR2d6HL5inaOu26APx3GzbMWlEQP9w4lPOPfjCA921rz66ruICa2DlH90IgKnWPEho5CHnjXn76bN5wCpUIctwDXze8ZOseoZz+F7a2pdyvZvbqaAU7fWjfvZij6Tt0R2Zj8Ev0XkkVj7KPDwhtkSec3ylhOjslBJkAIuofDvnGAr67pEbEj18vGJ/16IvPM5OpvinpBk5X4alat+JAFoCAbCaGBSA8hmuYd96jGXgEInAdCwxChksjjF47+w5n3ULUb413K3LTQqsbkw5F9mgE7JYmj37D8ZE/TTfS8htaG8gQQUv5aE4vs3RzoH9vNlDAu3UjNxg4h0BBIAVDWqLYszPBn0An27HwUf+GJjQOFArT9SrJSDyYEBLQd0eA+824OMPJIEAAvvryiEXvENhHwIyg4DMqhSLR2blDTApm3qQpq2Uz3k/cwU6mWANG+BvMMnFpd7DK6t+yPEtGPwM7ASoj29TXgIofobJKEIeEaIeofqI2CeXcQXPzyudIywFubcOBMDCyDiRYbWsJPu36Rs21pFqpgvyCmQDbdaBFHZAlHLqFVoAdRqNdCASO7WIsKwIAVCVq1pMayo0H1lGvr14b/3XkRqUvwOQwWlZFTAdh51BEFGj8qJ+GgzsNLn1cVoLRMP3ll+Ph5hR5FPZ6/QGVq3qpsBl6f6rXL6VDwbvQ6Grjph2E40A1BO1hN5MOI4ECPjcCasfEAAjfrwrtMtPPmT6+KOKMX45JWHSrKAEBR7Tw3QcmbLzgETQjkaqd4UDOPA83G+COQMAdSKIDiyGFK5ns5gqRbaEtsPS432dfZ5kTECoFKhDCejsfBi8Vi5voFrodX34LFCyR6GvIEVNB1SzGuxfZND2kJcZDOdkoMK5gTbGBVoGqrYG6wfAnHS4/RbLjGRtDVrCzoVORZAYqiEwpJYJmqxBEQkRzIwN6+oao2dzStPEMOkCUtQMXHegUXoHRHDtl0DVYnhmJTMxFQiFQ1N4DAglspQT1Nsa9KOw6kkDCDCiu4oK5luMZLDKE6AiKVoV/cBYpPPIgUYrrNSdWJtHhwsxd7o3yES1zLtjMHBVM/seViGV3Du/iCyzYynEMppq2h6FURiFURiFURiFURiFUfg/52dLiZcItWwAAAAASUVORK5CYII=");
                var palette = new ht.widget.Palette(),
                    graphView = new ht.graph.GraphView(),
                    splitView = new ht.widget.SplitView(palette, graphView, "h", 200),
                    createNodeInteractor = new CreateNodeInteractor(graphView),
                    view = splitView.getView(),
                    style = view.style;

                initPaletteModel(palette.dm());

                palette.getView().style.position = "absolute";
                style.position = "absolute";
                style.top = "0";
                style.right = "0";
                style.bottom = "0";
                style.left = "0";

                palette.handleDragAndDrop = function(e, state) {
                    if (state === 'end') {
                        var bound = graphView.getView().getBoundingClientRect(),
                            point = ht.Default.getClientPoint(e);

                        if (ht.Default.containsPoint({
                            x: bound.left,
                            y: bound.top,
                            width: bound.width,
                            height: bound.height
                        }, point)) {
                            var paletteNode = this.sm().ld(),
                                node = new ht.Node(),
                                lp = graphView.lp(e);
                            graphView.dm().add(node);
                            node.setPosition(lp.x, lp.y);
                            node.setImage(paletteNode.getImage());
                        }
                    }
                };
                palette.sm().ms(function(e) {
                    var selectedNode = palette.sm().ld();
                    if (selectedNode) {
                        createNodeInteractor._image = selectedNode.getImage();
                    }
                });

                graphView.setInteractors(new ht.List([
                    new ht.graph.ScrollBarInteractor(graphView),
                    new ht.graph.SelectInteractor(graphView),
                    new ht.graph.MoveInteractor(graphView),
                    new ht.graph.DefaultInteractor(graphView),
                    new ht.graph.TouchInteractor(graphView),
                    createNodeInteractor
                ]));

                document.body.appendChild(view);
                window.addEventListener("resize", function(e) {
                    splitView.iv();
                });
            }

            function initPaletteModel(model) {
                var group = new ht.Group(),
                    node = new ht.Node(),
                    draggableNode = new ht.Node();

                group.setName("Nodes");
                group.setExpanded(true);
                node.setImage("res/music.png");
                node.setName("Node");
                node.s("title", "music.png");
                draggableNode.setImage("res/music2.png");
                draggableNode.setName("Draggable Node");
                draggableNode.s("draggable", true);
                draggableNode.s("title", "drag me to graphview");
                group.addChild(node);
                group.addChild(draggableNode);
                model.add(group);
                model.add(node);
                model.add(draggableNode);
            }
        </script>
    </head>
    <body onload="init();">
    </body>
</html>