<!DOCTYPE html>
<html>
    <head>
        <title>Unboxing</title>
        <meta charset="UTF-8">
        <style>
            html, body {
                padding: 0;
                margin: 0;
            }
            .main {
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
            }
            .formpane {
                top: 10px;
                right: 10px;
                background: rgba(230, 230, 230, 0.85);
            }
        </style>

        <script>
            htconfig = {
                Style: {
                    'edge.width': 1
                }
            };
        </script>
        <script src="../../../../lib/core/ht.js"></script>
        <script src="../../../../lib/plugin/ht-modeling.js"></script>
        <script src="../../../../lib/plugin/ht-form.js"></script>
        <script>

            ht.Default.setImage('DieTexture', 'data:image/png;base64,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');

            var currentNode = null;

            function init(){
                dm = new ht.DataModel();
                g3d = new ht.graph3d.Graph3dView(dm);

                view = g3d.getView();
                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function (e) {
                    g3d.iv();
                }, false);

                createFormPane();
                formPane.getView().className = 'formpane';
                document.body.appendChild(formPane.getView());

                g3d.setEye(0, 700, 1000);
                g3d.setCenter(0, 150, 0);
                g3d.setGridVisible(true);

                node1 = createNode(-300, Math.PI/6, false, false);
                node2 = createNode(300, 0, true, true);

                sm = dm.sm();
                sm.ms(function(e){
                    currentNode = sm.ld();
                    formPane.setDisabled(currentNode == null);
                    if(currentNode){
                        formPane.v('frameVisible', currentNode.s('wf.visible'));
                        formPane.v('frameShort', currentNode.s('wf.short'));
                        formPane.v('frameWidth', currentNode.s('wf.width'));
                        formPane.v('frameSize', currentNode.a('frame.size'));
                        formPane.v('frameRotation', currentNode.a('frame.rotation'));
                        ['left', 'right', 'top', 'bottom', 'front', 'back'].forEach(function(face){
                            formPane.v(face + 'Angle', currentNode.s(face + '.angle'));
                        });
                    }
                });
                sm.setSelectionMode('single');
                sm.ss(node1);

                g3d.getWireframe = function(data){
                    if(data.s('wf.visible')){
                        var size = data.a('frame.size');
                        return {
                            width: data.s('wf.width'),
                            short: data.s('wf.short'),
                            color: data.s('wf.color'),
                            mat: ht.Default.createMatrix([
                                { s3: [size, size, size] },
                                { r3: [0, data.a('frame.rotation'), 0]}
                            ])
                        };
                    }
                    return null;
                };

            }

            function createNode(x, angle, short, toggleable){
                var node = new ht.CSGBox();
                dm.add(node);
                node.s3(300, 300, 300);
                node.p3(x, 150, 0);
                node.s({
                    'all.image': 'DieTexture',
                    'back.uv': [0.25, 0.75, 0.25, 1, 0.5, 1, 0.5, 0.75],
                    'front.uv': [0.25, 0.25, 0.25, 0.5, 0.5, 0.5, 0.5, 0.25],
                    'top.uv': [0.25, 0.5, 0.25, 0.75, 0.5, 0.75, 0.5, 0.5],
                    'bottom.uv': [0.25, 0, 0.25, 0.25, 0.5, 0.25, 0.5, 0],
                    'left.uv': [0, 0.75, 0, 1, 0.25, 1, 0.25, 0.75],
                    'right.uv': [0.5, 0.75, 0.5, 1, 0.75, 1, 0.75, 0.75],

                    'left.angle': angle,
                    'right.angle': angle,
                    'top.angle': angle,
                    'bottom.angle': angle,
                    'front.angle': angle,
                    'back.angle': angle,

                    'left.toggleable': toggleable,
                    'right.toggleable': toggleable,
                    'top.toggleable': toggleable,
                    'bottom.toggleable': toggleable,
                    'front.toggleable': toggleable,
                    'back.toggleable': toggleable,

                    'left.axis': 'bottom',
                    'right.axis': 'bottom',
                    'top.axis': 'top',
                    'bottom.axis': 'bottom',
                    'front.axis': 'bottom',
                    'back.axis': 'bottom',

                    'wf.visible': true,
                    'wf.short': short,
                    'wf.width': 3
                });
                node.a({
                    'frame.size': 1,
                    'frame.rotation': 0
                });
                return node;
            }

            function createFormPane(){
                formPane = new ht.widget.FormPane();
                formPane.setWidth(230);
                formPane.setHeight(350);

                formPane.addRow([{ element: 'Face:', font: 'bold 12px arial, sans-serif' }], [0.1]);
                formPane.addRow([], [0.1], 1.01, {background: '#43AFF1'});

                ['left', 'right', 'top', 'bottom', 'front', 'back'].forEach(function(face){
                    formPane.addRow([face,
                        {
                            id: face + 'Angle',
                            slider: {
                                min: 0,
                                max: Math.PI/2,
                                onValueChanged: function(){
                                    if(currentNode){
                                        currentNode.s(face + '.angle', this.getValue());
                                    }
                                }
                            }
                        }
                    ], [70, 0.1]);
                });

                formPane.addRow([{ element: 'Frame:', font: 'bold 12px arial, sans-serif' }], [0.1]);
                formPane.addRow([], [0.1], 1.01, {background: '#43AFF1'});
                formPane.addRow([
                    {
                        id: 'frameVisible',
                        checkBox: {
                            label: 'visible',
                            onValueChanged: function(oldValue, newValue){
                                if(currentNode){
                                    currentNode.s('wf.visible', newValue);
                                }
                            }
                        }
                    },
                    {
                        id: 'frameShort',
                        checkBox: {
                            label: 'short',
                            onValueChanged: function(oldValue, newValue){
                                if(currentNode){
                                    currentNode.s('wf.short', newValue);
                                }
                            }
                        }
                    }
                ], [0.1, 0.1]);
                formPane.addRow(['width',
                    {
                        id: 'frameWidth',
                        slider: {
                            min: 0,
                            max: 8,
                            step: 1,
                            onValueChanged: function(oldValue, newValue){
                                if(currentNode){
                                    currentNode.s('wf.width', newValue);
                                }
                            }
                        }
                    }
                ], [70, 0.1]);
                formPane.addRow(['size:',
                    {
                        id: 'frameSize',
                        slider: {
                            min: 0,
                            max: 2,
                            onValueChanged: function(){
                                if(currentNode){
                                    currentNode.a('frame.size', this.getValue());
                                }
                            }
                        }
                    }
                ], [70, 0.1]);
                formPane.addRow(['rotation',
                    {
                        id: 'frameRotation',
                        slider: {
                            min: 0,
                            max: Math.PI,
                            onValueChanged: function(oldValue, newValue){
                                if(currentNode){
                                    currentNode.a('frame.rotation', this.getValue());
                                }
                            }
                        }
                    }
                ], [70, 0.1]);
            }


        </script>
    </head>
    <body onload="init();">
    </body>
</html>
