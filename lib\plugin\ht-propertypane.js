!function(A,o){"use strict";var A=A.ht,r=A.widget,h=<PERSON><PERSON>Default,z=A.Color,w=h.getInternal(),q=w.fillRect,k=h.setImage,G=h.getImage,n=h.drawCenterImage,V=w.layout,j=h.def,i=(A.IsGetter.caseSensitive=1,k("proerptypane_category",16,16,"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAACxSURBVHjaYrx68STD1p0H/zPgAN7u9owMeAALjGFubo4hefLkSQZCgAVdICwsjGHVqlUoYk5ufigu3LdrEyNWA0CasRmCrAEdMCFzYJrQXQAKIxhG5mP1ArpmbAGJzGchJsCQYwmkGcYHsRlB0YiumFDU4Y0FslxAlYRUWlqKIdnd3U3QBRhekJCQYHjx4gXRscCErhmZJjkQQTZjcwHRSRlmCDrAl5RZ0AOM1GgECDAAKhF1/YP8df0AAAAASUVORK5CYII="),k("proerptypane_sort",16,16,"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAACqSURBVHjaYrx68SQDMnBy8/u/b9cmRgYswMltD1DOBUWOCZmzdefB/8g0OkDXjGFAb28vA8h2EI3LBTgNQLcVmyuwuYARFgYgv2NqQA0LbGHAgksDNgOxASZkxbhofIAFm1NxRSNOA4gNA7wGkBsGjOgpEaa5uLiYwdvdnhFX/MNig4mQZhAoLmZFUYPMZyKkGQTw8ZlwOxs1DGC2oruG4pSINRBJAQABBgDKqW8M60DHlgAAAABJRU5ErkJggg=="),w.addMethod(h,{propertyPaneHeaderLabelColor:h.labelColor,propertyPaneHeaderLabelFont:h.labelFont,propertyPaneSelectBackground:z.highlight,propertyPaneHeaderBackground:z.headerBackground},!0),j("ht.widget.PropertyPane",o,{ms_v:1,ms_fire:1,ms_ac:["headerLabelColor","headerLabelFont","headerLabelAlign","headerLabels","caseSensitive","indent","toolbarHeight","headerHeight","selectBackground","categoryIcon","sortIcon","sortFunc"],_caseSensitive:!(r.PropertyPane=function(A){var k=this,o=k._view=w.createView(1,k),z=k._propertyView=new r.PropertyView(A),j=k._input=h.createElement("input"),A=k._canvas=w.createCanvas(o);A.style.background=h.propertyPaneHeaderBackground||"",o.appendChild(A),o.appendChild(j),o.appendChild(z.getView()),z.isVisible=function(A){var o=j.value,z=this._visibleFunc,r=this.getPropertyName(A);if(r&&o)if(k._caseSensitive){if(r.indexOf(o)<0)return!1}else if(r.toLocaleLowerCase().indexOf(o.toLocaleLowerCase())<0)return!1;return!z||z(A)},z.mp(function(A){A=A.property;"indent"!==A&&"columnPosition"!==A&&"sortFunc"!==A&&"categorizable"!==A||k.iv()}),j.onkeydown=function(){z.ivm()},k._sortFunc=function(A,o){return h.sortFunc(z.getPropertyName(A),z.getPropertyName(o))},new i(k),k.iv()}),_headerLabels:["Property","Value"],_headerLabelColor:h.propertyPaneHeaderLabelColor,_headerLabelFont:h.propertyPaneHeaderLabelFont,_headerLabelAlign:"center",_indent:h.widgetIndent,_toolbarHeight:h.widgetTitleHeight,_headerHeight:h.widgetHeaderHeight,_selectBackground:h.propertyPaneSelectBackground,_categoryIcon:"proerptypane_category",_sortIcon:"proerptypane_sort",getPropertyView:function(){return this._propertyView},onPropertyChanged:function(A){this.iv()},addProperties:function(A){this._propertyView.addProperties(A)},drawHeaderLabel:function(A,o,z,r,k,j){var i=this;A.save(),A.beginPath(),A.rect(z,r,k,j),A.clip(),h.drawText(A,o,i._headerLabelFont,i._headerLabelColor,z,r,k,j,i._headerLabelAlign),A.restore()},validateImpl:function(){var A=this,o=this._propertyView,z=A._indent,r=A._canvas,k=A.getWidth(),j=A.getHeight(),i=A._toolbarHeight,c=A._headerHeight,h=i+c,X=A._selectBackground,b=A._input,d=A._headerLabels,r=(w.setCanvas(r,k,h),w.initContext(r)),X=(w.translateAndScale(r,0,0,1),r.clearRect(0,0,k,h),0<i?(o.isCategorizable()&&q(r,0,0,z,i,X),n(r,G(A._categoryIcon),z/2,i/2),o.getSortFunc()&&q(r,z,0,z,i,X),n(r,G(A._sortIcon),z+z/2,i/2),V(b,2*z+1,1,k-2*z-2,i-2),b.style.visibility="visible"):b.style.visibility="hidden",(z=o.getIndent())+o.getColumnPosition()*(k-z));0<c&&(A.drawHeaderLabel(r,d[0],0,i,X,c),A.drawHeaderLabel(r,d[1],X+1,i,k-X-1,c),w.drawVerticalLine(r,o.getColumnLineColor(),X,i,c),q(r,0,h-1,k,1,o.getRowLineColor())),V(o,0,h,k,j-h),r.restore()}}),function(A){this.pp=A,this.pv=A.getPropertyView(),this.addListeners()});j(i,o,{ms_listener:1,getView:function(){return this.pp._view},setCursor:function(A){this.getView().style.cursor=A},handle_mousedown:function(A){h.isLeftButton(A)&&this.handle_touchstart(A)},handleWindowMouseMove:function(A){this.handleWindowTouchMove(A)},handleWindowMouseUp:function(A){this.handleWindowTouchEnd(A)},lp:function(A){return h.getLogicalPoint(A,this.getView())},handle_mousemove:function(A){var o,z,r,k,j,i,c;w.getDragger()||(z=(o=this).pp,r=o.pv,c=z.getIndent(),k=z.getToolbarHeight(),j=z.getHeaderHeight(),i=(A=o.lp(A)).x,A=A.y,o.setCursor("default"),A<k?i<2*c&&o.setCursor("pointer"):A<k+j&&((A=(c=r.getIndent())+r.getColumnPosition()*(z.getWidth()-c))-10<i&&i<A+10&&o.setCursor("ew-resize")))},handle_touchstart:function(A){var o,z,r,k,j,i,c=this.pp;A.target!==c._input&&(h.preventDefault(A),o=this.pv,j=c.getIndent(),z=c.getToolbarHeight(),r=c.getHeaderHeight(),k=(i=this.lp(A)).x,i=i.y,this.setCursor("default"),i<z?k<j?o.setCategorizable(!o.isCategorizable()):k<2*j&&o.setSortFunc(o.getSortFunc()?null:c.getSortFunc()):i<z+r&&((i=(j=o.getIndent())+o.getColumnPosition()*(c.getWidth()-j))-10<k&&k<i+10&&h.startDragging(this,A)))},handleWindowTouchMove:function(A){var o=this.pp,z=this.pv,A=this.lp(A).x,r=z.getIndent(),o=o.getWidth()-r;16<o&&z.setColumnPosition(A=1-(z=16/o)<(A=(A=(A-r)/o)<z?z:A)?1-z:A)},handleWindowTouchEnd:function(A){}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);