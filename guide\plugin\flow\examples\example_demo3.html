<!DOCTYPE html>
<html>
    <head>
        <title></title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <style>
            .view {
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }
        </style>
        <script type="text/javascript">
           htconfig = {
                Style: {
                    'edge.color': "black",
                    'flow': true,
                    'edge.offset': 0,
                    'flow.element.background': "rgb(41, 249, 47)",
                    'flow.element.count': 1,
                    'flow.count': 2,
                    'flow.element.max': 10,
                    'flow.element.shadow.visible': false
                }
            };
        </script>
        <script type="text/javascript" src="../../../../lib/core/ht.js"></script>
        <script type="text/javascript" src="GridPainter.js"></script>
        <script type="text/javascript" src="../../../../lib/plugin/ht-flow.js"></script>
        <script type="text/javascript" src="../../../../lib/plugin/ht-edgetype.js"></script>
        <script type="text/javascript" src="../../../../lib/plugin/ht-autolayout.js"></script>
        <script type="text/javascript">
            ht.Default.setImage("server", 'data:image/png;base64,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');
            ht.Default.setImage("pc", "data:image/png;base64,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");
            ht.Default.setImage("router", 'data:image/png;base64,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');
            ht.Default.setImage("switch", 'data:image/png;base64,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');
            function init() {
                var graph = window.graph = new ht.graph.GraphView(),
                    dm = window.dm = graph.dm(),
                    view = graph.getView();
                
                document.body.appendChild(view);
                view.className = "view";
                
                var lastRouter = null,
                    firstRouter = null;
                for (var i = 0; i < 5; i++) {
                    var router = new ht.Node();
                    router.setImage("router");
                    dm.add(router);
                    if (lastRouter) {
                        var edge = new ht.Edge(router, lastRouter);
                        dm.add(edge);
                    }
                    lastRouter = router;
                    
                    if (!firstRouter) {
                        firstRouter = router;
                    }
                    createSwitch(router);
                }
                var edge = new ht.Edge(lastRouter, firstRouter);
                dm.add(edge);
                
                graph.enableFlow();
                
                window.addEventListener("resize", function() {
                    graph.iv();
                });
                
                graph.addBottomPainter(new ht.graph.GridPainter(graph));
                
                var autoLayout = new ht.layout.AutoLayout(graph);
                autoLayout.setRepulsion(2);
                autoLayout.layout("circular", function() {
                    graph.fitContent();
                });
            }
            function createSwitch(router) {
                var switcher = new ht.Node();
                    switcher.setImage("switch");
                    var edge = new ht.Edge(router,switcher);
                    dm.add(switcher);
                    dm.add(edge);
                   edge.s({
                        'edge.type': 'ripple',
                        'edge.ripple.size': 8,
                        'edge.ripple.straight': true,
                        'edge.color': 'red'
                   });
                    
                    
                var pc1 = new ht.Node(),
                    pc2 = new ht.Node(),
                    server = new ht.Node(),
                    edge1 = new ht.Edge(pc1, switcher),
                    edge2 = new ht.Edge(pc2, switcher),
                    edge3 = new ht.Edge(server, switcher);
                pc1.setImage("pc");
                pc2.setImage("pc");
                server.setImage("server");
                
                dm.add(pc1);
                dm.add(pc2);
                dm.add(server);
                
                dm.add(edge1);
                dm.add(edge2);
                dm.add(edge3);
            }
        </script>
    </head>
    <body onload="init();">
        
    </body>
</html>
