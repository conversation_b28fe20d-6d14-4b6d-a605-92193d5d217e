<!DOCTYPE html>
<html>
    <head>
        <title>Pipeline</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style> 
        <script src="../../../../lib/core/ht.js"></script>
        <script>                
                                     
            ht.Default.setImage('brick1', 'data:image/jpeg;base64,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');
            ht.Default.setImage('brick2', 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/7QAcUGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAAD/2wBDAAICAgICAQICAgICAgIDAwYEAwMDAwcFBQQGCAcICAgHCAgJCg0LCQkMCggICw8LDA0ODg4OCQsQEQ8OEQ0ODg7/2wBDAQICAgMDAwYEBAYOCQgJDg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg7/wAARCAEAAQADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD6w8XfGLxh4X8Safpdp4i8V6ne3MTSyB9emRI1BwO56nP4fhXKX37QnxHsIUd7zxGYJOFddZuXI/UfnxXmfxPlmj/aQiAkO230yFQnZiS5zn06cetUdP1lNZ8VXOki0muBFGzTERnbGo7t6c4/MV4uAwntaPM739Weliq/JVt0PX4/jl8TGtYw2p+IhI2T/wAh65BH15/Sqj/Hb4li82PqniAxleT/AMJHcxkH8zXHIIpI5PJ8twQQM84IqnfXNm2sQQQLKGk+UBhnBPv6cHn0ronhof1f/MyhWkz0a3+NvxHe8CJr3iFlJ4B8Q3LE5/Gs24+PHxRt/EBsjq/ib/pqW1m6Ajz0Od3f8q4LSmtU1yVJ4LhZEIaNiwVZPmwQD9P8mrt7d28sjPBp08skhKh3JyBnqP8AZ46UfVqb2/N/5h7aSWv6Hog+N/xI8l5ZNe8SxRjuus3LY7+tJB8dfiKW2SeINeYg5Uf23cL8vHU5rzJbNzslQbc43RLxgd/89eKYYfLuEMQYunUMNwOema0eFpf03/mZqvP+rHrQ+OXxBnyh1nxHC4By48QT4P05og+Nfjt5Nza34u+TIIPiK4Gfwz09683laKKNRtRZic7AMbVPc5+tQyQtCBsYhD8yuckBfwqPqdJ/8O/8y1iZ/wBJf5Hpl18cPHsen7I9S8YuwGWI8Rz5HfqO39Ki0v8AaH8a6ozRQat4kkCvtZ11+YkfX0rzi2fz2mEYL7hwSMEjOMc/j71oW2lCw0iS6sYV+1T5AK98fw/XGee9YSwSUtH+L/zOiOIurNa/I9B/4Xv45gnQT6l4tuIJF+UweIJd4P0JHHvUj/HTxddaZ9mfWPHdm7EbnTXpBIoBzwc9Djr6ZrhESC9b7WYo28xfMKeWFIJ4/Cs6ayhgk2sVkYy7ogxJXafb+nShYFX+J/eweKstvwPRJPjp4rvbiRV1b4gW7bcYTxJKPXB4NdfoPjvxzcx28t74l8f2sVyJDaSy69MyzBD82MN2zXk8On2UsBugqoggDOFGMAfz/lXruq6PZWVt8MLq01G9nS50+ZjZum1YDyxX3DE5B9MVy4jD8kG7u/qdFGrzSStoc/45+NPjfw5r2m6TaeLfESXdypkM8upTMiryAPvgZyD17D3rCuPjL8YJbEm08X36yDkSNdXAU+3+srx/41Q2d38SprLULueys0igYTQkAwnaWz9NxzjpzzU2iyatF4m0C0WT7fpgtmN/K6K3m8YC9eOxzg8iuvCUouhG6u31ObENqo7Ox6Pa/HH40SMFufFF1A8YDTKL64YsPbEh+tWbj4t/Gm2hmki8T6hcJGFcvNrV0vU9Nobnr+GDS3GlWd7oamzKWd3YWksjE4AI6gADv7+tcxa6rHLp8FxctMn7tPMAj57dFPPv9K3+q05Kxm68k79DWh+NXxkwWk1rWbhCeIoteu1c+wJc1Vuvjn8Xhct52oeKNPj3cFvEV5+JGG6CobG0tovEU6z3FtFAOUlzheeF+Xn3qbU9S+z+GZI4dOu9RuVm8qIWy7sEffbHIxj15NZyy+L2b+8uOMfVIuWnxq+LcqkprfiC625LlPEN18oAyeNxz2qE/Hn4ntqEduNf8SLJI2EUa5dZP1y/FUbPTpZ4bWSzj+xXghAEAHy7uo3DrkjP0zTr+1ineCCQTx3gl3lomyykfepfU4rq/vY/rPkbq/GL4sOwb/hIPEbKQQNviC4XBH/AzTJfjT8UxtX+1vF/u0fiK5wOP97p0rEuXW0024ku4I/lAkJHGMfMT+Q6VjLPLNardWpS6tZYvPjmjftxngDPcevH0pPCUnpr97/zBYia/pHU3H7QPxDgC28ep+M7y5b5cDxHMuD25z/h+NOuPjt8Tre3hQat4uSaTlPM8RXBBA685xn+teeJfx6j4uTTPssVxdQWhllltGDLCeSFc4GXJGfpXUWWlXNx4dmt7i4kSWUMYCwBMBf+PHTPtyOB1rmjhINuzf3s6HXaSul9yNKT9obx7sUx6p8Q5WXPmhdekAHuPm5Gaup8cvHc0AZfEXxHyU+6dZlBU+n+s5+tQLpyWNvEjWkO54nXeV4cLw2Pfp+dJaafbeWUMSx4OQ+eh6Y5oWAXWT+8JYvtFDF+OPjP7P8A6T4p+J9tKD9z+25zn8fM6Vc0f4zeMdRu4oZvF/xL09532W5uNbuAsh9iJKwrzSrK5vI/Jt2t1jgw535Mkn94+3bHt7mpviPpR0uHwDprKFNuBcmQTAne7gn5cnaP51jWwfJFvmd/U0p4hSkvdX3GB8SLWSb46eJb5ct9git18oPtD/JnafzrKjvL+1sEl0WxWzuHwLi52Z3jurH09ucYrZ8d3hH7SniLdAnkOLcvGRy37kcnHXtUMN/bR6bDbLFL5KqFBPzd+T9K9rL5Ww8W10PIxa/euxPY2MNvql3cwT3Ekc5XEblSFwOSCO+fpxikuFt7q4YmaFLhfvMp4Ppn8D1q79piO/7Ok8gUEu2Mj8Md6tsbe2s3ujp32yKdMOMZ/HHr0rblsZ8zZiiwjuGCm53KH+ZSOvt9OTzVlwIfs0cMck3LIAf4cdM/4960VghkkVrW6s/tCjc0Az5i+x468ilYNIwVt4wcnBzzS06AQGFxayH5lIXonWs5Lu6iyXgNwhkCSqgwWXHHPt1rZgs5I777RO/nu4Koq8KE/wAikurq1h2KQChfBZV6Env6AevSqtqktRXHXGnW1xHFeqHjljwhjL/JKOx9jTbZ0KiNg0bNk7XGDgVszxQzs8lgkm0IuUL5Gcc49z6VnW7W1zGZfNO7dtZouCMdVI/p1zUK3Uq9x08cQt/m2sy8pIjcnJ/lV6zX7P5khb5Y0DRMGByWzyPpVeCDyGmClmZeAJOv6f5NRSSyQ6lLttn2sqtknIV+c4pWV9Bp2JBCq3Ejxs3zDcHfrj09MU50hMm6REDOmBIOpP09Ktx+RNjfny852uMYb/Dmsuaf/Tvs4Dqig4dscnOCP8+1GiHdvU1bNtkjwSbc52SLn9Oa9Z12e0874V2sSj7VHoQNxswfnO8hSfUJt47V5FYIkV0GJBmKYJPJfPUH8q978b6Hpuk3Pwmu7LSU03UbnS5JLog/LLkdcdScZ5+npXFjo/u2/I68HL316nx78WNQis/jpN51rJdmWaCAQquQxMS8n/ZHc1rafcPBYx+XbRCEINrquAf/AK3NY3xB1G3g/aIvoLmGVZDNHtO3Kn9wDj6jHNdNYavawwNDcWhlhES4kEhwvTPGOtdOXP8AcJ+hhjVaozqLF4v7PG1ubhcec/OD6E+1ZmtDTbX4gQ6RLft56bX+0QwOI9jJuAfA7HPzHA578VdtLmBYbmPTLS61TaGkD3Mm1VRR8yoO/f61FqaR3G7W7eB5BPCEmhOQdnXG3/JrRr3rkJ2RnWWh20GqSGyvFmNwu6QvFglh6LnpWxLcz22ixQQxuCkgIUDGevfuM+vrTLO8i8u2hKiO5CYjQLgr7Gr86GWEIxUlWywHHIPFaTvLVmaaRJI6xab8kIjuJYisiq/3PUrWbZIWkgt/OKNFtUvPyQuecnqasyl21JWPlCJIicnru/wqEahFDAnnqyh38sgdAanlK5jO8W6XYajq6aQ9vcNbmJHFzu2x3Sg4dfbnHHBxj1qrbm0gtfsSJJbNBOYVQjAyP4V9h6/WtmBVW4nfezKZflO3G0cVJeWsF1Gh3GKUNjzFOD/wL3rFI0vfQrWVtDG0rr5LPM5YsF2g9vz4q2jGKHcqB8PtwOc8VnpavbXEz+bMY3OEB6R49Kb9tuD4gEJgaQwwDL8eW+4jp3yAKTVwuXJRHc3Fss2Umh+4U5A3cnp36U/WrVbPU9Lu4p1kiuoN7oRt8tskdOhPGc8cVX814x5kJbJGQNuMirGp3ZvdFh3ogexRY1VF6Lknn3OetJQdy+ZE1taEsihTNJLKu1RySCeeP6VB8b57I/Gjw8mlWP2CwLPJdi4jIljG9gqvnuNnTnG7FaWkRrJcLcAziS2VXQLwWYt8g7dyMnsK5r4ra5Br3xG8OwB7u7uLWP7PeyysWLT5dyM+isxGevFcWP0pvyTOjBv3zlPHLuf2rtciZcRyJbYwnzf6pRz+vNa9poGnJdGe5muPLSUAeUo29Oozywx9BXNePEvG/au12GNg9t/o4yn3gRErHI/HrXWyStcaSLNUiTjcCEywz612Zem8NE5sZpVY/dpdvrUlol47QIu6ErH97Pb69setIb9W2QQ2xG7mKYcbvzrEs7OCG3O64PnINx2JgoQeuPTrz35qdbmysfCc1xdN5dtEz+YZpcbeeeewPp+ldsYuSsjlbSepuRRwXEPnRkRyCQA7Blskdfp0pq4W5aU5jCjBPY/545rP8NlbSzE6z+fbSvuUN0x3GB2Nbep39hLq11IkC2JSN5isTFg6DoBxWbbW5aSezIIba4GoCfefIYHc0so2n0UDt9f51Z+y6Ve4j1J5jAwHKgHHfkVRji/eW+pw6pPJpjxJiExhdjnHTn/64q+0kRvIyWLL6E4OP8mrm7tNMiOm6K95BDY+ILZrSa8S08r5CoyBjpn/AArYeMH7fdzRs074O8Jjc5/ib/GmujR7p2jmFmjHGOM9+D+dRXU7TXE21jDbBg0aF8jbtGQfrWUraI0V9TOuxqM0sf2V4/OQfvkIyzr/AI1owt58MUgQEn7+eP8AJrN0+K4F1qElk0l35A8xomyCiEfez6HpjtircN3G8wMJ5A3Pkdj2/UVs42jYzTvqabRomnlyzMGcKMdMc/5+lZPkNBCqlPMZm3+oB/xqxqF3Ha2KR4kk81NyZBKq31HaoJ3c6fHKxSGeTDLskyg/2fx/rWDTXQ3TTLybYphKiI5ZOUK8x+49+teu67rE2oa18NLWWWV4tP0hYkknbJdnVnwPQKGCDvha8j0dp28TWq3ALJOcHHQc/wCeK978XaGunN8PJHtPIuY5bmNJw2FlQDr9RuP1OK5sY/3MvQ2wv8RHx941Yv8AtKaj9ojY2/8AaKK5jPIj8lQcehOak8M6Pf20d62rS+bctfyPDEBiCKH+Db+GT+NV/FF03/DRGvPcLGALk7SvTARQM+h4PNdnaPHKwibGx48AHng47enNdGXQvQi7mWMnapJWJY9W0lby4ghuRI8GMGP7pY46fpV43nk7A1u8tuXCqAMMR/nNc9o2l2tj9oxNNc6iHZ2KjkBTxx/n9K2bO5B8OmUFjGc/JKMMCfX3xzXbKEebRnLGTtqINkviiRVijDiXczqeWU9CR647eta0oHl3FzkEIm4ovU4/rWTpcmdUkuG+Xe5UAtyMcfl7962Bc2y3XlbmTgudo+vH19qza6Dv3I0iuGtHjVdzEZiDrnzB7H8az4G8xo45UZmU85X/AFZ9/wDGtzUJLST4fi3sN9pdCcvK/wB7IOflx/CckHPtWXaQ+db7/n4Qbx0yR7/54rMuyGrDI/kgeZxzhBx6/j9KfP8AJiWfOwOCVXiQN2/CravJDNIxfy8AbQuQyj/6/rVAoLrR9XW4jntjLEiWkizfvYHOGLY/ujkYOc5+lZO6LVie684yReTHCoc+ZLFtOCvcgfX9ayf9W06+V/FuQ9CvtXRpFFFCjRXTSXUsSqcKQAR15PQHH/6qz7kxyaoXTKlIViIHRm53NUxKdh7gywwxyEEMnDDs3pWZPENmoKgaVIz82Op7bhjqOT9a6ixt4X0t5ktGvLlnESwCTAY4+6OnzHt7/WsDTtQuIbw2GrW99bjy1WaMrny/m+7zjlT+VVqhadSfTLw2upabewQJIIJ0fy52IWX5hhG+uKqfELTVsPjjHYQ2TafF9pMsQlbeyrJk8kcNjJGehwKvMEiEFxJFFfwqRJNHIm3Jzwpx/MdOKwvEt9cX3xC8Om7iUyW8McbF1zkZZgD64zjPXiuTGNujO/Y6MMrVYnFeMrgWn7WWru9xDBGUjAlebCyHyV9f4sZ/+vVu2vTc30U1qG+ynIeQJjd649qTxjbae/7VmrNd2a3wiu4N6E8uohTAPbbn+VdB5hl3GQIo/upgKv8A9auzK4y9hH0OfHNe1Yo8uK4O2NEcnBYJkn6+1ZniHRtUvtDePTLjTyJDhornlXP972PB/St6H5rd1hQmTHzYPLKP8M+1UDv+UhiIsE5HB/D9a7YNxkmuhySs1ZmPpdncJYjSnu7e5upWKqFI8wMMAhSOMDHU/rUw0Zl1aRbo3JCHakkvyq/f6H6itC1EVpIrRma0ec8Slctzw34c1p3pkghtLctHdwIGYMjE5z9fT8Kcved2NPlWhUSXE8FphhahB99QASTyEPXPeqq6o3mItoy+Yf3e6TnA9/f3/lVsuz7fmDMi55POe1LH5f2FohGYZJZfmCttVmPHPt/P9ayktdColnSZTNpqC4cucH5XbI25961kv1trhUXy13uDCpxjHfd6/TpWM337uPy/Juosgjbgk46/Q+tcbZ3GpxtO2xllDH9w7DC89vY+npWtLDqom72sRKpyvY6jxFNHNdWl/wCSpubXdlkGN0ZG05x35x9MelXYJGu95gaOEC2A2PJwGB9RnA9qxn1Ga6t/ssvlQMCCwEYIIdfUc4JracxQ2YKlLdBEVcKv3RjrgdQcn3qYrkdmU3zK463ULoASG4iv5HjEhiCN50bA/Mo/hI56daguVhgadIrmG52pvCJyRxwR7Zzx147VhaPeyCaW3+VLuNzJFMnDPFwCMdMdOe9aVpKJNau0fEckt2ASrAKc4wwx0P8AjWmIhaVrE0pXV7mro9yq3Fu6ttkLfOT0J9f/AK9fSni3WptUX4TW04X9zpUkztjBaR2kU/8Aosfma+YWhtoNcjmi87YznnO3n0x2r1QXlvP8TPB8sRljkOlKHgkm3J5gRtxUEcZ4OPUnrXk5g+Wiz0MGr1UeA+OEjm+PGvxSb4Ua92ghgDwg5PtVy01Gdza3drBeSWRY5cxbehwc5/l3riPGsP279rjW/NkumS21pCFilEbsQikgN0A7ZP8AWvTIkv7dEh1Ca0uXDsweDIjCnlRg91U4J74J4zW2Xz5qUdDPGQ5ZvU33SJrcS+TGLo5JdeD+dJcWUz8BCkhXcVkiwH6f/W/CltTtmtvLheQv852jd07/AE966OVFkjPnX7rmBTNLDJ+8VueQTkZIxx3P1rsm7M5o6nEPO0sckE8kItihU7Fy3+7n+tOW2t4tQiJmb7OQNqF8bf8Ae9q210vS4JPJkvbm1kuSBHcA5ZG5xuGOm4jIHWl/smS18N/ZJrix1tBKXgTlQSSc8nnP6GoUkirXJruZo7iGwtikIMRL4XO5W6HNZkOoxhpLO2ws8ThD5iEEe9SywTWv72WKAuAilSQT7cdiP0qG3n3XVyso23cn7yRANpYZxQ1fYV7bm1aRq99N9reTyQ+wDP8ArPlGc9woOapw2LWjOjspt/uidW3Z5I5zz3FNiZP7U8vy/wB+ijGE6g1KkoMcrKUG05IY9dp54rncdTRz0KOrSnTbhIhcBbgSggAZGMZOf+AkUjMsUgbcpB5R85GOtLqstxBrRhWWHybm2Eodody7scKx647H/INprSLT7q3t2hTeQHh2HcvTPT/OKlMp6oge8uLTTJJbEYu96vGOqsR1qV7u1ubHUf7VndNamCz2cYGTJu+8PqDn26+1WRErW8irF5kcvO5MDHuOlc0fMb4mRXckE6W1jbmGAPa/MrKcFj7cDAHHWh1EtLDUG9S3C7NNkyNBM3y4zlXIxg/y/OqHiG+vdS+Mlu9zB5N5G8MA2R7c7VxnjHJx/wDrroby1hu7y6ms7sw28xX7IFIMDuB+9+dsbCcHj1rltVvtQvPjV4dt7+3ktL4RQhy8YHmBUOHKj1UDnvXJipr2Ur9mdFCDVRWOJ8e75v2rvEoWTbmWLyyG54iTv6cdexrqzK0mnvKok3tkZHJzXO+L7KD/AIai1yLdIq/akXIPU+SjY+nT0611OnyNdNI0UbI0UvlZ29WGOg/rzXo5TTth4tnJjpfvWkaCqVXqUbAB+v8AhzXPalY6zdatbyabIBBsA64wffn6VuKRPHc2j7jIflZ0GGGf/wBZqH7Zq9pM+nLoD/YmtxHFeu+ApH3SeOnXvnpXpU1Z3RxS10Zhh9SgvBa31vcCFZ/+PnILKeh4GQVzzV+/l1D93BbqLlgDsdE6HB+Zh1/CrVk5ntQZU8jaOUfn/Hj09qqX95HYapD5qGeO9l8oKnVMnhsdcdaVTV7BDYzNEn/tOxN2l3fTzh8zQXluEaAjgg47ZBwPQiujvbrbaow2MqOAxQYIPH6/0qklnH9oupxLLJMSrZeQgYHcDpk5/HPtVbUHd41RZBF8wY54B5+bPfPSs5xU56Fxk4xuSRGSXVpVt5FW6A/5a5IIPTPtVa91C/07TZr+4AdflhAiTI3E479jWlbxSNvbzP8ASUOV4xlMYH078VfvrKO/0D7Pd22QX5RT97A9ff1rJSUXYvfUxrC6sJbqWfS7mQwzkGcSnueqquOx4rTu9g0GUo0jl1YqSc/gP8Kzv7MghhgdFjjki4wFIwv8s/z/AApLhmuLe/tLXdOQhAlB43k/Lzzgjr9KmMrSVynG6djFknmiuEurVkV40CtlfvL0Gfbk89afpktzHcXdxNvaJFG0iQYbJOCV9Bj64pIor6OYLdxSSOiZ/dyZDe/068cngVdtJ7nT9QnVI7RZLgstuZ8BR6ZPpjn1zXsyUWjz4t3Ow8NfY9U1L/Tg6SxAmRCNoPo3HcZ+tez6no0mmeMPAHmIxklsZpvnGPlBbBGe5BHtXzrY2Vzpt4JbxxPbhFIuoZgyrK33hx16da+kL/UrnXvHHw/33qyiDwxbiIR7c4CNkYHTnPNfNZmk6cmezgnapFHxp4rs/tX7WPisqqS3EGoSyYC/MRkIDnjpk/X8OfT7+a2u1gMZki3run42qP8Ad9j6VwWsoYv2nvE86IwMt/crjPA2sv5ZHau5Rrea3VoSs0SfI/H3TwCfc1tllNKgmRjp81Vm3Dc+XbqkTmOcZGe2z/Hj6VcsZrWaOafYskbAklPlbGM8etULKBLjxFIunqCYhl45X+XOM4/+tUxutRgvG0lvD0yxzjzPtYi2FPQHtjp+tdU7bI5ol3VTZr4f8+08yDyZR5c8p3MxP8WOmPb+VctcSXcskECL9riDpuGwqWxzz+P0zWzcyMPJ0ybd5Q+dsj+LpzWNqDizzKjyjzU25xllwTzjjoM/pUKKSHzNkr2d5eRxz3t99t1kzhbmWWFR8ycDoPu4AAHYfnW1e39pLpd2bYxm+QiTyI+JEGevPbr/AJNVrKC4Ojia91GW4uWlVopF+U+T/Cdvq3pVS50yU6pJqHnSLIH2+Uo+Qrxwc9qlK+5Q/ZM99FJb3qiQ4GQTgN1/LrUj+bHG8wjQTRsQTtG0jucd6fEBdXzR21xE12mf3cXVtuM8flz9KmvIrz+y707WieNoVKjBJ3/dYD/ewPY1Eo8r1KWpe8yGcKQArrEoctyCM849RxWzZw3Mkcd1aSwPEruqox+6dpz79ABXl0GoX8WoC2ubV4GcFYpQ+Ebb1GOx59hXY2Tzr+6EriEoxADZ55yR6dv1qZwsOMhIJd9v9oRf3TsWRN4yi/405ZFt9WaWHcyuoLH+6O/61cWJzpMkczeVKV/dMB8pYZ/T+dUoYPthu986W08VoWjAXiRx0X6Gsti3qbdn9l1PULayvlQxb94dAVGD64/n9K83NpOvxk0aWfck0tyxBIyMbTjHtgDjjpXWeFrq5tfH2n3mp/aLGxivEafeAyleAeP7uCeOO3pWl4v0200X4/ado1ne2eqW9qVkS7t8COVZIt6+vOCMjtXHjIr2U/Q6MLJ+0ieMeMLe5uP20Ncs4migea8jSPzeI0byV+b6cH6V1UBltZtkV2u4ZXzU43e4H4Vh+Nbe2k/aQ8Q3BhUvHfoEl3fNH+5TqPT/AOvUk98baGKMIVYyYLFe2Oo9q9fLIOOGjfaxw42adVmlcaklj4iN1cTE/bdod8AeU68Zx6nv9aku5bqXULWJZJWjB8yUMeMDp+HWqNrqFnc27R+crzowDxuuQp68Z7e9JDa2sGtT6oGaOWZdkql8h/T8fyr0aUYXvE45yl1Njeiws7yGPqc5xxx+ves6+gmv9NnhwkEzr+6cHP0P09qfc+TIu0/N3XcOCaks5TLYpJdiSCcElUbgkZ605vk2CKvczPsusabp8RiuvtzvtjmUSgFB/EcY7Y6elSXjrBZwESkRSop80xck5HBP50uoz3As0MDwh5CcOykrgY+ZvbJA/GktLsXWgi31AEzCTynUKW8wr90Kfw69q5Krl8TN4JbFyMXm27uUiAML7hnBLJ3x9PTrVm71BA0LpIRvUbFK8E+uT+PHaiymnt4WR7F7cOvKyru43cVaa2jvIY7Zo3eQnzY8HBB/oOKw5W9TS6SsyjbXl4+vWvlSW8Ucb+bdlurr/dGe/vUlncNFcXMW1Irb7QXATgkn1/lWU8MUTXMUUv2wDaDPKuJV65QkcEHjHHGKspNA8xJLRoOZDsJ9P6DpWnLz2fYm/LoWGgjh1z7fgw5IOwnHlL3z7GsHXRKmsRt8/kld6Kx/z2rr/IWSxVzceYrgp0yYj2b3B/PiuZ1qwklWOYRCdFBimR+gT+/7Hj8jXTh5/vEY1Y+7oaGgw3dxod/HcShGjgLwKvIl9v5//Wr0TwpC0fxQ8P7lnRRpCeaWfO1/KJC4X6jjqO+K8u8O3UZ1ZrYySsI0byuBtOe36f8A6q9vt7/7Z8aNEnZbdYJdHjEhjHUiEKePXIHPrXl53BWkzvyyeqR806u9zL+0lq5WeOJf7RuVZzzu+fPA/r7V6FHHKujlgPNVHHmSBdoHPH0NeUXRE/7RE6PPJFE2pXTeYDwSrdH/ADNemXlw9tpMfluIJJW53D5XA+7z7/nW2XyvQS9PyRni1aqzqNAhuh4qlnbUHtYX2yyyIvzDaTx9PWtiLWVfXL+3kkN08znDlOEXtjPb6VzFlerdW0UaNCzFR5uPkLnj36fpxWbbeHdTi8YXWsxvcQxyRhCZj8oBOP3Y656jPt3rqVKMr8zsc/O1sjrtQtrGLS4buEsbiJ8SRSNjcvdh6jPGar3Ki80GcWsYuZntnNsDjlsHaM9uaW6nZGuGvI0gtbeD955nLcYPLe/Uj27UzT5Wext3+UBxuBT+7zyP0/CsLWLvcrWtnrx8PWV/rMTCZgqzKjhfLIHQgADt2pbu5ihitrhZ28h0BkaaEkgjqMDqMVr3mrXsen2i+baQaO920d3G8JZmDL8jgn7q5+9w3XtWf9qt44TAtzCk1rPslQjcrgZ4X9OeaiTu7l9CoLIDXJr+xjRi48xJUXGRgc/Tr71e1O6/4pG6njxDdlUeMsu4HDA4Of59vxq5BcWwOwadPbeWn7ucFjn8O5/p9KdeWbT6bJAjx7Zch2ZfuqR2/Os3Jt6lRXY5LS7tn1i68+2LTXDGVzL80dvgHcVY9B7fT056OASGzlSCPoOJR6Hsa4x7WLR7z+z7nUF1GaWHZuQ7Ug7bffOPwPr1rXg1q2tZLW1CNaxEhPPdDtkYDr9On19qc6WnugpX3Lt2ZoI7SUvmGa8+yjY2SrbQ2SM529s46/Q0Isj6g7RlxuO1VPb/AOtWhcPYHw3Ha2llBJfys8k8pxyMIEC+w+b35qv5SWU0TqbgsjchjlW7YAPb/GsOVo1vchnhuXtRvCPAn7tzkEHJ4FV7h3n+N2lIZ2JSAR5PykbIiuPoMVtahocV3NbDR9UkS0Fl5l/FEC7QYxn73U98dPf08/0m6uZ/jZYzSwSWdqsEg82V97SkIRu9gfSuDGztSl6HXhYfvFqYPiq5839qTxgCYnsTeLlwv/TBM5PTit9bK0uNLd2mWOdAPIZwWzx19Ow+tZfjGCF/2ofEsT3CrHLeKQ4XAP7lMgj8q08osccYJbL7VzwPw969/LY3wsE30R5OLl++bMK5H9mabzay30Mo2ea6jMZORu+uTkD27Vr6bb7vDcEl5Mzuw2gSLgnB4+p5/X2qXetxblZQDG42kJ8w/T+dQ3VqJY03opRJVdM5PPb6D867VDlasc/NdaotfaLeKG8jwq2k8SiUzgblI6bT/COv1qmuqW0trsEke08B3XBX2/X8eKzb1TfTNFFan54/LlBfKs3OPpnmsS10JNLvAl0zXBvDm4t590o+UfKEy3BHPHQ+9O8b2adxWdrp6GrpNy6XjWNt/aCwFWNjPgxyMSMMSDkY9vSuiUyW7QTpbRRWrgElQMq2cHJ9/wAKx7TR3i8yOW7uZY7ZM2c7uwkiJz26bAO1adjpE1tcTS3M3mWJAMiQH5gf4n54544qarTd2VBWWjOiuWWSyDuGWSHGCnG/PY/5zVmBysZkVDkcE4yRWQb77RMkoCvCBlQi4AHv7/n+lW/NVssqxCQNgjeePwFcqjc1bsZ7XFnDeSmBRa3MrEtGiFQ//wBbNVtMYS30sKy+ZMmcgDjpzux9fpW6126RypiNnJHzlPmA9j2rKury+i0+4lN40LMPkeKJeOe/qeeprSnDRxRM5a3ZoSwJZsPIdJQzAsVGVIPf6j07VZWQXtnfxja11FEfKGNpbA7evT86oW7mTT7Y3Fwskg+aaQL8p/8Ar89OBVGaOYyGaKRY32YAb7uD7+pqVB3aHzW1Nz4X6Zp3iD4jx+G9aSO0mfzPs+flYyhSQjf7x4z6muw0jSbTQ/iFpEFrfSagXgnaeO44aBt7rtPvgKc+hryHwnqWo6f8SNN1CO4ggvdNnF0u+LaylWDLk+nFfQD6nZ6x+1DbXlqm+9nsJri+yNgSWbMpVAeQiB0XHPQ1wZyrU3bqjry7WaZ8m293EfjF4itbxIY7W4v7jErn5gAzEMD3yf5CvY49N0vVPBN3PZXck/2d1iS9WJso3BPyt7H+fSvK7jTPP8bPbQeWlzLfzI8rvkj9+dx5xk7cn8K9niu7G002DTNMDfZdmACuBI3PPH8ZOD14HarwkWqMUiK8rzbORzFpVjJbmPMLDEUznJLcZOfX/Gu88OagDoMdjrYklW8b/Rg3ymIjn5T3PQ/jTZruG/8ACs1i9lb+XPEvlHbwrghgxbse2e9cTrz+KDpOj6ncabp/2Wzv97I8jpJM74JTgYQfLnP19cV1RpyqGDkoanay7LPxNJZ3VwqWLoixvOuQ5fnb79D78Vc1s6fpcdrFtjGoyBmMRBULDggBcZwfb2NcHqMsPjGOO4jsZbWZT+8h3krHL0GX6FRztPGM1WN7qP8Awkmk6Vq91aXV3doURbuYjlR/Djv159qy5JuXK+hpeKjdHUx3rahdR2UDtFvUmCVF5DY9xWsmneVPHBex2sMgjBMkUWAMAc4HepodLjtLz7NLfJDeW1rvV05dc5CFV6bf8+9U5LW9N4Ly5limkZ8ylGwQuMcA+pHQcCsnMpRMHxVPfadZwCJJCGfLSqPurn+L0Bo03VL28s4zLp9x5bxBllC5A56n26c9q6o6hcrDctbabLfLNbFHZyqqPTdu+nXB49azeXs1gt2kiuDhREHJA6jaeO/p3qlNcii4/MfLre5T+z2VzlJVUu27eIzkoQeCvoOv61X1K3tLPT7bhb60iZpTbPG0f7zsQeT8o/z3rfezW0kjjZd02xVckcg98+3T8K57xRrOl6Zb6R/aM13At7I4gYQ5jbHynPde3PTFZwvKasW9E7jNIs7U2N3PD5jGaUSSPI2FjOSTt77enJ5/Krm7dblJLlflPlsScDqMEe2aw9G1z9zqFjBaQtI6OhlRhGUYcqDx0PHPrWjJqaf2bbRzpBctPCstuXjIYEZ+ctjGGOeK0rwnzEQkmjc8MXy2Xjaxj1SM3CTzmzmiRyrypINrfMOg5G09iK5y4Ei/Fy1tPs0kZtfNiCsvJABGff8ATmtXVTdnVoluI7WC4WATGVBgonfnv1p1/ezX/wAfp7u7uRdO1qCCihePJULtAwOn45z614uLf7mfoejh1+8iea+LIUf9q7xhc7XaVLpI0B48s+SmTitNZHW8xM1ssJQMMv8AODn09KteNfAvj7WPix4h17w7pum3djdzrOsYvgJYvkUYbcBnlDyMiueXwX8RJIZB/wAI5dtdIgJUTxYHHuw9R+dexluOw1OhGLktjzsVhq06jaRteYi2qFG3nO3aqjav4+nvQmbm2KMx27yCeFI54xmuOn8G/FSFYgnhfURIcYMFzFtbrwcPnr9RzViLwz8WPL3S+EdTww2fIY9w+nzdO1dUc0wzduZGDwVa17G3K9ojlZZPPaKVQCpxJkdOPX25pfPzC11KvkS4yAZN2T9eOTx7Vzp8LePUvEz4a1yN1XLxMoYg5OOnGOtOi0Hx0FRh4W1bgEsRDnaf16Z/zito4/DW1mvvRnLC1v5X9zO00sz3t1deavkWkKh/Nn+VSigFifYfrimNqSyaOLy1ndXIX92qrkoWA3dcYwc//rriGh8dfvVHhzWjbkFXcwHy3THPvj9ODVOSw8YrbXIj0HXLYuNsii2bDDb246/571zVMZTlqpL70b08PNKziz0QRFLpHWTbHF0RR069BSxE/PtCRnfuYnue5Pv/AJ5rzcS+JrK3Kx6JrY8sZaOOzkyw/L68/wCHNqbV/EvkmS28P63uJDMBaMSB75HJ/wDr0QxFC/xIJUqnY9ItJI5BmU+XJJGQRICQMZ6/571Subm3MJjGVilQHBTG09DjPX6V51c+JtWazDSaNrluwQnAtWGQOo6fe68Vzi6zfRedfTQa9efPvETwONo64A24/CtY1KLd+YzcKiVuU9htZRDpflQ/u41X90ztyfrRNeGPLEZWTdweVz1rzaLxcP7LLy2eoyyPgoDCY8D/AGgV/D2/nQk8eNb3CRTLJGzhsFonCH2JA69a6XVpb3RjyVOiPSHNuZhLbS4VbJWMW3JD8M2fbj6j8K9u0WLZ8b/D0u1DFdaCLwTKdwKsm3A7gnHQ57+tfHEvjqG103yH0q8/eFySkZ2ruBGPXHtXv3wu+INl4j8fWMc0UkMunaUtuyP8uVQBQQD7fr9a8bNqkJUJWkejgIy9rHQ4/R5ba68beJ5RAXng1OVQd2PLLSSZ49wOvsfWvZtNUP4Vlitp7dGKFyGlC7e2f8968P1H4UeNovGGrap4Z1bSHW7u5J33ytEURnLKT1BOCOauf8K0+Icyk37w3fAJW2u9oH4EZLdOtcmGzSlGkot6nRWwU3NtI9ftIj/aEeootzqYMQt10+3QDqeZWJ/hXr68Vrf2Xe63pNpZLHrGl2DymV3ljVcFenHJ/Hjj615JpPg34uWNuba3a4aPJKkX6x5H90njj39al/sL4yWlpNaiG+8l8+bt1RGGe4GWzW8s0pNboyWBqJ7Ho1xPp+kQ3d7cPZ3Ef2lIpUEgWUjnkj146VBf694cHgk67Ld6c4kgCwwu8TSKxw2zAzhuOe1eQXXgfx/deZNNoE8r/wAbrcozv0Prz+Ncxq/hTxxPdL5Xgu7Fygw8kUcY3AZPI3c1zVMwgndM1p4ObVmj6jsnN3ZJfTuvktgQAR+WQnJGQecAY4rMWZZo55rnEVu/yRCOMmQZ459uvHavBLTVvi7punxxGDVvsqHLxXMaSAdOC3J44q9Hq/jm30eWJ9I1O2WU5ZJLfcR3yGA/TirhmNGSeopYSpG2h9Oaf9njtYLOCNreJDhNi43cdDntVGHw3pVlrE2rJDOl88nnSyvMSGKnOMdNo2jivni18UeLrNYZfJ1va6ZjSa2c7T+XbFX/APhZviuTbbvZr56NveT7I7MyjjkHp9azeJgtpFqjLqj2+X7Jqotr25SWFsZZEwRJg8Z/wrgvHlnHrPhGTSlhjvLSWH7RBcCEmaykRiJMHtlTjB/oDXAr401eG6jn23CR7v8AVNCVVu/PfPNZuvfEHWzfJqzSXF5dpKskNm6sYyR0+XjI6+pq6eJVNqSewOjKWltxmmTSRWupCCC9tpBa7LSeaLIuCeD97jHfnv8AWvSdG1uPVoYbTVbD7NcQw+Wnmx/Ju6j7vCscE56fnXKJ8Xbafw79pm0NXvWVisDfLGG7/J1/CqNp8V7X7RNJqNhFbyS4ZhbcZb/gX4e9ddXMqdRWtqc8MJOLvc9aS5SfXre3kR7qGKAoXPUZ5Zcf0rClt/K+LVv9lmDRJY4z2I24H49K4mb4laT9ma5WKeGRnyQRhdnu3rWv4U12z1/xlcXdsFJNk4dGl3gdB97rj+teRjJJ0Zeh34aLVRHpPin4q+NNL+PGt+GtJtNCiisJ1jie7gdpJMxqxbhhgcn8qozfFL4hLMZHtfD028bt8Vo+Tx/106cdK5TxUIT+1l41un3ROLyLDngkeXHz16du361tJbbtWkuSjxRBh5czfKGByMpnuOK6MNgKdSim1qYVcVKM7HRQfEHxhLkMNGjl2DJ+xY+o+8ffj1zUl18S/GdvqU1t/ZNhcIpKlhp0mEbAOB8w4/r9a4bWtZ8TaE0FxY2GnX9kt0sV7PJIQ+1+FUDsf9odB9aytd8c65pml2d7ql1ptto00uLdYA4ll/hKSAE4A5wev6V6CySEopxSOX+0pJ6s9Fh+IPim4aO48nRVH8ObVgWzjjl+ue1U5vif4ljjljs4tAurjB3Qm2fa3OCGbd0rG0PULHU5LoQ3JKWkikw+UA21uRnGcfXNbb2lkNUglt45Gt1UrORtV58ngsBxxzyMZxWE8rpwfK42ZrHHTavcWy8YeN10/wC0G20IW+4B0WBiR35XfkdetXX8fa/ukga20eQgcJ9lY9O4+bqeeKquLWGSSKAhNxyUMm44B/Sq16iNCEkk2zFg0UmMsh9Qfzqo5dh+kRSxtZ9SzcfEXxM8EiW+i6NJcAbkQ20i4HHo3QcU+H4m+K7W3UXOj6DZOzHMSRSklfoX6fyrIZlj1JEdpbhyDh2TCseMbueRgmqdxEsrMbmSVYFO0Ro2GkHRiD64/Hg1X9kYe/NYn+0KtrXIbz4o+I5biWzuPDujxRmTdJdhZNp9h83J/lWqPHfiOy0xnt9F0pFdfMiRUlO/2XDZJORx6+lQalothe2duNKupApU9AQj54Xng4HP1zT4Jv7C1hf9HmQAgxtbfvApG7afRSOeAS386x/sbDpbamv9pVXoULrx54tbR4dSvNP0+1Eh4D20gQtj7nLDkevaubg8T6tqUcJjtNNjjOHecRuQOvH3uBz1612t0/iLULG8s/7Skka5RGmhkiEiYP3tydN3Jw3X1qIWBfWLixuYHa9to1R2MWxZuCxC9BuXjiiWRUHq7NfMFm1VaLQ5lbnVxD+6hsJcEKD9k8wE8fLy3t7H9a9J0XTPEGg3VtN4h0/SrGTU9NN3ZpBDslWEZKF15wH9M5P41TTSI7eO4UX8e2CMzbYiD8wx8oXufp3qrp/ibUfFvja3ttVuBMllo5tI9vysUGfmGfQHGfauPH5VhKNFyjGz6bnThMfiKlSzloZHhfx/4q1r5tPhso53j3PCqk9MZ6sOf1/OuguviB4z0zULNLuOyijnVpC09owyBwdvzep+teG/DO/ebxxHYvbzwyMCiNjkFTw+fTjr9fSvpiIw2Phu6urqJb23R90tk0PmjzN+PkU9znt1zXXDBYR004xX4/5nNLFV1NqTMK3+JHipJGjkXR5C44X7M4x6fxfrWkPHfi+SCK4+w6L9kEhRn8l1Xd7jfkVUk0nSJb6I6fmG08weWoAUxMCcocehzxUOoy/YNUjkErTSAcFGwGPpg/jzW39nYZ2tEy+uVu5WtPixr114guNNW00RruInAS3kPHHU7uvbFGrfELxINejt5NK0eS2z88ixSBkOcDd83TOPzqRLmK7mS9i8r7QD+8dI9rDP69uvNWH81dPnuLa42zTj95+8yq7eOnqR39hWEsso2tY2jjal73Myfxbr3mATabpSxuG2A27gMceu7pxWX/wm2qRzOjW+nOWTDosDnsR2JwOtdPbn9+BcqXBGxgQDwe9UtL8uTUzAsYWFZdpYLjIBxkZ/lSeV4bblBY+r3MYeOda/dQtp2kyqf+ndwUUA89axpvHWoprDwpo2mXCFd7Mtu+B3456V1dzHY3djNFBfjzIiQjIdxJH8A98gfQVnWyW9xp+2WL7S6FAN4yQPUdhz+dZSyqg18P8AX3lrH1F1OXuPGGry6hHE+iWkRmHQWsuSAeOp/XvirVp4m11o5hL4d0yUM4IZLVwWA6HOe/T/ABrfktYZpLdj9+1DCJwcgZP549vrWLrlwY7m32jYqKArL696qOTYeLu0DzKs1ZMrapr+o2emq99oukR2+8PE727YXPHDZ6knpTIvEWqGx2weHtKlkOTH/oz8/L0/zzUqzXd/4Vji1Z0CJK7oGm81VI6YPrzSaZHc3Gl3FzaXklrd204cwxkgsrjg+gPX6803lWEtfl/MmOYV9uYw9P8AiFqt1YraR+HdEmhkYhmSJ228E8jp+FdfoesanZ+KotG1TSLTTXn037ajxxBd0RI2sVH8J5/SuZ0xJLLzEexeONn3OzxhcN74xz0q1ol6L/8AaYDSiVgmkFHQKAAwVM1wVsupU6MnbWx1QxlSdRLoO8dKkn7S/iK2RZY83abTjGB5S1qwSSWlxH9pjvbqxwvMp+WPn7+CeT0461Pq9lHrH7WXiaGdtsSTxBXY7TuMKYB+tT3sM1v5thHK5jmBSck5VR6rnODXsYCm/Yp9zgxM1z2MyO61q6XXF1QJDhWVI3GNiEcD37HPWvLdd8NSavo6R3sdx/Z8soMDfxR8/wAJ/vcgZ6H3r3aGzWLT/Ijc3uThi7bwU9ee3B61Fcvax2NvALiG3wgKo7qrBfXbz/dI6c+9fQ4XEuCSa1PJrUk3dM5XwPBqGn+DhpN5crc3lu20OExiPPyhu5PPau3Z/sdjMU2PjMhw2AFB7k9xk1zOh3Za+njm+0C76Mj2fl5HXIx22kDPtVySDyvECW5c3Nq9s2fOmV87mx9306euf0qMRBTqu46c3GBb+xW89w9/ZPEHdCSkBXAJJJGR/n86sLDeahpcsn2aKKS2iYq2fMJG3lMDGCcdfyrDv5Nat7dra2t7Bf3TFTbptZSPYfzq/wCFdS1Q6Uq38vm28UhVUJ5bpnf6/wA6KuHtDmTQqdX3rMz7HUhe/Y5Ft3aBlCl06K2Om084xnmrNydOnWVJY18mR2xtmIUtt6F+2eeKveVb2KyPGq28DSn/AFI+7kHBwP8AIqCCyS4sz5dzHLEwDwDJAYHrnPI/KuVQbV2zZyVzkNFbU9N017OFdQS3huHYvOwkOWAwgY5+TurZ5GK07+HU73Qbr7Hqr2V2R+93KTuXHA6j8+e9dcIIBBFbz5FqzrGxlOQN2AM46Dpz2wKZp0Hl319b6tbm32yFYIxOJIyuO7c5J59O3ritnXk5Xa1M1SVtGclHrWvaf4ZjkNuq2ZdfPKfvJYssR97AyMn0yK7G+t90lhCY5PNgL5aeUs04fHLNxxwOf/rVleIHlg1K1i0uyktrOXbvL7TEWwwbjn+gqxDqps9Mi02a2jvgsm+0Zsnap649uPu84/KnOTnqolRSjo2Wv7Ej0i6dru9CTIRHFCC0bKzHl1IzjBKsBznFNufB2q+D/iszXUsWpyy6aZY5YpSfNV1JyuccnOcGqer3EV/uluJ7a7ferDCkEcYTAx09+1dJJquo6n440q+1K8N3exaSil5EyGVIiqg4/wBnGSP8a8jNaanQcm9kd2Am41Ul1Z4T8Nik/jqFWIhkgiklAchjtXC56++cdvwr3aFvK1pozLPbMEM0jsu6KRTgnrkEcD6V5n8JNFLa9rerQyS+dBamNFkI27ZH5+o2jH417Fp0+mzeG7ay8+C2VbLIheAwhQG27hn+ENjHtTo03UpptbE1Zcs9GQTy7NJCXDytbo+9J3bMhJzznuTnrXN3llPfyC9h/cq48q4R3wWPQMvYEfl19ag068s9SvNStNYlLTWl40UZUbi4/hZdvG0grx/+uunXwtd6d4mH2gSzWU9qsltcrxGZC2Nh7bht6cHpXVVj7N8rMYS5tSG0077DathjNHlYzK4wCdoBz6c5/MVSuvOkvGgitbaCED5AThm98jqD+nFYsviHWpfFTadDo1x/Z6uouPNUqQ5zxn6gfTJ5rpY4omvo7pLRY52h24P3lA52/hQ6coP3g509inFNHB9+L5dpDupyVP09KGKFo5FK5j5SUcHH4dauWNpea7oN3qWmJPDIhdZ4nt8sxAHzL3wfXGMiqcJ87T8shDqMOCcE/h7UdRPYguQZlkaCG2aZzuw3yqW9Tj/JqjP9uOnbbKIGRV5QcBcdcZxxWzPbRR42SpLv6Jt2kLz+YFZkzeU1xb4BiaI+Y2PlVO/4nNZ77F2tucXrD6xb3Ud3DM01s4XdFkDY4/i9xz0q/fW/9o6WysCPNTcURc89hz2rRm0y2TRUvoLm6ng8ry51DBtgHQfQ1XtRO+nuB+8CD7w4xt69O1W2pQV+hK0kVY0k1S1MCWaWSJEyvAXzh1PRSOMHBwOtNimktvJ/s2KeCcOWKiNsMD13D16daGuMaXPNpcbbEBeRkUAr3Zh7nn8qJdSmCiZPPeSZFfYEwACuc8+35Vg9rM2vrcSdRJbszgiQ/vMSDAJ9qwvCrD/hfFxcGI73tZf3aNyv3enr0+tdRpRS4vI7e5iY+agR96nPOMdeo6dK5TSLf+z/ANoSWa4c/ZhaTIII1JIIxwB3Jxx16fjXFjmvq89OhvhU/bRR1Piu8Fn+0t4sS32xxyzREEruU/u04I79On8q1v3VxC6wx7mjRsnZtVfoTxVLxxbxv+0Z4jgkXcDeRyKAcYzEp7f5FbqXUM1wbSKzgVWf52K/fI/iP+RXZlzaoowxVnMis4obvQ3SW6SCYwFJV34Zf90Drj196wtY8P2fiGxZwyxagQtvCRK3m4Vslz7HA4PT8a6eS0sZrgLJs37dwjHB469O3vWPZrPL/q3lltoH/dzhyCHHb06fn+FenGKduZnI5NbI46+0r+z/AA/d6naX17JbQyi3tYJNxZEBAKN/sAl8EY5PvWdZXtp4f1a4vPEF3KJCVa0JdXilVxk8+qjOAOv616je28U2+KaFWtJ02zxuc5PfPPU/zridS0WW0hdI9OWW2NuJYrXyg7MBwNvbdxjOK0hJ87u9/wCtyJfCjvIVt7rT4ryKQSxygPG+chlPT8Pas66huYN2y7htYVzlAmGb2B9eevak0VYB4Bht7F7ppEi4V0IKnrtAPb27+tNgvZr1biGaDzyYiG2pjBHBH19uv86wrJqXKXTs1cL2KPSdFt7y4gu2SdTABF/rLdu349eO4zV7S9M1CXS4A0McKCMBDLMA7HqCBzg/jVW0gni0kWP2i/ci482P7S4lAA5CkDsMfnUd7rOo2ulzXvlNeTQHYUhDbSjdcD2GDUKMnOyZbcVG9jR1C3u4YbWZklixOFkdxxjrz9dvX60qSziGaJZmntyoaKV+drdMjHPHHFXoEbUNDs474xwu6BtpOUQkds06Kzhjh83fHv8AmjxAMbMeoPGM96uKcZO5F00jz6PX5rrx5Ho0mmPZo6F2SddzP+R/H3GfpW5faQ/9pLapaoL1HVvKLbSR6jGQT04rWS3tk1S5ufk+2SlUkkKjcQv3Bn0G41E+pXN2Xga0ffbfcmdSJNvYkdwfX0repUTs4KxlCFr8zK9zbLBYQ3D3Dr9li8m6tMbgCWPzjP4A4yOB05yujyRyeJLWO3gd40tmw2euI8nPuOcjuDWtY4eNLU3NhFO6Habp/Lg3erd9vH48Vow+HL/wv4o0uzvDbRalIjy3SRurhVZAVBwSMkH/ADmvIx0rYecfJno4VXqxfmfOfgDUdW0/xxNcRTfuJpTARKCqMNxx+Azn/Jr2rxZp+t6hqk9/aT6bPELJYLV1OCpK5fK8+456buOleL+Fj5uu31rJHL5UepyrFg4BXcxxnGOCPwr3XzY9I0N7gxs1vBEZZAqn5AMYzjtz171WEg/ZxlFixMl7RpoxvCl2lnoCW06xXGpGVppbhlA8vOPlX2Hb8a9TsL+SPUrQSM6QJl5Q5BDHHBGfTjnrmvJbKTR7jVrvUktLyaCMNvQfujC7c9xhuh4ru7g2upaalssplRoVw6fI0ZI4/H2rpqyc567mEFyx0NrxHeobWOPz1kljlEjuI8Fs5x9RzWRbRi6kMM4eOM8B0OMN2xWesMKrJBLLNuGAoPJQDGD9eKtS3UGk+H7m7cbV/wBYhGBv6gpk8ZIJP4VlyqKKu5MuLv06NpzK4KAqGyV3dsNnseOelYl1dabZ3Cyszx3Eq+Z5aSZQOSemM5z6dK5z+12v2tpYoZZ9PKNFO03y5bA+XrwcZptpqNtLqhXfHAUwkUYcc9P/AK34VSjdXFJ2di9eSmZYZoJFEyguDj5sHGcD05qjPbf6Rfu8zKzDeoZMgLng49v0rSlZPt0cu9oxtCZROGB7Z7Vl6wZIJoDLEEtiB5MpOQ/I/wA44707JMCM6ZKnzQ3K+XglSBtzu9h29vegRtbWbQWpTgf6oKSDnqMetaNpn7DHLiQ56RnoPpSsPPhDqZo5ATvzgA0m7ILXMn7IraekRhAY/OATgBuuf/rdK5/xO2sx/ZLizjS7zIqSqzgb178np9a6lplt5vJaKWKVOJA54Y4B/LkVDFbQ6tYywSSzRNg4/d5MbjkHB7deO/FZSfKrlx3MR7bXDa6dd3lm1vAYN1lLG4bEWcrgqehOeoGDTrXULW4/aCuDbhGXYz5K58uTywSPwJP4Vo2dzDNpMRe5ll2s6H935bIV6Ap0HH6Vymj26N+0NLFCjMNkpOecrtz/AJNebi7qhL0Oyg06sTpPF0zyftCa+5czyyTose0DIxEuBkfQ8+tac13e2TzPbRf2jIUAWPIGX7KcdvftVK+itZf2qNesp9kElzs8xANp3GEfNj8PyNbEkMMepJJvDLExVRGvfuSfSvTyqdqEX5HHjY/vGaXnyjJEDHj94VGcHqQD7UjXbR4jFvG9u75bGQc9zx3+tUtPuXnvJs7ViYgKVJ+9zj+VacpKQmFuhbCqD1/+uf6V3zV3c5YOxyd5cXT+IpVYbFjX5NqncBzz/ntXNte63Z65Z38Ectyo8xbn99uxnbjavp8vB4zz616QbSK7b5mEJQkCTHB9frx61lyWdskZEO4SlQoiAwWHrn+lSvdadrlXurEiSSppsb2u1x5W9u5OfTpwBnjrXM31xBJqeD9qR54dg2NwhzuDj+6c5569a6Bhfv8A2cbWGVlt9yzwCMID2Uk/nxxyKqtAl1eIhzFkfIvBkXrnn04PqfzqPbNu6K9mup0NlqWnzXEdmJLcak0Xm+ScCV1Axux3HH0zRqbsums8at5xIYyRx5CscgHB64A6d6fFpNtHp8F+NjalHE0Vubdtu5f7jk9R3HQZqv8A2qDo81zPLcKISxncIWGB97gelXFrmTREr2sySyby5ntnRxHECVmkQqJwcfNz259s1tIgEY2yZXawG053Z7H24H4gVhaXLd6lor3FzaO1vMP3CF/nCev06cV0cDxrp6lkUAp1yCQfT60pcydmNWtdHPFbm2mnje4e48xg8b7MFPY56549MVrQXEC6hJdXECSuLIRhQ/XBYgj35P1qvPueRj0XbjJ6MOtVbiS3jUthjG8RAKnlPY/Xjmiyegc1iaeCxvlsfNsoEuYnKrdRkltjdQR0I49M9fWluNGutG8daNJc6kkguoGZVZSNseMKV9v89qxprzbbk/NG205IHTj0pdOv7rU/GujFJlMywFFilblsA4GfQD864M1pWw0jrwFS9ZHJ+CdOsdT8XXlrqHmC3C3G3DYB+fjn15zn2rvLnQobe6QteXGpWnlj5opv9Yc8g5JyDgfKeAM15j4HnuZPHGuWEMggma5mRw4xhFkLDafXB6+x9K9KtXvl1Z7S4j8+IQlvPVAG4OMDHGff2rbBT5oJxZliY2nqSeKnupfGT3Go27wwlY1c2oAVn2kqcjP3QMNnpgVpWl7EfB8cz2xjm8xUE6/MZsY3HA6HFcxc3k1tr3ymWRQVjEJYgqT/AHvUkEfhirt1HGIhqCXl/FOIyPJXiIDoeB1PGPpmtIRV9yZSv0NXf/xPJSt5E8hQSIiodpHfJ7nPaubs7aT/AITq8Wa8u7zTruRh5NxNlY3bk/L2Axx7E1o/aLZoQ9xLtlhh8wIuAAnHP4cVVjvUWGAQ27lWUyF3wPMyfvZ/zmrvvbqRbYRtOgTW/tMbRHyflW1C7ASM4dv77D17j8qzpNM0yHzvsl3dwzyMbmQcfNLuXZtUjjaM/UGrGpeIYLX7NHgzTzHy0ZBvCuOxxyM9M10bHTf+Efh1EiE6zbQAXYl+8w25wMcen405RfKr7MFL3imfLmjuI4o1itnfcqDgAk9PYVEzFrNIZ7dpYjywdcqoB7+/vVdBcfY1nEjWr3EZISZQQCOcDH4c0abJ9pt5UMsssiguWK8fTHce9Y1LbFxu9SQWUNtYxW8cs0UT5liO/JK9+fSpRG0qkLG4wvBXGAP8OlW5T/okCyt9l8uXarIu5QMdh61Rgv3hvJJVLmFMxysEGWXnkKev0rFXZo3YovJcpMS6hmU4JLbs/wD1/brVqwht01f7cJI4y4JJ67yOvHr2z1rNtbmO6tQ0V1LJDIWUSvBs8wDIyR2P8uabDY/NgSBnQCZI0c7Rz8rE9/pUO8kNOzM/yI4Y5YbFHEsspk+9nbuPv3Oa5+ynmtvjfYyzMsUhkdT+7LY+XrwR0x9PrXXXDQSMkSzyJe7gGVEyXP1z29fSuY0WG5HxW0yaQvNIfNICgEkbW9e3tXFjXehO/Y6cMv3sTV8W2yv+1lf3oaTzUSIANngCPt69a6JJ7f5IhOyl3ONvDd92f8KwfFsxX9pTWZhuZ3WLgtyWKAd+1aF0Q1rEtqzC+3bmHGGX2759678qilh1Y5sdJ+1HRTw/2pZ2/wBo+0YBMbuhDY6nI/kR0rpEeNpDK8fzsoGTxn8O+KyBa6dFfW+oTTTh0URvtDMoGQTkemT6c10psLF5iFmuorZUwBOuCzcZC+q+/wDhXotWjqcl7soqIt0zzSH94NqgthQOgHt/jWai3CzNHc21wsrRfupZeBuBxnPbGTx1NaZhtbhkLNIfkwiyjDLkEZ9vr34qGzjjuPnlDs6OQd5z5eMj6fjWNR6qxpBdzRhhjiWN1Qhhy7EH5sjtVN7dE8QXN/beT5zxKJ1OAcDgMfbHX6VpjCXH7xN6kc5cgDr09+ayr5Bb30LRRq0ry/MCMmTj1/z+lZzs0VDce0Ae4skhmktMMSsG0ASD6N2HHFOsXgOguyWhgnMskaYXaSVYgnB7fz/EUyGzmZo5p96TK/ykZKhf7vPQ9a05JobbznvI41thtVRLgcccLS527X6Dslt1K145t/DNowt2leWVkQ274yQuVwAMY5Oe4xWWup2dulraMLpbl4g7weVukVien1rQi8XWywyWM0dtPoyEeUkCbZI+vA/xrBe3a01j7dZWYtvNnMn71zJIcjpkn0p63uhq3U6tGt4dShZIjNCRh45xlZM/eU/hWZeabEiyeR5l4BtI+fLY7Bj+HX/GnCeZoSZU+zswwFEgzj1H+NRvLC0wiNwpPO5Q/IFdEVbUwk9bFQ2UFxrGn2Nw0dm05USyj5v3Rbrt7Nj1zn8a5vQdJ1DRfjlY28k6zC2vXhDhNx+65ztODnj889al0zSGn177ReXbOIrtZIoXkP3R1Vh3yMcVdt737d8dtBh1DT5bO7Lz71nmBBZ0kZHXB/un61wZpNqg01udmBgnV0Oa8NK0/jy/TDZN9MSAMEgSMT/X869jn03y9DgnhzbiQsoMHBQ5/wDrf/rrzHwfbpa/EjxLc+c2+KW42B3yX/fYIHp64716VdXN9Dpvn2UEl3chl8m0U5ErHAxj+taYWLcI/Izru0mQ6bLt1rUbaRZZYwmyWWQ7iSw3/oCB+Fctaappr6tNa297b6gkU5XYGwTjqB/nFPk1d7mHxLbva2qTJcJBO8U5G59xRygPZeRnj9K4TwhpVrb+JLyeKCWW0tJTHulkUEDBIPHtnn3Hrx14WNOrCU9dDnrOUJKPc73SNJsb2zvtNubOTyzOBATLmULkDYD1Kn09/auf1HUk0a5m07TbS2njsUeCOHymkLhTzknqevHNdLeC30zUF8q5k2FRLC/3Sc/1zWcJ4TfW0/2u3iu3Y+Uxb95IR1GD1NJUtrDdTuc3JLc23jSWK7trSF7hVmWGJQijI4GFyM9Mg9PxrQS9mt7q1SELNK5AVOMsPx79eetacltbw6tc6jLOJgwwswX/AFYOMrz3z39MVkRQXmn6w2pxXUev3cUTRxIluFMSnH73Bz8w6EemaxU3CNrGllJ3OivLa9fw/fTXr2ZvC4WGCFslccFjjg4HpxUUMM0N0si3+yQphti4BXuDjt7VbtGa2lgZs3c0oyLby14Vxz06n/GqpsruVpBb2rllJ8sMMfh+Az/9eoVluUxJZJIZI1Z4zByUBOefb26Uk0zRNbsIXeQPg4Aw6n6/Wqq7R8m6H7NJMhiGzLK/Py89OnalNyGVgsqv5DYwoJIPQjFS10BEzYjmlaGLMRzlGGPy/wAay7u4NtphmitpJQsu0BJOW/wA9eRWsQFsvML+cwHAHDMx7e1MhiY5aa6leCQ/MhIGPbjoenNZSWmhot9TIivNOuGMyzonQb9uZE/zzWNpMsTfFrTPkCRAv5YcZIO1hUOuSX7eINDj0+xxaRLLEXUhZJWIPyvjqnf2b61F4fv4/wDhd2nQsslrKIHc7gBghMY9Op6V5mKqfuZp9jvo07Ti0bXxF/5L3qO2GV91rDIZEXA3fX16fStmzj32qzNtecgGMt/Ae/T+dVPFNst3+0rqGnvvjhFikpmcnqF6DHfgfrWpET5DlpnjbGwELkEkcP8AQeletlOuHR52Of7wndbh7F2tjFLK6YWNxtwCPm5//VV61glWZY7mS0R0+WBHf5kG3HI6bvb3qkl8qTR2+ozRvPkpFsA5HY4HfjpWi7QTySPIzPOHyWPO5vx9u9eg3pY5upobJ3/dsud58vJUDlSMCh2YTSCEhYnbIKLtzj/P1qjFcx/vAGhcDKsOo4qYSH5mKlWzwgbIx/jUSh3BT7Dt7y7HiUAOp+d8/L/9brVqOBjMCi+cixMxIOSvXj6dOaoxz7rdpGuAYFDCRk5dj6Bf8/jUEF9FcXEqISAj8OyYO3jr6HrxWTRoro6BYme1SKa4RQchFDZPvWHq9ta3Oitp95d28ewqsLqxLhtuV/AjscVo70RyFkinhz8+AQ2PUfl0pL6+S602C3kjf5CViMow3l4+UH3HOP6U+TReYuYwofDFi/iZL4XErRpHhLYABAwXa5bI6ZzgDjpVTW18u6RY920fOZOS3Pb2HFbcEkaquW3AEYBOc1QvWtTfQmUxSy/PiPHU5GCP0rSFFQWgpVXLcq3cSnw7BGySyt5mSEbDE45+b16flXLahrdjY6fDc7ZHnidVBlIyVxjYff3/AMa6iz0Ke6muHlv3NuZvNnkcrGI857HPIzxSDw74StvESXN7ey3MTH5McJE/ZlOAd3y9OnWutSXNZq5zNXV0Urt4jpf2uytZo5nCq6NODtJ4z7gc9Oaj8OaVaaj8RtGS8hu5pJZt6FpMNGyRvsKkdFBXp145zzWTqmp6UniRrO2FyIoSw81z8vTqM9frVzw5cj/hb2ixR5Mu9mJU8hvLfH8yfwrhzGmlhZt66P8AI6sHNuvFLy/M5nRL4W/xg1O0LMsh1C5WUbMlMyN83px1+n0r27TI0XxYkQupr+KBhKgkGSAp3fj/ADrxnTlNr8bPEt1PnZPd3cciCIMQxmBD+qj5evavS9H8S2Ok+KUaZ3jmidvN81ONoz8w9RjP5VhgKVSVNPyNMXUiqjDxBoOn2Ey/YtNuLTczTFonDyMSxPU+nboQK5VYJrSzmmtsN5kbOV2+WsTE87iepx9eT7V6XpnjHw54ytZ7XQJrqd2JCTSgAEj34I4Yc+w6Vxer28RsZbe63WMX75pLieNtjRp/Ht64BHX/AAr0KcZRSpyjaxyyabckzjrq5bXbGzaylnspoHC3qK4G8MeSv04/+vT9M0NR4omm3mO6s42CLcZOAOS6/X1qTSVWaxmcETW5JS0uYcjtljn05HPHWt9NPup9PuGa8uoBaW+5Njbtp4A3E8/X1/Ct60+TRGVOPNuctbax9v168MM9wtkqDb5y7DIxyG454FdLapItrEojijudoMcvmY4OSPXnHaslNLjW6wIlywKlmG4Edzz6V08NqjaTMdwkeJAXC/KW6/kK5ZSb1tY2iulypBdvJfW8S7bV8NiZPn2Ed8D/AOtV9NSl3QJcRXEc6HDBid4YcAj+8Dk+vBqn8jaSH8kwSZ2MB96NTzx6/h1qW1O+1jaB1lQZCO/Bx7frXO43942UraGhawWDLPuZIJgQySOD8v8An9aps3mMV8/dLy4dxlsD1Pr0o83/AEQ7o08xTkKcksP8az47603SJgYCk7fu4PesZON9epom7aGhvFzbxqhCuDko3yll9PrzVS4FwJEEbPAFOSSNwIPHOajdR9uPlOgkAV1WXJwvfGO/5VJI/wDo7wOS+CGjIHA55/8A1VHM4lKNzMkQ22xZW3rE/GcH/P8AhXPWHln4oQXMUMK7d+3djAGORk10F9apfWrxhjDctGREBj943Yc8f0rhvDy3CfHaPTNVWWKPy5Aybc7CFwPxrzsxnajJW3OzBxbmnc9P8XXIg/aguFaH7U0tpEgC85+Uj/A1Ya4hiKxugQueB2J7/jXGfEnWY7P9pBrqCNzZy2cZ3g/LHncoyR6fh0rp4LkS2duAtuQw35PLJ0AIPqa9HKpXopJnFjornu0RtZefrC3O9owjbghUZYjsf9k1pSv5rCJbiWCYJu/d44+v+eajRomuyHmj8xs7EJ5Ydz9OOvSrQNnFfAyASSvGN+ODgdOPxPPuK9JRVzjbbWpkXsOpx3kjJJF9pVCqQRKNsnflumT9cfnWkdRgt7e1aWKSSS5XlVhL7AOSSOw6D1p4cSyYLsmE5JPAA9P8Ke6xNp+3DmPZgr0BHvjvUuLZakkTQz2txDHLE+Y3bcVcYbvzt9DT5FRcSRNtQYx5hyze2cdPaspWgn09LwSXFlGcPvYfdVcn8vaoLW/h1K4k+x3ErRRIXZXi2h0zwy57e30pOFnqLmujTt4kiuN/mOy796O7cD2+laccbfaALk+bJnedgwcH+h/nn3NcldiaSZFwdgznYc5T12+tacd2/wDZBS0m+cReWhkHKn3/AMP8a1dNkqaLO6ORppYpP3fnNEgYbSQGK9/59KW9j0uTTyZRKt6/PmK/CqOM/wD16wsfYY5p72a4mMcu6Qr8qkEZPB9Tn9PeuU1Dxda28bXVzYP9jiBaOUS/Kq8DJbgc+lVFcujJa5tUddiJmm0YTl5WIJO/MmDnAz0qrdW48m6kmkbUAr4WJfu4+7n/AOv9a5yxa11XULbU/JUbDg2jtlXXhlk7fMp/Dk9c1s67rOk29pDbXstw13qRMVjBFFlnk2ks3sFAJ/zitW0veJUW/dMK70S333F/cagsVtHl90iDbH+PcdPejww9pbfEzQZLASySJeCOWUSMz/xAde20jpwcGo7yx8nTbI6lqEEcJPli3blG7t1HTbn1x+FaHh2CSw1zQxHLbLuvx8wXYWAX/wBBP8J7j05rjzJJ0JLyZ04K6qp+aLWgSX3/AA0P42kd1is0u7k2p5VpUMmGHv8AePHGMVzWteGL+68V63NqqXEmhxyH+zpoL3y5mkwQEK9dmNxPsPfje0u68r45a2SEuTFfXgDOwwCHP5nmqum6EdR+Imq6zdFoRDHFtxvKMw5AU9AeDnOf1royVONDm5uiMcxlera3Uh8EpLb65Fa6VElpawZzBH8gb169z616JqUk6eEZLG0vNmpyL5aTXDEhUYjzEI5GMAY7CuY06whfxgsFqXt0VyZHeUf6vGd6sMcH069a3ZJbW7uUdpBb2zk5KpwmepOMj6Hiu7EyTmpHJST5bGXZzJDZwQxggQgBAq7VYkkFvp159hVqPWpNQxLHCEhjQQXOzI3yc89gR70lyLO4sWKQRpIMFSkY+XouTjqP/r1dCwWtqIYtoYHCDGR2z/LrXLNqWptG8dCL7Orawhlla3iyoypz9cj8aSRMfIY8zr0cNgMM+v8ASrUgZLq2cj5WBwgBJH5d6qXM8gVo445wCmAUG5lz0P6VzSXU2j2HL8y7CEhlA3YLCkkMXmIpdbZmG0Hd/F7L61jkagY3njubeWMbY2L4HP8APHByeOtUIriS5ktGntFiukyPOY+YAnPT+eeev4VMI8xUpcp0TyKjP5vnNOo+XAx5nripL6OCPWFmihPzgZyoyp+6f8+9ZDQ3X9pSiNzCAgUOTnzMYPQ8AH161dvrtftTqnlXNwibpEVuowPm/n71zVYqNmb022SK0kDMW+8X3bichenFSyndMJNqIQMFYxnOKzYr1mhZpUZbfcNrFApAI4Hv9feryXMhVYwYs7MxMW4Irm5kzWzSKt3G3lh8FJQjDLPlT6cD+dY/hxjJ8X7fGGZ4nJOcEDZ6/nVi/mu7TTZ7lo2iiETEDbkg/wCFc14Rv1vfjBYywOyRlGznjOIyeM+tcGOkvZSXkdmFi3NM6T4oxpL8flguJo4opNNG/YgULhn/AIRxj+lJY32nx2qx2/3sCM+dyEx71J8W/BfxA1n4nWGueH9FEyCyaC5hFyo2fNxJuPc7iMdRXM2Hw4+JttIG/wCEcnlnIw5S6i6dcHJ4FduWVqcKava/W5x4uEpM3Lq6so9cjlgmt1u5SonMmcMmD8qdvritkahbtIqb4md8qmDxnjj9K4bUPBfxDW8hlm0G5VUYEosiHd+O7jjvVs+B/HRtQ03he/QEHGWTp+f513U8bQ5muZfec88PPlWjOvnv4oYzJ/y0XhRnAYev0/rWLe3l3LdW+6bzbdEG9V+RW+p9Pf3+tY8fhLx/DGY5PDWplQo5j2kn8Aaml8OfECO3DReEtZnw44KKuQPxx/ntWrxVBr4196M40qieif3Ff+1rixvmj3F7N18toyxYIB0Cj8+feuk05oLLw/JLJc26yhtu0HBBPReetcrdaD4+kVV/4Q/Xv3p35MS7Ux689TjpxVGbwz42Nv5c/hDU2XbiMGLgMeeuevFYLE0lJ2mn8zZ05tK8Tq7DXLNLe8vNSDXTJtwuzcU+8c4GMfTvxV601n7XDextceTcMrP5p+UAf3gDz9K4Cy0bxuVWK/8AB+uwSeWN7mHOevoTV6bRfF6XCmPwzrhYY4+yHhe1dFHEUmr86+9GVWnO9lH8DtJtPlmtbeS41WW62P5wHBWQHop46D+tYupW9kbwSxwocbDNERhWwSQCOn4ViXSeNo9Nf7L4X8RtOi7ET7Gyg59fQD86pWcfjP7Os154d18SAbij2Lkxfjjn0rqjjMPzW5196Od0Ktr2O5jtYJZLmXdOGPzKrP0J9uw7VpmVIbKKa5hWRIA0hc8+QNpy574615pI3i9GaKDRtcUMS0caWDjPX0HXFYMdz8Rm16YR+H/EdtbFfmD2cjDb36Drz0qamOw9rKQ4Yape9juNWsn8ReLi9ncwzwFUaCVX2rjGeF/76JHNN8OXxm8b6NaSXG9zcohIh+bIyDj8z+FcRZS+MtOuraZdE1pXDbspp0mFJ6cbev8A9atHwdF4ib4zaQ954e1u3i+2tM91PYyLAispzzjhsAYHQk15eZVYqg+WS13O/BwftVzI6O2tm/4TLxJB/aP2K4W8uAkrIWZnMvbqAenPpmu1sVn+xvbakZLiORAzywttUYx6dyR78Vy17o3iJfGmsXljoWsXkM1yzpLBbN8ytzz6n29varUNh40+yyCDR9eUgbm22TZUY57VpgsRRjQjeS2XUyxVOcqktOpt20uNYspbmOGyt/nQDGVZGGcEflz/ACqjZ+ZexG7aSK1UsWltUQlV7jrwwI6jgHNc/Yp4xS1e5i8O+I2kDsVRLViVXpnnoTx9M+9Zdynia2s2nGgeIGkUbSklowIJ9wPfpVTx+HbUudfeEMLVWnKekaliFYWsreFxOMPHEMYHXj069OKqXMl5Ha2l35Ejo78qoyF5xgnkDvzXHuPEq2YlTRddk2x7SBavyeO+PwxxVi1u/FsdiB/ZGtoHGCiWjlTgg46cjpUPF0L35/xKVCpb4TrZ1vW1ZdR82P7GluNgTllOe/etISyeSrNI0LbxhguSV4zx2P8AKuEWbxJFdN/xKNStpSxwn2STvyf5ms271TxaNSi26RrCwAgGVbRwM856jkfrRPHUUr8yHDCzbtY9CmwYZpYCyIHDBhyDn+tNks5483JJebA/ddAM/wBP5Vwbar4iaz8qHTdYD4JH+iy4z/3z+NZ1xrvi+1WOVdM1d0Q/Lus3JOfbbn0/zmuaWNpLqbRw030PWYhmM/JuwAxYsCfrUciKJLi4jhHmNgM245JXt/nivKo/Gepj7M9xouowsIwrqbVwASfp19u9Qv4t1h722zpd4kYDFpDbOApP1H1/KoeLoT6lLDVY9D1fKmGEwkh+wPQciqksbOrF7lGljfKske0Y9DnvxXly+Mp4bpZJZXKE8ZhYb/bp/nFW73xhqA03zo9LuV2rxshZww7dR/n3qfa0rasr2VRvRHf3svk2sm7kMOcvkHPTHvWZp8Glw/tF6NZ2k63Nktq0qvIg+6YTkbfbPXtXlcvjHXJ5PIbSdRJdcBTbkLH15zjjg/hiuk8KXM978SrW9NncSzxWrxuxQgAbT3I75/HA9K4cTVhOnLl7HVSpShOLZ//Z');           
            
            function init(){                                 
                dataModel = new ht.DataModel();                
                g3d = new ht.graph3d.Graph3dView(dataModel);   
                g2d = new ht.graph.GraphView(dataModel);                   
                mainSplit = new ht.widget.SplitView(g3d, g2d, 'v', 0.5);   
                
                view = mainSplit.getView();  
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    mainSplit.invalidate();
                }, false);                         
              
                g3d.setGridVisible(true); 
                g3d.setEye(0, 400, 400); 
                g3d.setCenter(0, 0, -100); 
                
                g2d.translate(400, 160);      
                g2d.setZoom(0.6, true);
                g2d.setEditable(true);
                
                wall1 = new ht.Shape();
                dataModel.add(wall1);                   
                wall1.setPoints([
                    {x: 200, y: 50},
                    {x: 400, y: 50},
                    {x: 500, y: 200},
                    {x: 100, y: 200}
                ]); 
                wall1.translate(-100, -140);
                wall1.setClosePath(true); 
                wall1.setThickness(40);
                wall1.setTall(40);
                wall1.setElevation(wall1.getTall()/2);
                wall1.s({
                    'shape3d': 'cylinder',
                    'shape.border.width': 20,
                    'shape.border.color': '#9E9E9E',
                    'shape.background': null,
                    'shape3d.image': 'brick2',
                    'shape3d.uv.scale': [1, 5],
                    'repeat.uv.length': 32
                });                 
                
                wall2 = new ht.Shape();
                dataModel.add(wall2);                   
                wall2.setPoints([
                    {x: 96, y: 209}, 
                    {x: 43, y: 211}, 
                    {x: -1, y: 199}, 
                    {x: 7, y: 126},
                    {x: 54, y: 127}, 
                    {x: 41, y: 89}, 
                    {x: 98, y: 60}, 
                    {x: 114, y: 95},
                    {x: 159, y: -3}, 
                    {x: 290, y: 66}, 
                    {x: 251, y: 137}, 
                    {x: 296, y: 155},
                    {x: 289, y: 199}, 
                    {x: 260, y: 213}, 
                    {x: 149, y: 213}, 
                    {x: 77, y: 261}
                ]); 
                wall2.setClosePath(true);
                wall2.setSegments([1, 2, 4, 4, 4, 4, 2, 2]);
                wall2.translate(-400, -140);
                wall2.setThickness(30);
                wall2.setTall(80);
                wall2.setElevation(wall2.getTall()/2);
                wall2.s({
                    'shape3d': 'cylinder',
                    'shape3d.resolution': 30,
                    'shape3d.color': '#E74C3C',
                    'shape.border.width': 10, 
                    'shape.border.color': '#E74C3C',
                    'shape.background': null
                });                                 
    
                wall3 = new ht.Shape();
                dataModel.add(wall3);                   
                wall3.setPoints([
                    {x: -500, y: 0},
                    {x: -500, y: -200},
                    {x: 500, y: -200},
                    {x: 500, y: 0}                    
                ]);               
                wall3.setTall(60);
                wall3.setThickness(60);
                wall3.setElevation(wall3.getTall()/2);
                wall3.s({
                    'shape.background': null,
                    'shape3d': 'cylinder',   
                    'shape3d.reverse.color': 'yellow',   
                    'shape3d.top.visible': false,
                    'shape3d.bottom.color': '#E74C3C',
                    'shape.border.width': 10,
                    'shape3d.image': 'brick1',
                    'shape3d.uv.scale': [1, 6],
                    'repeat.uv.length': 32
                });                 
            }             
            
        </script>
    </head>
    <body onload="init();">                                
    </body>
</html>
