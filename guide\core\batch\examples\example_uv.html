<!DOCTYPE html>
<html>
    <head>
        <title>Batch UV</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
                background: #426AA1;
            }
        </style>                          

        <script src="../../../../lib/core/ht.js"></script>         

        <script>

            ht.Default.setImage('colors', 'data:image/png;base64,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');
            ht.Default.setImage('die', 'data:image/png;base64,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');
            
            function init() {
                items = [
                    {
                        label: 'Enable Batch',
                        type: 'check',
                        selected: true,
                        action: function() {
                            switchBatch();
                        }
                    },
                    {
                        id: 'batchBrightness',
                        label: 'Batch Brightness',
                        type: 'check',
                        action: function() {
                            updateBatch();
                        }
                    },
                    {
                        id: 'batchBlend',
                        label: 'Batch Blend',
                        type: 'check',
                        action: function() {
                            updateBatch();
                        }
                    },
                    {
                        id: 'batchReverseFlip',
                        label: 'Batch Reverse Flip',
                        type: 'check',
                        action: function() {
                            updateBatch();
                        }
                    },
                    {
                        id: 'batchReverseCull',
                        label: 'Batch Reverse Cull',
                        type: 'check',
                        action: function() {
                            updateBatch();
                        }
                    }                            
                ];

                toolbar = new ht.widget.Toolbar(items);
                dataModel = new ht.DataModel();

                g3d = new ht.graph3d.Graph3dView(dataModel);
                g3d.setGridVisible(true);
                g3d.setGridColor('#74AADA');
                g3d.setGridSize(100);
                g3d.setEye(0, 800, 2000);
                g3d.setFar(20000);

                borderPane = new ht.widget.BorderPane();
                borderPane.setTopView(toolbar);
                borderPane.setCenterView(g3d);

                view = borderPane.getView();
                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function(e) {
                    borderPane.invalidate();
                }, false);

                updateBatch();

                var column = 40,
                        row = 40,
                        gap = 100,
                        startX = -column * gap / 2 + gap / 2,
                        startZ = row * gap / 2 - gap / 2,
                        size = gap * 0.55;

                for (var i = 0; i < row; i++) {
                    for (var j = 0; j < column; j++) {
                        createNode(startX + gap * j, startZ, size).s({
                            'batch': 'uvBatch',                            
                            'bottom.uv': [0.25, 0, 0.25, 0.25, 0.5, 0.25, 0.5, 0],
                            'left.uv': [0, 0.75, 0, 1, 0.25, 1, 0.25, 0.75],
                            'right.uv': [0.5, 0.75, 0.5, 1, 0.75, 1, 0.75, 0.75],                            
                            'all.blend': '#00FFFF',
                            'top.visible': i % 2 === 0
                        });
                    }
                    startZ -= gap;
                }


                var types = ['box', 'sphere', 'cylinder', 'cone', 'torus', 'star', 'rect', 'roundRect',
                    'triangle', 'rightTriangle', 'parallelogram', 'trapezoid'];
                for(var i=0; i<types.length; i++){                     
                    var node = createNode(-types.length*gap+gap+gap*i*2, 0, gap*1.5);
                    node.s({
                        'batch': 'imageBatch',
                        'shape3d': types[i],
                        'shape3d.blend': '#00FFFF',
                        'shape3d.top.visible': false
                    });
                    node.setElevation(gap * 2);
                };

            }

            function updateBatch() {
                var brightness = toolbar.v('batchBrightness');
                var blend = toolbar.v('batchBlend');
                var reverseCull = toolbar.v('batchReverseCull');
                var reverseFlip = toolbar.v('batchReverseFlip');
                
                ht.Default.setBatchInfo('imageBatch', {                    
                    brightness: brightness,
                    blend: blend,
                    image: 'colors',
                    reverseColor: 'green',
                    reverseCull: reverseCull,
                    reverseFlip: reverseFlip                    
                });                                                
                
                ht.Default.setBatchInfo('uvBatch', {
                    brightness: brightness,
                    blend: blend,
                    image: 'die',
                    topUv: [0.25, 0.5, 0.25, 0.75, 0.5, 0.75, 0.5, 0.5],
                    backUv: [0.25, 0.75, 0.25, 1, 0.5, 1, 0.5, 0.75],                    
                    frontUv: [0.25, 0.25, 0.25, 0.5, 0.5, 0.5, 0.5, 0.25],
                    reverseColor: 'yellow',
                    reverseCull: reverseCull,
                    reverseFlip: reverseFlip
                });    
                
                g3d.setBatchBlendDisabled(!blend);
                g3d.setBatchBrightnessDisabled(!brightness);
                g3d.invalidateBatch('imageBatch');
                g3d.invalidateBatch('uvBatch');                
            }

            function createNode(x, z, size) {
                var node = new ht.Node();
                node.s3(size, size, size);
                node.p3(x, size / 2, z);
                dataModel.add(node);
                return node;
            }

            function switchBatch() {
                g3d.invalidateBatch('imageBatch');
                g3d.invalidateBatch('uvBatch');
                
                dataModel.each(function(node) {
                    var batchName = node.s('batch');
                    if (batchName) {
                        node.s('batch', null);
                        node.batchName = batchName;
                    } else {
                        node.s('batch', node.batchName);
                    }
                });
            }

        </script>
    </head>
    <body onload="init();">                                  
    </body>
</html>