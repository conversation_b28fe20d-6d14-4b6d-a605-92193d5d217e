!function(Q,H){"use strict";function y(H){return w().createElement(H)}function N(){var H=y("div");return H.tabIndex=-1,H.style.outline="none",H}function j(){return y("canvas")}function r(H,M){q(H,"-webkit-transform",M),q(H,"-ms-transform",M),q(H,"transform",M)}function q(H,M,G){H.style.setProperty(M,G,A)}function Z(H,M){H.appendChild(M)}function M(){return w().documentElement}function a(){return M().clientWidth}function Y(){return M().clientHeight}function J(H){var M=H.touches[0];return M||H.changedTouches[0]}function I(H){this.$1d=H,this.addListeners()}var e=Q.ht,l="px",g="left",$="top",b="bottom",n="right",L="innerHTML",h="className",K="width",U="height",_="position",p="absolute",R="0",E="opacity",s="background",f=e.Default,t=f.getInternal(),G=e.Color,B=f.animate,C=f.isTouchable,m=f.isTouchEvent,A=null,c=(Math.sqrt,Q.parseInt),d=Q.setTimeout,P=(Math.round,G.titleIconBackground),w=function(){return document},F=t.addEventListener,k=t.removeEventListener;t.addMethod(f,{dialogCloseIcon:{width:100,height:100,comps:[{type:"shape",points:[10,10,90,90],segments:[1,2],borderWidth:8,borderColor:P},{type:"shape",points:[90,10,10,90],segments:[1,2],borderWidth:8,borderColor:P}]},dialogMaximizeIcon:{width:100,height:100,comps:[{type:"rect",rect:[10,15,80,75],borderWidth:6,borderColor:P},{type:"rect",rect:[10,10,80,20],background:P}]},dialogRestoreIcon:{width:100,height:100,comps:[{type:"rect",rect:[10,34,56,56],borderWidth:8,borderColor:P},{type:"rect",rect:[10,34,56,14],background:P},{type:"rect",rect:[34,10,56,14],background:P},{type:"rect",rect:[82,10,8,56],background:P},{type:"rect",rect:[66,62,24,8],background:P}]},dialogTitleLabelColor:f.labelSelectColor,dialogTitleLabelFont:(C?"18":"14")+"px arial, sans-serif",dialogContentLabelFont:f.labelFont,dialogTitleBackground:G.titleBackground,dialogHeaderBackground:G.headerBackground,dialogButtonBackground:"#1ABC9C",dialogButtonSelectBackground:"#16A085",dialogButtonLabelColor:"#fff"},!0);f.def(I,H,{ms_listener:1,getView:function(){return this.$1d.getView()},clear:function(){delete this.$2d,delete this.$3d,delete this.$4d,delete this.$5d},handle_touchstart:function(H){var M=this,G=M.$1d,j=G.$6d,I=G._config,G=G.$7d,t=H,Q=H.target;G.contains(Q)||f.preventDefault(H),f.isLeftButton(H)&&(m(H)&&(t=J(H)),t=M.$8d={x:t.pageX,y:t.pageY},M.$9d={x:t.x,y:t.y},!I.maximized&&I.draggable&&j.contains(Q)&&(M.$4d=!0,f.startDragging(M,H)),M.handle_mousemove(H)&&(M.$2d=!0,f.startDragging(M,H)),G.contains(Q)||Q.focus())},handle_mousedown:function(H){this.handle_touchstart(H)},handle_touchend:function(H){if(f.isLeftButton(H)&&!this.$5d&&!this.$3d){var M=this.$1d,G=H.target,j=M._config,I=M.$18d,t=M.$16d;for(j.closable&&I.contains(G)&&M.hide(),j.maximizable&&t.contains(G)&&(j.maximized?M.restore():M.maximize());G&&(G.className||"").indexOf("dialog-button")<0;)G=G.parentNode;G&&G.buttonItem&&M.action&&M.action(G.buttonItem,H),delete this.$8d,delete this.$9d}},handle_mouseup:function(H){this.handle_touchend(H)},handleWindowTouchEnd:function(H){var M=this,G=M.$1d,j=G.$10d;M.$2d&&M.$3d?j.fire({kind:"endResize",target:G,originEvent:H}):M.$4d&&M.$5d&&j.fire({kind:"endMove",target:G,originEvent:H}),this.clear()},handleWindowMouseUp:function(H){this.handleWindowTouchEnd(H)},handle_mousemove:function(H){var M,G,j=this.$1d._config;return!j.maximized&&(("w"===j.resizeMode||"h"===j.resizeMode||"wh"===j.resizeMode)&&(M={x:(M=(j=this.getView()).querySelector(".resize-area").getBoundingClientRect()).left,y:M.top,width:M.width,height:M.height},G=(H=m(H)?J(H):H).clientX,H=H.clientY,f.containsPoint(M,{x:G,y:H})?(q(j,"cursor","nwse-resize"),!0):void q(j,"cursor","")))},handleWindowTouchMove:function(H){H.preventDefault();var M,G,j,I,t,Q,r,E,y,N,Z=H,e=(m(H)&&(Z=J(H)),this),b=e.$8d,n=e.$9d;n.x==b.x&&n.y==b.y&&f.getDistance(n,{x:Z.pageX,y:Z.pageY})<=1||(M=(n=e.$1d)._config,G=n.$21d,N=M.resizeMode||"wh",E=Z.pageX-b.x,Z=Z.pageY-b.y,e.$2d?(j=(I=G.offsetWidth)+E,t=(Q=G.offsetHeight)+Z,j=Math.max(j,50),t=Math.max(t,50),"center"!==M.position&&M.position!=A||(y={},r=G.getBoundingClientRect(),y.x=r.left,y.y=r.top,M.position=y),"w"===N?(n.setSize(j,Q),b.x+=j-I):"h"===N?(n.setSize(I,t),b.y+=t-Q):"wh"===N&&(n.setSize(j,t),b.x+=j-I,b.y+=t-Q),e.$3d?n.$10d.fire({kind:"betweenResize",target:n,originEvent:H}):(e.$3d=!0,n.$10d.fire({kind:"beginResize",target:n,originEvent:H}))):e.$4d&&(y=(r=G.getBoundingClientRect()).width,N=r.height,j=a(),I=Y(),t=c(G.style.left)||0,Q=c(G.style.top)||0,(Z=n.adjustPosition({x:r=t+E,y:E=Q+Z},{width:y,height:N},{width:j,height:I}))&&(r=Z.x,E=Z.y),y=r-t,N=E-Q,q(G,g,r+l),q(G,$,E+l),b.x+=y,b.y+=N,M.position={x:r,y:E},e.$5d?n.$10d.fire({kind:"betweenMove",target:n,originEvent:H}):(e.$5d=!0,n.$10d.fire({kind:"beginMove",target:n,originEvent:H}))))},handleWindowMouseMove:function(H){this.handleWindowTouchMove(H)},handleWindowResize:function(){var H=this,M=H._config,G=H.$21d,j=a(),I=Y(),t=M.width,Q=M.height,r=M.position||"center";M.maximized?(q(G,K,j+l),q(G,U,I+l),q(G,$,R),q(G,g,R)):(q(G,K,t+l),q(G,U,Q+l),"center"===r?(q(G,g,(j-t)/2+l),q(G,$,(I-Q)/2+l)):(q(G,g,r.x+l),q(G,$,r.y+l))),H.iv()},handle_mousewheel:function(H){H.stopPropagation()},handle_DOMMouseScroll:function(H){H.stopPropagation()}}),e.widget.Dialog=function(H){var M=this,G=M._view=t.createView(null,M),G=(G[h]="ht-widget-dialog",q(G,_,p),q(G,$,R),q(G,b,R),q(G,g,R),q(G,n,R),q(G,K,"auto"),q(G,U,"auto"),f.baseZIndex!=A&&q(G,"z-index",f.baseZIndex+""),M.$11d=new I(M));M.bindingHandleWindowResize=G.handleWindowResize.bind(M),M.$10d=new e.Notifier,H&&M.setConfig(H)},P="Dialog",G={ms_v:1,ms_fire:1,_modal:!0,setTitle:function(H){this._config.title=H,this.getView().querySelector(".dialog-container-title span").innerHTML=H},clearNodes:function(H){if(H)for(;H.firstChild;)H.removeChild(H.firstChild)},$31d:function(){var H,M=this,G=M.$25d(),j=M.$6d=N(),I=M._config,t=M.$12d=M.$13d(),Q=y("span"),r=0,E=c(t.style.right);return j[h]="dialog-container-title",q(j,"cursor","default"),q(j,"white-space","nowrap"),q(j,"overflow","hidden"),q(j,"font",f.dialogTitleLabelFont),q(j,s,I.titleBackground||f.dialogTitleBackground),q(j,"color",I.titleColor||f.dialogTitleLabelColor),q(j,_,p),q(j,g,R),q(j,n,R),q(j,"display","block"),q(j,"line-height",G+l),q(j,"height",G+l),I.titleAlign&&q(j,"text-align",I.titleAlign),I.titleIcon&&(G=M.$14d=M.$15d(),Z(j,G),r=c(G.style.width)+c(G.style.marginRight)),I.title&&I.title.trim&&(I.title=I.title.trim()),Q[L]=I.title||"&nbsp;",Z(j,Q),I.maximizable&&(G=M.$16d=M.$17d(),Z(t,G),H=c(G.style.marginRight),E+=c(G.style.width)+H),I.closable&&(G=M.$18d=M.$19d(),Z(t,G),H=c(G.style.marginRight),E+=c(G.style.width)+H),H&&(E+=H/2),q(Q,n,E+l),q(Q,g,r+l),q(Q,_,p),q(Q,$,R),q(Q,b,R),q(Q,"text-overflow","ellipsis"),q(Q,"overflow","hidden"),Z(j,t),j},$13d:function(){var H=N();return q(H,_,p),q(H,g,R),q(H,n,5+l),q(H,$,R),q(H,b,R),q(H,"text-align",n),q(H,"white-space","nowrap"),H[h]="dialog-title-controls",H},$20d:function(){var H=this.$21d=N(),M=this._config,G=M.borderWidth;return q(H,_,"fixed"),q(H,"box-shadow","rgba(0, 0, 0, 0.2) 0px 5px 10px 0px"),q(H,"padding","0 "+G+l+" "+G+l+" "+G+l),q(H,"box-sizing","border-box"),q(H,"-moz-box-sizing","border-box"),H[h]="dialog-container",q(H,s,M.titleBackground||f.dialogTitleBackground),H},$22d:function(){var H,M,G=this._config,j=this.$7d=N(),I=G.content,t=0,Q=G.contentPadding||0,r=(G.buttons!=A&&0<G.buttons.length&&(t=G.buttonsHeight||32),"string"==typeof(r=I)||r instanceof String?j[L]=I:I.getView?(H=I.getView(),Z(j,H)):Z(j,H=I),q(j,_,p),q(j,"font",f.dialogContentLabelFont),G.width&&q(j,K,G.width-10-2*Q+l),H&&(M=H.style[_],q(H,_,"unset")),e.Default.appendToScreen(j),j.offsetWidth+1),I=j.offsetHeight,E=this.$25d();return G.width==A?G.width=r+10+2*Q:q(j,K,"auto"),G.height==A&&(G.height=I+E+t+5+2*Q),e.Default.removeHTML(j),H&&(q(H,"box-sizing","border-box"),q(H,"-moz-box-sizing","border-box"),q(H,K,"100%"),q(H,U,"100%"),M&&q(H,_,M)),j[h]="dialog-content",q(j,$,E+Q+l),q(j,b,t+Q+l),q(j,g,Q+l),q(j,n,Q+l),q(j,"overflow","hidden"),j},getOverlayDiv:function(){return this.$41d},$23d:function(){if(this.$41d)return this.$41d;var H=this.$41d=N();return H[h]="dialog-overlay",q(H,_,p),q(H,$,R),q(H,b,R),q(H,g,R),q(H,n,R),q(H,s,"rgba(235, 235, 235, 0.7)"),H},_config:A,setSize:function(H,M){var G=this._config;G&&(G.width=H,G.height=M,this.isShowing()&&(this.bindingHandleWindowResize(),this.iv()))},getConfig:function(){return this._config},$24d:function(){var H=this._config.titleIconSize||16;return C&&(H*=1.2),H},$25d:function(){return this._config.titleHeight||f.widgetTitleHeight},$26d:function(H){var M=this._config;q(H,"margin-right",(M.titleIconGap||(C?8:4))+l),q(H,"cursor","pointer"),q(H,"display","inline-block"),q(H,"vertical-align",$)},$27d:function(){var H=this.$30d=N(),M=C?20:10;return q(H,K,M+l),q(H,U,M+l),q(H,_,p),q(H,b,R),q(H,n,R),H[h]="resize-area",H},$15d:function(){var H=j(),M=(H[h]="dialog-title-control dialog-title-control-icon",this.$25d()),G=this.$24d();return this.$26d(H),t.setCanvas(H,G,M),H},$17d:function(){var H=j(),M=(H[h]="dialog-title-control dialog-title-control-maximize",this.$25d()),G=this.$24d();return this.$26d(H),t.setCanvas(H,G,M),H},$19d:function(){var H=j(),M=(H[h]="dialog-title-control dialog-title-control-close",this.$25d()),G=this.$24d();return this.$26d(H),t.setCanvas(H,G,M),H},$28d:function(){var H=N();return q(H,s,f.dialogContentBackground||"white"),q(H,K,"100%"),q(H,U,"100%"),q(H,_,"relative"),H},$29d:function(){var Q=this,r=N(),H=(q(r,"font-size",0+l),q(r,_,p),q(r,b,R),q(r,g,R),q(r,"white-space","nowrap"),q(r,"overflow","hidden"),q(r,n,R),q(r,s,f.dialogHeaderBackground),r[h]="dialog-container-buttons",this._config),E=H.buttonsAlign||n,M=H.buttonsHeight||32,y=0;return q(r,"text-align",E),q(r,"line-height",M+l),Q.$42d=[],H.buttons.forEach(function(M){var H,G={};for(H in M)G[H]=M[H];M.background||(G.background=f.dialogButtonBackground),M.selectBackground||(G.selectBackground=f.dialogButtonSelectBackground),M.labelColor||(G.labelColor=f.dialogButtonLabelColor);var j=f.createObject(e.widget.Button,G),I=j.getView(),t=(I[h]="dialog-button "+M.className,I.buttonItem=M,Q.$42d.push(j),q(I,_,"relative"),q(I,"display","inline-block"),q(I,"text-align",g),q(I,"height",24+l),C?10:5),t=(E===g||E===n?q(I,"margin-"+E,t+l):0!==y&&q(I,"margin-"+g,t+l),q(I,"vertical-align","middle"),f.getTextSize(j.getLabelFont(),M.label).width+10);j.onClicked=function(H){M.action&&M.action.call(Q,M,H)},q(I,K,t+l),Z(r,j.getView()),y++}),r},setConfig:function(H){var M,G,j,I,t,Q,r,E;H&&(G=(M=this)._view,M._config=H,M.action=H.action,M.clearNodes(G),H.borderWidth==A&&(H.borderWidth=5),E=M.$23d(),j=M.$28d(),I=M.$31d(),t=M.$22d(),Q=M.$20d(),r=M.$30d=M.$27d(),Z(G,E),Z(G,Q),Z(Q,j),Z(j,I),Z(j,t),H.buttons!=A&&0<H.buttons.length&&(E=M.$29d(),Z(j,E)),Z(Q,r),H.maximized?(H.maximized=!1,M.bindingHandleWindowResize(),M.maximize(!0)):M.bindingHandleWindowResize(),M.isShowing()&&M.iv())},hide:function(){var H=this,M=H.$21d,G=H.$41d,j=H._view;j[h]="ht-widget-dialog",B(M).duration(200).scale(.7).set(E,R).end(function(){H.onHidden&&H.onHidden(),e.Default.removeHTML(j),H.$10d.fire({kind:"hide",target:H})}),B(G).duration(200).set(E,R).end(),k(Q,"resize",H.bindingHandleWindowResize)},isShowing:function(){return!!this._view.parentNode},setModal:function(H){this._modal=H;this.$23d().style.display=H?"block":"none";var M=this._view;q(M,K,H?"auto":R),q(M,U,H?"auto":R)},isModal:function(){return this._modal},$32d:function(){var H=this,M=H._config,G=H.$21d;r(G,""),H.iv(),M.maximized?H.$10d.fire({kind:"maximize",target:H}):H.$10d.fire({kind:"restore",target:H})},maximize:function(H){var M,G,j,I=this,t=I.$21d,Q=I._config;Q.maximized||(Q.maximized=!0,I.$33d(),I.$16d[h]="dialog-title-control dialog-title-control-minimize",G=Q.width,Q=Q.height,j=c(t.style.left)||0,M=c(t.style.top)||0,I.$36d=G,I.$37d=Q,I.$34d=j,I.$35d=M,G=H?0:200,j=(Q=this._view.parentNode)&&Q!==document.body?this._view.parentNode.getBoundingClientRect():f.getWindowInfo(),B(t).duration(G).set(g,j.left+l).set($,j.top+l).set(K,j.width+l).set(U,j.height+l).end(function(){I.$32d()}))},restore:function(){var H=this,M=H.$34d,G=H.$35d,j=H.$36d,I=H.$37d,t=H._config;t.maximized&&(t.maximized=!1,H.$33d(),H.$16d[h]="dialog-title-control dialog-title-control-maximize",H.isShowing()&&M!=A&&G!=A&&j!=A&&I!=A&&(t=H.$21d,B(t).duration(200).set(g,M+l).set($,G+l).set(K,j+l).set(U,I+l).end(function(){H.$32d()}))),delete H.$34d,delete H.$35d,delete H.$36d,delete H.$37d},show:function(H){var M=this,G=M._view,j=M._config,I=M.$21d,t=M.$41d;H=H||j.parentDOM,j&&j.zIndex!=A&&q(G,"z-index",j.zIndex+""),H?e.Default.appendChild(H,G):e.Default.appendToScreen(G),r(I,"scale(0.7)"),q(I,E,R),M.iv(),M.validate(),M._view[h]+=" dialog-show",d(function(){B(I).duration(200).scale(1).set(E,"1").end(function(){q(I,K,I.clientWidth+l),q(I,U,I.clientHeight+l),M.onShown&&M.onShown(),M.$10d.fire({kind:"show",target:M});var H=j&&j.content;H&&H.iv&&H.iv()}),B(t).duration(200).set(E,"1").end(),F(Q,"resize",M.bindingHandleWindowResize)},30)},setPosition:function(H){var M=this.$21d;M.style.left=H.x+"px",M.style.top=H.y+"px"},addEventListener:function(H,M,G){this.$10d.add(H,M,G)},removeEventListener:function(H,M){this.$10d.remove(H,M)},$38d:function(H,M,G,j){H=t.initContext(H),t.translateAndScale(H,0,0,1),H.clearRect(0,0,G,G),G=(G-j)/2;f.drawStretchImage(H,f.getImage(M),"fill",0,G,j,j),H.restore()},$33d:function(){var H,M,G=this,j=G._config,I=G.$16d,j=j.maximized?f.dialogRestoreIcon:f.dialogMaximizeIcon;I&&j&&(H=G.$25d(),M=G.$24d(),G.$38d(I,f.getImage(j),H,M))},$39d:function(){var H,M,G=this,j=G._config,I=G.$14d,j=j.titleIcon;I&&j&&(H=G.$25d(),M=G.$24d(),G.$38d(I,f.getImage(j),H,M))},$40d:function(){var H,M,G=this.$18d,j=f.dialogCloseIcon;G&&j&&(H=this.$25d(),M=this.$24d(),this.$38d(G,f.getImage(j),H,M))},invalidate:function(H){var M=this,G=M.$42d,H=(M._68I||(M._68I=1,f.callLater(M.validate,M,A,H),M.onInvalidated&&M.onInvalidated(),M.fireViewEvent("invalidate")),M._config.content);H.invalidate&&H.invalidate(),G&&G.forEach(function(H){H.iv()})},validateImpl:function(){var H=this,M=(H.$40d(),H.$33d(),H.$39d(),H._config.content);M.initView&&(M.setX(0),M.setY(0),M.setWidth(H.$7d.clientWidth),M.setHeight(H.$7d.clientHeight))},adjustPosition:function(H,M,G){var j=M.width,M=M.height,I=G.width,G=G.height,t=H.x,H=H.y,Q=this._config,r=Q.minDragSize||20;return"inside"===Q.dragMode?((t=I<t+j?I-j:t)<0&&(t=0),(H=G<H+M?G-M:H)<0&&(H=0)):null!=Q.dragMode&&"none"!==Q.dragMode||(I-r<(t=t<-j+r?-j+r:t)&&(t=I-r),(H=G-r<H?G-r:H)<0&&(H=0)),{x:t,y:H}}},f.def(e.widget[P],H,G)}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);