!function(U,V){"use strict";var U=U.ht,k=U.graph,Q=U.Default,U=U.Color,L=null,F="px",a=Q.getInternal(),D=a.getPinchDist,O=Q.preventDefault,f=Q.getTouchCount,Z=Q.startDragging;a.addMethod(Q,{overviewBackground:U.widgetBackground,overviewMaskBackground:U.transparent,overviewContentBorderColor:U.widgetBorder,overviewContentBackground:U.background},!0),k.Overview=function(U){var V=this,k=V._view=a.createView(1,V);V._canvas=a.createCanvas(k),k.style.background=Q.overviewBackground,k.appendChild(V._mask=a.createDiv()),V.setGraphView(U),V.addListeners()},Q.def("ht.graph.Overview",V,{ms_v:1,ms_fire:1,ms_listener:1,ms_ac:["maskBackground","contentBorderColor","contentBackground","autoUpdate","fixToRect"],_autoUpdate:!0,_fixToRect:!1,_rate:1,_scrollRect:{x:0,y:0,width:0,height:0},_maskBackground:Q.overviewMaskBackground,_contentBorderColor:Q.overviewContentBorderColor,_contentBackground:Q.overviewContentBackground,getGraphView:function(){return this.gv},setGraphView:function(U){var V,k=this;k.gv!==U&&(V=k.gv,k.gv=U,V&&(V.removeViewListener(k.handleGraphViewChanged,k),V.ump(k.handleGraphViewPropertyChanged,k)),U&&(U.addViewListener(k.handleGraphViewChanged,k),U.mp(k.handleGraphViewPropertyChanged,k)),k.fp("graphView",V,U),k.redraw())},getCanvas:function(){return this._canvas},getMask:function(){return this._mask},dispose:function(){this.setGraphView(null)},onPropertyChanged:function(U){this.redraw()},handleGraphViewChanged:function(U){this._autoUpdate&&"validate"===U.kind&&this.redraw()},handleGraphViewPropertyChanged:function(U){"canvasBackground"!==U.property&&(!this.getFixToRect()||"zoom"!==U.property&&"translateX"!==U.property&&"translateY"!==U.property)||this.redraw()},redraw:function(){var U=this;U._redraw||(U._redraw=1,U.iv(50))},validateImpl:function(){var U,V,k,v,D,O,f,Z,t,P,_,R,H,G=this,d=G.gv,S=G._canvas,j=G.getWidth(),i=G.getHeight(),c=G._redraw,X=Q.devicePixelRatio;d?(U=G._mask.style,V=d.getViewRect(),k=(H=(H=this.getFixToRect())?"boolean"==typeof H?d.getContentRect():H:d.getScrollRect()).x,v=H.y,D=H.width,O=H.height,f=G._rate=Math.max(D/j,O/i),Z=G._x=(j-D/f)/2,t=G._y=(i-O/f)/2,0!==V.width&&0!==V.height||G.hasRetry||(Q.callLater(G.iv,G,L),G.hasRetry=!0),j===S.clientWidth&&i===S.clientHeight||(a.setCanvas(S,j,i),c=1),a.isSameRect(H,G._scrollRect)||(G._scrollRect=H,c=1),c&&(P=a.initContext(S),_=(H=d.getDataModel()).getBackground()||G._contentBackground,R=G._contentBorderColor,P.clearRect(0,0,j*X,i*X),H=H.getBackground()&&0<H.a("width")&&0<H.a("height"),_&&!H&&a.fillRect(P,Z*X,t*X,D/f*X,O/f*X,_),a.translateAndScale(P,-k/f+Z,-v/f+t,1/f),d._42(P),P.restore(),R&&a.drawBorder(P,R,Z*X,t*X,D/f*X,O/f*X)),U.background=G._maskBackground,U.left=Z+(V.x-k)/f+F,U.top=t+(V.y-v)/f+F,U.width=V.width/f+F,U.height=V.height/f+F,G._redraw=null):c&&((P=a.initContext(S)).clearRect(0,0,j*X,i*X),P.restore(),G._redraw=null)},center:function(U){var V,k,v,D,O,f=this,Z=f.gv;Z&&(V=Z._zoom,k=Z._29I,v=f._rate,O=f._scrollRect,U=Q.getLogicalPoint(U,f._canvas),D=O.x+(U.x-f._x)*v,O=O.y+(U.y-f._y)*v,Z.setTranslate((k.width/2-D)*V,(k.height/2-O)*V))},handle_mousedown:function(U){this.handle_touchstart(U)},handleWindowMouseUp:function(U){this.handleWindowTouchEnd(U)},handleWindowMouseMove:function(U){this.handleWindowTouchMove(U)},handle_mousewheel:function(U){this.handleScroll(U,U.wheelDelta)},handle_DOMMouseScroll:function(U){2===U.axis&&this.handleScroll(U,-U.detail)},handleScroll:function(U,V){O(U),this.gv&&(U=this.gv,0<V?U.scrollZoomIn():V<0&&U.scrollZoomOut())},handle_touchstart:function(U){var V,k,v;O(U),this.gv&&Q.isLeftButton(U)&&(k=(V=this).gv,1===(v=f(U))?Q.isDoubleClick(U)&&k.isResettable()?k.reset():(V.center(U),Z(V,U)):2===v&&(V._dist=D(U),Z(V,U)))},handleWindowTouchEnd:function(U){delete this._dist},handleWindowTouchMove:function(U){var V,k;this.gv&&(V=this._dist,1===(k=f(U))?this.center(U):2===k&&V!=L&&this.gv.handlePinch(L,D(U),V))}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);