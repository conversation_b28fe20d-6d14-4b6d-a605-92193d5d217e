!function(b,G,v){"use strict";function B(b){return E().createElement(b)}function z(){return B("div")}function f(){return B("canvas")}function N(b,G,s){b.style.setProperty(G,s,_)}function C(b,G){return b.style.getPropertyValue(G)}function F(b,G){b.appendChild(G)}function x(b){var G=b.scrollWidth,s=b.scrollHeight;return b===E().body&&(G=Math.max(G,E().documentElement.scrollWidth),s=Math.max(s,E().documentElement.scrollHeight)),{width:G,height:s}}function y(b){var G=b.touches[0];return G||b.changedTouches[0]}var l=b.ht,W=l.Default,d=W.isTouchable,Q=W.isTouchEvent,s=l.Color,i="px",A="0",k="innerHTML",w="className",t="position",J="absolute",p="width",D="height",q="left",Y="top",R="right",a="bottom",L="max-width",h="max-height",_=null,u="none",X="",Z=b.parseInt,$=b.setTimeout,S=W.getInternal(),n=W.animate,b=s.titleIconBackground,E=function(){return document},e=S.addEventListener,b=(S.removeEventListener,S.addMethod(W,{panelExpandIcon:{width:16,height:16,comps:[{type:"triangle",rect:[4,4,10,8],background:b,rotation:3.14}]},panelCollapseIcon:{width:16,height:16,comps:[{type:"triangle",rect:[4,4,10,8],background:b}]},panelLockIcon:{width:100,height:100,comps:[{type:"roundRect",rect:[10,50,80,40],borderWidth:10,borderColor:b},{type:"shape",points:[37,45,37,20,37,13,43,13,63,13,69,13,70,19,70,44],segments:[1,2,3,2,3,2],borderWidth:10,borderColor:b}]},panelUnLockIcon:{width:100,height:100,comps:[{type:"roundRect",rect:[10,50,80,40],borderWidth:10,borderColor:b},{type:"shape",points:[37,45,37,20,37,13,43,13,63,13,69,13,70,19,70,26],segments:[1,2,3,2,3,2],borderWidth:10,borderColor:b}]},panelMinimizeIcon:{width:100,height:100,comps:[{type:"shape",points:[10,35,35,35,35,10],segments:[1,2,2],borderWidth:8,borderColor:b},{type:"shape",points:[90,35,65,35,65,10],segments:[1,2,2],borderWidth:8,borderColor:b},{type:"shape",points:[10,65,35,65,35,90],segments:[1,2,2],borderWidth:8,borderColor:b},{type:"shape",points:[65,90,65,65,90,65],segments:[1,2,2],borderWidth:8,borderColor:b}]},panelRestoreIcon:{width:300,height:300,comps:[{type:"rect",rect:[10,24,268,56],background:b},{type:"rect",rect:[10,118,268,56],background:b},{type:"rect",rect:[10,213,268,56],background:b}]},panelTitleLabelColor:W.labelSelectColor,panelTitleLabelFont:W.labelFont,panelContentLabelFont:W.labelFont,panelTitleBackground:s.titleBackground,panelSeparatorWidth:1,panelSeparatorColor:v},!0),l.widget.Panel=function(b){var d=this,G=d._view=S.createView(null,d);d.$1i=0,d.$18i=new l.Notifier,d.$2i="leftTop",N(G,Y,A),N(G,q,A),N(G,t,J),N(G,"overflow","hidden"),d._interactor=new o(d),d.setConfig(b),d.addEventListener(function(b){var G=d.getPanelConfig(b.id),s=G.content;"beginRestore"!==b.kind&&"betweenResize"!==b.kind&&"endToggle"!==b.kind||(s&&s.invalidate&&s.invalidate(),G.items&&G.items.forEach(function(b){b&&b.content&&b.content.invalidate&&b.content.invalidate()}))})}),o=(W.def(b,G,{ms_v:1,_dragContainment:"parent",setConfig:function(b){if(b){for(var G=this,s=G._view.parentNode;this._view.children.length;)this._view.removeChild(this._view.children[0]);G._config=b,G.$35i=[],b.expanded==_&&(b.expand!=_?b.expanded=b.expand:b.expanded=!0);var d=G._view,e=G.$24i(b,d,!0),o=e[2],v=b.width,e=(G.$35i.push(e[1]),b.items&&b.items.forEach(function(b){b.expanded==_&&(b.expand!=_?b.expanded=b.expand:b.expanded=!0);b=G.$24i(b,o.children[0]);G.$35i.push(b[1])}),z()),e=(N(e,p,10+i),N(e,D,10+i),N(e,t,J),N(e,a,A),N(e,R,A),e[w]="resize-area",F(d,e),G.$10i(),b.flowLayout&&N(d,t,"relative"),N(d,"opacity",A),F(E().body,d),v==_&&(v=d.offsetWidth),N(d,p,v+i),N(d,L,v+i),b.content);e&&e.isSelfViewEvent&&(e.setX(0),e.setY(0),e.setWidth(v-2*(b.borderWidth||0)),e.setHeight(b.contentHeight)),b.items&&b.items.forEach(function(b){j(b)}),b.buttons&&b.buttons.indexOf("toggle")<0||j(b),b.minimized==_&&b.minimize!=_&&(b.minimized=b.minimize),b.minimized&&!1!==b.minimizable&&G.minimize(!0),E().body.removeChild(d),N(d,"opacity",X),s&&F(s,d),G.iv()}function j(b){b.expanded=!b.expanded,G.togglePanel(b.id,!0,!0)}},getPanelConfig:function(b){var G=this._config,s=G.items;if(G.id===b)return G;if(s)for(var d=0;d<s.length;d++){var e=s[d];if(e.id===b)return e}},getPanelView:function(b){for(var G=this.$35i,s=0;s<G.length;s++){var d=G[s].parentNode;if(d.$15i===b)return d}},setDragContainment:function(b){this._dragContainment=b},getDragContainment:function(){return this._dragContainment},$20i:function(){return this._config.restoreIconSize||24},$5i:function(b){b=b.titleIconSize||16;return d&&(b*=1.2),b},$4i:function(b){return b.titleHeight||W.widgetTitleHeight},setTitle:function(b,G){var s;(G=G==_?this._config.id:G)!=_&&(s=this.getPanelConfig(G),G=this.getPanelView(G),s.title=b,G.querySelector(".panel-title span").innerHTML=b)},setInnerPanel:function(b){var G,s=this,d=b.id,e=s.$35i,o=!1;if(b.expanded==_&&(b.expanded=!0),d!=_){var v=s.getPanelConfig(d);if(v){var j,o=!0,d=s.getPanelView(d),E=d.parentNode,c=d.children[0];if(d!==s._view){for(j in s.$11i(),v)delete v.key;for(j in b)v[j]=b[j];G=s.$24i(b,E,!1,d)[1],E.removeChild(d);for(var l=0;l<e.length;l++)if(e[l]===c){e.splice(l,1,G);break}s.$12i(),v.expanded=!v.expanded,s.togglePanel(v.id,!0,!0),s.iv()}}}o||(s.$11i(),G=s.$24i(b,s._view.children[1])[1],e.push(G),s._config.items||(s._config.items=[]),(E=s._config.items).push(b),s.$12i(),1<E.length&&(d=E[E.length-2],o=s.getPanelView(d.id).children[0],N(o,"border-bottom",s.$55i(d))),b.expanded=!b.expanded,s.togglePanel(b.id,!0,!0),s.iv())},removeInnerPanel:function(b){var G,s=this,d=-1,e=s._config.items;if(e)for(E=0;E<e.length;E++)if(e[E].id===b){d=E;break}if(s.$11i(),0<=d){for(var o=s.$35i,v=s.getPanelView(b),j=v.children[0],E=0;E<o.length;E++)if(o[E]===j){o.splice(E,1);break}e.splice(d,1),v.parentNode.removeChild(v)}s.$12i(),0<e.length&&(v=e[e.length-1],G=s.getPanelView(v.id).children[0],N(G,"border-bottom",s.$55i(v)))},$6i:function(b){N(b,"cursor","pointer"),N(b,"display","inline-block"),N(b,"margin-right",(d?8:4)+i),N(b,"vertical-align",Y)},$24i:function(b,G,s,d){var e=this,o=e._config.flowLayout,v=s?G:z(),j=e.$50i(b),E=e.$3i(b,s),c=(v[w]="ht-widget-panel"+(s?" outer-panel":" inner-panel"),b.borderWidth==_&&(b.borderWidth=s?2:0),b.borderWidth),s=(N(v,"border-width","0 "+c+i+" "+c+i+" "+c+i+" "),N(v,"border-color",b.titleBackground||W.panelTitleBackground),N(v,"border-style","solid"),F(v,E),F(v,j),s||(d?G.insertBefore(v,d):F(G,v)),!o&&s&&!1!==b.minimizable&&(c=f(),d=e.$20i(),o=b.restoreToolTip,S.setCanvas(c,d,d),c[w]="control-button button-minimize button-minimize-restore",e.$6i(c),N(c,"display","none"),F(G,c),c.title=o||""),b.panelBackground||b.titleBackground||W.panelTitleBackground);if(N(v,"background-color",s),b.id==_){for(var l=e.$1i++;e.getPanelConfig(l);)l=e.$1i++;b.id=l}return v.$15i=b.id,b.width&&(v.style.width=b.width+i),[v,E,j]},$9i:function(b){var G=f(),s=(G[w]="control-button button-toggle button-toggle-expand",G.title=b.toggleToolTip||"",this.$4i(b)),b=this.$5i(b);return this.$6i(G),S.setCanvas(G,b,s),G},$8i:function(b){var G=f(),s="control-button button-independent-switch",s=(!0===b.independent?G[w]=s+" button-independent-switch-on":G[w]=s+" button-independent-switch-off",G.title=b.independentSwitchToolTip||"",this.$4i(b)),b=this.$5i(b);return this.$6i(G),S.setCanvas(G,b,s),G},$7i:function(b){var G=f(),s=(G[w]="control-button button-minimize button-minimize-minimize",G.title=b.minimizeToolTip||"",this.$4i(b)),b=this.$5i(b);return this.$6i(G),S.setCanvas(G,b,s),G},$55i:function(b){var G=this._config,s=G.items,d=b.separatorWidth||W.panelSeparatorWidth,e=b.titleBackground||W.panelTitleBackground,e=!1!==b.expanded?e:b.separatorColor||W.panelSeparatorColor||W.brighter(e);return(d=G===b||s&&s.indexOf(b)===s.length-1?0:d)+i+" solid "+e},$3i:function(G,s){function b(){var b=v.$9i(G);F(c,b)}function d(){var b;!j&&s&&!1!==G.minimizable&&(b=v.$7i(G),F(c,b))}var e,o,v=this,j=v._config.flowLayout,E=z(),c=z(),l=v.$4i(G),P=G.titleBackground,m=G.titleColor,M=G.titleIcon,K=G.buttons,M=(E[w]="panel-title",N(E,t,"relative"),N(E,"background",P||W.panelTitleBackground),N(E,"color",m||W.panelTitleLabelColor),N(E,Y,A),N(E,"box-sizing","border-box"),N(E,"-moz-box-sizing","border-box"),N(E,"padding","0 5px 0 0"),N(E,p,"100%"),N(E,"cursor","default"),N(E,"white-space","nowrap"),N(E,"font",W.panelTitleLabelFont),M&&((P=f())[w]="control-button panel-title-icon",m=v.$4i(G),M=v.$5i(G),v.$6i(P),S.setCanvas(P,M,m),F(E,P)),B("span"));N(M,"display","inline-block"),N(M,"margin-left","5px"),M[k]="<span>"+G.title+"</span>",F(E,M),N(E,"line-height",l+i),c[w]="panel-title-controls",N(c,t,J),N(c,q,A),N(c,R,5+i),N(c,Y,A),N(c,a,A),N(c,"text-align",R);if(K)for(var V=0;V<K.length;V++){var g=K[V];"string"==typeof g?"minimize"===g?d():"independentSwitch"===g?(o=void 0,s||(o=v.$8i(G),F(c,o))):"toggle"===g&&b():"object"==typeof g&&(o=g,e=g=void 0,(g=f())[w]="control-button custombutton-"+o.name,g.title=o.toolTip||"",g._action=o.action,o=v.$4i(G),e=v.$5i(G),v.$6i(g),S.setCanvas(g,e,o),F(c,g))}else d(),b();return F(E,c),E},$50i:function(b){var G,s=z(),d=b.contentHeight,e=z(),o=(N(e,t,"relative"),s[w]="panel-body",N(s,"overflow","hidden"),b.contentBackground);return N(s,"background",o=o===v?"white":o),N(s,"font",W.panelContentLabelFont),F(s,e),b.content&&((o=b.content).getView?(F(e,o.getView()),G=e.children[0]):o instanceof Element?(F(e,o),G=e.children[0]):e[k]=o,o.isSelfViewEvent||G&&(N(G,p,"100%"),N(G,D,"100%")),d&&N(e,D,d+i)),s},$10i:function(){var b=this._config,G=this._view.querySelector(".resize-area").style;b.flowLayout||!0===b.minimized||!1===b.expanded?G.display=u:G.display="block"},$11i:function(){var b=this._view,G=b.children[1];0<=this.$13i?this.$13i++:this.$13i=1,N(G,h,X),N(b,L,X)},$12i:function(){var b,G;0==--this.$13i&&(G=(b=this._view).children[1],N(G,h,G.scrollHeight+i),N(b,L,b.offsetWidth+i))},$14i:function(){var b=this._view,G=b.children[0],s=G.children[1].children,d=this._config,e=0;b.$26i=b.offsetWidth,e+=G.children[0].offsetWidth,d.titleIcon&&(e+=G.children[1].offsetWidth,s=G.children[2].children);for(var o=0;o<s.length;o++)e+=s[o].offsetWidth+5;b.$51i=e+15},togglePanel:function(b,G,s){var d=this,e=d._view,o=e.children[1],v=_,j=d.$35i,E=j.length,c=d._config.exclusive,l=d.$2i,P=[],m=d._config.narrowWhenCollapse;function M(b){var b=b.target,G=b.parentNode,s=d.getPanelConfig(G.$15i);delete G.$19i,b!==o&&d.$12i(),d.$18i.fire({kind:"endToggle",target:d,id:s.id})}for(var K,V,g,B=0;B<E;B++){var z=j[B].parentNode,f=z.$15i,F=d.getPanelConfig(f);f===b&&(v=z),!G&&c&&F.expanded&&z!==e&&f!==b&&!0!==F.independent&&P.push(z)}v&&!v.$19i&&(v.$19i=!0,K=v.children[1],V=v.querySelector(".button-toggle"),g=d.getPanelConfig(v.$15i),V&&(v===e||g.expanded||!0===g.independent||P.forEach(function(b){d.togglePanel(b.$15i,!0)}),v!==e&&d.$11i(),s=s?0:200,d.$18i.fire({kind:"beginToggle",target:d,id:v.$15i}),g.expanded?(m&&v===e&&d.$14i(),V[w]="control-button button-toggle",0<=l.indexOf("Bottom")?V[w]+=" button-toggle-expand":V[w]+=" button-toggle-collapse",N(K,p,K.clientWidth+i),g.expanded=!1,N(v.children[0],"border-bottom",d.$55i(g)),n(K).duration(s).set("opacity",A).set(h,A).end(M),m&&v===e&&n(v).duration(s).set(L,v.$51i+i).end(),v[w]+=" panel-collapse",n(v).duration(s).set("padding-bottom",A).end()):(V[w]="control-button button-toggle",0<=l.indexOf("Bottom")?V[w]+=" button-toggle-collapse":V[w]+=" button-toggle-expand",N(K,p,X),g.expanded=!0,N(v.children[0],"border-bottom",d.$55i(g)),n(K).duration(s).set("opacity","1").set(h,K.scrollHeight+i).end(M),m&&v===e&&n(v).duration(s).set(L,(v.$26i||v.offsetWidth)+i).end(),v[w]=v[w].replace(" panel-collapse",X),n(v).duration(s).end()),d.$28i(g,!0),d.$10i()))},$16i:function(){var b=this._view,G=b.$22i,s=b.$23i,d=this.$2i;return G==_&&(0<=d.indexOf(q)?G=b.$22i=0:0<=d.indexOf(R)&&(G=b.$22i=100)),s==_&&(0<=d.indexOf("Top")?s=b.$23i=0:0<=d.indexOf("Bottom")&&(s=b.$23i=100)),[G,s]},$25i:function(){var b=this,G=b._view,s=G.$21i,d=b.$20i(),e=b.$16i(),o=e[0],e=e[1],v=b.$2i;G.children[0].style.display=u,G.children[1].style.display=u,G.children[2].style.display=X,N(G,"padding",A),N(G,L,d+i),"leftTop"===v?(N(G,q,Z(C(G,q))+(s.width-d)*o/100+i),N(G,Y,Z(C(G,Y))+(s.height-d)*e/100+i)):"leftBottom"===v?(N(G,q,Z(C(G,q))+(s.width-d)*o/100+i),N(G,a,Z(C(G,a))+(s.height-d)*(1-e/100)+i)):"rightTop"===v?(N(G,R,Z(C(G,R))+(s.width-d)*(1-o/100)+i),N(G,Y,Z(C(G,Y))+(s.height-d)*e/100+i)):"rightBottom"===v&&(N(G,R,Z(C(G,R))+(s.width-d)*(1-o/100)+i),N(G,a,Z(C(G,a))+(s.height-d)*(1-e/100)+i)),G[w]+=" panel-minimized",b.$18i.fire({kind:"endMinimize",target:b,id:G.$15i})},$17i:function(){var b=this,G=b._config,s=b._view;N(s,"-webkit-transform",X),N(s,"-ms-transform",X),N(s,"transform",X),G.minimized?b.$25i():(b.$18i.fire({kind:"endRestore",target:b,id:G.id}),s[w]=s[w].replace(" panel-minimized",X)),delete s.$19i},minimize:function(b){var G,s,d,e,o,v,j=this,E=j._view;E.$19i||E.children[0].style.display!==u&&(G=j._config,v=E.getBoundingClientRect(),s=(d=j.$20i())/v.width,d=d/v.height,e=(o=j.$16i())[0],o=o[1],E.$52i=s,E.$53i=d,E.$21i=v,j.$18i.fire({kind:"beginMinimize",target:j,id:E.$15i}),v=b?0:200,G.minimized=!0,E.$19i=!0,G.expanded&&(E.$26i=E.offsetWidth),N(E,"-webkit-transform-origin",b=e+"% "+o+"%"),N(E,"-ms-transform-origin",b),N(E,"transform-origin",b),n(E).duration(v).scale(s,d).end(function(){j.$17i()}),j.$10i())},restore:function(){var b,G,s,d,e,o,v,j,E,c,l,P,m,M,K,V,g,B=this,z=B._view,f=z.parentNode,F=B._config;z.$19i||F.minimized&&(m=z.$21i,M=z.$52i,K=z.$53i,F.borderWidth,V=B.$20i(),f=x(f),"leftTop"===(g=B.$2i)?(b=Z(C(z,q)),v=s=Z(C(z,Y)),0<(E=(e=b)+m.width-f.width)&&(b<=E?b=0:b-=E),0<(c=s+m.height-f.height)&&(s<=c?s=0:s-=c),l=(e-b)/(m.width-V)*100,P=(v-s)/(m.height-V)*100,N(z,q,b+i),N(z,Y,s+i)):"leftBottom"===g?(b=Z(C(z,q)),j=d=Z(C(z,a)),0<(E=(e=b)+m.width-f.width)&&(b<=E?b=0:b-=E),0<(c=d+m.height-f.height)&&(d<=c?d=0:d-=c),l=(e-b)/(m.width-V)*100,P=100*(1-(j-d)/(m.height-V)),N(z,q,b+i),N(z,a,d+i)):"rightTop"===g?(G=Z(C(z,R)),v=s=Z(C(z,Y)),0<(E=(o=G)+m.width-f.width)&&(G<=E?G=0:G-=E),0<(c=s+m.height-f.height)&&(s<=c?s=0:s-=c),l=100*(1-(o-G)/(m.width-V)),P=(v-s)/(m.height-V)*100,N(z,R,G+i),N(z,Y,s+i)):"rightBottom"===g&&(G=Z(C(z,R)),j=d=Z(C(z,a)),0<(E=(o=G)+m.width-f.width)&&(G<=E?G=0:G-=E),0<(c=d+m.height-f.height)&&(d<=c?d=0:d-=c),l=100*(1-(o-G)/(m.width-V)),P=100*(1-(j-d)/(m.height-V)),N(z,R,G+i),N(z,a,d+i)),z.children[0].style.display="block",z.children[1].style.display="block",z.children[2].style.display=u,N(z,"-webkit-transform","scale("+M+", "+K+")"),N(z,"-ms-transform","scale("+M+", "+K+")"),N(z,"transform","scale("+M+", "+K+")"),z.$22i=l,z.$23i=P,N(z,"-webkit-transform-origin",l+"% "+P+"%"),N(z,"-ms-transform-origin",l+"% "+P+"%"),N(z,"transform-origin",l+"% "+P+"%"),F.narrowWhenCollapse&&!F.expanded?N(z,L,z.$51i+i):N(z,L,z.$26i+i),B.$18i.fire({kind:"beginRestore",target:B,id:z.$15i}),z.$19i=!0,F.minimized=!1,$(function(){n(z).scale(1,1).end(function(){B.$17i()})},30),B.$10i())},addEventListener:function(b,G,s){this.$18i.add(b,G,s)},removeEventListener:function(b,G){this.$18i.remove(b,G)},setPosition:function(b,G){var s=this._view,d=this.$2i;"leftTop"===d?(N(s,q,b+i),N(s,Y,G+i),N(s,R,X),N(s,a,X)):"leftBottom"===d?(N(s,q,b+i),N(s,a,G+i),N(s,R,X),N(s,Y,X)):"rightTop"===d?(N(s,R,b+i),N(s,Y,G+i),N(s,q,X),N(s,a,X)):"rightBottom"===d&&(N(s,R,b+i),N(s,a,G+i),N(s,q,X),N(s,Y,X)),delete s.$22i,delete s.$23i},getPosition:function(){var b=this._view,G=this.$2i;return"leftTop"===G?{x:Z(C(b,q)),y:Z(C(b,Y))}:"leftBottom"===G?{x:Z(C(b,q)),y:Z(C(b,a))}:"rightTop"===G?{x:Z(C(b,R)),y:Z(C(b,Y))}:"rightBottom"===G?{x:Z(C(b,R)),y:Z(C(b,a))}:void 0},setPositionRelativeTo:function(b){var G=this,s=G._view.querySelectorAll(".button-toggle"),d="control-button button-toggle",e=G.getPosition();G.$2i=b,G.setPosition(e.x,e.y);for(var o=0;o<s.length;o++){var v=s[o];G.getPanelConfig(v.parentNode.parentNode.parentNode.$15i).expanded?0<=b.indexOf("Bottom")?v[w]=d+" button-toggle-collapse":v[w]=d+" button-toggle-expand":0<=b.indexOf("Bottom")?v[w]=d+" button-toggle-expand":v[w]=d+" button-toggle-collapse"}G.iv()},getPositionRelativeTo:function(){return this.$2i},invalidate:function(b){var G=this,b=(G._68I||(G._68I=1,W.callLater(G.validate,G,_,b),G.onInvalidated&&G.onInvalidated(),G.fireViewEvent("invalidate")),this._config),G=b.content;G&&G.invalidate&&G.invalidate(),b.items&&b.items.forEach(function(b){b&&b.content&&b.content.invalidate&&b.content.invalidate()})},getIconStretch:function(b){return this._config.iconStretch||"fill"},$27i:function(b,G,s,d,e){b=S.initContext(b),S.translateAndScale(b,0,0,1),b.clearRect(0,0,s,s),s=(s-d)/2;W.drawStretchImage(b,W.getImage(G),this.getIconStretch(e),0,s,d,d),b.restore()},$28i:function(b){var G=this,s=b.id,s=G.getPanelView(s).querySelector(".button-toggle"),d=0<=G.$2i.indexOf("Bottom"),e=d?W.panelCollapseIcon:W.panelExpandIcon,d=d?W.panelExpandIcon:W.panelCollapseIcon;s&&(d=b.expanded?W.getImage(d):W.getImage(e),e=G.$4i(b),b=G.$5i(b),G.$27i(s,d,e,b,"toggle"))},$29i:function(b){var G=b.id,G=this.getPanelView(G).querySelector(".button-independent-switch"),s=W.panelUnLockIcon,d=W.panelLockIcon;G&&(d=!0!==b.independent?W.getImage(d):W.getImage(s),s=this.$4i(b),b=this.$5i(b),this.$27i(G,d,s,b,"switch"))},$30i:function(b){var G,s=b.id,s=this.getPanelView(s).querySelector(".button-minimize-minimize"),d=W.panelMinimizeIcon;s&&(G=this.$4i(b),b=this.$5i(b),this.$27i(s,W.getImage(d),G,b,"miminize"))},$31i:function(b){var G,s=b.id,s=this.getPanelView(s).querySelector(".button-minimize-restore"),b=b.titleIcon||W.panelRestoreIcon;s&&(G=this.$20i(),this.$27i(s,W.getImage(b),G,G,"restore"))},$32i:function(e){var o=this,b=e.id,v=o.getPanelView(b);e.buttons&&e.buttons.forEach(function(b){var G,s,d=b.name,b=b.icon;d&&b&&((d=v.querySelector(".custombutton-"+d))&&(G=o.$4i(e),s=o.$5i(e)-1,o.$27i(d,W.getImage(b),G,s,"custom")))})},$33i:function(b){var G,s=b.id,s=this.getPanelView(s).querySelector(".panel-title-icon"),d=b.titleIcon;s&&d&&(G=this.$4i(b),b=this.$5i(b),this.$27i(s,W.getImage(d),G,b,"title"))},validateImpl:function(){var G=this,b=G._config;G.$28i(b),G.$30i(b),G.$31i(b),G.$32i(b),G.$33i(b),b.items&&b.items.forEach(function(b){G.$28i(b),G.$29i(b),G.$32i(b)})},setWidth:function(b){var G=this,s=G._view,d=G._config;G._view.style.width=b+i,d&&(d.width=b,N(s,L,X)),G.iv(),G.fp&&G.fp(p,_,b)},setHeight:function(b){var G=this,s=G._config,d=G._view.children[1],e=G.$4i(s);b=Math.max(e,b-e),s&&(s.contentHeight=b),d&&((e=d.children[0])&&N(e,D,b+i),N(d,h,X)),G.iv(),G.fp&&G.fp(D,_,b)}}),function(b){var G=b.getView();this.$34i=b,this.addListeners(),e(G,"dblclick",this.$42i.bind(this))}),s=(W.def(o,G,{ms_listener:1,getView:function(){return this.$34i.getView()},clear:function(){delete this.$37i,delete this.$38i,delete this.$36i,delete this.$39i},$42i:function(b){for(var G=this.$34i,s=b.target,d=G.$35i,e=d.length,o=0;o<e;o++){var v=d[o];v.contains(s)&&(b.preventDefault(),G.togglePanel(v.parentNode.$15i))}},handle_touchstart:function(b){var G,s,d,e=this,o=e.$34i,v=o._config.flowLayout,j=b;W.isLeftButton(b)&&(G=b.target,s=o.getView().children[0],d=o.getView().querySelector(".button-minimize-restore"),Q(b)&&(j=y(b)),j=e.$40i={x:j.pageX,y:j.pageY},e.$41i={x:j.x,y:j.y},(!v&&s.contains(G)||d&&d.contains(G))&&(e.$38i=!0,W.startDragging(e,b)),!v&&e.handle_mousemove(b)&&(e.$37i=!0,W.startDragging(e,b),o.$11i()))},handle_mousedown:function(b){this.handle_touchstart(b)},handle_touchend:function(b){var G=this,s=G.$34i,d=b.target,e=s.$35i,o=e.length,v=0,j=s.getView(),E=j.querySelector(".button-minimize"),j=j.querySelector(".button-minimize-restore");if(!G.$39i&&!G.$36i){if(E&&E.contains(d)||j&&j.contains(d))b.preventDefault(),s._config.minimized?s.restore():s.minimize();else for(;v<o;v++){var c,l=e[v],P=l.parentNode.$15i,m=s.getPanelConfig(P),M=l.querySelector(".button-toggle"),K=l.querySelector(".button-independent-switch");M===d?(b.preventDefault(),s.togglePanel(P)):K===d?(b.preventDefault(),M="button-independent-switch-off",c="button-independent-switch-on",m.independent==_?m.independent=!0:m.independent=!m.independent,m.independent?K[w]=K[w].replace(M,c):K[w]=K[w].replace(c,M),s.$29i(m)):d[w]&&0<=d[w].indexOf("control-button custombutton-")&&l.contains(d)&&d._action.call(s,m,s.getPanelView(P),b)}delete G.$40i,delete G.$41i}},handle_mouseup:function(b){this.handle_touchend(b)},handleWindowTouchEnd:function(b){var G=this,s=G.$34i;G.$37i&&G.$36i?(s.$18i.fire({kind:"endResize",target:s,id:s.getView().$15i}),s.$12i()):G.$38i&&G.$39i&&s.$18i.fire({kind:"endMove",target:s,id:s.getView().$15i}),this.clear()},handleWindowMouseUp:function(b){this.handleWindowTouchEnd(b)},handle_mousemove:function(b){var G=this.getView(),s=G.querySelector(".resize-area").getBoundingClientRect(),s={x:s.left,y:s.top,width:s.width,height:s.height},d=(b=Q(b)?y(b):b).clientX,b=b.clientY,e=this.$34i._config;if(e.expanded&&!0!==e.minimized&&W.containsPoint(s,{x:d,y:b}))return G.style.cursor="nwse-resize",!0;G.style.cursor=X},handleWindowTouchMove:function(b){b.preventDefault();var G,s,d,e,o,v,j,E,c,l,P,m,M,K,V,g,B,z,f,F,A,w,t=b,b=(Q(b)&&(t=y(b)),this),J=b.$40i,L=b.$41i;L.x==J.x&&L.y==J.y&&W.getDistance(L,{x:t.pageX,y:t.pageY})<=1||(L=b.$34i,s=(G=b.getView()).parentNode,m=(d=L._config).resizeMode||"wh",e=t.pageX-J.x,o=t.pageY-J.y,v=L.$2i,b.$37i?(j=G.children[1].children[0],E=(c=G.offsetWidth)+e,l=(P=j.offsetHeight)+o,E=Math.max(E,100),l=Math.max(l,100),"w"===m?(N(G,p,E+i),d.width=E):"h"===m?(N(j,D,l+i),d.contentHeight=l):"wh"===m&&(N(G,p,E+i),N(j,D,l+i),d.width=E,d.contentHeight=l),0<=v.indexOf("right")&&N(G,R,Z(C(G,R))-(E-c)+i),0<=v.indexOf("Bottom")&&N(G,a,Z(C(G,a))-(l-P)+i),J.x=t.pageX,J.y=t.pageY,(m=d.content)&&m.isSelfViewEvent&&(m.setX(0),m.setY(0),m.setWidth(d.width-2*(d.borderWidth||0)),m.setHeight(d.contentHeight)),b.$36i?L.$18i.fire({kind:"betweenResize",target:L,id:L.getView().$15i}):(b.$36i=!0,L.$18i.fire({kind:"beginResize",target:L,id:L.getView().$15i}))):b.$38i&&(E=(j=G.getBoundingClientRect()).width,c=j.height,P=(l=x(s)).width,t=l.height,m=L._dragContainment,"leftTop"===v?(B=(M=Z(C(G,q))||0)+e,f=(V=Z(C(G,Y))||0)+o,"parent"===m&&((B=P<B+E?P-E:B)<0&&(B=0),(f=t<f+c?t-c:f)<0&&(f=0)),A=B-M,w=f-V,L.setPosition(B,f),J.x+=A,J.y+=w):"rightBottom"===v?(z=(K=Z(C(G,R))||0)-e,F=(g=Z(C(G,a))||0)-o,"parent"===m&&(P<(z=z<0?0:z)+E&&(z=P-E),t<(F=F<0?0:F)+c&&(F=t-c)),A=z-K,w=F-g,L.setPosition(z,F),J.x-=A,J.y-=w):"rightTop"===v?(z=(K=Z(C(G,R))||0)-e,f=(V=Z(C(G,Y))||0)+o,"parent"===m&&(P<(z=z<0?0:z)+E&&(z=P-E),t<(f=f<0?0:f)+c&&(f=t-c)),A=z-K,w=f-V,L.setPosition(z,f),J.x-=A,J.y+=w):"leftBottom"===v&&(B=(M=Z(C(G,q))||0)+e,F=(g=Z(C(G,a))||0)-o,"parent"===m&&(P<(B=B<0?0:B)+E&&(B=P-E),t<(F=F<0?0:F)+c&&(F=t-c)),A=B-M,w=F-g,L.setPosition(B,F),J.x+=A,J.y-=w),b.$39i?L.$18i.fire({kind:"betweenMove",target:L,id:L.getView().$15i}):(b.$39i=!0,L.$18i.fire({kind:"beginMove",target:L,id:L.getView().$15i}))))},handleWindowMouseMove:function(b){this.handleWindowTouchMove(b)}}),l.widget.PanelGroup=function(b){var G=this,s=G._view=S.createView(null,G);s.style.border="1px dashed black",s.style.position="absolute",s.style.background="rgba(120, 120, 120, 0.4)",G.$48i=new l.List,G._tolerance=100,G._config=b||{hGap:0,vGap:0},G.bindHandlePanelMove=G.handlePanelMove.bind(G),G.bindHandlePanelEvent=G.handlePanelEvent.bind(G),G.invalidate()});W.def(s,G,{invalidate:function(){var b=this;b._68I||(b._68I=1,$(function(){b.validate()},50))},validate:function(){var b;this._68I&&(delete this._68I,(b=this.$48i.get(0))&&(b=b.getView().parentNode)&&(this.layoutPanels(b,"leftTop"),this.layoutPanels(b,"rightTop"),this.layoutPanels(b,"leftBottom"),this.layoutPanels(b,"rightBottom")))},setLeftTopPanels:function(){var b=this.$43i,G=this.$48i;b==_&&(b=this.$43i=new l.List);for(var s=0;s<arguments.length;s++){var d=arguments[s];"string"==typeof d?b.$49i=d:d._config.flowLayout||(d.setPositionRelativeTo("leftTop"),b.contains(d)||b.add(d),G.contains(d)||this.add(d))}},setRightTopPanels:function(){var b=this.$44i,G=this.$48i;b==_&&(b=this.$44i=new l.List);for(var s=0;s<arguments.length;s++){var d=arguments[s];"string"==typeof d?b.$49i=d:d._config.flowLayout||(d.setPositionRelativeTo("rightTop"),b.contains(d)||b.add(d),G.contains(d)||this.add(d))}},setLeftBottomPanels:function(){var b=this.$45i,G=this.$48i;b==_&&(b=this.$45i=new l.List);for(var s=0;s<arguments.length;s++){var d=arguments[s];"string"==typeof d?b.$49i=d:d._config.flowLayout||(d.setPositionRelativeTo("leftBottom"),b.contains(d)||b.add(d),G.contains(d)||this.add(d))}},setRightBottomPanels:function(){var b=this.$46i,G=this.$48i;b==_&&(b=this.$46i=new l.List);for(var s=0;s<arguments.length;s++){var d=arguments[s];"string"==typeof d?b.$49i=d:d._config.flowLayout||(d.setPositionRelativeTo("rightBottom"),b.contains(d)||b.add(d),G.contains(d)||this.add(d))}},add:function(b){var G;b._config.flowLayout||((G=this.$48i).contains(b)||(b.addEventListener(this.bindHandlePanelMove),b.addEventListener(this.bindHandlePanelEvent),G.add(b)))},remove:function(b){var G=this,s=G.$48i;s.contains(b)&&(b.removeEventListener(G.bindHandlePanelMove),b.removeEventListener(G.bindHandlePanelEvent),s.remove(b),G.$43i.contains(b)&&G.$43i.remove(b),G.$44i.contains(b)&&G.$44i.remove(b),G.$45i.contains(b)&&G.$45i.remove(b),G.$46i.contains(b)&&G.$46i.remove(b))},layoutPanels:function(b,G,s){var d=this,e=d._config,o=e.hGap||0,v=e.vGap||0;if(b){var j=d.$43i;if("leftBottom"===G?j=d.$45i:"rightTop"===G?j=d.$44i:"rightBottom"===G&&(j=d.$46i),j){var E=j.$49i,c=o,l=v;if(b.contains(d._view)&&b.removeChild(d._view),j&&0<j.size())for(var P=0;P<j.size();P++){var m=j.get(P),M=m.getView();b=b||M.parentNode,s!==P?m.setPosition(c,l):("leftTop"===G?(d._view.style.right="",d._view.style.bottom="",d._view.style.left=c+i,d._view.style.top=l+i):"leftBottom"===G?(d._view.style.right="",d._view.style.top="",d._view.style.left=c+i,d._view.style.bottom=l+i):"rightTop"===G?(d._view.style.left="",d._view.style.bottom="",d._view.style.right=c+i,d._view.style.top=l+i):"rightBottom"===G&&(d._view.style.left="",d._view.style.top="",d._view.style.right=c+i,d._view.style.bottom=l+i),d._view.style.width=M.offsetWidth+i,d._view.style.height=M.offsetHeight+i,b.insertBefore(d._view,M)),"h"===E?c+=M.offsetWidth+o:"v"===E&&(l+=M.offsetHeight+v)}}}},handlePanelEvent:function(b){var G,s,d,e,o,v,j,E,c,l,P,m,M;"beginToggle"!==b.kind&&"endToggle"!==b.kind&&"beginRestore"!==b.kind&&"endMinimize"!==b.kind&&"endResize"!==b.kind||(G=this,s=b.target,d=s.getView().parentNode,P=G.$43i,e=G.$44i,M=G.$45i,o=G.$46i,v=G._config,E=j=_,(c=s.$47i)==_&&(c=s.$47i=0),"beginToggle"===b.kind?c=s.$47i=c+1:"endToggle"===b.kind&&(c=s.$47i=c-1),P&&P.contains(s)?(j="leftTop",E=P):M&&M.contains(s)?(j="leftBottom",E=M):e&&e.contains(s)?(j="rightTop",E=e):o&&o.contains(s)&&(j="rightBottom",E=o),"beginToggle"===b.kind&&j&&1===c?(P=(l=z()).style,m=E.$49i,M="each",P.fontSize="0",P.position="absolute",P.width="100%","leftTop"===j?(P.left=0,P.top=0):"leftBottom"===j?(P.left=0,P.bottom=0,"v"===m&&(M="reverseEach")):"rightTop"===j?(P.right=0,P.top=0,P.textAlign="right","h"===m&&(M="reverseEach")):"rightBottom"===j&&(P.right=0,P.bottom=0,P.textAlign="right",M="reverseEach"),E[M](function(b){var b=b.getView(),G=b.style,s=z();G.position="static",s.style.textAlign="left",s.style.position="relative",s.style.display="inline-block","leftTop"===j?(s.style.marginLeft=v.hGap+i,s.style.marginTop=v.vGap+i):"leftBottom"===j?(s.style.marginLeft=v.hGap+i,s.style.marginBottom=v.vGap+i):"rightTop"===j?(s.style.marginRight=v.hGap+i,s.style.marginTop=v.vGap+i):"rightBottom"===j&&(s.style.marginRight=v.hGap+i,s.style.marginBottom=v.vGap+i),s.appendChild(b),l.appendChild(s),"h"===m?s.style.verticalAlign="leftTop"===j||"rightTop"===j?"top":"bottom":l.appendChild(B("br"))}),G.$54i=l,d.appendChild(l)):"endToggle"===b.kind&&j&&0===c?$(function(){(d=d.parentNode.parentNode).removeChild(G.$54i),delete G.$54i,E.each(function(b){b=b.getView();b.style.position="absolute",d.appendChild(b)}),G.layoutPanels(d,j)},30):"beginRestore"!==b.kind&&"endMinimize"!==b.kind&&"endResize"!==b.kind||j&&G.layoutPanels(d,j))},handlePanelMove:function(b){var t,J,L,N,G,s,d,C,x,y,e,o,v,j,E,W,Q,i,p,D,q,Y,c;b.kind.indexOf("Move")<0||(e=(t=this)._config,J=e.hGap||0,L=e.vGap||0,s=(G=(e=(N=b.target)._view).getBoundingClientRect()).width,d=G.height,C=s/2,x=d/2,y=e.parentNode,e=t.$43i,o=t.$44i,v=t.$45i,j=t.$46i,E=y.getBoundingClientRect(),W=t._tolerance,"endMove"===b.kind&&((c=t._corner)&&(N.setPositionRelativeTo(c),t.layoutPanels(y,c)),delete t._corner),"betweenMove"===b.kind&&(Q=E.left,i=E.top,p=E.width,D=E.height,q=G.left+s/2,Y=G.top+d/2,e==_&&(e=t.$43i=new l.List),v==_&&(v=t.$45i=new l.List),o==_&&(o=t.$44i=new l.List),j==_&&(j=t.$46i=new l.List),delete t._corner,e.contains(N)?(e.remove(N),t.layoutPanels(y,"leftTop")):v.contains(N)?(v.remove(N),t.layoutPanels(y,"leftBottom")):o.contains(N)?(o.remove(N),t.layoutPanels(y,"rightTop")):j.contains(N)&&(j.remove(N),t.layoutPanels(y,"rightBottom")),(c=function(b,G){var s=Q+J,d=i+L;if(0===G.size()){var e=s+C,o=d+x,v=("leftBottom"===b?o=i+D-L-x:"rightTop"===b?e=Q+p-J-C:"rightBottom"===b&&(e=Q+p-J-C,o=i+D-L-x),q-e),j=Y-o;if((f=Math.sqrt(v*v+j*j))<W)return t._corner=b,G.add(N),t.layoutPanels(y,b,0),!0}else if(1===G.size()){var E=G.get(0).getView().getBoundingClientRect(),c=E.left+C,l=E.top+x,P=E.left+E.width+J+C,m=d+x,M=s+C,K=E.top+E.height+L+x,E=("leftBottom"===b?(l=E.top+E.height-x,m=i+D-L-x,K=E.top-L-x):"rightTop"===b?(c=E.left+E.width-C,P=E.left-J-C,M=Q+p-J-C):"rightBottom"===b&&(c=E.left+E.width-C,l=E.top+E.height-x,P=E.left-J-C,m=i+D-L-x,M=Q+p-J-C,K=E.top-L-x),q-c),c=Y-l,l=q-P,P=Y-m,m=q-M,M=Y-K,K=Z(Math.sqrt(E*E+c*c)),E=Z(Math.sqrt(l*l+P*P)),c=Z(Math.sqrt(m*m+M*M)),l=[K,E,c];if(l.sort(function(b,G){return b-G}),(P=l[0])<W)return t._corner=b,P===K?(G.add(N,0),t.layoutPanels(y,b,0),!0):P===E?(G.add(N),G.$49i="h",t.layoutPanels(y,b,1),!0):P===c?(G.add(N),G.$49i="v",t.layoutPanels(y,b,1),!0):void 0}else if(1<G.size()){for(var V=_,g={},B=[],m=G.$49i,z=0;z<G.size();z++){var f,F=G.get(z).getView().getBoundingClientRect(),A=F.left+C,w=F.top+x,v=("leftBottom"===b?w=F.top+F.height-x:"rightTop"===b?A=F.left+F.width-C:"rightBottom"===b&&(A=F.left+F.width-C,w=F.top+F.height-x),z===G.size()-1&&(V=F),q-A),j=Y-w;g[f=Z(Math.sqrt(v*v+j*j))]=z,B.push(f)}if("leftTop"===b&&"h"===m?(e=V.left+V.width+J+C,o=d+x):"leftTop"===b&&"v"===m?(e=s+C,o=V.top+V.height+L+x):"leftBottom"===b&&"h"===m?(e=V.left+V.width+J+C,o=i+D-L-x):"leftBottom"===b&&"v"===m?(e=s+C,o=V.top-L-x):"rightTop"===b&&"h"===m?(e=V.left-J-C,o=d+x):"rightTop"===b&&"v"===m?(e=Q+p-J-C,o=V.top+V.height+L+x):"rightBottom"===b&&"h"===m?(e=V.left-J-C,o=i+D-L-x):"rightBottom"===b&&"v"===m&&(e=Q+p-J-C,o=V.top-L-x),v=q-e,j=Y-o,g[f=Z(Math.sqrt(v*v+j*j))]=z,B.push(f),B.sort(function(b,G){return b-G}),(P=B[0])<W)return t._corner=b,G.add(N,g[P]),t.layoutPanels(y,b,g[P]),!0}})("leftTop",e)||c("leftBottom",v)||c("rightTop",o)||c("rightBottom",j)))}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);