<!doctype html>
<html>
    <head>
        <title>HT for Web Form Manual</title>
        <meta charset="UTF-8">    
        <meta name="viewport" content="user-scalable=yes, width=1024">
        <style type="text/css"> 
            h1, h2, h3, h4, h5, h6, p, blockquote {
                margin: 0;
                padding: 0;
            }
            body {
                font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif;
                font-size: 13px;
                line-height: 18px;
                color: #737373;
                background-color: white; 
                margin: 10px 13px 10px 13px;
            }
            table {
                margin: 10px 0 15px 0;
                border-collapse: collapse;
            }
            td,th {	
                border: 1px solid #ddd;
                padding: 3px 10px;
            }
            th {
                padding: 5px 10px;	
            }
            a {
                color: #0069d6;
            }
            a:hover {
                color: #0050a3;
                text-decoration: none;
            }
            a img {
                border: none;
            }
            p {
                margin-bottom: 9px;
            }
            h1, h2, h3, h4, h5, h6 {
                color: #404040;
                line-height: 36px;
            }
            h1 {
                margin-bottom: 18px;
                font-size: 30px;
            }
            h2 {
                font-size: 24px;
            }
            h3 {
                font-size: 18px;
            }
            h4 {
                font-size: 16px;
            }
            h5 {
                font-size: 14px;
            }
            h6 {
                font-size: 13px;
            }
            hr {
                margin: 0 0 19px;
                border: 0;
                border-bottom: 1px solid #ccc;
            }
            blockquote {
                padding: 13px 13px 21px 15px;
                margin-bottom: 18px;
                font-family:georgia,serif;
                font-style: italic;
            }
            blockquote:before {
                content:"\201C";
                font-size:40px;
                margin-left:-10px;
                font-family:georgia,serif;
                color:#eee;
            }
            blockquote p {
                font-size: 14px;
                font-weight: 300;
                line-height: 18px;
                margin-bottom: 0;
                font-style: italic;
            }
            code, pre {
                font-family: Monaco, Andale Mono, Courier New, monospace;
            }
            code {
                background-color: #fee9cc;
                color: rgba(0, 0, 0, 0.75);
                padding: 1px 3px;
                font-size: 12px;
                -webkit-border-radius: 3px;
                -moz-border-radius: 3px;
                border-radius: 3px;
            }
            pre {
                display: block;
                padding: 14px;
                margin: 0 0 18px;
                line-height: 16px;
                font-size: 11px;
                border: 1px solid #d9d9d9;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            pre code {
                background-color: #fff;
                color:#737373;
                font-size: 11px;
                padding: 0;
            }
            sup {
                font-size: 0.83em;
                vertical-align: super;
                line-height: 0;
            }
            * {
                -webkit-print-color-adjust: exact;
            }
            @media screen and (min-width: 914px) {
                body {
                    width: 854px;
                    margin:10px auto;
                }
            }
            @media print {
                body,code,pre code,h1,h2,h3,h4,h5,h6 {
                    color: black;
                }
                table, pre {
                    page-break-inside: avoid;
                }
            }                        
            iframe{
                width: 100%;
                border: 1px solid #34495E;
                margin: 0;                
            }            
            .logo{
                vertical-align: middle;
            }

        </style>        
        <script>
            function init() {
                var logoSrc = 'data:image/png;base64,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',
                    logos = document.querySelectorAll('.logo'),
                    i = 0;
                for (; i < logos.length; i++) {
                    logos[i].src = logoSrc;
                }
                
                var iframes = document.querySelectorAll('iframe'),
                    func = function(){};
                for (i=0; i < iframes.length; i++) {
                    var iframe = iframes[i];
                    
                    // a small hack to make it work on android
                    iframe.ontouchstart = func;
                    
                    var div = document.createElement('div');
                    div.innerHTML = '<a href="' + iframe.src + '" target="_blank">Run→</a>';
                    iframe.parentNode.insertBefore(div, iframe);
                }   
            }
        </script>    
    </head>
    <body onload="init();">
        <a href="http://www.hightopo.com"><img class="logo"></a>HT for Web Form Manual
        <hr style="margin: 1px 0 20px 0">
<p>Index</p>

<ul><li><a href="#ref_overview">Overview</a> </li><li><a href="#ref_formpane">FormPane</a><ul><li><a href="#ref_func">Attribute Function</a></li><li><a href="#ref_layout">Layouting Parameter</a></li><li><a href="#ref_element">Component Element</a></li></ul></li><li><a href="#ref_button">Button</a></li><li><a href="#ref_radioButton">Radio Button</a></li><li><a href="#ref_checkBox">Check Box</a></li><li><a href="#ref_textField">Text Field</a></li><li><a href="#ref_textArea">Text Area</a></li><li><a href="#ref_image">Image</a></li><li><a href="#ref_slider">Slider</a></li><li><a href="#ref_combobox">Combo Box</a></li><li><a href="#ref_multicombobox">Multi-Combo Box</a></li><li><a href="#ref_colorpicker">Color Picker</a></li></ul>

<hr/>

<div id="ref_overview"></div>

<h2>Overview</h2>

<p>The <code>HT for Web</code> provides form plug-ins that contain components such as drop-down menus, sliders and buttons, and form panels with component layout features.
<code>html</code> also provides part of a similar component, however, there are many differences between the rendering of different browser platforms and even interactive effects, and some component styles cannot be customized, and the components provided by <code>HT</code> provide consistent style and interaction and support rich custom extensibility capabilities.</p>

<p>Using the form plug-in requires the introduction of the <code>ht.js</code> core library and the introduction of a <code>ht-form.js</code> form plug-in library.</p>

<div id="ref_formpane"></div>

<h2>FormPane</h2>

<p>The <code>HT for Web</code> provides a form panel component that features layout components that can be added to the table single-sided component for normal <code>html</code> elements or any view component built into <code>HT</code>. The form panel component is designed to add a row, each line adds any defined component, and by specifying the width information for each component, and the row height information for each row, to achieve the overall layout of all components, for a more complex interface can be implemented through nested form panels.</p>

<p><iframe src="examples/example_easing.html" style="height:400px"></iframe> </p>

<p><iframe src="examples/example_unboxing.html" style="height:400px"></iframe> </p>

<div id="ref_func"></div>

<h3>Attribute Function</h3>

<p><code>ht.widget.FormPane</code> is a form panel component class whose main configurable properties and functions are as follows:</p>

<ul><li><code>vPadding</code> Gets and sets the spacing between the top and top of the form and the contents of the component through <code>getVPadding</code> and <code>setVPadding</code>, with the default value <code>8</code></li><li><code>hPadding</code> Gets and sets the spacing between the left and right of the form and the contents of the component through <code>getHPadding</code> and <code>setHPadding</code>, with the default value <code>8</code></li><li><code>vGap</code> Gets and sets the form component&#39;s horizontal spacing by <code>getVGap</code> and <code>setVGap</code>, with the default value <code>6</code></li><li><code>hGap</code> Gets and sets the form component&#39;s vertical spacing by <code>getHGap</code> and <code>setHGap</code>, with the default value <code>6</code></li><li><code>rowHeight</code> Gets and sets default row height by <code>getRowHeight</code> and <code>setRowHeight</code></li><li><code>labelVPadding</code> Gets and sets the spacing between the top and top of the text through <code>getLabelVPadding</code> and <code>setLabelVPadding</code>, with the default value <code>0</code></li><li><code>labelHPadding</code> Gets and sets the spacing between the left and right of the text through <code>getLabelHPadding</code> and <code>setLabelHPadding</code>, with the default value <code>2</code></li><li><code>scrollBarColor</code> Gets and sets the scroll bar color through <code>getScrollBarColor</code> and `setScrollBarColor</li><li><code>scrollBarSize</code> Gets and sets the scroll bar width through <code>getScrollBarSize</code> and <code>setScrollBarSize</code></li><li><code>autoHideScrollBar</code> Gets and sets whether auto hide the scroll bar, the default is <code>true</code></li><li><code>addRow(items, widths, height, params)</code> Adds a line of components<ul><li><code>items</code> An array of elements that can be string, <code>json</code> format to describe component parameter information, <code>html</code> elements, or <code>null</code></li><li><code>widths</code> Each element width information array, width value greater than <code>1</code> represents fixed absolute value, less than or equals <code>1</code> represents relative value, also can be <code>80+0.3</code> combination</li><li><code>height</code> The row height information, the value is greater than <code>1</code> means fixed absolute value, less than or equals <code>1</code> represents the relative value, also can be a combination of <code>80+0.3</code>, with default row height for null</li><li><code>params</code> Additional parameters with <code>json</code> format, such as inserting row index and row border or background color, etc., such as <code>{index: 2, background: &#39;yellow&#39;, borderColor: &#39;red&#39;}</code></li></ul></li><li><code>removeRow(index)</code> Deletes the specified row</li><li><code>clear()</code> Clears all line information</li><li><code>getItemById(id)</code> Find the corresponding <code>item</code> element according to <code>id</code></li><li><code>getValue(id)</code> Gets the corresponding <code>item</code> element according to <code>id</code>, and the shorthand function is <code>v(id)</code></li><li><code>setValue(id, value)</code> Sets corresponding <code>item</code> element according to <code>id</code>, and the shorthand function is <code>v(id, value)</code></li><li><code>v(jsonObj)</code> Batch <code>jsonObj</code> all <code>key</code> and <code>value</code> pairs, set <code>id</code> and <code>value</code> to the corresponding <code>item</code> element</li></ul>

<div id="ref_layout"></div>

<h3>Lay outing Parameter</h3>

<p>Absolute and relative values:</p>

<ul><li>The absolute value represents the width or the height is fixed, the <code>HT</code> system set the absolute value while a value is greater than <code>1</code></li><li>The value of the relative value is less than or equal to <code>1</code> indicates that the true assignment value is the total width or total height after deducting all absolute values redistribution</li><li>The combination of absolute and relative values, representing the minimum width or row height to absolute value, and when there is room after deducting absolute values, then increase the distribution by relative</li></ul>

<p>For example, <code>[80, 0.1, 60, 0.2, &#39;20+0.3&#39;]</code> represents:</p>

<ul><li>First listed is fixed value <code>80</code></li><li>Second listed is <code>Math.max(0, (0.1/(0.1+0.2+0.3))*(width-80-60-20))</code></li><li>Third listed is fixed value <code>60</code></li><li>Forth listed is <code>Math.max(0, (0.2/(0.1+0.2+0.3))*(width-80-60-20))</code></li><li>Fivth listed is <code>20 + Math.max(0, (0.3/(0.1+0.2+0.3))*(width-80-60-20))</code></li></ul>

<div id="ref_element"></div>

<h3>Component Element</h3>

<p>There are three types of component elements:</p>

<ul><li><code>HTML</code> Native element, set primitive element object, or use <code>json</code> to set to <code>element</code> attribute</li><li><code>FormPane</code> Internally drawn text information that can be set to a string or used <code>json</code> to set to <code>element</code> attribute</li><li><code>HT</code> Self-band components such as <code>Button</code>, <code>CheckBox</code> and <code>ComboBox</code>, etc., can set the component object, or use <code>json</code> to set to <code>element</code> attribute</li></ul>

<p>Native objects can be added in reference to the <code>www.hightopo.com</code> hyperlink at the bottom of the control panel in <a href="examples/example_easing.html">Easing Instance</a></p>

<pre><code>var href = document.createElement(&#39;a&#39;);
href.setAttribute(&#39;href&#39;, &#39;http://www.hightopo.com&#39;);     
href.innerHTML = &#39;http://www.hightopo.com&#39;;            
href.style.font = formPane.getLabelFont();            
href.style.lineHeight = formPane.getRowHeight() + &#39;px&#39;;
href.style.textAlign = &#39;right&#39;;
formPane.addRow([href], [0.1]);</code></pre>

<p>For added elements, if a <code>string</code> string is displayed as a label text, and if you need to define additional parameters for the text, you can use the <code>json</code> format</p>

<pre><code>{
    element: &#39;Hightopo&#39;, // Text content
    color: &#39;red&#39; // Text color
    font: &#39;bold 24px arial&#39;, // Text font
    align: &#39;left&#39;, // Text align, optional value: left|center|right
    vAlign: &#39;top&#39;, // Text vertical align, optional value: top|middle|bottom
}</code></pre>

<p>The <code>json</code> format of the element supports the following predefined properties:</p>

<ul><li><code>id</code> Uniquely identifies the attribute and can be added to the corresponding <code>item</code> object through <code>formPane.getItemById(id)</code></li><li><code>borderColor</code> Border color, when the property is set, the element renders the rectangle border of the color</li><li><code>background</code> Background color, when the property is set, the element renders the rectangle background of the color</li></ul>

<p>The following predefined properties will automatically build the view component according to <code>json</code> information and be stored on the element&#39;s <code>element</code> attribute while the <code>element</code> attribute is undefined, which is also true of defining <code>ht.widget.Toolbar</code> element by using <code>json</code> information automatically build the <code>element</code> view component:</p>

<ul><li><code>textField</code> Text field, after setting this property, <code>HT</code> will automatically build the <code>ht.widget.TextField</code> object based on the property value and is stored on the <code>element</code> attribute</li><li><code>textArea</code> Text area, after setting this property, <code>HT</code> will automatically build the <code>ht.widget.TextArea</code> object based on the property value and is stored on the <code>element</code> attribute</li><li><code>button</code> Button, after setting this property, <code>HT</code> will automatically build the <code>ht.widget.Button</code> object based on the property value and is stored on the <code>element</code> attribute</li><li><code>checkBox</code> Check box, after setting this property, <code>HT</code> will automatically build the <code>ht.widget.CheckBox</code> object based on the property value and is stored on the <code>element</code> attribute</li><li><code>image</code> Image, after setting this property, <code>HT</code> will automatically build the <code>ht.widget.Image</code> object based on the property value and is stored on the <code>element</code> attribute</li><li><code>comboBox</code> Combo box, after setting this property, <code>HT</code> will automatically build the <code>ht.widget.ComboBox</code> object based on the property value and is stored on the <code>element</code> attribute</li><li><code>slider</code> Slider, after setting this property, <code>HT</code> will automatically build the <code>ht.widget.Slider</code> object based on the property value and is stored on the <code>element</code> attribute</li></ul>

<p>Example: </p>

<pre><code>{
    id: &#39;679&#39;,
    button: {
        label: &#39;Hightopo Company&#39;,
        icon: &#39;ht_logo&#39;,
        toolTip: &#39;<EMAIL>&#39;,            
        onClicked: function(){
            console.log(&#39;button is clicked.&#39;);
        }
    }
}</code></pre>

<p>The example <code>json</code> format defines an element of <code>id</code> for <code>679</code>, and <code>HT</code> automatically constructs a <code>button</code> object for the <code>ht.widget.Button</code> and passes <code>button.setLabel(&#39;Hightopo Company&#39;)</code>, <code>button.setIcon(&#39;Ht_logo&#39;)</code>, <code>button.setToolTip(&#39;<EMAIL>&#39;)</code> and <code>button.onClicked = function () {console.log(&#39;button is clicked&#39;);}</code> these steps set to initialize the <code>button</code> object, and then add an <code>element</code> attribute to the <code>json</code> format, whose value is the <code>button</code> object.</p>

<p><iframe src="examples/example_formpane.html" style="height:650px"></iframe></p>

<div id="ref_button"></div>

<h2>Button</h2>

<p>The <code>ht.widget.Button</code> is the buttons class whose main configurable properties and functions are as follows:</p>

<ul><li><code>label</code> Gets and sets label by <code>getLabel</code> and <code>setLabel</code></li><li><code>labelFont</code> Gets and sets label font by <code>getLabelFont</code> and <code>setLabelFont</code></li><li><code>labelColor</code> Gets and sets label color by <code>getLabelColor</code> and <code>setLabelColor</code></li><li><code>labelSelectColor</code> Gets and sets selected label color by <code>getLabelSelectColor</code> and <code>setLabelSelectColor</code></li><li><code>borderColor</code> Gets and sets border color by <code>getBorderColor</code> and <code>setBorderColor</code></li><li><code>background</code> Gets and sets background by <code>getBackground</code> and <code>setBackground</code></li><li><code>selectBackground</code> Gets and sets selected background by <code>getSelectBackground</code> and <code>setSelectBackground</code></li><li><code>icon</code> Gets and sets icon by <code>getIcon</code> and <code>setIcon</code></li><li><code>iconColor</code> Gets and sets icon color by <code>getIconColor</code> and <code>setIconColor</code></li><li><code>disabled</code> Gets and sets whether use the feature by <code>isDisabled</code> and <code>setDisabled</code>, the default is <code>false</code></li><li><code>toolTip</code> Gets and sets tooltip by <code>getToolTip</code> and <code>setToolTip</code>, can enable and disable the tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li><li><code>orientation</code> Gets and sets the sort of the label and icon by <code>getOrientation</code> and <code>setOrientation</code>, can set horizontal and vertical by <code>horizontal</code> and <code>vertical</code>, or the shorthand <code>h</code> and <code>v</code></li><li><code>selected</code> Gets and sets whether the button is selected by <code>isSelected</code> and <code>setSelected</code></li><li><code>pressed</code> Gets and sets whether the button is pushed by <code>isPressed</code> and <code>setPressed</code></li><li><code>clickable</code> Gets and sets whether the button is clickable by <code>isClickable</code> and <code>setClickable</code></li><li><code>togglable</code> Gets and sets whether the button is togglable by <code>isTogglable</code> and <code>setTogglable</code></li><li><code>groupId</code> Gets and sets group id by <code>getGroupId</code> and <code>setGroupId</code>, the <code>togglable</code> button of the same group has the mutex function</li></ul>

<p><iframe src="examples/example_button.html" style="height:500px"></iframe>  </p>

<div id="ref_radioButton"></div>

<h2>Click Button</h2>

<p><code>ht.widget.RadioButton</code> is a radio button class whose main configurable properties and functions are as follows:</p>

<ul><li><code>label</code> Gets and sets label by <code>getLabel</code> and <code>setLabel</code></li><li><code>labelFont</code> Gets and sets label font by <code>getLabelFont</code> and <code>setLabelFont</code></li><li><code>labelColor</code> Gets and sets label color by <code>getLabelColor</code> and <code>setLabelColor</code></li><li><code>icon</code> Gets and sets icon by <code>getIcon</code> and <code>setIcon</code></li><li><code>iconColor</code> Gets and sets icon color by <code>getIconColor</code> and <code>setIconColor</code></li><li><code>disabled</code> Gets and sets whether use the feature by <code>isDisabled</code> and <code>setDisabled</code>, the default is <code>false</code></li><li><code>toolTip</code> Gets and sets tooltip by <code>getToolTip</code> and <code>setToolTip</code>, can enable and disable the tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li><li><code>selected</code> Gets and sets whether the button is selected by <code>isSelected</code> and <code>setSelected</code></li><li><code>pressed</code> Gets and sets whether the button is pushed by <code>isPressed</code> and <code>setPressed</code></li><li><code>pressBackground</code> Gets and sets the background while pressed by <code>getPressBackground</code> and <code>setPressBackground</code></li><li><code>padding</code> Gets and sets padding by <code>getPadding</code> and <code>setPadding</code>, the default is <code>0</code></li><li><code>getRadioIcon</code> Returns <code>radioOn</code> icon while this function is selected, which is not selected then return the <code>radioOff</code> icon, can be overloaded with custom</li><li><code>groupId</code> Gets and sets group id by <code>getGroupId</code> and <code>setGroupId</code>, the <code>togglable</code> button of the same group has the mutex function</li></ul>

<p><iframe src="examples/example_radiobutton.html" style="height:200px"></iframe> </p>

<div id="ref_checkBox"></div>

<h2>Check Box</h2>

<p><code>ht.widget.CheckBox</code> is a check box class whose main configurable properties and functions are as follows:</p>

<ul><li><code>label</code> Gets and sets label by <code>getLabel</code> and <code>setLabel</code></li><li><code>labelFont</code> Gets and sets label font by <code>getLabelFont</code> and <code>setLabelFont</code></li><li><code>labelColor</code> Gets and sets label color by <code>getLabelColor</code> and <code>setLabelColor</code></li><li><code>icon</code> Gets and sets icon by <code>getIcon</code> and <code>setIcon</code></li><li><code>iconColor</code> Gets and sets icon color by <code>getIconColor</code> and <code>setIconColor</code></li><li><code>disabled</code> Gets and sets whether use the feature by <code>isDisabled</code> and <code>setDisabled</code>, the default is <code>false</code></li><li><code>toolTip</code> Gets and sets tooltip by <code>getToolTip</code> and <code>setToolTip</code>, can enable and disable the tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li><li><code>selected</code> Gets and sets whether the button is selected by <code>isSelected</code> and <code>setSelected</code></li><li><code>pressed</code> Gets and sets whether the button is pushed by <code>isPressed</code> and <code>setPressed</code></li><li><code>pressBackground</code> Gets and sets the background while pressed by <code>getPressBackground</code> and <code>setPressBackground</code></li><li><code>padding</code> Gets and sets padding by <code>getPadding</code> and <code>setPadding</code>, the default is <code>0</code></li><li><code>getRadioIcon</code> Returns <code>radioOn</code> icon while this function is selected, which is not selected then return the <code>radioOff</code> icon, can be overloaded with custom</li></ul>

<p><iframe src="examples/example_checkbox.html" style="height:200px"></iframe> </p>

<div id="ref_textField"></div>

<h2>Text Field</h2>

<p><code>ht.widget.TextField</code> is a text field class whose main configurable properties and functions are as follows:</p>

<ul><li><code>getElement()</code> Returns the internal <code>html</code> native <code>input</code> text field element, which can add the listening or modifying style</li><li><code>type</code> Gets and sets the text field type by <code>getType()</code> and <code>setType()</code>, which is set to the <code>type</code> attribute of the native <code>elemenet</code>, such as <code>number</code> type only allows to enter numbers</li><li><code>editable</code> Gets and sets whether the text field editable by <code>isEditable</code> and <code>setEditable</code>, <code>boolean</code> type, the default is <code>true</code></li><li><code>toolTip</code> Gets and sets the tooltip by <code>getToolTip</code> and <code>setToolTip</code>, enable or disable the tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li><li>Gets and sets the text value type by <code>getText</code> or <code>getValue</code>, <code>setText</code> or <code>setValue</code>, and ultimately is the <code>style.value</code> property value of the native <code>element</code>.</li><li>Gets and sets the text color by <code>getColor</code> and <code>setColor</code>, and ultimately is the <code>style.color</code> property value of the native <code>element</code>.</li><li>Gets and sets the text font by <code>getFont</code> and <code>setFont</code>, and ultimately is the <code>style.font</code> property value of the native <code>element</code>.</li><li>Gets and sets the background by <code>getBackground</code> and <code>setBackground</code>, and ultimately is the <code>style.background</code> property value of the native <code>element</code>.</li></ul>

<div id="ref_textArea"></div>

<h2>Text Area</h2>

<p><code>ht.widget.TextArea</code> is a text area class whose main configurable properties and functions are as follows:</p>

<ul><li><code>getElement()</code> Returns the internal <code>html</code> native <code>input</code> text field element, which can add the listening or modifying style</li><li><code>editable</code> Gets and sets whether the text field editable by <code>isEditable</code> and <code>setEditable</code>, <code>boolean</code> type, the default is <code>true</code></li><li><code>toolTip</code> Gets and sets the tooltip by <code>getToolTip</code> and <code>setToolTip</code>, enable or disable the tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li><li>Gets and sets the text value type by <code>getText</code> or <code>getValue</code>, <code>setText</code> or <code>setValue</code>, and ultimately is the <code>style.value</code> property value of the native <code>element</code>.</li><li>Gets and sets the text color by <code>getColor</code> and <code>setColor</code>, and ultimately is the <code>style.color</code> property value of the native <code>element</code>.</li><li>Gets and sets the text font by <code>getFont</code> and <code>setFont</code>, and ultimately is the <code>style.font</code> property value of the native <code>element</code>.</li><li>Gets and sets the background by <code>getBackground</code> and <code>setBackground</code>, and ultimately is the <code>style.background</code> property value of the native <code>element</code>.</li></ul>

<p><iframe src="examples/example_text.html" style="height:300px"></iframe> </p>

<div id="ref_image"></div>

<h2>Image</h2>

<p><code>ht.widget.Image</code> is a image class whose main configurable properties and functions are as follows:</p>

<ul><li><code>borderColor</code> Gets and sets border color by <code>getBorderColor</code> and <code>setBorderColor</code></li><li><code>background</code> Gets and sets background by <code>getBackground</code> and <code>setBackground</code></li><li><code>icon</code> Gets and sets icon by <code>getIcon</code> and <code>setIcon</code></li><li><code>iconColor</code> Gets and sets icon color by <code>getIconColor</code> and <code>setIconColor</code></li><li><code>toolTip</code> Gets and sets tooltip by <code>getToolTip</code> and <code>setToolTip</code>, enable and disable tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li><li><code>stretch</code> Gets and sets image stretch type by <code>getStretch</code> and <code>setStretch</code>, the default is <code>centerUniform</code>, optional value as follows:<ul><li><code>fill</code> Image fills the entire rectangular area, and if the picture&#39;s width and height ratio and the rectangle is inconsistent, it will cause the picture to stretch and distort</li><li><code>uniform</code> Image always keep the original width and height ratio unchanged, and fill the rectangular area as far as possible</li><li><code>centerUniform</code> When the rectangular area is larger than the picture size, the picture is drawn in the center position in the original size, and the <code>uniform</code> is used when the space is not enough.</li></ul></li></ul>

<p><iframe src="examples/example_image.html" style="height:260px"></iframe> </p>

<div id="ref_slider"></div>

<h2>Slider</h2>

<p><code>ht.widget.Slider</code> is a slider class whose main configurable properties and functions are as follows:</p>

<ul><li><code>value</code> Gets and sets the current value by <code>getValue</code> and <code>setValue</code>, number type, the default is <code>50</code></li><li><code>min</code> Gets and sets minimum by <code>getMin</code> and <code>setMin</code>, , number type, the default is <code>0</code></li><li><code>max</code> Gets and sets maximum by <code>getMax</code> and <code>setMax</code>, number type, the default is <code>100</code></li><li><code>step</code> Gets and sets the step by <code>getStep</code> and <code>setStep</code>, number type, the default is null represent continuous</li><li><code>button</code> Gets and sets slider button by <code>getButton</code> and <code>setButton</code></li><li><code>thickness</code> Gets and sets slider thickness by <code>getThickness</code> and <code>setThickness</code>, number type, the default is <code>3</code></li><li><code>padding</code> Gets and sets slider padding by <code>getPadding</code> and <code>setPadding</code>, number type, the default is <code>4</code></li><li><code>background</code> Gets and sets slider background by <code>getBackground</code> and <code>setBackground</code>, string type</li><li><code>leftBackground</code> Gets and sets slider left background by <code>getLeftBackground</code> and <code>setLeftBackground</code>, string type</li><li><code>disabled</code> Gets and sets whether enable the feature by <code>isDisabled</code> and <code>setDisabled</code>, the default is <code>false</code></li><li><code>toolTip</code> Gets and sets tooltip by <code>getToolTip</code> and <code>setToolTip</code>, enable and disable tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li><li><code>instant</code> Property to obtain an immediate state by <code>isInstant</code> and <code>setInstant</code>, the default is <code>true</code>, to represent real-time changes to model values as an editor of the table and property pages</li></ul>

<p><iframe src="examples/example_slider.html" style="height:300px"></iframe>    </p>

<div id="ref_combobox"></div>

<h2>Combo Box</h2>

<p><code>ht.widget.ComboBox</code> is a combo box class whose main configurable properties and functions are as follows:</p>

<ul><li><code>value</code> Gets and sets the current value by <code>getValue</code> and <code>setValue</code>, can be any type</li><li><code>values</code> Gets and sets drop-down optional value by <code>getValues</code> and <code>setValues</code>, <code>Array</code> type, with the array length consistent with <code>value</code></li><li><code>labels</code> Gets and sets the corresponding text for the drop-down optional value by <code>getLabels</code> and <code>setLabels</code>, <code>Array</code> type, with the array length consistent with <code>values</code></li><li><code>icons</code> Gets and sets the corresponding icon for the drop-down optional value by <code>getIcons</code> and <code>setIcons</code>, <code>Array</code> type, with the array length consistent with <code>values</code></li><li><code>background</code> Gets and sets background by <code>getBackground</code> and <code>setBackground</code>, string type</li><li><code>selectBackground</code> Gets and sets selected background by <code>getSelectBackground</code> and <code>setSelectBackground</code>, string type</li><li><code>indent</code> Gets and sets icon indent by <code>getIndent</code> and <code>setIndent</code>, number type</li><li><code>editable</code> Gets and sets whether the combo box is editable by <code>isEditable</code> and <code>setEditable</code>, <code>boolean</code> type, the default is <code>false</code></li><li><code>selectIcon</code> Gets and sets the drop-down selected icon by <code>getSelectIcon</code> and <code>setSelectIcon</code></li><li><code>dropDownIcon</code> Gets and sets the drop-down arrow icon by <code>getDropDownIcon</code> and <code>setDropDownIcon</code></li><li><code>dropDownWidth</code> Gets and sets the width of drop-down by <code>getDropDownWidth</code> and <code>setDropDownWidth</code>, the default is the width of combo box</li><li><code>disabled</code> Gets and sets whether enable the feature by <code>isDisabled</code> and <code>setDisabled</code>, the default is <code>false</code></li><li><code>toolTip</code> Gets and sets tooltip by <code>getToolTip</code> and <code>setToolTip</code>, enable and disable tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li></ul>

<p><iframe src="examples/example_combobox.html" style="height:360px"></iframe></p>

<div id="ref_multicombobox"></div>

<h2>Multi-Combo Box</h2>

<p><code>ht.widget.MultiComboBox</code> is a multi-combo box class whose main configurable properties and functions are as follows:</p>

<ul><li><code>value</code> Gets and sets the current value by <code>getValue</code> and <code>setValue</code></li><li><code>background</code> Gets and sets background by <code>getBackground</code> and <code>setBackground</code>, string type</li><li><code>indent</code> Gets and sets icon indent by <code>getIndent</code> and <code>setIndent</code>, number type</li><li><code>editable</code> Gets and sets whether the combo box is editable by <code>isEditable</code> and <code>setEditable</code>, <code>boolean</code> type, the default is <code>false</code></li><li><code>dropDownIcon</code> Gets and sets the drop-down arrow icon by <code>getDropDownIcon</code> and <code>setDropDownIcon</code></li><li><code>dropDownWidth</code> Gets and sets the width of drop-down by <code>getDropDownWidth</code> and <code>setDropDownWidth</code>, the default is the width of combo box</li><li><code>disabled</code> Gets and sets whether enable the feature by <code>isDisabled</code> and <code>setDisabled</code>, the default is <code>false</code></li><li><code>toolTip</code> Gets and sets tooltip by <code>getToolTip</code> and <code>setToolTip</code>, enable and disable tooltip by <code>enableToolTip()</code> and <code>disableToolTip()</code></li><li><code>dropDownComponent</code> Sets the drop-down component class by <code>setDropDownComponent</code> and <code>getDropDownComponent</code>, inherit from <code>ht.widget.BaseDropDownTemplate</code></li><li><code>open()</code> Open the drop-down</li><li><code>close()</code> Close the drop-down</li></ul>

<p>Template combo box and normal combo box are similar, however, it is a developer customization, so the drop-down box can be placed in any component (<code>HT</code> component or normal <code>DOM</code> element), to customize the content of the drop-down box, you need to customize a class inheritance <code>ht.widget.BaseDropDownTemplate</code> and overloads several methods:</p>

<ul><li><code>getView()</code> Overload this method returns the <code>DOM</code> object to be displayed in the drop-down box</li><li><code>onOpened(value)</code> This method is call backed when the drop-down is open, overloading this method to display the <code>value</code> parameter in the drop-down box</li><li><code>onClosed()</code> This method is call backed when the drop-down is close, overloading this method to do some cleanup work</li><li><code>getValue()</code> This method is call backed when the drop-down is close, overloading this method returns the value of the drop-down </li><li><code>getWidth()</code> Overloads this method returns the width of the drop-down, and if this method is not overloaded, the width of the drop-down and the width of <code>MultiComboBox</code> remain consistent</li><li><code>getHeight()</code> Overloads this method returns the height of the drop-down</li></ul>

<p><iframe src="examples/example_multicombobox.html" style="height:250px"></iframe></p>

<div id="ref_colorpicker"></div>

<h2>Color Picker</h2>

<p><code>ht.widget.ColorPicker</code> is the color selection box, inherited from <code>MultiComboBox</code>, which adds two additional attributes, except for the property and function inherited from <code>MultiComboBox</code>:</p>

<ul><li><code>instant</code> Gets and sets immediate state by <code>isInstant</code> and <code>setInstant</code>, the default is <code>true</code>, to represent real-time changes to model values as an editor of the table and property pages</li><li><code>clearButtonVisible</code> Gets and sets whether the clear button is visible by <code>isClearButtonVisible</code> and <code>setClearButtonVisible</code>, the default is <code>true</code></li></ul>

<p><iframe src="examples/example_colorpicker.html" style="height:400px"></iframe></p>    <hr id="contact" style="margin: 20px 0 1px 0">
    <a href="http://www.hightopo.com"><img class="logo"></a>Welcome to contact us <a href="mailto:<EMAIL>"><EMAIL></a>
</body>
</html>
