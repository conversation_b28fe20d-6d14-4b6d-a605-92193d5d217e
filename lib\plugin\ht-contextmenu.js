!function(D,A){"use strict";function K(){return document}function _(O,B){return O.querySelectorAll(B)}function f(O){var B=K().createElement(O);return"ul"===O&&(k(B,d,"relative"),k(B,P,p),k(B,H,p),k(B,"list-style",J),k(B,"box-sizing","border-box"),k(B,"-moz-box-sizing","border-box"),k(B,L,"inline-block"),k(B,"vertical-align","text-bottom"),k(B,"border","1px solid "+X.contextMenuBorderColor),k(B,"box-shadow","0 0 16px 1px "+X.contextMenuShadowColor),k(B,"overflow","hidden"),X.contextMenuBorderRadius&&k(B,"border-radius",X.contextMenuBorderRadius+V)),B}function Q(O){var B=O.touches[0];return B||<PERSON>.changedTouches[0]}function e(){return f("div")}function o(){return f("canvas")}function k(O,B,U){O.style.setProperty(B,U,b)}function y(O,B){O.appendChild(B)}function E(O){var B=this,U=O._view,O=(B.$11b=O,B.addListeners(),a(U,"contextmenu",function(O){O.preventDefault()}),B.$37b=B.$36b.bind(B));a(U,"mouseover",O),a(U,"mouseout",O)}var r=D.ht,d="position",S="absolute",V="px",j="left",Z="top",t="innerHTML",N="className",W="width",q="height",p="0",L="display",J="none",g="visibility",C="user-select",P="margin",H="padding",b=null,O=r.Color,X=r.Default,$=X.getInternal(),m=D.setTimeout,I=D.setInterval,l=D.clearTimeout,G=D.clearInterval,z=D.parseInt,R=X.isLeftButton,h=X.isDragging,T=X.startDragging,F=X.getDistance,n=X.isTouchable,s=X.isTouchEvent,i=X.getPagePoint,c=X.isRightButton,w=O.widgetIconHighlight,B=O.widgetIconBorder,O=O.widgetIconGradient,a=$.addEventListener,U=$.removeEventListener;$.addMethod(X,{contextMenuCheckIcon:{width:16,height:16,comps:[{type:"border",rect:[1,1,14,14],width:1,color:B},{type:"shape",points:[13,3,7,12,4,8],borderWidth:2,borderColor:w}]},contextMenuRadioIcon:{width:16,height:16,comps:[{type:"circle",rect:[2,2,12,12],borderWidth:1,borderColor:B},{type:"circle",rect:[4,4,8,8],borderWidth:1,borderColor:w,gradient:X.imageGradient,gradientColor:O,background:w}]},contextMenuLabelFont:(n?"16":"13")+"px arial, sans-serif",contextMenuLabelColor:"#000",contextMenuBackground:"#fff",contextMenuDisabledLabelColor:"#888",contextMenuHoverBackground:"#648BFE",contextMenuHoverLabelColor:"#fff",contextMenuSeparatorWidth:1,contextMenuSeparatorColor:"#E5E5E5",contextMenuScrollerColor1:"#FDFDFD",contextMenuScrollerColor2:"#D3D3D3",contextMenuScrollerBorderColor:"#C3C3C3",contextMenuBorderColor:"#C3C3C3",contextMenuShadowColor:"rgba(128, 128, 128, 0.5)",contextMenuBorderRadius:5,contextMenuSubmenuMark:"▶"},!0);X.def(E,A,{ms_listener:1,getView:function(){return this.$11b._view},handle_touchstart:function(O){if(X.preventDefault(O),R(O)){for(var B=this,U=B.$11b,C=B.getView().children,J=0;J<C.length;J++){var D=C[J],z=D.$24b,A=D.$25b;if(z&&z.contains(O.target))return U.scrollUp(D),B.$28b=m(function(){B.$29b=I(function(){U.scrollUp(D)},100)},500),void T(B,O);if(A&&A.contains(O.target))return U.scrollDown(D),B.$28b=m(function(){B.$29b=I(function(){U.scrollDown(D)},100)},500),void T(B,O)}B.$30b=i(O)}},handle_mousedown:function(O){this.handle_touchstart(O)},handle_touchend:function(O){if(R(O)){var B=this,U=B.$30b,C=s(O)?{x:Q(O).pageX,y:Q(O).pageY}:{x:O.pageX,y:O.pageY};if(!U||1<F(U,C))delete B.$30b;else{for(var U=B.getView(),C=B.$11b,J=O.target,D=b,z=b,A=C._items,m=U.$26b,T=0;T<m.length;T++)if((z=m[T]).contains(J)){D=z.getAttribute("data-id");break}D&&A&&((U=C.$17b(A,function(O){return O._id===D}))&&(A=!1,U.disabled instanceof Function?A=U.disabled.call(C,U):!0===U.disabled&&(A=!0),A||(U.items?s(O)&&B.$36b(z,!0):C.$1b(U,O)))),delete B.$30b}}},$36b:function(O,B){if(!h()){var U,C=this.$11b,J=this.getView(),D=C.$20b||J.children[0],z=C._currentMenuLi,A=J.$26b,m=J.children,T=O.target,$=J.getBoundingClientRect(),f=X.getWindowInfo(),r=f.width,q=f.height,K=function(O){for(var B=0;B<m.length;B++){var U=m[B],C=new RegExp(O+"$"),J=U[N];if(J&&(C.test(J)||0<=J.indexOf(O+" ")))return U}},_=function(O){for(var B=0;B<A.length;B++){var U=A[B],C=new RegExp(O+"$"),J=U[N];if(J&&(C.test(J)||0<=J.indexOf(O+" ")))return U}};if(B)U=O;else{if("mouseover"===O.type){for(var Q=0;Q<A.length;Q++){var e=A[Q];if(e.contains(T)){U=e;break}}!U&&z&&(f=z.parentNode,((B=K("submenu"+z.getAttribute("data-id")))&&B.contains(T)||f&&f.contains(T))&&(U=z))}else if("mouseout"===O.type){for(var y=!1,d=O.relatedTarget,Q=0;Q<m.length;Q++){var S=m[Q];if("hidden"!==S.style.visibility&&S.contains(d)){y=!0;break}}if(y)return}!U&&D&&(U=_("menu-item"+(D.$45b||"NaN")))}if(U!=z){if(z)for(var t=z;t;){t[N]=t[N].replace(" menu-item-hover",""),t[N].indexOf("disabled")<0&&(null!=(W=C.getItemByProperty("_id",t.getAttribute("data-id"))).background?W.background instanceof Function?k(t,"background-color",W.background.call(C,W)):k(t,"background-color",W.background):k(t,"background-color",X.contextMenuBackground),k(t,"color",X.contextMenuLabelColor));var W=K("submenu"+t.getAttribute("data-id")),p=(W&&k(W,g,"hidden"),t.parentNode),t=_("menu-item"+(p.$45b||"NaN"))}if(U){for(var L=U,b=[];L;){L[N]+=" menu-item-hover",L[N].indexOf("disabled")<0&&(k(L,"background-color",X.contextMenuHoverBackground),k(L,"color",X.contextMenuHoverLabelColor));var R=K("submenu"+L.getAttribute("data-id")),p=(R&&(k(R,g,"visible"),b.push(R)),L.parentNode),L=_("menu-item"+(p.$45b||"NaN"))}b.reverse(),b.forEach(function(O){var B,U,C,J,D,z;B=_("menu-item"+(O=O).$45b).getBoundingClientRect(),U=B.top-$.top,C=B.left-$.left,k(O,Z,U+V),k(O,j,C+B.width+V),B=O.getBoundingClientRect(),J=B.top,D=B.left,z=B.height,B=B.width,J=J+z+2,z=D+B+2,q<J&&k(O,Z,U+q-J+V),r<z&&k(O,j,C-B+V)})}}C.setCurrentMenuItem(U),C.$20b=U?U.parentNode:J.children[0]}},handle_mouseup:function(O){this.handle_touchend(O)},handleWindowTouchEnd:function(O){var B=this;B.$28b!=b&&(l(B.$28b),delete B.$28b),B.$29b!=b&&(G(B.$29b),delete B.$29b),delete B.$34b,delete B.$30b,delete B.$35b},handleWindowMouseUp:function(O){this.handleWindowTouchEnd(O)},handle_mousemove:function(O){this.handle_touchmove(O)},handle_touchmove:function(O){if(!h()&&R(O)){for(var B=this,U=B.getView().children,C=b,J=0;J<U.length;J++){var D=U[J];if(D.contains(O.target)){C=D;break}}var z=B.$30b,A=s(O)?{x:Q(O).pageX,y:Q(O).pageY}:{x:O.pageX,y:O.pageY};C&&z&&2<F(z,A)&&(T(B,O),B.$34b=C,B.$35b=C.$18b)}},handleWindowTouchMove:function(O){O.preventDefault();var B=this.$11b,U=this.$34b,C=this.$35b,J=this.$30b;J&&U&&(0<(O=(s(O)?{x:Q(O).pageX,y:Q(O).pageY}:{x:O.pageX,y:O.pageY}).y-J.y)?B.scrollUp(U,U.$18b-(C-O)):B.scrollDown(U,C-O-U.$18b))},handleWindowMouseMove:function(O){this.handleWindowTouchMove(O)},$10b:function(O,B){O.preventDefault();for(var U,C,J=this.getView().children,D=b,z=0;z<J.length;z++){var A=J[z];if(A.contains(O.target)){D=A;break}}D&&(C=(U=this.$11b).getRowHeight(),.05<Math.abs(B)&&(0<B?U.scrollUp(D,B*C):B<0&&U.scrollDown(D,-B*C)))},handle_mousewheel:function(O){this.$10b(O,O.wheelDelta/40)},handle_DOMMouseScroll:function(O){this.$10b(O,-O.detail)},$44b:function(O){this.getView().contains(O.target)||this.$11b.hide()},$41b:function(O){X.preventDefault(O)},$4b:function(O,B){var U,C,J=this.$11b;(B=B||J._items)&&B.length&&O.keyCode&&(C=[O.keyCode],O.shiftKey&&C.push(16),O.ctrlKey&&C.push(17),O.altKey&&C.push(18),/mac/.test(D.navigator?D.navigator.userAgent.toLowerCase():"")?O.metaKey&&C.push(17):O.metaKey&&C.push(91),C.sort(),U=C.join(),(C=J.$17b(B,function(O){if(O.key)return(O=O.key.slice(0)).sort(),U===O.join()}))&&(!1!==C.preventDefault&&O.preventDefault(),B=!1,C.disabled instanceof Function?B=C.disabled.call(J,C):!0===C.disabled&&(B=!0),B||J.$1b(C,O)))},$39b:function(O){this.$32b=i(O)},$38b:function(O){var B;X.preventDefault(O),R(O)||((B=this)._showContextMenu=c(O),B._showContextMenu||(B.$31b=i(O),B.$33b=m(function(){B._showContextMenu=!0,delete B.$33b},600)))},$40b:function(O){var B=this;B._showContextMenu&&(c(O)||B.$31b&&(!B.$32b||F(B.$31b,B.$32b)<10))&&B.$11b.show(O),B.$33b!=b&&(l(B.$33b),delete B.$33b),delete B.$31b,delete B.$32b}}),r.widget.ContextMenu=function(O){var B=this,U=B._view=$.createView(null,B);U[N]="ht-widget-contextmenu",B.setItems(O),B.$13b=new E(B),k(U,"font",X.contextMenuLabelFont),k(U,d,S),k(U,"cursor","default"),k(U,"-webkit-"+C,J),k(U,"-moz-"+C,J),k(U,"-ms-"+C,J),k(U,C,J),k(U,"box-sizing","border-box"),k(U,"-moz-box-sizing","border-box"),X.baseZIndex!=b&&k(U,"z-index",z(X.baseZIndex)+2+""),B.$3b=function(O){B.$13b.$4b(O)}},O={$16b:b,$5b:0,_items:b,$21b:b,$19b:b,_enableGlobalKey:!(B="ContextMenu"),ms_v:1,ms_ac:["currentMenuItem"],enableGlobalKey:function(){!1===this._enableGlobalKey&&(a(K(),"keydown",this.$3b),this._enableGlobalKey=!0)},disableGlobalKey:function(){this._enableGlobalKey=!1,U(K(),"keydown",this.$3b)},setItems:function(O){this._items=O},getItems:function(){return this._items},getVisibleFunc:function(){return this.$16b},setVisibleFunc:function(O){this.$16b=O},setLabelMaxWidth:function(O){this.$43b=O},$1b:function(O,B){var U;"check"===O.type?O.selected=!O.selected:"radio"===O.type&&(U=O.groupId,this.$17b(this._items,function(O){O.groupId===U&&(O.selected=!1)}),O.selected=!0),this.hide(),O.action?O.action.apply(O.scope||this,[O,B]):O.href&&(B=O.linkTarget||"_self",D.open(O.href,B))},getItemById:function(O){return this.getItemByProperty("id",O)},setItemVisible:function(O,B){O=this.getItemById(O);O&&(O.visible=B)},getItemByProperty:function(B,U,O){return(O=O||this._items)&&0!==O.length&&this.$17b(O,function(O){return O[B]===U})||b},scrollUp:function(O,B){var U;0!==(B=z(B=B==b?20:B))&&(U=0,O.$18b>B&&(U=O.$18b-B),this.$42b(O,U),O.scrollTop=U,O.$18b=U)},scrollDown:function(O,B){var U,C,J;0!==(B=z(B=B==b?20:B))&&(J=(U=O.$22b)-(C=O.$23b),C+O.$18b+B<U&&(J=O.$18b+B),this.$42b(O,J),O.scrollTop=J,O.$18b=J)},$42b:function(O,B){B=B==b?O.$18b:B;var U=O.$24b,C=O.$25b;U&&(k(U,"top",B+V),k(U,L,0==B?J:"block")),C&&(k(C,"bottom",-B+V),B==O.$22b-O.$23b?k(C,L,J):k(C,L,"block"))},getRowHeight:function(){return this._view.querySelector(".menu-item").offsetHeight},$17b:function(O,B){for(var U=0;U<O.length;U++){var C=O[U];if(B(C))return C;if(C.items){C=this.$17b(C.items,B);if(C)return C}}},$2b:function(O,B){for(var U=this,C=0;C<O.length;C++){U.$5b++;var J,D,z,A,m,T,$=O[C];!1===$.visible?this._clearItemId($):X.isFunction($.visible)&&!1===$.visible()||U.$16b&&!U.$16b.call(U,$)?this._clearItemId($):(J=f("li"),D=U.$5b+"",k(J,"white-space","nowrap"),k(J,L,"block"),"separator"===$||!0===$.separator?((z=e())[N]="separator",k(z,q,X.contextMenuSeparatorWidth+V),k(z,"background",X.contextMenuSeparatorColor),y(J,z)):($._id=D,J.setAttribute("data-id",D),z=f("span"),A=f("span"),T=o(),m=e(),k(z,L,"inline-block"),k(z,q,"1.2em"),k(A,L,"inline-block"),k(A,q,"1.2em"),k(A,"line-height","1.2em"),T[N]="prefix",k(T,L,"inline-block"),k(T,W,"1em"),k(T,q,"1em"),k(T,"vertical-align","middle"),k(T,P,"0 0.2em"),"check"===$.type&&$.selected?T[N]+=" check-prefix":"radio"===$.type&&$.selected&&(T[N]+=" radio-prefix"),T._item=$,y(J,T),$.icon&&((T=o())[N]="contextmenu-item-icon",k(T,L,"inline-block"),k(T,q,"1.2em"),k(T,W,"1.2em"),k(T,"margin-right","0.2em"),k(T,"float","left"),T.$50b=$.icon,T._item=$,y(z,T)),A[t]=$.label,y(z,A),y(J,z),m[N]="suffix",k(m,L,"inline-block"),k(m,"margin-left","1em"),k(m,"margin-right","0.4em"),k(m,"text-align","right"),k(m,"font-size","75%"),$.items&&(m[t]=X.contextMenuSubmenuMark),$.suffix&&(m[t]=$.suffix),y(J,m),J[N]="menu-item menu-item"+D,null!=$.background?$.background instanceof Function?k(J,"background-color",$.background.call(U,$)):k(J,"background-color",$.background):k(J,"background-color",X.contextMenuBackground),k(J,"color",X.contextMenuLabelColor),k(J,H,"3px 0"),$.disabled instanceof Function?$.disabled.call(U,$)&&(J[N]+=" disabled",k(J,"color",X.contextMenuDisabledLabelColor)):$.disabled&&(J[N]+=" disabled",k(J,"color",X.contextMenuDisabledLabelColor)),$.items&&(U.$21b||(U.$21b=new r.List),(T=f("ul"))[N]="submenu"+D,T.$45b=D,k(T,g,"hidden"),k(T,d,S),y(U._view,T),U.$21b.add(T),U.$2b($.items,T))),y(B,J))}},setCurrentMenuItem:function(O){if(this._currentMenuLi=O,this.$19b=b,this.iv(),O){var B=this._items;if(B&&0!==B.length)for(var U=O.getAttribute("data-id"),C=0,J=B.length;C<J;C++)if(U===(O=B[C])._id)return void(this.$19b=O)}},rebuild:function(){var O,B=this,U=B._items,C=B._view;C&&(C[t]="",B.$21b=b,B.$5b=0,B._currentMenuLi=b,B.$19b=b,B.$20b=b,C.$26b=b,U&&0!==U.length&&(O=f("ul",B._r),y(C,O),B.$2b(U,O)))},addTo:function(O){var B,U,C,J,D;O&&(U=(B=this).$13b,B.$12b=O,B.$9b=function(O){U.$44b(O)},C=B.$6b=function(O){U.$38b(O)},J=B.$7b=function(O){U.$39b(O)},D=B.$8b=function(O){U.$40b(O)},X.mockTouch&&(a(O,"touchstart",C,!0),a(O,"touchmove",J),a(O,"touchend",D)),a(O,"mousedown",C,!0),a(O,"mousemove",J),a(O,"mouseup",D),B.$27b=function(O){U.$41b(O)},a(O,"contextmenu",B.$27b))},showOnView:function(O,B,U){O=O.getView?O.getView():O;var C=X.getWindowInfo(),O=O.getBoundingClientRect();this.show(O.left+C.left+B,O.top+C.top+U)},show:function(O,B,U){var C,J,D,z,A,m,T,$,f,r=this,U=U==b,q=r._view;q&&(r.invalidate(),1===arguments.length&&(B=s(T=O)?(O=(D=Q(T)).pageX,D.pageY):(O=T.pageX,T.pageY)),C=(D=X.getWindowInfo()).width,J=D.height,f=(D={pageX:O,pageY:B,clientX:O-(O=D.left),clientY:B-(B=D.top),target:1,originEvent:T}).clientX,$=D.clientY,z=function(O){O.style.height=J-6+V;function B(O){k(O,d,S),k(O,"text-align","center"),k(O,W,"100%"),k(O,"font-size",10+V),k(O,"padding","2px 0"),k(O,"border","0px solid "+X.contextMenuScrollerBorderColor),k(O,"background-color",X.contextMenuScrollerColor1),O.style.backgroundImage="-webkit-linear-gradient(top, "+X.contextMenuScrollerColor1+", "+X.contextMenuScrollerColor2+")",O.style.backgroundImage="linear-gradient(to bottom, "+X.contextMenuScrollerColor1+", "+X.contextMenuScrollerColor2+")"}var U=e(),C=e();U[N]="menu-arrow-item menu-arrow-item-top",C[N]="menu-arrow-item menu-arrow-item-bottom",B(U),k(U,"top",p),k(U,"left",p),k(U,"border-bottom-width",1+V),U[t]="▲",B(C),k(C,"bottom",p),k(C,"left",p),k(C,"border-top-width",1+V),C[t]="▼",O.$24b=U,O.$25b=C,O.$18b=O.scrollTop,O.$22b=O.scrollHeight,O.$23b=O.clientHeight,y(O,U),y(O,C),r.$42b(O)},r.beforeShow&&r.beforeShow(D),(A=r._items)&&(T&&T.preventDefault(),A.length&&(r.rebuild(),(q.$26b=_(q,".menu-item")).length&&(X.appendToScreen(q),(T=q.children[0]).offsetHeight>J&&z(T),A=$+(U?1:0),U=f+(U?1:0),(m=function(O){for(var B,U,C,J,D,z,A=0,m=0,T=0,$=r.$43b;T<O.children.length;T++)(C=O.children[T]).getAttribute("data-id")&&(J=C.children[1],D=C.children[2],B=J.children,$&&(U=B[0],(U=1<B.length?B[1]:U).offsetWidth>$&&(U[t]="<marquee scrollamount='3'>"+U[t]+"</marquee>",U.children[0].style.verticalAlign="text-bottom",k(U,W,$+V),k(U,L,"inline-block"))),A<(B=J.offsetWidth)&&(A=B),m<(U=D.offsetWidth)&&(m=U));for(T=0;T<O.children.length;T++)(C=O.children[T]).getAttribute("data-id")&&(J=C.children[1],D=C.children[2],z=J.children[0],!J.children[1]&&z.style.width&&k(z,W,A+V),k(J,W,A+V),k(D,W,m+V))})(T),T=$+3+q.offsetHeight,$=f+3+q.offsetWidth,k(q,Z,J<T?A-(T-J)+B+V:A+B+V),k(q,j,C<$?U-($-C)+O+V:U+O+V),(f=r.$21b)&&f.each(function(O){m(O),O.offsetHeight>J&&z(O)}),r.$9b&&(X.mockTouch&&a(K(),"touchstart",r.$9b,!0),a(K(),"mousedown",r.$9b,!0)),r.afterShow&&r.afterShow(D),r.$47b()))))},isShowing:function(){return!!this._view&&this._view.parentNode!=b},getRelatedView:function(){return this.$12b},hide:function(){var O=this,B=O._view;B&&B.parentNode&&(B.parentNode.removeChild(B),X.mockTouch&&U(K(),"touchstart",O.$9b,!0),U(K(),"mousedown",O.$9b,!0),O.afterHide&&O.afterHide())},dispose:function(){var O=this,B=O.$12b;O._view&&(this.hide(),O.disableGlobalKey(),B&&(X.mockTouch&&(U(B,"touchstart",O.$6b,!0),U(B,"touchmove",O.$7b),U(B,"touchend",O.$8b)),U(B,"mousedown",O.$6b,!0),U(B,"mousemove",O.$7b),U(B,"mouseup",O.$8b),U(B,"contextmenu",O.$27b)),O._view=O._items=O.$21b=O._currentMenuLi=O.$19b=O.$12b=O.beforeShow=O.afterShow=O.afterHide=O.$9b=O.$3b=O.$6b=O.$7b=O.$8b=O.$27b=b)},$46b:function(O,B,U,C,J){O=$.initContext(O);$.translateAndScale(O,0,0,1),O.clearRect(0,0,U,C),X.drawStretchImage(O,X.getImage(B),"fill",0,0,U,C,J,this),O.restore()},$47b:function(){var O=this._view;if(this.isShowing()){for(var B=_(O,".check-prefix"),U=0;U<B.length;U++){var C=B[U],J=C.clientWidth,D=C.clientHeight;C.$48b=J,C.$49b=D,$.setCanvas(C,J,D)}var z=_(O,".radio-prefix");for(U=0;U<z.length;U++){var A=z[U];J=A.clientWidth,D=A.clientHeight,A.$48b=J,A.$49b=D,$.setCanvas(A,J,D)}var m=_(O,".contextmenu-item-icon");for(U=0;U<m.length;U++){var T=m[U];J=T.clientWidth,D=T.clientHeight,T.$48b=J,T.$49b=D,$.setCanvas(T,J,D)}}},validateImpl:function(){var O=this,B=O._view;if(O.isShowing()){for(var U=_(B,".check-prefix"),C=0;C<U.length;C++){var J=U[C],D=J.$48b,z=J.$49b;D&&z&&O.$46b(J,X.contextMenuCheckIcon,D,z,J._item)}var A=_(B,".radio-prefix");for(C=0;C<A.length;C++){var m=A[C];D=m.$48b,z=m.$49b,D&&z&&O.$46b(m,X.contextMenuRadioIcon,D,z,m._item)}var T=_(B,".contextmenu-item-icon");for(C=0;C<T.length;C++){var $=T[C];D=$.$48b,z=$.$49b,D&&z&&O.$46b($,X.getImage($.$50b),D,z,$._item)}}},_clearItemId:function(O){var B=this;delete O._id,O.items&&O.items.forEach(function(O){B._clearItemId(O)})}},X.def(r.widget[B],A,O)}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);