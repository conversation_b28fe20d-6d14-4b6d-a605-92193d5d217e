!function(B,O){"use strict";function L(B,L,D){P("ht."+B,L,D)}function J(B,L,D){D?B.push(L.x,L.y):B.push(L.x,L.y,L.z)}function a(X,B,L){var D,d=X.data.getAttaches();if(d&&d.each(function(B){B instanceof M&&B.s("attach.operation")&&(D=D||[]).push(B)}),D){t.forEach(function(B){B=function(B,L,D,d){var m,T,r,Q,u=0,O=[];if(d)for(m=d.length;u<m;u+=3)T=d[u],r=d[u+1],Q=d[u+2],K(O,new I([L[3*T],L[3*T+1],L[3*T+2]],null,D?[D[2*T],D[2*T+1],0]:Z),new I([L[3*r],L[3*r+1],L[3*r+2]],null,D?[D[2*r],D[2*r+1],0]:Z),new I([L[3*Q],L[3*Q+1],L[3*Q+2]],null,D?[D[2*Q],D[2*Q+1],0]:Z),B);else for(m=L.length/3;u<m;u+=3)r=(T=u)+1,Q=u+2,K(O,new I([L[3*T],L[3*T+1],L[3*T+2]],null,D?[D[2*T],D[2*T+1],0]:Z),new I([L[3*r],L[3*r+1],L[3*r+2]],null,D?[D[2*r],D[2*r+1],0]:Z),new I([L[3*Q],L[3*Q+1],L[3*Q+2]],null,D?[D[2*Q],D[2*Q+1],0]:Z),B);return O}(B,X[B].vs,X[B].tuv);m=m?m.concat(B):B}),m=k.$15n(m),D.forEach(function(B){var L=B.s("attach.operation");m[L]&&(m=m[L](function(B,L,D){var d,m=w(B),T=[],r=(B.appendAnchorMatrix3d(m),L?L.getData3dUI(B):null);if(r&&r.s("shape3d")&&!r.s("csg.cull.box")&&(d=r.shapeModel,d=r.shapeModel?d:y.getShapeModel(r,r.s("shape3d"))))for(var Q=y.createNormalMatrix(m),u=0,O=uf.length;u<O;u++){var X=(h=uf[u]).length?h+"_":h,n=d[X+"vs"],J=d[X+"uv"],v=d[X+"is"],R=d[X+"ns"];if(n&&n.length)for(var l,G,f,_=0,E=v?v.length:n.length/3;_<E;_+=3)f=v?(l=v[_],G=v[_+1],v[_+2]):(G=(l=_)+1,_+2),K(T,new I(s([n[3*l],n[3*l+1],n[3*l+2]],m),R?s([R[3*l],R[3*l+1],R[3*l+2]],Q):null,[J[2*l],J[2*l+1],0]),new I(s([n[3*G],n[3*G+1],n[3*G+2]],m),R?s([R[3*G],R[3*G+1],R[3*G+2]],Q):null,[J[2*G],J[2*G+1],0]),new I(s([n[3*f],n[3*f+1],n[3*f+2]],m),R?s([R[3*f],R[3*f+1],R[3*f+2]],Q):null,[J[2*f],J[2*f+1],0]),B)}if(!d)for(u=0;u<6;u++)for(var h=t[u],j=Xf[u],J=D?L.getFaceUv(B,h):Z,g=D?L.getFaceUvScale(B,h):Z,z=D?L.getFaceUvOffset(B,h):Z,M=0;M<2;M++){var Y,a,A,$=V[j+3*M],U=V[j+3*M+1],W=V[j+3*M+2];D&&(A=J?(Y=[J[2*$-(A=8*u)],J[2*$+1-A],0],a=[J[2*U-A],J[2*U+1-A],0],[J[2*W-A],J[2*W+1-A],0]):(Y=[S[2*$],S[2*$+1],0],a=[S[2*U],S[2*U+1],0],[S[2*W],S[2*W+1],0]),g&&(Y[0]*=g[0],Y[1]*=g[1],a[0]*=g[0],a[1]*=g[1],A[0]*=g[0],A[1]*=g[1]),z&&(Y[0]+=z[0],Y[1]+=z[1],a[0]+=z[0],a[1]+=z[1],A[0]+=z[0],A[1]+=z[1])),K(T,new I(s([q[3*$],q[3*$+1],q[3*$+2]],m),null,Y),new I(s([q[3*U],q[3*U+1],q[3*U+2]],m),null,a),new I(s([q[3*W],q[3*W+1],q[3*W+2]],m),null,A),B)}return k.$15n(T)}(B,X.gv,X.csg.tuv)))}),t.forEach(function(B){(B=X[B]).vs=[],B.ns=[],B.tuv&&(B.tuv=[])}),X.csg&&!X.csg.ns&&(X.csg.ns=[]);var m,n=[];if(m.$19n().forEach(function(B){var L,D=B.$10n;if(D instanceof M){if(D.s("attach.cull"))return;L=(L=D.s("csg.cull.color"))&&y.toColorArray(L),D="csg"}for(var D=X[D],d=D.vs,m=D.ns,T=D.tuv,r=B.$9n,Q=2;Q<r.length;Q++)if(J(d,r[0].$24n),J(d,r[Q-1].$24n),J(d,r[Q].$24n),m&&(J(m,r[0].$22n),J(m,r[Q-1].$22n),J(m,r[Q].$22n)),T&&(J(T,r[0].uv,!0),J(T,r[Q-1].uv,!0),J(T,r[Q].uv,!0)),L)for(var u=d.length/3*4,O=0;O<3;O++)n[--u]=L[3],n[--u]=L[2],n[--u]=L[1],n[--u]=L[0]}),n.length){for(var T=y.toColorArray(X.csg.color),r=0,Q=X.csg.vs.length/3*4;r<Q;r+=4)n[r]==O&&(n[r]=T[0],n[r+1]=T[1],n[r+2]=T[2],n[r+3]=T[3]);X.csg.cs=n}}Of.forEach(function(B){var L=X[B];L.visible&&L.vs.length?(L.ns&&L.ns.length?v(L,"ns"):L.ns=H(L.vs),v(L,"vs"),v(L,"tuv"),L.cs&&v(L,"cs")):delete X[B]}),B&&(b(X,Z,B,Z,L),X.clear())}function d(B,L){this.$22n=B,this.w=L}function A(B,L,D,d,J){var m=[],T=[],r=[],Q=new E;if((B=B.getData3dUI(L)).mat){var u,X=(new h).fromArray(B.mat),L=new h,n=(L.makeRotationFromEuler(d),L.setPosition(D),(new h).getInverse(L)),O=(new h).scale({x:1/J.x,y:1/J.y,z:1/J.z}),v=[],R=new E,l=new E;for(B.eachShapeModel(function(B,L,D,d,m){for(var T,r,Q,u=0,O=L?L.length:B.length/3;u<O;u++)L?(R.fromArray(B,3*L[u]),l.fromArray(D,3*L[u])):(R.fromArray(B,3*u),l.fromArray(D,3*u)),m&&(R.applyMatrix4(m),l.transformDirection(m)),T=v,Q=l,(r=R).applyMatrix4(X),r.applyMatrix4(n),Q.transformDirection(X),Q.transformDirection(n),T.push(new ff(r.clone(),Q.clone()))}),v=f(v,Q.set(1,0,0)),v=f(v,Q.set(-1,0,0)),v=f(v,Q.set(0,1,0)),v=f(v,Q.set(0,-1,0)),v=f(v,Q.set(0,0,1)),v=f(v,Q.set(0,0,-1)),u=0;u<v.length;u++){var G=v[u];r.push(.5+G.position.x/J.x,.5-G.position.y/J.y),G.position.applyMatrix4(O),m.push(G.position.x,G.position.y,G.position.z),T.push(G.$22n.x,G.$22n.y,G.$22n.z)}return{vs:m,ns:T,uv:r}}function f(B,L){for(var D=[],d=.5*Math.abs(J.dot(L)),m=0;m<B.length;m+=3){var T,r,Q,u,O=0<B[m+0].position.dot(L)-d,X=0<B[m+1].position.dot(L)-d,n=0<B[m+2].position.dot(L)-d;switch((O?1:0)+(X?1:0)+(n?1:0)){case 0:D.push(B[m]),D.push(B[m+1]),D.push(B[m+2]);break;case 1:if(O&&(T=B[m+1],r=B[m+2],Q=_(B[m],T,L,d),u=_(B[m],r,L,d)),X){T=B[m],r=B[m+2],Q=_(B[m+1],T,L,d),u=_(B[m+1],r,L,d),D.push(Q),D.push(r.clone()),D.push(T.clone()),D.push(r.clone()),D.push(Q.clone()),D.push(u);break}n&&(T=B[m],r=B[m+1],Q=_(B[m+2],T,L,d),u=_(B[m+2],r,L,d)),D.push(T.clone()),D.push(r.clone()),D.push(Q),D.push(u),D.push(Q.clone()),D.push(r.clone());break;case 2:O||(r=_(T=B[m].clone(),B[m+1],L,d),Q=_(T,B[m+2],L,d),D.push(T),D.push(r),D.push(Q)),X||(r=_(T=B[m+1].clone(),B[m+2],L,d),Q=_(T,B[m],L,d),D.push(T),D.push(r),D.push(Q)),n||(r=_(T=B[m+2].clone(),B[m],L,d),Q=_(T,B[m+1],L,d),D.push(T),D.push(r),D.push(Q))}}return D}function _(B,L,D,d){var m=B.position.dot(D)-d,m=m/(m-(L.position.dot(D)-d));return new ff(new E(B.position.x+m*(L.position.x-B.position.x),B.position.y+m*(L.position.y-B.position.y),B.position.z+m*(L.position.z-B.position.z)),new E(B.$22n.x+m*(L.$22n.x-B.$22n.x),B.$22n.y+m*(L.$22n.y-B.$22n.y),B.$22n.z+m*(L.$22n.z-B.$22n.z)))}}var r,Q,u,X=B.ht,Z=null,B=Math,G=B.PI,$=B.cos,f=B.sin,U=B.abs,W=B.max,i=B.sqrt,x=1e-5,T=X.Default,P=T.def,e=T.startAnim,F=T.createMatrix,s=T.transformVec,y=T.getInternal(),D=y.addMethod,m=y.superCall,N=y.toPointsArray,H=y.createNormals,v=y.toFloatArray,C=y.glMV,B=(y.glPop,X.graph3d.Graph3dView,X.Math),E=B.Vector3,h=B.Matrix4,c=B.Euler,b=(B.Quaternion,y.batchShape),w=y.createNodeMatrix,o=y.getFaceInfo,p=y.transformAppend,Bf=y.drawFaceInfo,Lf=y.createAnim,B=y.cube(),V=B.is,q=B.vs,S=B.uv,B=y.ui(),Df=X.Node,df=X.Shape,mf="left",Tf="right",rf="top",Qf="bottom",n="dw.expanded",R=".expanded",l="dw.angle",_=".angle",K=(r=new X.Math.Vector3,Q=new X.Math.Vector3,u=new X.Math.Vector3,function(B,L,D,d,m){var T;r.copy(L.$24n),Q.copy(D.$24n),u.copy(d.$24n),u.equals(r)||Q.equals(r)||Q.equals(u)||(L.$22n&&D.$22n&&d.$22n||(T=Q.sub(r).cross(u.sub(r)).normalize().multiplyScalar(1).toArray(),L.$22n||(L.$22n=new j(T)),D.$22n||(D.$22n=new j(T)),d.$22n||(d.$22n=new j(T))),B.push(new g([L,D,d],m)))}),t=[mf,"front",Tf,"back",rf,Qf],uf=["",rf,Qf],Of=t.concat("csg"),Xf=[0,6,12,18,24,30],k=(D(T,{createFrameModel:function(B,L,D,d){B=B==Z?.07:B,L=L==Z?B:L,D=D==Z?B:D;var m=(d=d||{}).top,T=d.bottom,r=d.left,Q=d.right,u=d.front,d=d.back,O=[],X=[];return!0===u?(O.push(-.5,.5,.5,-.5,-.5,.5,.5,-.5,.5,.5,-.5,.5,.5,.5,.5,-.5,.5,.5),X.push(0,0,0,1,1,1,1,1,1,0,0,0)):!1!==u&&(O.push(-.5,.5,.5,-.5,-.5,.5,-.5+B,-.5,.5,-.5+B,-.5,.5,-.5+B,.5,.5,-.5,.5,.5,.5-B,.5,.5,.5-B,-.5,.5,.5,-.5,.5,.5,-.5,.5,.5,.5,.5,.5-B,.5,.5,-.5+B,.5,.5,-.5+B,.5-L,.5,.5-B,.5-L,.5,.5-B,.5-L,.5,.5-B,.5,.5,-.5+B,.5,.5,-.5+B,-.5+L,.5,-.5+B,-.5,.5,.5-B,-.5,.5,.5-B,-.5,.5,.5-B,-.5+L,.5,-.5+B,-.5+L,.5),X.push(0,0,0,1,B,1,B,1,B,0,0,0,1-B,0,1-B,1,1,1,1,1,1,0,1-B,0,B,0,B,L,1-B,L,1-B,L,1-B,0,B,0,B,1-L,B,1,1-B,1,1-B,1,1-B,1-L,B,1-L)),!0===d?(O.push(-.5,.5,-.5,.5,-.5,-.5,-.5,-.5,-.5,.5,-.5,-.5,-.5,.5,-.5,.5,.5,-.5),X.push(1,0,0,1,1,1,0,1,1,0,0,0)):!1!==d&&(O.push(-.5,.5,-.5,-.5+B,-.5,-.5,-.5,-.5,-.5,-.5+B,-.5,-.5,-.5,.5,-.5,-.5+B,.5,-.5,.5-B,.5,-.5,.5,-.5,-.5,.5-B,-.5,-.5,.5,-.5,-.5,.5-B,.5,-.5,.5,.5,-.5,-.5+B,.5,-.5,.5-B,.5-L,-.5,-.5+B,.5-L,-.5,.5-B,.5-L,-.5,-.5+B,.5,-.5,.5-B,.5,-.5,-.5+B,-.5+L,-.5,.5-B,-.5,-.5,-.5+B,-.5,-.5,.5-B,-.5,-.5,-.5+B,-.5+L,-.5,.5-B,-.5+L,-.5),X.push(1,0,1-B,1,1,1,1-B,1,1,0,1-B,0,B,0,0,1,B,1,0,1,B,0,0,0,1-B,0,B,L,1-B,L,B,L,1-B,0,B,0,1-B,1-L,B,1,1-B,1,B,1,1-B,1-L,B,1-L)),!0===r?(O.push(-.5,.5,-.5,-.5,-.5,-.5,-.5,-.5,.5,-.5,-.5,.5,-.5,.5,.5,-.5,.5,-.5),X.push(0,0,0,1,1,1,1,1,1,0,0,0)):!1!==r&&(O.push(-.5,.5,-.5,-.5,-.5,-.5,-.5,-.5,-.5+D,-.5,-.5,-.5+D,-.5,.5,-.5+D,-.5,.5,-.5,-.5,.5,.5-D,-.5,-.5,.5-D,-.5,-.5,.5,-.5,-.5,.5,-.5,.5,.5,-.5,.5,.5-D,-.5,.5,-.5+D,-.5,.5-L,-.5+D,-.5,.5-L,.5-D,-.5,.5-L,.5-D,-.5,.5,.5-D,-.5,.5,-.5+D,-.5,-.5+L,-.5+D,-.5,-.5,-.5+D,-.5,-.5,.5-D,-.5,-.5,.5-D,-.5,-.5+L,.5-D,-.5,-.5+L,-.5+D),X.push(0,0,0,1,D,1,D,1,D,0,0,0,1-D,0,1-D,1,1,1,1,1,1,0,1-D,0,D,0,D,L,1-D,L,1-D,L,1-D,0,D,0,D,1-L,D,1,1-D,1,1-D,1,1-D,1-L,D,1-L)),!0===Q?(O.push(.5,.5,-.5,.5,-.5,.5,.5,-.5,-.5,.5,-.5,.5,.5,.5,-.5,.5,.5,.5),X.push(1,0,0,1,1,1,0,1,1,0,0,0)):!1!==Q&&(O.push(.5,.5,-.5,.5,-.5,-.5+D,.5,-.5,-.5,.5,-.5,-.5+D,.5,.5,-.5,.5,.5,-.5+D,.5,.5,.5-D,.5,-.5,.5,.5,-.5,.5-D,.5,-.5,.5,.5,.5,.5-D,.5,.5,.5,.5,.5,-.5+D,.5,.5-L,.5-D,.5,.5-L,-.5+D,.5,.5-L,.5-D,.5,.5,-.5+D,.5,.5,.5-D,.5,-.5+L,-.5+D,.5,-.5,.5-D,.5,-.5,-.5+D,.5,-.5,.5-D,.5,-.5+L,-.5+D,.5,-.5+L,.5-D),X.push(1,0,1-D,1,1,1,1-D,1,1,0,1-D,0,D,0,0,1,D,1,0,1,D,0,0,0,1-D,0,D,L,1-D,L,D,L,1-D,0,D,0,1-D,1-L,D,1,1-D,1,D,1,1-D,1-L,D,1-L)),!0===m?(O.push(.5,.5,.5,.5,.5,-.5,-.5,.5,-.5,-.5,.5,-.5,-.5,.5,.5,.5,.5,.5),X.push(1,1,1,0,0,0,0,0,0,1,1,1)):!1!==m&&(O.push(.5,.5,.5,.5,.5,-.5,.5-B,.5,-.5,.5-B,.5,-.5,.5-B,.5,.5,.5,.5,.5,-.5+B,.5,.5,-.5+B,.5,-.5,-.5,.5,-.5,-.5,.5,-.5,-.5,.5,.5,-.5+B,.5,.5,.5-B,.5,.5,.5-B,.5,.5-D,-.5+B,.5,.5-D,-.5+B,.5,.5-D,-.5+B,.5,.5,.5-B,.5,.5,.5-B,.5,-.5+D,.5-B,.5,-.5,-.5+B,.5,-.5,-.5+B,.5,-.5,-.5+B,.5,-.5+D,.5-B,.5,-.5+D),X.push(1,1,1,0,1-B,0,1-B,0,1-B,1,1,1,B,1,B,0,0,0,0,0,0,1,B,1,1-B,1,1-B,1-D,B,1-D,B,1-D,B,1,1-B,1,1-B,D,1-B,0,B,0,B,0,B,D,1-B,D)),!0===T?(O.push(.5,-.5,.5,-.5,-.5,-.5,.5,-.5,-.5,-.5,-.5,-.5,.5,-.5,.5,-.5,-.5,.5),X.push(1,0,0,1,1,1,0,1,1,0,0,0)):!1!==T&&(O.push(.5,-.5,.5,.5-B,-.5,-.5,.5,-.5,-.5,.5-B,-.5,-.5,.5,-.5,.5,.5-B,-.5,.5,-.5+B,-.5,.5,-.5,-.5,-.5,-.5+B,-.5,-.5,-.5,-.5,-.5,-.5+B,-.5,.5,-.5,-.5,.5,.5-B,-.5,.5,-.5+B,-.5,.5-D,.5-B,-.5,.5-D,-.5+B,-.5,.5-D,.5-B,-.5,.5,-.5+B,-.5,.5,.5-B,-.5,-.5+D,-.5+B,-.5,-.5,.5-B,-.5,-.5,-.5+B,-.5,-.5,.5-B,-.5,-.5+D,-.5+B,-.5,-.5+D),X.push(1,0,1-B,1,1,1,1-B,1,1,0,1-B,0,B,0,0,1,B,1,0,1,B,0,0,0,1-B,0,B,D,1-B,D,B,D,1-B,0,B,0,1-B,1-D,B,1,1-B,1,B,1,1-B,1-D,B,1-D)),{vs:O,uv:X}}}),D(X.Style,{"csg.cull.color":O,"csg.cull.box":!0,"dw.flip":!1,"dw.s3":[.999,.999,.5],"dw.t3":O,"dw.expanded":!1,"dw.toggleable":!0,"dw.axis":"left","dw.start":0,"dw.end":G/2,"dw.angle":0,"attach.cull":!1,"attach.operation":"subtract"},!0),t.forEach(function(B){var L={};L[B+R]=!1,L[B+".toggleable"]=!1,L[B+".axis"]=mf,L[B+".start"]=0,L[B+".end"]=G/2,L[B+_]=0,D(X.Style,L,!0)}),function(){this.$4n=[]}),j=(k.$15n=function(B){var L=new k;return L.$4n=B,L},k.prototype={clone:function(){var B=new k;return B.$4n=this.$4n.map(function(B){return B.clone()}),B},$19n:function(){return this.$4n},union:function(B){var L=new z(this.clone().$4n),B=new z(B.clone().$4n);return L.$3n(B),B.$3n(L),B.$6n(),B.$3n(L),B.$6n(),L.$7n(B.$2n()),k.$15n(L.$2n())},subtract:function(B){var L=new z(this.clone().$4n),B=new z(B.clone().$4n);return L.$6n(),L.$3n(B),B.$3n(L),B.$6n(),B.$3n(L),B.$6n(),L.$7n(B.$2n()),L.$6n(),k.$15n(L.$2n())},intersect:function(B){var L=new z(this.clone().$4n),B=new z(B.clone().$4n);return L.$6n(),B.$3n(L),B.$6n(),L.$3n(B),B.$3n(L),L.$7n(B.$2n()),L.$6n(),k.$15n(L.$2n())},inverse:function(){var B=this.clone();return B.$4n.map(function(B){B.flip()}),B}},k.cube=function(B){var D=new j((B=B||{}).center||[0,0,0]),d=B.radius?B.radius.length?B.radius:[B.radius,B.radius,B.radius]:[1,1,1];return k.$15n([[[0,4,6,2],[-1,0,0]],[[1,3,7,5],[1,0,0]],[[0,1,5,4],[0,-1,0]],[[2,6,7,3],[0,1,0]],[[0,2,3,1],[0,0,-1]],[[4,5,7,6],[0,0,1]]].map(function(L){return new g(L[0].map(function(B){B=new j(D.x+d[0]*(2*!!(1&B)-1),D.y+d[1]*(2*!!(2&B)-1),D.z+d[2]*(2*!!(4&B)-1));return new I(B,new j(L[1]))}))}))},k.sphere=function(B){var D,d=new j((B=B||{}).center||[0,0,0]),m=B.radius||1,L=B.slices||16,T=B.stacks||8,r=[];function Q(B,L){L*=G;B=new j($(B*=2*G)*f(L),$(L),f(B)*f(L));D.push(new I(d.$20n(B.$21n(m)),B))}for(var u=0;u<L;u++)for(var O=0;O<T;O++)D=[],Q(u/L,O/T),0<O&&Q((u+1)/L,O/T),O<T-1&&Q((u+1)/L,(O+1)/T),Q(u/L,(O+1)/T),r.push(new g(D));return k.$15n(r)},k.cylinder=function(B){var d=new j((B=B||{}).start||[0,-1,0]),L=new j(B.end||[0,1,0]),m=L.$13n(d),T=B.radius||1,D=B.slices||16,r=m.$14n(),B=.5<U(r.y),Q=new j(B,!B,0).cross(r).$14n(),u=Q.cross(r).$14n(),O=new I(d,r.$11n()),X=new I(L,r.$14n()),n=[];function J(B,L,D){L=L*G*2,L=Q.$21n($(L)).$20n(u.$21n(f(L))),B=d.$20n(m.$21n(B)).$20n(L.$21n(T)),L=L.$21n(1-U(D)).$20n(r.$21n(D));return new I(B,L)}for(var v=0;v<D;v++){var R=v/D,l=(v+1)/D;n.push(new g([O,J(0,R,-1),J(0,l,-1)])),n.push(new g([J(0,l,0),J(0,R,0),J(1,R,0),J(1,l,0)])),n.push(new g([X,J(1,l,1),J(1,R,1)]))}return k.$15n(n)},function(B,L,D){var d=this;3==arguments.length?(d.x=B,d.y=L,d.z=D):"x"in B?(d.x=B.x,d.y=B.y,d.z=B.z):(d.x=B[0],d.y=B[1],d.z=B[2])}),I=(j.prototype={clone:function(){return new j(this.x,this.y,this.z)},$11n:function(){return new j(-this.x,-this.y,-this.z)},$20n:function(B){return new j(this.x+B.x,this.y+B.y,this.z+B.z)},$13n:function(B){return new j(this.x-B.x,this.y-B.y,this.z-B.z)},$21n:function(B){return new j(this.x*B,this.y*B,this.z*B)},$16n:function(B){return new j(this.x/B,this.y/B,this.z/B)},dot:function(B){return this.x*B.x+this.y*B.y+this.z*B.z},lerp:function(B,L){return this.$20n(B.$13n(this).$21n(L))},length:function(){return i(this.dot(this))},$14n:function(){return this.$16n(this.length())},cross:function(B){var L=this;return new j(L.y*B.z-L.z*B.y,L.z*B.x-L.x*B.z,L.x*B.y-L.y*B.x)}},function(B,L,D){this.$24n=new j(B),this.$22n=L?new j(L):null,this.uv=D?new j(D):null}),g=(I.prototype={clone:function(){return new I(this.$24n.clone(),this.$22n.clone(),this.uv?this.uv.clone():null)},flip:function(){this.$22n=this.$22n.$11n()},$18n:function(B,L){return new I(this.$24n.lerp(B.$24n,L),this.$22n.lerp(B.$22n,L),this.uv?this.uv.lerp(B.uv,L):null)}},d.$17n=function(B,L,D){L=L.$13n(B).cross(D.$13n(B)).$14n();return new d(L,L.dot(B))},d.prototype={clone:function(){return new d(this.$22n.clone(),this.w)},flip:function(){this.$22n=this.$22n.$11n(),this.w=-this.w},$5n:function(B,L,D,d,m){for(var T=this,r=0,Q=[],u=0;u<B.$9n.length;u++){var O=(J=T.$22n.dot(B.$9n[u].$24n)-T.w)<-x?2:x<J?1:0;r|=O,Q.push(O)}switch(r){case 0:(0<T.$22n.dot(B.$8n.$22n)?L:D).push(B);break;case 1:d.push(B);break;case 2:m.push(B);break;case 3:for(var X=[],n=[],u=0;u<B.$9n.length;u++){var J,v=(u+1)%B.$9n.length,R=Q[u],l=Q[v],G=B.$9n[u],v=B.$9n[v];2!=R&&X.push(G),1!=R&&n.push(2!=R?G.clone():G),3==(R|l)&&(J=(T.w-this.$22n.dot(G.$24n))/T.$22n.dot(v.$24n.$13n(G.$24n)),R=G.$18n(v,J),X.push(R),n.push(R.clone()))}3<=X.length&&d.push(new g(X,B.$10n)),3<=n.length&&m.push(new g(n,B.$10n))}}},function(B,L){this.$9n=B,this.$10n=L,this.$8n=d.$17n(B[0].$24n,B[1].$24n,B[2].$24n)}),z=(g.prototype={clone:function(){var B=this.$9n.map(function(B){return B.clone()});return new g(B,this.$10n)},flip:function(){this.$9n.reverse().map(function(B){B.flip()}),this.$8n.flip()}},function(B){var L=this;L.$8n=null,L.front=null,L.back=null,L.$4n=[],B&&L.$7n(B)}),nf=(z.prototype={clone:function(){var B=this,L=new z;return L.$8n=B.$8n&&B.$8n.clone(),L.front=B.front&&B.front.clone(),L.back=B.back&&B.back.clone(),L.$4n=B.$4n.map(function(B){return B.clone()}),L},$6n:function(){for(var B=this,L=0;L<B.$4n.length;L++)B.$4n[L].flip();B.$8n&&B.$8n.flip(),B.front&&B.front.$6n(),B.back&&B.back.$6n();var D=B.front;B.front=B.back,B.back=D},$1n:function(B){var L=this;if(!L.$8n)return B.slice();for(var D=[],d=[],m=0;m<B.length;m++)L.$8n.$5n(B[m],D,d,D,d);return L.front&&(D=L.front.$1n(D)),d=L.back?L.back.$1n(d):[],D.concat(d)},$3n:function(B){var L=this;L.$4n=B.$1n(L.$4n),L.front&&L.front.$3n(B),L.back&&L.back.$3n(B)},$2n:function(){var B=this,L=B.$4n.slice();return B.front&&(L=L.concat(B.front.$2n())),L=B.back?L.concat(B.back.$2n()):L},$7n:function(B){var L=this;if(B.length){L.$8n||(L.$8n=B[0].$8n.clone());for(var D=[],d=[],m=0;m<B.length;m++)this.$8n.$5n(B[m],L.$4n,L.$4n,D,d);D.length&&(L.front||(L.front=new z),this.front.$7n(D)),d.length&&(L.back||(L.back=new z),L.back.$7n(d))}}},X.Symbol=function(B,L,D){m(nf,this),this.s3(20,20,20),this.s({"all.visible":!1,shape:"rect"}),this.setIcon(B,L,D)}),M=(L("Symbol",Df,{setIcon:function(B,L,D){var d,m=this;return nf.superClass.setIcon.call(m,B),B?(d={for3d:!0,face:"center",position:44,names:[B]},D&&(d.transaprent=!0),L&&(d.autorotate=L),m.addStyleIcon("symbol",d)):m.removeStyleIcon("symbol"),m.setWidth(y.getImageWidth(T.getImage(B),m)||20),d}}),X.CSGNode=function(){m(M,this),this.s({shape:"rect","attach.thickness":1.001,"2d.attachable":!0})}),Jf={position:1,width:1,height:1,rotation:1,rotationX:1,rotationZ:1,rotationMode:1,tall:1,elevation:1,anchor:1,anchorElevation:1,"s:attach.cull":1,"s:attach.operation":1},Y=(L("CSGNode",Df,{get3dUIClass:function(){return Y},onPropertyChanged:function(B){var L=this.getHost();M.superClass.onPropertyChanged.call(this,B),Jf[B.property]&&(L instanceof vf||L instanceof M)&&L.fp("csgNodeChange",!0,!1)}}),function(B,L){m(Y,this,[B,L])}),vf=(P(Y,B.Node3dUI,{drawBody:function(L,D,d){var m=this;m._shape3d?Y.superClass.drawBody.call(m,L,D,d):(C(m.gv),Of.forEach(function(B){Bf(m,L,D,B,d)}))},validate:function(B,L){var D=this,d=D.gv,m=D.data;if(m.s("shape3d"))return Y.superClass.validate.call(D,B,L),void(D._shape3d=!0);D._shape3d=!1;var T=w(m,d.getMat(m)),r=(m.appendAnchorMatrix3d(T),B&&B.uv);D.vf2("csg",r);for(var Q=0;Q<6;Q++)for(var u=t[Q],O=Xf[Q],u=D.vf2(u,r,L),X=u.mat||T,n=u.vs,J=u.uv,v=u.tuv,R=0;R<2;R++){var l,G=V[O+3*R],f=V[O+3*R+1],_=V[O+3*R+2];p(n,X,[q[3*G],q[3*G+1],q[3*G+2]]),p(n,X,[q[3*f],q[3*f+1],q[3*f+2]]),p(n,X,[q[3*_],q[3*_+1],q[3*_+2]]),v&&(J?v.push(J[2*G-(l=8*Q)],J[2*G+1-l],J[2*f-l],J[2*f+1-l],J[2*_-l],J[2*_+1-l]):v.push(S[2*G],S[2*G+1],S[2*f],S[2*f+1],S[2*_],S[2*_+1]))}a(D,B,L)},vf2:function(B,L,D){var d=this.gv.getFaceVisible(this.data,B),B=o(this,B,D);return B.vs=[],B.tuv=d&&(B.texture||L)?[]:Z,B.visible=d,B}}),X.CSGShape=function(){var B=this;m(vf,B),B.s({"shape.background":Z,"2d.hostable":!0,"shape.border.width":8}),B.setTall(240),B.setElevation(120),B.setThickness(14)}),Rf=(L("CSGShape",df,{IRotatable:!1,get3dUIClass:function(){return Rf},setRotationX:function(B){},setRotation:function(B){},setRotationZ:function(B){},setSegments:function(B){}}),function(B,L){m(Rf,this,[B,L])}),lf=(P(Rf,B.Shape3dUI,{drawBody:function(L,D,d){var m=this;m.undrawable||(C(m.gv),Of.forEach(function(B){Bf(m,L,D,B,d)}))},validate:function(L,D){var B,d,m,T,r=this,Q=r.data,u=Q.getPoints();(r.undrawable=u.size()<2)?r.clear():(B=Q.isClosePath(),d=W(Q._thickness/2,x),u=N(u,Z,Z,B),Of.forEach(function(B){r.vf(B,L&&L.uv,!0,D)}),B&&(r.left.visible=!1,r.right.visible=!1),r._12O(u,d),r._20Q(u),m=Q.getPointsMatrix3d(),T=X.Math.requestVector3(),t.forEach(function(B){if(r[B])for(var L=r[B].vs,D=0,d=L.length;D<d;D+=3)T.set(L[D],L[D+1],L[D+2]).applyMatrix4(m),L[D]=T.x,L[D+1]=T.y,L[D+2]=T.z}),X.Math.releaseVector3(T),a(r,L,D))}}),X.DoorWindow=function(){m(lf,this),this.setElevation(100),this.s3(100,200,14)}),Gf=(L("DoorWindow",M,{IDoorWindow:!0,toggle:function(B){this.setExpanded(!this.s(n),B)},isExpanded:function(){return this.s(n)},setExpanded:function(B,L){var D,d=this,m=d.$25n,T=d.s(n);m&&(m.stop(!0),delete d.$25n),T!==B&&(d.beginTransaction(),D=B?d.s("dw.end"):d.s("dw.start"),d.s(n,B),(L=Lf(L))?(T=d.s(l),L.action=function(B){d.s(l,T+(D-T)*B)},L.finishFunc=function(){d.endTransaction()},d.$25n=e(L)):(d.s(l,D),d.endTransaction()))},getMat:function(){var B,L,D=this,d=D.s("dw.s3"),m=D.s("dw.t3"),T=D.s("dw.flip"),r=D.s(l);return d||r||m||T?(B=[],T&&B.push({r3:[0,G,0]}),d&&B.push({s3:d}),r&&(d=D.getFinalScale3d(),T=D.s("dw.axis"),D=d[0]/2,L=d[1]/2,d[2],T===mf?B.push({t3:[D,0,0]},{r3:[0,-r,0]},{t3:[-D,0,0]}):T===Tf?B.push({t3:[-D,0,0]},{r3:[0,r,0]},{t3:[D,0,0]}):T===rf?B.push({t3:[0,-L,0]},{r3:[-r,0,0]},{t3:[0,L,0]}):T===Qf?B.push({t3:[0,L,0]},{r3:[r,0,0]},{t3:[0,-L,0]}):"v"===T?B.push({r3:[0,r,0]}):"h"===T&&B.push({r3:[r,0,0]})),m&&B.push({t3:m}),F(B)):Z}}),X.CSGBox=function(){m(Gf,this),this.setElevation(100),this.s3(100,200,100)});L("CSGBox",M,{ICSGBox:!0,toggleFace:function(B,L){this.setFaceExpanded(B,!this.s(B+R),L)},isFaceExpanded:function(B){return this.s(B+R)},setFaceExpanded:function(L,B,D){var d,m=this,T=m.$25n,r=m.s(L+R);T&&(T.stop(!0),delete m.$25n),r!==B&&(d=B?m.s(L+".end"):m.s(L+".start"),m.s(L+R,B),(D=Lf(D))?(r=m.s(L+_),D.action=function(B){m.s(L+_,r+(d-r)*B)},m.$25n=e(D)):m.s(L+_,d))},getFaceMat:function(B){var L=this.s(B+_);if(!L)return Z;var D,d,m,T,r,Q,u=this.s(B+".axis"),O=this.getFinalScale3d(),X=this.getAnchor3d(),n=O[0]*X.x,J=O[1]*X.y,v=O[2]*X.z,R=O[0]-n,l=O[1]-J,G=O[2]-v;switch(B+"_"+u){case"front_left":D=-n,m=G,r=-L,Q=T=d=0;break;case"front_right":D=R,m=G,r=L,Q=T=d=0;break;case"front_top":d=l,m=G,T=-L,Q=r=D=0;break;case"front_bottom":d=-J,m=G,T=L,Q=r=D=0;break;case"front_h":m=G,T=L,Q=r=d=D=0;break;case"front_v":m=G,r=L,Q=T=d=D=0;break;case"back_left":D=R,m=-v,r=-L,Q=T=d=0;break;case"back_right":D=-n,m=-v,r=L,Q=T=d=0;break;case"back_top":d=l,m=-v,T=L,Q=r=D=0;break;case"back_bottom":d=-J,m=-v,T=-L,Q=r=D=0;break;case"back_h":m=-v,T=L,Q=r=d=D=0;break;case"back_v":m=-v,r=L,Q=T=d=D=0;break;case"left_left":D=-n,m=-v,r=-L,Q=T=d=0;break;case"left_right":D=-n,m=G,r=L,Q=T=d=0;break;case"left_top":D=-n,d=l,r=T=m=0,Q=-L;break;case"left_bottom":D=-n,d=-J,r=T=m=0,Q=L;break;case"left_h":D=-n,r=T=m=d=0,Q=L;break;case"left_v":D=-n,r=L,Q=T=m=d=0;break;case"right_left":D=R,m=G,r=-L,Q=T=d=0;break;case"right_right":D=R,m=-v,r=L,Q=T=d=0;break;case"right_top":D=R,d=l,r=T=m=0,Q=L;break;case"right_bottom":D=R,d=-J,r=T=m=0,Q=-L;break;case"right_h":D=R,r=T=m=d=0,Q=L;break;case"right_v":D=R,r=L,Q=T=m=d=0;break;case"top_left":D=-n,d=l,r=T=m=0,Q=L;break;case"top_right":D=R,d=l,r=T=m=0,Q=-L;break;case"top_top":d=l,m=-v,T=-L,Q=r=D=0;break;case"top_bottom":d=l,m=G,T=L,Q=r=D=0;break;case"top_h":d=l,T=L,Q=r=m=D=0;break;case"top_v":d=l,r=T=m=D=0,Q=L;break;case"bottom_left":D=-n,d=-J,r=T=m=0,Q=-L;break;case"bottom_right":D=R,d=-J,r=T=m=0,Q=L;break;case"bottom_top":d=-J,m=G,T=-L,Q=r=D=0;break;case"bottom_bottom":d=-J,m=-v,T=L,Q=r=D=0;break;case"bottom_h":d=-J,T=L,Q=r=m=D=0;break;case"bottom_v":d=-J,r=T=m=D=0,Q=L}return F([{t3:[-D,-d,-m]},{r3:[T,r,Q]},{t3:[D,d,m]}])}});function ff(B,L){this.position=B,this.$22n=L}ff.prototype.clone=function(){return new ff(this.position.clone(),this.$22n.clone())};D(X.graph3d.Graph3dView,{createDecal:function(B,L,D,d){var m=this,T=m.getDataAt(B);if(T){var r,Q,u,B=m.intersectObject(B,T);if(B)return L="object"==typeof L?L.x?new E(L.x,L.y,L.z):new E(L[0],L[1],L[2]):new E(L=L||20,L,L),D=D===O?.1:D,u=new E(m.getUp()),r=(Q=new E(m.getCenter()).sub(new E(m.getEye())).normalize()).clone().cross(u).normalize(),u.copy(r).cross(Q).normalize(),d&&u.applyAxisAngle(B.worldNormal,-d),r=new X.Node,Q=B.worldNormal.clone().setLength(D).add(B.world),d=(new h).lookAt(B.worldNormal.clone(),new E(0,0,0),u),D=(new c).setFromRotationMatrix(d),u=A(m,T,B.world.clone(),D,L),r.p3(Q.x,Q.y,Q.z),r.setEuler(D),r.s3(L.x,L.y,L.z),r.s({shape3d:u,"shape3d.reverse.flip":!0}),r}}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),void Object);