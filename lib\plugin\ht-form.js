!function(F,B,x){"use strict";function o(B,n){return B.querySelector(n)}function O(B,n){return 0<=(n=s.getLogicalPoint(n,B._canvas||B._view)).x&&0<=n.y&&n.x<=B.getWidth()&&n.y<=B.getHeight()}function h(B,n){var O=this;(O._view=q.createView(null,O)).appendChild(O._element=$(B,n)),O.setHeight(W),O.setWidth(80),O.iv()}function D(){var B=this,n=B._view=q.createView(null,B);B.setHeight(W),<PERSON>.setWidth(80),B._canvas=Q(n),new xj(B),B.iv()}var X,n="ht.widget.",y=F.ht,e=y.Color,N=y.widget,r=e.widgetBorder,z=e.widgetBackground,i=e.widgetIconBorder,A=e.transparent,d=e.background,V=e.highlight,s=y.Default,u=s.def,W=s.widgetRowHeight,H=s.removeHTML,U=s.drawText,w=s.getTextSize,M=s.isLeftButton,J=s.getWindowInfo,_=s.getImage,Y=s.drawCenterImage,E=s.preventDefault,T=s.getLogicalPoint,Z=s.labelFont,P=s.labelColor,l=s.labelSelectColor,p=s.widgetIndent,I=s.startDragging,$=s.createElement,f=s.getClientPoint,a=s.scrollBarInteractiveSize,g=s.isArray,q=s.getInternal(),v=q.addEventListener,t=q.removeEventListener,K=(q.createView,q.createDiv),Q=q.createCanvas,Bj=q.createImage,S=q.setCanvas,c=q.initContext,m=q.translateAndScale,C=q.isString,b=q.drawBorder,nj=q.getImageWidth,Oj=q.getImageHeight,G=q.fillRect,R=q.layout,ej=q.addMethod,rj=q.isH,uj=q.createGradientByPackedData,k=s.isTouchable,Wj=s.isTouchEvent,j=Math.round,Hj="readonly",L=k?"touchstart":"mousedown",Mj={x:0,y:0,width:0,height:0},Zj=(ej(s,{textFieldFont:Z,textFieldColor:P,textFieldBorderColor:r,textAreaFont:Z,textAreaColor:P,textAreaBorderColor:r,radioButtonLabelFont:Z,radioButtonLabelColor:P,radioButtonPressBackground:r,checkBoxLabelFont:Z,checkBoxLabelColor:P,checkBoxPressBackground:r,buttonLabelFont:Z,buttonLabelColor:P,buttonLabelSelectColor:l,buttonBackground:z,buttonBorderColor:r,buttonSelectBackground:V,sliderBackground:e.widgetIconBackground,sliderLeftBackground:e.widgetIconHighlight,sliderThickness:3,sliderPadding:4,sliderButton:Bj(14,14,{type:"circle",rect:[0,0,14,14],borderWidth:1,borderColor:i,gradient:"linear.northeast",gradientColor:e.widgetIconGradient,background:z}),comboBoxMaxHeight:-1,comboBoxLabelFont:Z,comboBoxLabelColor:P,comboBoxLabelSelectColor:l,comboBoxShadowColor:A,comboBoxBorderColor:r,comboBoxBackground:d,comboBoxSelectBackground:V,comboBoxDropDownIcon:Bj(12,16,{type:"shape",points:[1,5,6,11,11,5],borderWidth:2,borderColor:i}),imageBorderColor:x,imageBackground:x,formPaneLabelColor:P,formPaneLabelFont:Z,formPaneLabelAlign:"left",formPaneLabelVAlign:"middle",formPaneHPadding:8,formPaneVPadding:8,formPaneLabelHPadding:2,formPaneLabelVPadding:0,formPaneHGap:6,formPaneVGap:6},!0),u(h,B,{ms_v:1,ms_fire:1,ms_tip:1,ms_ac:["toolTip"],onPropertyChanged:function(B){this.iv()},getToolTip:function(){return this._toolTip||this.getText()},getElement:function(){return this._element},getText:function(){return this._element.value},setText:function(B){this._element.value=B},getValue:function(){return this.getText()},setValue:function(B){this.setText(B)},getColor:function(){return this._element.style.color},setColor:function(B){this._element.style.color=B},getBackground:function(){return this._element.style.background},setBackground:function(B){this._element.style.background=B},getBorder:function(){return this._element.style.border},setBorder:function(B){this._element.style.border=B},getFont:function(){return this._element.style.font},setFont:function(B){this._element.style.font=B},isEditable:function(){return!this._element.hasAttribute(Hj)},setEditable:function(B){var n=this._element;B?n.removeAttribute(Hj):n.setAttribute(Hj,!0)},validateImpl:function(){R(this._element,0,0,this.getWidth(),this.getHeight())},setFocus:function(){return s.setFocus(this._element),this.fireViewEvent("focus"),!0}}),N.TextField=function(){N.TextField.superClass.constructor.call(this,"input",s.textFieldBorderColor),this.setColor(s.textFieldLabelColor),this.setFont(s.textFieldLabelFont)},u(n+"TextField",h,{getType:function(){return this._element.getAttribute("type")},setType:function(B){var n=this._element,O=s.numberListener;n.setAttribute("type",B),t(n,"keydown",O,!1),"number"===B&&v(n,"keydown",O,!1)}}),N.TextArea=function(){N.TextArea.superClass.constructor.call(this,"textarea",s.textAreaBorderColor),this.setColor(s.textAreaLabelColor),this.setFont(s.textAreaLabelFont)},u(n+"TextArea",h,{}),u(n+"Button",B,{ms_v:1,ms_fire:1,ms_tip:1,ms_ac:["groupId","label","icon","iconColor","orientation","toolTip","labelFont","labelColor","labelSelectColor","borderColor","background","selectBackground","togglable","selected","pressed","clickable","align","vAlign"],_clickable:!0,_togglable:!(N.Button=function(){var B=this,n=B._view=q.createView(null,B);B.setHeight(W),B.setWidth(80),B._canvas=Q(n),new Zj(B),B.iv()}),_selected:!1,_pressed:!1,_orientation:"h",_labelFont:s.buttonLabelFont,_labelColor:s.buttonLabelColor,_labelSelectColor:s.buttonLabelSelectColor,_borderColor:s.buttonBorderColor,_background:s.buttonBackground,_selectBackground:s.buttonSelectBackground,_align:"center",_vAlign:"middle",onClicked:function(B){},onSelectedChanged:function(){},getValue:function(){return this.isSelected()},setValue:function(B){this.setSelected(B)},onValueChanged:function(){},onPropertyChanged:function(B){var n=this,O=n._view.parentNode;n.iv(),"selected"===B.property&&(null!=n.getGroupId()&&O&&O.handleGroupSelectedChanged&&O.handleGroupSelectedChanged(n),n.onSelectedChanged(),n.onValueChanged(B.oldValue,B.newValue))},getToolTip:function(){return this._toolTip||this._label},getCurrentBackground:function(){return this._pressed||this._selected?this._selectBackground:this._background},getCurrentBorderColor:function(){return this._borderColor},validateImpl:function(){var B,n=this,O=n._canvas,e=n.getWidth(),r=n.getHeight(),u=n._pressed||n._selected,W=n.getCurrentBackground(),O=(S(O,e,r),c(O)),W=(m(O,0,0,1),O.clearRect(0,0,e,r),W&&G(O,0,0,e,r,W),b(O,n.getCurrentBorderColor(),0,0,e,r),n.getIconColor()),H=_(n.getIcon(),W),M=nj(H),Z=Oj(H),x=n.getLabel(),o=n.getLabelFont(),u=u?n._labelSelectColor:n._labelColor,N=null==x?Mj:w(o,x),E=N.width,N=N.height;rj(n)?"left"===(B=n.getAlign())?(M&&Y(O,H,M/2,r/2,n,n,W),E&&U(O,x,o,u,M,0,E,r,"center")):"right"===B?(M&&Y(O,H,e-E-M/2,r/2,n,n,W),E&&U(O,x,o,u,e-E,0,E,r,"center")):(M&&Y(O,H,e/2-(M+E)/2+M/2,r/2,n,n,W),E&&U(O,x,o,u,e/2-(M+E)/2+M,0,E,r,"center")):"top"===(B=n.getVAlign())?(M&&Y(O,H,e/2,Z/2,n,n,W),E&&U(O,x,o,u,e/2-E/2,Z,E,N,"center")):"bottom"===B?(M&&Y(O,H,e/2,r-N-Z/2,n,n,W),E&&U(O,x,o,u,e/2-E/2,r-N,E,N,"center")):(M&&Y(O,H,e/2,r/2-(Z+N)/2+Z/2,n,n,W),E&&U(O,x,o,u,e/2-E/2,r/2-(Z+N)/2+Z,E,N,"center")),O.restore()}}),N.ButtonInteractor=function(B){this.button=B,this.addListeners()}),xj=(u(n+"ButtonInteractor",B,{ms_listener:1,getView:function(){return this.button._view},handle_mousedown:function(B){M(B)&&this.handle_touchstart(B)},handleWindowMouseMove:function(B){this.handleWindowTouchMove(B)},handleWindowMouseUp:function(B){this.handleWindowTouchEnd(B)},handle_touchstart:function(B){E(B),this.button.setFocus(B)&&this.button.isClickable()&&(I(this,B),this.button.setPressed(!0))},handleWindowTouchMove:function(B){},handleWindowTouchEnd:function(B){var n=this.button;n.setPressed(!1),O(n,B)&&(n.isTogglable()&&(null!=n.getGroupId()?n.setSelected(!0):n.setSelected(!n.isSelected())),n.onClicked(B))}}),u(D,B,{ms_v:1,ms_fire:1,ms_tip:1,ms_ac:["label","labelFont","labelColor","toolTip","icon","iconColor","selected","pressed","pressBackground","padding"],_padding:0,_selected:!1,_pressed:!1,onClicked:function(B){},onSelectedChanged:function(){},onValueChanged:function(){},getValue:function(){return this.isSelected()},setValue:function(B){this.setSelected(B)},onPropertyChanged:function(B){this.iv(),"selected"===B.property&&(this.onSelectedChanged(),this.onValueChanged(B.oldvalue,B.newValue))},getToolTip:function(){return this._toolTip||this._label},validateImpl:function(){var B=this,n=B._canvas,O=B.getWidth(),e=B.getHeight(),r=B.getCheckIcon,n=(S(n,O,e),c(n)),O=(m(n,0,0,1),n.clearRect(0,0,O,e),B._padding),u=B._iconColor,W=_(r?B.getCheckIcon():B.getRadioIcon()),H=nj(W),M=Oj(W);B._pressed&&(n.fillStyle=B._pressBackground,n.beginPath(),r?n.rect(O,e/2-M/2,H,M):n.arc(O+H/2,e/2,Math.min(H,M)/2,0,2*Math.PI,!0),n.fill()),Y(n,W,O+H/2,e/2,B,B),O+=H+1,W=_(B.getIcon(),u),H=nj(W),Y(n,W,O+H/2,e/2,B,B,u),U(n,B._label,B._labelFont,B._labelColor,O+=H,0,0,e),n.restore()}}),function(B){this.c=B,this.addListeners()}),oj=(u(xj,B,{ms_listener:1,getView:function(){return this.c._view},handle_mousedown:function(B){M(B)&&this.handle_touchstart(B)},handleWindowMouseMove:function(B){this.handleWindowTouchMove(B)},handleWindowMouseUp:function(B){this.handleWindowTouchEnd(B)},handle_touchstart:function(B){E(B),this.c.setFocus(B)&&(I(this,B),this.c.setPressed(!0))},handleWindowTouchMove:function(B){},handleWindowTouchEnd:function(B){var n=this.c;n.setPressed(!1),O(n,B)&&(n.handleClick(B),n.onClicked(B))}}),N.CheckBox=function(){N.CheckBox.superClass.constructor.call(this)},u(n+"CheckBox",D,{_labelFont:s.checkBoxLabelFont,_labelColor:s.checkBoxLabelColor,_pressBackground:s.checkBoxPressBackground,getCheckIcon:function(){return this._selected?"check":"uncheck"},handleClick:function(B){this.setSelected(!this.isSelected())}}),N.RadioButton=function(){N.RadioButton.superClass.constructor.call(this)},u(n+"RadioButton",D,{ms_ac:["groupId"],_pressBackground:s.radioButtonPressBackground,_labelFont:s.radioButtonLabelFont,_labelColor:s.radioButtonLabelColor,getRadioIcon:function(){return this._selected?"radioOn":"radioOff"},onPropertyChanged:function(B){var n=this,O=n._view.parentNode;n.iv(),null!=n.getGroupId()&&"selected"===B.property&&(O&&O.handleGroupSelectedChanged&&O.handleGroupSelectedChanged(n),n.onSelectedChanged(),n.onValueChanged(B.oldValue,B.newValue))},handleClick:function(B){this.setSelected(!0)}}),N.Slider=function(){var B=this,n=B._view=q.createView(null,B);B.setHeight(W),B.setWidth(80),B._canvas=Q(n),new oj(B),B.iv(),B.enableToolTip()},u(n+"Slider",B,{ms_v:1,ms_fire:1,ms_tip:1,ms_ac:["value","min","max","step","button","toolTip","instant","thickness","padding","background","leftBackground"],_min:0,_max:100,_value:50,_step:x,_instant:!0,_button:s.sliderButton,_thickness:s.sliderThickness,_padding:s.sliderPadding,_background:s.sliderBackground,_leftBackground:s.sliderLeftBackground,onPropertyChanged:function(B){B=B.property;"min"!==B&&"max"!==B&&"step"!==B||this.setValue(this._value),this.iv()},adjustValue:function(B){var n=this._min,O=this._max,e=this._step;return O<(B=(B=null==B?0:B)<n?n:B)&&(B=O),B=0<e?Math.floor((B-n)/e+1e-6)*e+n:B},getToolTip:function(){return this._toolTip||this._value},getValue:function(){return this._value},setValue:function(B){var n=this,O=n._value;O!==(B=n.adjustValue(B))&&(n._value=B,n.fp("value",O,B),n.onValueChanged(O,B))},onValueChanged:function(B,n){},onBeginValueChanged:function(){},onEndValueChanged:function(){},drawBackground:function(B,n,O,e,r){G(B,n,O,e,r,this.getBackground())},drawLeftBackground:function(B,n,O,e,r){var u=this.getLeftBackground();u&&G(B,n,O,e,r,u)},drawButton:function(B,n,O,e,r){Y(B,_(this._button),n+e/2,O+r/2,this,this)},getButtonWidth:function(){var B=_(this._button);return B?B.width:0},validateImpl:function(){var B=this,n=B._canvas,O=B._min,e=B._max,r=B._value,u=B._padding,W=B._thickness,H=B.getWidth(),M=B.getHeight(),Z=B.getButtonWidth(),x=(M-W)/2,r=Z/2+(r-O)/(e-O)*(H-2*u-Z),e=(S(n,H,M),c(n));m(e,0,0,1),e.clearRect(0,0,H,M),B.drawBackground(e,u,x,H-2*u,W),B.drawLeftBackground(e,u,x,r,W),B.drawButton(e,u+r-Z/2,0,Z,M),e.restore()}}),function(B){this.slider=B,this.addListeners()}),Nj=(u(oj,B,{ms_listener:1,getView:function(){return this.slider._view},handle_mousedown:function(B){M(B)&&this.handle_touchstart(B)},handleWindowMouseMove:function(B){this.handleWindowTouchMove(B)},handleWindowMouseUp:function(B){this.handleWindowTouchEnd(B)},setValue:function(B){var n=this.slider,O=n.getPadding()+n.getButtonWidth()/2,e=n.getMin();n.setValue(e+(T(B,n._canvas).x-O)/(n.getWidth()-2*O)*(n.getMax()-e))},handle_touchstart:function(B){var n=this;E(B),n.slider.setFocus(B)&&(n.slider.onBeginValueChanged(),n.setValue(B),I(n,B),s.showToolTip(B,n.slider.getToolTip(B),n.slider))},handleWindowTouchMove:function(B){this.setValue(B),s.showToolTip(B,this.slider.getToolTip(B),self.slider)},handleWindowTouchEnd:function(B){this.setValue(B),this.slider.onEndValueChanged(),s.hideToolTip()}}),N.ComboBox=function(){var H=this,O=H._view=q.createView(null,H),n=H._listView=new N.ListView,e=H._canvas=Q(O),r=n._view,u=n.sm();H.setHeight(W),H.setWidth(80),u.setSelectionMode("single"),n.drawRow=function(B,n,O,e,r,u,W){H.drawRow(B,n,O,e,r,u,W)},O.style.display="inline-block",q.setBorder(O,s.comboBoxBorderColor),r.style.boxShadow="0px 0px 10px "+s.comboBoxShadowColor,null!=s.baseZIndex&&(r.style.zIndex=parseInt(s.baseZIndex)+1+""),v(r,"mousemove",function(B){u.ss(n.getDataAt(B))},!1),v(r,"keydown",function(B){var n;q.isEnter(B)&&(n=u.ld())&&(H.setValue(n.value),H.close()),q.isEsc(B)&&H.close()},!1),v(O,"keydown",function(B){(q.isDown(B)||q.isUp(B))&&H.open()},!1),n.onDataClicked=function(B,n){H.setValue(B.value),H.close()},v(O,L,function(B){var n;!M(B)||(n=B.target)!==e&&n!==O||(E(B),H._editable&&B.stopPropagation(),H.toggle())},!1),H._handleWindowClick=function(B){M(B)&&(B=B.target)!==H._input&&!r.contains(B)&&B!==O&&(H._input?H.setInputValue(H._input):H.close())},H.iv()},s.def(n+"ComboBox",B,{ms_v:1,ms_fire:1,ms_tip:1,ms_ac:["dropDownIcon","dropDownWidth","dropDownBackground","toolTip","strict","indent","background","labelFont","labelColor","labelSelectColor","maxHeight","selectBackground","value","values","labels","icons","editable"],_strict:!0,_editable:!1,_maxHeight:s.comboBoxMaxHeight,_labelFont:s.comboBoxLabelFont,_labelColor:s.comboBoxLabelColor,_labelSelectColor:s.comboBoxLabelSelectColor,_background:s.comboBoxBackground,_dropDownWidth:x,_dropDownIcon:s.comboBoxDropDownIcon,_dropDownBackground:s.comboBoxBackground,_selectBackground:s.comboBoxSelectBackground,_indent:p,getInput:function(){return this._input},getListView:function(){return this._listView},onPropertyChanged:function(B){this.iv(),"value"===B.property&&this.onValueChanged(B.oldValue,B.newValue)},onValueChanged:function(B,n){},getToolTip:function(){return this._toolTip||this.toLabel(this._value)},getLabelColor:function(B,n){return n?this._labelSelectColor:this._labelColor},isEqual:function(B,n){return this._strict?B===n:B==n},toLabel:function(B){var n=this.getValues(),O=this.getLabels();if(O&&n&&O.length===n.length)for(var e=0;e<n.length;e++)if(this.isEqual(n[e],B))return O[e];return null==B?"":B+""},toIcon:function(B){var n=this.getValues(),O=this.getIcons();if(O&&n&&O.length===n.length)for(var e=0;e<n.length;e++)if(this.isEqual(n[e],B))return O[e];return null},drawValue:function(B,n,O,e,r,u,W){var H=this,M=H.getIndent(),Z=H.toLabel(n),x=_(H.toIcon(n));x&&(Y(B,x,e+M/2,r+W/2),e+=M),Z&&U(B,Z,H.getLabelFont(n,O),H.getLabelColor(n,O),e,r,0,W)},drawRow:function(B,n,O,e,r,u,W){var n=n.value,H=this.getIndent();O&&G(B,e,r,u,W,this._selectBackground),this.drawValue(B,n,O,e,r,u-H,W)},validateImpl:function(){var B=this,n=B._canvas,O=B.getIndent(),e=B.getWidth(),r=B.getHeight(),u=e-O,W=B._background||"",W=(B._view.style.background=W,B._listView._view.style.background=B._dropDownBackground,S(n,e,r),c(n));m(W,0,0,1),W.clearRect(0,0,e,r),0<O&&Y(W,_(B._dropDownIcon),e-O/2,r/2,B,B),W.beginPath(),W.rect(0,0,u,r),W.clip(),B.drawValue(W,B._value,!1,0,0,u,r),W.restore()},isOpened:function(){return!!this._listView._view.parentNode},open:function(){var n=this,B=n._listView,O=B.dm(),e=n.getValues()||[],r=e.length;if(!n.isOpened()){O.clear();for(var u=0;u<r;u++){var W=new y.Data,H=e[u];W.setName(n.toLabel(H)),W.value=H,O.add(W),n.isEqual(H,n._value)&&O.sm().ss(W)}var M,Z=J(),x=Z.left,o=Z.top,Z=Z.height,N=n._view.getBoundingClientRect(),x=N.left+x,E=N.top+o,N=N.height,U=n.getIndent(),_=n.getWidth(),Y=n.getHeight(),P=x+1,I=E+N,Q=n.getDropDownWidth()||_,k=r*Y;0<n._maxHeight&&(k=Math.min(k,n._maxHeight)),B.setRowHeight(Y),o+Z<I+k&&(o+Z-E-N<E-o?I=E-(k=Math.min(k,E-o)):k=o+Z-I),n._editable&&((M=n._input=s.createElement("input",n.getSelectBackground(),n.getLabelFont(),n.toLabel(n._value))).className="ht-widget-combobox-input",y.Default.appendToScreen(M),R(M,x,E+1,_-U,Y),v(M,"keydown",function(B){q.isEnter(B)?n.setInputValue(M):q.isEsc(B)&&n.close()},!1),n.onInputCreated(M)),B.getView().className="ht-widget-combobox-popup",y.Default.appendToScreen(B.getView()),R(B,P,I,Q,k),B.setFocus(),y.Default.callLater(function(B){v(F,L,n._handleWindowClick,!0)}),n.onOpened&&n.onOpened(),q.closePopup(n)}},onInputCreated:function(B){},setInputValue:function(B){var n=this.getLabels(),O=this.getValues(),e=B.value;if(n)for(var r=0;r<n.length;r++)e===n[r]&&(e=O[r]);"string"==typeof e&&O&&O.length&&"number"==typeof O[0]&&(e=parseFloat(e)),this.setValue(e),this.close()},close:function(){var B=this;B.isOpened()&&(H(B._listView._view),B._input&&(H(B._input),delete B._input),t(F,L,B._handleWindowClick,!0),B.onClosed&&B.onClosed(),s.popup===B&&delete s.popup),B.setFocus()},toggle:function(){this.isOpened()?this.close():this.open()}}),N.BaseDropDownTemplate=function(B){this._master=B},s.def(n+"BaseDropDownTemplate",B,{ms_ac:["master"],getView:function(){},onOpened:function(B){},onClosed:function(){},getValue:function(){},getWidth:function(){},getHeight:function(){}}),N.MultiComboBox=function(){var O=this,e=O._view=q.createView(null,O),r=O._canvas=Q(e);O.setHeight(W),O.setWidth(80),e.style.display="inline-block",q.setBorder(e,s.comboBoxBorderColor),v(e,"keydown",function(B){(q.isDown(B)||q.isUp(B))&&O.open()},!1),v(e,L,function(B){var n;!M(B)||(n=B.target)!==r&&n!==e||(E(B),O._editable&&B.stopPropagation(),O.toggle())},!1),O._handleWindowClick=function(B){var n;M(B)&&(B=B.target,n=O._dropDownComponentInstanceView,B===O._input||B===e||n&&n.contains(B)||O.close(!0))},O.iv()},s.def(n+"MultiComboBox",B,{ms_v:1,ms_fire:1,ms_tip:1,ms_ac:["dropDownIcon","toolTip","background","labelFont","labelColor","value","editable","indent","dropDownComponent"],_editable:!1,_background:s.comboBoxBackground,_dropDownIcon:s.comboBoxDropDownIcon,_indent:p,_labelFont:s.comboBoxLabelFont,_labelColor:s.comboBoxLabelColor,onPropertyChanged:function(B){this.iv(),"value"===B.property&&this.onValueChanged(B.oldValue,B.newValue)},onValueChanged:function(B,n){},getDropDownComponentInstance:function(){return this._dropDownComponentInstance},getToolTip:function(){return this._toolTip},validateImpl:function(){var B=this,n=B._canvas,O=B._indent,e=B.getWidth(),r=B.getHeight(),O=e-O,u=B._background||"",u=(B._view.style.background=u,S(n,e,r),c(n));m(u,0,0,1),u.clearRect(0,0,e,r),Y(u,_(B._dropDownIcon),e-10,r/2,B,B),u.beginPath(),u.rect(0,0,O,r),u.clip(),this.drawValue(u,0,0,O,r),B._input&&B._input.value!==B._value&&(B._input.value=null==B._value?"":B._value),u.restore()},drawValue:function(B,n,O,e,r){U(B,this._value,this.getLabelFont(),this.getLabelColor(),n+1,O,0,r)},isOpened:function(){return!!this._dropDownComponentInstanceView},open:function(){var B,n,O,e,r,u,W,H,M=this;M.isOpened()||(B=s.getClass(M._dropDownComponent),B=M._dropDownComponentInstance=new B(M),n=M._dropDownComponentInstanceView=B.getView(),O=(r=J()).left,r=r.top,O=(e=M._view.getBoundingClientRect()).left+O,e=e.top+r,r=M.getIndent(),u=M.getWidth(),W=M.getHeight(),B.beforeOpen&&B.beforeOpen(M._value),M._editable&&(H=M._input=s.createElement("input",s.comboBoxSelectBackground,M.getLabelFont(),M._value),y.Default.appendToScreen(H),R(H,O,e+1,u-r,W),v(H,"keydown",function(B){q.isEnter(B)?(M.setValue(H.value),M.close(!0)):q.isEsc(B)&&M.close(!0)},!1),M.onInputCreated(H)),n.style.boxShadow="0px 0px 10px "+s.toolTipShadowColor,n.style.position="absolute",null!=s.baseZIndex&&(n.style.zIndex=parseInt(s.baseZIndex)+1+""),y.Default.appendToScreen(n),y.Default.setFocus(n),this.layoutDropDown(),B.onOpened&&B.onOpened(M._value),y.Default.callLater(function(B){v(F,L,M._handleWindowClick,!0)}),M.onOpened&&M.onOpened(),q.closePopup(M),M.fireViewEvent("open"))},layoutDropDown:function(){var B=this._dropDownComponentInstance,n=J(),O=n.left,e=n.top,r=n.width,n=n.height,u=this._view.getBoundingClientRect(),W=u.left+O,H=u.top+e,u=u.height,M=this.getWidth(),Z=H+u,M=B.getWidth()||M,x=B.getHeight();O+r<W+M&&(W-=W+M-O-r),R(B,W,Z=e+n<Z+x&&e+n-H-u<H-e?H-x:Z,M,x)},onInputCreated:function(B){},close:function(B){var n,O=this;O.isOpened()&&(B||O.setValue(O._dropDownComponentInstance.getValue()),B=O._dropDownComponentInstance,n=O._dropDownComponentInstanceView,B.onClosed&&B.onClosed(),H(n),delete O._dropDownComponentInstanceView,delete O._dropDownComponentInstance,O._input&&(H(O._input),delete O._input),t(F,L,O._handleWindowClick,!0),O.onClosed&&O.onClosed(),s.popup===O&&delete s.popup,O.fireViewEvent("close")),O.setFocus()},toggle:function(){this.isOpened()?this.close():this.open()}}),N.Image=function(){var B=this,n=B._view=q.createView(null,B);n.onmousedown=E,B.setHeight(W),B.setWidth(80),B._canvas=Q(n),new Nj(B),B.iv()},u(n+"Image",B,{ms_v:1,ms_fire:1,ms_tip:1,ms_ac:["icon","iconColor","stretch","toolTip","borderColor","background"],_borderColor:s.imageBorderColor,_background:s.imageBackground,_stretch:"centerUniform",onClicked:function(B){},onPropertyChanged:function(B){this.iv()},validateImpl:function(){var B=this,n=B._canvas,O=B.getWidth(),e=B.getHeight(),r=B._iconColor,u=_(B._icon,r),W=B._background,n=(S(n,O,e),c(n));m(n,0,0,1),n.clearRect(0,0,O,e),W&&G(n,0,0,O,e,W),this.drawImage(n,u,0,0,O,e,r),b(n,B._borderColor,0,0,O,e),n.restore()},drawImage:function(B,n,O,e,r,u,W){n&&s.drawStretchImage(B,n,this._stretch,O,e,r,u,null,this,W)}}),function(B){this.image=B,this.addListeners()}),Ej=(u(Nj,B,{ms_listener:1,getView:function(){return this.image._view},handle_mousedown:function(B){M(B)&&this.handle_touchstart(B)},handleWindowMouseMove:function(B){this.handleWindowTouchMove(B)},handleWindowMouseUp:function(B){this.handleWindowTouchEnd(B)},handle_touchstart:function(B){E(B),I(this,B)},handleWindowTouchMove:function(B){},handleWindowTouchEnd:function(B){var n=this.image;O(n,B)&&n.onClicked(B)}}),X=document.createElement("canvas"),function(B){var n=X.getContext("2d").createLinearGradient(0,0,0,1);try{return n.addColorStop(0,B),!0}catch(B){return!1}}),Uj=(N.ColorPicker=function(){N.ColorPicker.superClass.constructor.call(this),this.setEditable(!0),this.setDropDownComponent(N.ColorPickerTemp)},u(n+"ColorPicker",N.MultiComboBox,{ms_ac:["instant","clearButtonVisible","paletteColors","paletteBorderColor"],_clearButtonVisible:!0,_paletteBorderColor:"black",setValue:function(B){var n;B!==x&&(""===B||null===B||Ej(B)?(n=this._value,this._value=B,this.fp("value",n,B)):this.handleErrorValue(B))},handleErrorValue:function(B){},drawValue:function(B,n,O,e,r){this._value&&(B.beginPath(),B.rect(n+2,O+2,20,r-4),B.fillStyle=this._value,B.fill()),U(B,this._value,this.getLabelFont(),this.getLabelColor(),n+23,O,0,r)}}),N.ColorPickerTemp=function(e){function O(B){var n,O=(B=B.target).className,e=!1;0<=["color_R","color_G","color_B"].indexOf(O)?(n=parseInt(B.value),B.value=0<=n&&n<=255?parseInt(B.value):0,e=!0):"color_A"===O&&(n=parseFloat(B.value),B.value=0<=n&&n<=1?parseFloat(B.value):0,e=!0),e&&r.updateInputChange()}var r=this,B=r._view=q.createView(null,r),n=r.$10o=document.createElement("div"),u=r.$12o=K(),W=r._paletteColorsDiv=K(),H=n.style,M=(r._h=360,r._s=100,r._v=100,B.className="colorPickerPopup ht-widget-colorpicker-popup",B.style.position="absolute",B.style.background=y.Color.background||"white",N.ColorPickerTemp.superClass.constructor.call(r,e),'<div style="border: 1px solid rgba(0, 0, 0, 0); position: absolute; width: 40px; height: 20px; top: 5px; left: 5px; bottom: 5px; background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAIAAAACUFjqAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAA6SURBVChTY/iPCk6DwRkYoKv0sWPHjiIBBohyCADKQVXBAIr0kSNHoMIwwAC1BAyApkGFYYAS6f//AVzgDAPz1XkAAAAAAElFTkSuQmCC) repeat;"></div>                      <div class= "preview" style="border: 1px solid black; position: absolute; width: 40px; height: 20px; top: 5px; left: 5px; bottom: 5px;"></div>                      <div style="margin-left: 50px; color: '+s.labelColor+";font: "+s.labelFont+'; line-height: 30px;" >                             <span>R:</span><input class="color_R" style="width: 28px;" type="'+(k?"number":"text")+'">                             <span>G:</span><input class="color_G" style="width: 28px" type="'+(k?"number":"text")+'">                             <span>B:</span><input class="color_B" style="width: 28px" type="'+(k?"number":"text")+'">                             <span>A:</span><input class="color_A" style="width: 28px" value="1" type="'+(k?"number":"text")+'">                      </div>'),Z='<div>        <div class="satval" style="position: absolute;cursor:crosshair;margin-left:5px;top: 6px;border:1px solid black;width:128px;height:128px;background-image: url(data:image/png;base64,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***********************************/X0jtV/JuizLAD3aT/aDMDw76MNA0WDP2voRyIEZA6gxgJYYOeIYpsAPvKRj1QTgGNuQOwMOOV/OuAd4HbdXlHtYb8PAN7MBDTPX0sIFklIQOfOy7IAFgl4QsDyeE0OYAWD6QTw4Q9/OEMBSFuEhQmAex7Bil+TDViEgXQK3G1A7xyAlehbmUBU0kfmABAFIHn9DAvgAX6t/C8JB8kBLFugKQMJ7CoBfOhDH5oSAXjbhtDtSMWPWgDQPiByvpcAbVR8M8SzEn6gNQj3/bPOKcHn6QJELECkLYiQgdUG9HYGIgTAKYVtAvjgBz/oIgCwgpMCADM/MAjAI/mj3QC1ukvThlzFdqb96voAS5JrJOKxCAAZWIRjZgDeToBV6TXvP7xvRgiIjAtHBoGQzgBiIVwE8IEPfGA0AkDlvjMkdEn+YDfAZRGEUWSznYck/GDFhz2/BHAt9UcUgTYKrJ1TAh3pAqCrAT0tQMTne7oAHnVg+XxtnYGbAN7//vdDBFBUb2jcFyELcMkvZVT8LPB7qrOVDWgKApkDqJH8VoXXUn6LELhzpD39EKsgEQN3f8nPR32/RAhWRwBtAyKgtkLD0t9r3YPDCOB973ufRgAkyHeJAMixNgBp99EIFT8aAGphH5ruk8ffWxIfCQI9cwARSxDJALykwHUNpJ2A0CDQsgLeGQFEBXhyAWkmQCMAjRS2CeC9733v1AgAagNGgj3LaiiJfZU9AG4zfwZzgkjolwJ6zt9HSUCT8qjMZ8DZocCPVH6NDFCgo8GgpQhcBPCe97xnLAKAg0EjT6gBtes+EbWAZAOIgrDICCGKBgoAHgJC24DacA8a+Ennah2ADOCjZOAlACT4SyOAd7/73TUEQEDF1gJBKxeA5gESyQGt4GQRRI3cBxSBWfm1Pr9GAMjt1vNABn8ybAHSBdBUglcRWIM/SBfAsgdS68+jCDRS2EEA73rXu5oQgCfwA4FKTskfAT1p6bwU9oEdgCq1oFVzi1g8qb9V9aNS3lIAFtClqUEtHNTOiSoCdEioZg7AIoooAXCKYPHOd75z8gSQWNVd54FkYc0QuAJD8GerC9BUASA/Z5ACA6jOU/2j4R9ynmcMWBsi8hAAaglcBPCOd7xjZQQA5AQehZBODppCAGcBTMIoz9XAu2oFAIaGbpJA/L9nBgDpAGiZgQZyz0QgB1YrH9AWAyH3cRPA29/+9moCAGYA4FzAGB12AbuGHJBrOio6GhiGFIFlDzIUQJbsl6S9Vf29rUCODFDgW4GfJwdABoA8gaGWCZTghgjgbW97WxYBaOEeQgBu+R8AeFUYaIWN6H0tC1GCEanwqDpwEACaC8BgR2wCV80R8HPHpSEgr+z3dgRQGxAlAG3Bj5sA3vrWt0IEAE4J9o7zvQSQBmxnBoAsBqKIAkDzAvR+no5BMAT0KIw+4uuRxUBWDqBN+EWAj4aAlg0ob/d0BlACsGzBDgJ4y1ve0oIA4LUAyBxAQ8lPaGIP3I62Dc3zWyqCWgWQXfERj1+TA0RagN6w0OvzvcSAjPaGCeDNb35zEwJIsgXmz7W3IQEgB1QORFpo5+gOqLMEEjmhQWFGLx8hpYi019J+73EkB6iV/*****************************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);background-color: red;background-size:contain;">            <div class="satval_pointer" style="position:absolute;left: 124px; top: -3px;width: 7px; height: 7px;background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAHCAYAAADEUlfTAAAACXBIWXMAAAsTAAALEwEAmpwYAAABNmlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjarY6xSsNQFEDPi6LiUCsEcXB4kygotupgxqQtRRCs1SHJ1qShSmkSXl7VfoSjWwcXd7/AyVFwUPwC/0Bx6uAQIYODCJ7p3MPlcsGo2HWnYZRhEGvVbjrS9Xw5+8QMUwDQCbPUbrUOAOIkjvjB5ysC4HnTrjsN/sZ8mCoNTIDtbpSFICpA/0KnGsQYMIN+qkHcAaY6addAPAClXu4vQCnI/Q0oKdfzQXwAZs/1fDDmADPIfQUwdXSpAWpJOlJnvVMtq5ZlSbubBJE8HmU6GmRyPw4TlSaqo6MukP8HwGK+2G46cq1qWXvr/DOu58vc3o8QgFh6LFpBOFTn3yqMnd/n4sZ4GQ5vYXpStN0ruNmAheuirVahvAX34y/Axk/96FpPYgAAACBjSFJNAAB6JQAAgIMAAPn/AACA6AAAUggAARVYAAA6lwAAF2/XWh+QAAAANUlEQVR42myNsQ0AMAzC8P9Hu0sjpU3YkLFADWBmIInqQsgDAar8kCY6zDtYzfUzte5ROQMAeFAiAMRBE+oAAAAASUVORK5CYII=)"></div>        </div>        <div class="hue_picker" style="position:absolute;width: 46px; height: 140px;left: 152px;top:0;">            <div class="hue_image" style="border: 1px solid black;position:absolute;left:7px;top:6px;width:30px; height:128px; background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAEACAIAAABdw+MhAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAALBJREFUeNrs2sEJgDAQRNGJBPuvV5R4FAuYnF4aeGRv+9mx0n0zOQEAAAAAAAAAAAAAAABsAsbK0/3Bk6MLXO1WAQAAAAAAAAAAAAAAAOBLCUn3bGCWWwgAAAAAAAAAAAAAAACA36Z/rzKw7vKIcgEAAAAAAAAAAAAAAADYBIzyzUBmuYUAAAAAAAAAAAAAAAAA2AmM8s1AZrmFAAAAAAAAAAAAAAAAAPht+uWU8AowAGyEi9doBGciAAAAAElFTkSuQmCC);background-size:contain;"></div>            <div class="hue_pointer" style="position:absolute;top:1px;left:0;width:46px; height:12px; background: url(data:image/png;base64,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)"></div>        </div>        <div style="position: absolute; left: 210px;top:6px;">        <input type="button" value="&#10003" class="color_ok" style="color: '+s.labelColor+'"><br><br>        <input type="button" value="&#10005" class="color_clear" style="color: '+s.labelColor+'">        </div>        </div>',H=(H.font=s.labelFont,H.height="30px",H.lineHeight="30px",H.position="relative",H.whiteSpace="nowrap",n.innerHTML=M,u.innerHTML=Z,r.buildPaletteColors(),W.style.top="166px",B.appendChild(n),B.appendChild(u),B.appendChild(W),v(B,"click",function(B){var n=B.target,O=n.className;"color_ok"===O?(r.$13o=r.$9o(),e.close(!1,B)):"color_clear"===O?(r.$13o=null,e.close()):0<=["color_R","color_G","color_B","color_A"].indexOf(O)?n.select(!1,B):"colorPalette"===O&&(O=n._color,r.$13o=O,e.close(!1,B))}),v(B,"keydown",function(B){var n=B.target.className;0<=["color_R","color_G","color_B","color_A","color_ok","color_clear"].indexOf(n)&&13==B.keyCode&&(O(B),r.$13o=r.$9o(),e.close())}),new Uj(r),v(B,"change",O),v(B,"mousedown",function(B){E(B)}),o(B,".color_clear"));e._clearButtonVisible||(H.style.display="none"),y.Default.onElementCreated&&(y.Default.onElementCreated(B.querySelector("input.color_R")),y.Default.onElementCreated(B.querySelector("input.color_G")),y.Default.onElementCreated(B.querySelector("input.color_B")),y.Default.onElementCreated(B.querySelector("input.color_A")),y.Default.onElementCreated(B.querySelector(".color_ok")),y.Default.onElementCreated(B.querySelector(".color_clear"))),y.Default.onWidgetColorPickerCreated&&y.Default.onWidgetColorPickerCreated(this)},u(N.ColorPickerTemp,N.BaseDropDownTemplate,{_51o:1,buildPaletteColors:function(){var B,n,O,e=this._paletteColorsDiv,r=this._master.getPaletteColors(),u=this.calculatePaletteWidth(),W=this._master.getPaletteBorderColor(),H={x:0,y:0,width:u,height:u};if(e.innerHTML="",r&&0<r.length)for(var M=0;M<r.length;M++)for(var Z=0;Z<r[M].length;Z++)(B=r[M][Z])&&(g(B)?(n=Q(x,!0),S(n,u,u,1),(O=n.getContext("2d")).beginPath(),O.rect(H.x,H.y,H.width,H.height),uj(O,B,function(B){return B},!1,H),O.fill(),O.restore(),O=n.style):((O=(n=K()).style).background=B,O.width=u+"px",O.height=u+"px"),O.left=5+Z*(u+5)+"px",O.top=5+M*(u+5)+"px",O.border="1px solid "+W,O.boxSizing="border-box",n.className="colorPalette",n._color=B,n._index=[M,Z],e.appendChild(n))},updateInputChange:function(){var B,n=this,O=n._view,e=n.$9o(),e=(O.querySelector(".preview").style.backgroundColor=e,y.Default.toColorData(e));e&&(4===e.length&&e[3],e=n.$5o(e[0]/255,e[1]/255,e[2]/255),B=o(O,".satval_pointer"),n._h=j(360*e.h),n._s=j(100*e.s),n._v=j(100*e.v),B.style.left=127*e.s-3+"px",B.style.top=127-127*e.v-3+"px",o(O,".hue_pointer").style.top=128-127*e.h+"px",B=n.$2o(n._h/360,1,1),O.querySelector(".satval").style.backgroundColor=n.$4o(B.r,B.g,B.b,!0)),n._master._instant&&n._master.setValue(n.$9o())},$6o:function(B){var n=this,O=n._view,e=n._master,r=n._h,u=n._s,W=n._v,u=n.$2o(r/360,u/100,W/100),W=u.r,H=u.g,u=u.b,W=(B||(O.querySelector("input.color_R").value=j(255*W),O.querySelector("input.color_G").value=j(255*H),O.querySelector("input.color_B").value=j(255*u)),n.$9o()),H=(O.querySelector(".preview").style.backgroundColor=W,n.$2o(r/360,1,1));O.querySelector(".satval").style.backgroundColor=n.$4o(H.r,H.g,H.b,!0),!B&&e._instant&&e.setValue(W)},$5o:function(B,n,O){var e,r,u=Math.max(Math.max(B,n),O),W=Math.min(Math.min(B,n),O);return W==u?e=r=0:(e=(W=u-W)/u,r=B==u?(n-O)/W:n==u?2+(O-B)/W:4+(B-n)/W,(r/=6)<0&&(r+=1),1<r&&--r),{h:r,s:e,v:u}},$4o:function(B,n,O,e){return B=j(255*B),n=j(255*n),O=j(255*O),(((e=e==x?!0:e)?"#":"")+(B=1==(B=B.toString(16)).length?"0"+B:B)+(n=1==(n=n.toString(16)).length?"0"+n:n)+(O=1==(O=O.toString(16)).length?"0"+O:O)).toUpperCase()},$3o:function(B,n,O){var e,r,u;if(O==x&&(O=null),3==(B="#"==B.substr(0,1)?B.substr(1):B).length)e=B.substr(0,1),e+=e,r=B.substr(1,1),r+=r,u=B.substr(2,1),u+=u;else{if(6!=B.length)return O;e=B.substr(0,2),r=B.substr(2,2),u=B.substr(4,2)}return e=parseInt(e,16),r=parseInt(r,16),u=parseInt(u,16),isNaN(e)||isNaN(r)||isNaN(u)?O:n?{r:e,g:r,b:u}:{r:e/255,g:r/255,b:u/255}},$2o:function(B,n,O){var e,r,u;if(0==O)u=r=e=0;else{var W=Math.floor(6*B),B=6*B-W,H=O*(1-n),M=O*(1-n*B),Z=O*(1-n*(1-B));switch(W){case 1:e=M,r=O,u=H;break;case 2:e=H,r=O,u=Z;break;case 3:e=H,r=M,u=O;break;case 4:e=Z,r=H,u=O;break;case 5:e=O,r=H,u=M;break;case 6:case 0:e=O,r=Z,u=H}}return{r:e,g:r,b:u}},$9o:function(){var B=this._view,n=o(B,"input.color_R").value,O=o(B,"input.color_G").value,e=o(B,"input.color_B").value,B=o(B,"input.color_A").value||1;return""===n||""===O||""===e||""===B?x:1==B?"rgb("+n+","+O+","+e+")":"rgba("+n+","+O+","+e+","+B+")"},getView:function(){return this._view},onOpened:function(B){var n,O,e,r,u;B&&((O=(n=this)._view).querySelector(".preview").style.backgroundColor=B,(B=y.Default.isString(B)&&0<=B.indexOf("rgba")?B.replace(/[\s*|rgba|\(\)]/g,"").split(",").map(function(B,n){return 3===n?255*parseFloat(B):parseInt(B)}):y.Default.toColorData(B))&&(e=4===B.length?B[3]/255:1,r=n.$5o(B[0]/255,B[1]/255,B[2]/255),u=o(O,".satval_pointer"),o(O,"input.color_R").value=B[0],o(O,"input.color_G").value=B[1],o(O,"input.color_B").value=B[2],o(O,"input.color_A").value=e.toFixed(2),n._h=j(360*r.h),n._s=j(100*r.s),n._v=j(100*r.v),u.style.left=127*r.s-3+"px",u.style.top=127-127*r.v-3+"px",o(O,".hue_pointer").style.top=128-127*r.h+"px",n.$6o(!0)))},onClosed:function(){},getValue:function(){return this.$13o},calculatePaletteWidth:function(){this._view;var B=this._master.getPaletteColors();if(B&&0<B.length)return(this.getWidth()-5-5-5*((B=B[0].length)-1))/B},getHeight:function(){this._view;var B=this._master.getPaletteColors(),n=170,O=this.calculatePaletteWidth();return 0<O&&(n+=B.length*(O+5)),n},getWidth:function(){return 252}}),function(B){this.$8o=B,this.setUp()}),_j=(u(Uj,B,{ms_listener:1,getView:function(){return this.$8o._view},setUp:function(){this.addListeners()},tearDown:function(){this.removeListeners(),this.clear()},clear:function(){delete this.$7o},handle_touchstart:function(B){var n=this,O=B.target,e=n.$8o,r=n.getView(),u=o(r,".hue_picker"),r=o(r,".satval");u.contains(O)?(e.fi({kind:"beginEditHue"}),n.$7o=1,n.$1o(B,1)):r.contains(O)&&(e.fi({kind:"beginEditSV"}),n.$7o=2,n.$1o(B,2)),n.$7o&&s.isDoubleClick(B)&&(e.$13o=e.$9o(),e._master.close())},$1o:function(B,n){s.preventDefault(B);var O,e,r,u=this.$8o,W=this.getView(),H=o(W,".hue_picker"),M=o(W,".satval");B=Wj(B)?(r=B).touches[0]||r.changedTouches[0]:B,1===n?(O=H.getBoundingClientRect(),e=B.clientY-O.top,127<(e=(e-=7)<0?0:e)&&(e=127),o(W,".hue_pointer").style.top=e+1+"px",r=j(360-(r=e)*(360/127)),u._h=r,u.fi({kind:"betweenEditHue",newValue:r}),u.$6o()):2===n&&(O=M.getBoundingClientRect(),H=B.clientX-O.left-1,127<(e=(e=B.clientY-O.top-1)<0?0:e)&&(e=127),u._s=j(100*(H=127<(H=H<0?0:H)?127:H)/127),u._v=j(100-100*e/127),u.fi({kind:"betweenEditSV",newValue:{s:u._s,v:u._v}}),(n=o(W,".satval_pointer")).style.left=H-3+"px",n.style.top=e-3+"px",u.$6o())},handle_mousedown:function(B){this.handle_touchstart(B)},handle_mouseup:function(B){this.handle_touchend(B)},handle_touchend:function(B){var n=this.$7o;1===n?this.$8o.fi({kind:"endEditHue"}):2===n&&this.$8o.fi({kind:"endEditSV"}),this.clear(B)},handle_mousemove:function(B){this.handle_touchmove(B)},handle_touchmove:function(B){this.$7o&&s.startDragging(this,B)},handleWindowMouseUp:function(B){this.handleWindowTouchEnd(B)},handleWindowTouchEnd:function(B){this.clear(B)},handleWindowMouseMove:function(B){this.handleWindowTouchMove(B)},handleWindowTouchMove:function(B){var n=this.$7o;n&&this.$1o(B,n)}}),N.FormPane=function(B){var e=this,n=e._view=q.createView(1,e),B=(B||(e._canvas=Q(n)),e._contentDiv=K(x,n)),O=B.style;O.overflow="hidden",O.left="0px",O.right="0px",O.top="0px",O.bottom="0px",e._79O=K(x,n),e._rows=[],e._itemMap={},e.iv(),B.handleGroupSelectedChanged=function(n){var O;n.isSelected()&&null!=(O=n.getGroupId())&&e._rows.forEach(function(B){B=B.items;B&&B.forEach(function(B){B&&(B=B.element)&&B!==n&&B.setSelected&&B.getGroupId&&B.getGroupId()===O&&B.setSelected(!1)})})},new _j(e)},u(n+"FormPane",B,{ms_v:1,ms_fire:1,ms_txy:1,ms_lp:1,ms_vs:1,ms_hs:1,ms_value:1,ms_ac:["labelColor","labelFont","labelAlign","labelVAlign","vPadding","hPadding","labelHPadding","labelVPadding","hGap","vGap","rows","rowHeight","scrollBarColor","scrollBarSize","autoHideScrollBar"],_29I:Mj,_91I:0,_59I:0,_labelColor:s.formPaneLabelColor,_labelFont:s.formPaneLabelFont,_labelAlign:s.formPaneLabelAlign,_labelVAlign:s.formPaneLabelVAlign,_hPadding:s.formPaneHPadding,_vPadding:s.formPaneVPadding,_labelHPadding:s.formPaneLabelHPadding,_labelVPadding:s.formPaneLabelVPadding,_hGap:s.formPaneHGap,_vGap:s.formPaneVGap,_rowHeight:s.widgetRowHeight,_scrollBarColor:s.scrollBarColor,_scrollBarSize:s.scrollBarSize,_autoHideScrollBar:s.autoHideScrollBar,getScrollWidth:function(){return this._91I},getScrollHeight:function(){return this._59I},onPropertyChanged:function(B){B=B.property;this.iv(),"translateX"===B?this._42o():"translateY"===B&&this._43o()},adjustTranslateX:function(B){var n=this.getWidth()-this._91I;return 0<(B=B<n?n:B)?0:Math.round(B)},setTranslateX:function(B){var n=this,O=(B=n.adjustTranslateX(B),n._65O);n._65O=B,n._contentDiv.scrollLeft=-B,n.fp("translateX",O,B)},adjustTranslateY:function(B){var n=this.getHeight()-this._59I;return 0<(B=B<n?n:B)?0:Math.round(B)},setTranslateY:function(B){var n=this,O=(B=n.adjustTranslateY(B),n._66O);n._66O=B,n._contentDiv.scrollTop=-B,n.fp("translateY",O,B)},setPadding:function(B){this.setVPadding(B),this.setHPadding(B)},getLabelFont:function(B){return B&&B.font?B.font:this._labelFont},getLabelColor:function(B){return B&&B.color?B.color:this._labelColor},getLabelAlign:function(B){return B&&B.align?B.align:this._labelAlign},getLabelVAlign:function(B){return B&&B.vAlign?B.vAlign:this._labelVAlign},getRowBorderColor:function(B){return B?B.borderColor:null},getRowBackground:function(B){return B?B.background:null},getItemBorderColor:function(B){return B?B.borderColor:null},getItemBackground:function(B){return B?B.background:null},getItemById:function(B){return this._itemMap[B]},getViewById:function(B){B=this.getItemById(B);return B?B.element:null},getItemView:function(B){if(B){B=B.element;if(B){if(B.tagName)return B;if(B.getView)return B.getView()}}return null},updateItemElement:function(B,n){var O,e=this,B=e.getItemById(B);B&&((O=e.getItemView(B))&&O.parentNode&&O.parentNode.removeChild(O),B.element=n,(O=e.getItemView(B))&&(O.style.position="absolute",e._contentDiv.appendChild(O)),e.iv())},addRow:function(B,n,O,e){var r=this;if(B){for(var u=0;u<B.length;u++){var W=B[u];W&&((W.tagName||W.getView)&&(B[u]={element:W}),q.initItemElement(W),null!=W.id&&(r._itemMap[W.id]=W))}B.forEach(function(B){B=r.getItemView(B);B&&(B.style.position="absolute",r._contentDiv.appendChild(B))})}e=e||{};return e.items=B,e.widths=n,e.height=O,null==e.index?r._rows.push(e):r._rows.splice(e.index,0,e),r.iv(),e},removeRows:function(n){var O,e;n&&(O=this,e=[],n.forEach(function(B){B.items&&B.items.forEach(function(B){var n=O.getItemView(B);n&&n.parentNode&&n.parentNode.removeChild(n),B&&null!=B.id&&delete O._itemMap[B.id]})}),O._rows.forEach(function(B){n.indexOf(B)<0&&e.push(B)}),O._rows=e,O.iv())},removeRow:function(B){B=B&&"object"==typeof B?[B]:[this._rows[B]];this.removeRows(B)},clear:function(){for(var B=this,n=B._view,O=this._contentDiv;O.firstChild;)O.removeChild(O.firstChild);for(;n.firstChild;)n.removeChild(n.firstChild);B._canvas&&n.appendChild(B._canvas),n.appendChild(O),n.appendChild(B._79O),B._rows=[],B._itemMap={},B.iv()},validateImpl:function(){var B,n,O,e,r,u=this,W=u._canvas,H=u._rowHeight,M=u.getWidth(),Z=u.getHeight(),x=u._hPadding,o=u._vPadding,N=u._vGap,E=u.ty(),U=u.tx(),_=M-2*x,Y=u._rows,P=Y.length,I=0,Q=Z-2*o-(P-1)*N;for(u._29I={x:-U,y:-E,width:M,height:Z},W&&(S(W,M,Z),r=c(W),m(r,0,0,1),r.clearRect(0,0,M,Z)),y=0;y<P;y++)null==(e=(O=Y[y]).height)?Q-=H:C(e)?(B=e.split("+"),1<(n=parseFloat(B[0]))?Q-=n:I+=n,1<(n=parseFloat(B[1]))?Q-=n:I+=n):1<e?Q-=e:I+=e;Q<0?(u._59I=Z-Q,Q=0):u._59I=Z;for(var k=o+E,F=M,y=0;y<P;y++){null==(e=(O=Y[y]).height)?e=H:C(e)?(B=e.split("+"),e=1<(n=parseFloat(B[0]))?n:n/I*Q,e+=1<(n=parseFloat(B[1]))?n:n/I*Q):e<=1&&(e=e/I*Q);var s,q=x+U,v=(r&&(s=u.getRowBorderColor(O),(v=u.getRowBackground(O))&&G(r,q,k,_,e,v),s&&b(r,s,q,k,_,e)),u.validateRow(r,M,O.items,O.widths,q,k,_,e)-x);F<v&&(F=v),k+=e+N}o&&((W=u._vPaddingDiv)||((W=u._vPaddingDiv=K()).className="vPadding"),u._contentDiv.appendChild(W),R(W,q,k-E,_,o)),r&&r.restore(),u._91I=F,u._92I(),u._93I(),u.tx(u.tx()),u.ty(u.ty())},validateRow:function(B,n,O,e,r,u,W,H){if(!O)return 0;for(var M,Z,x=this._hGap,o=O.length,N=0,E=W-(o-1)*x,U=0;U<o;U++)I=e[U],C(I)?(M=I.split("+"),1<(Z=parseFloat(M[0]))?E-=Z:N+=Z,1<(Z=parseFloat(M[1]))?E-=Z:N+=Z):1<I?E-=I:N+=I;for(E<0&&(n-=E,E=0),U=0;U<o;U++){var _,Y,P=O[U],I=e[U];C(I)?(M=I.split("+"),I=1<(Z=parseFloat(M[0]))?Z:Z/N*E,I+=1<(Z=parseFloat(M[1]))?Z:Z/N*E):I<=1&&(I=I/N*E),P&&(B&&(_=this.getItemBorderColor(P),(Y=this.getItemBackground(P))&&G(B,r,u,I,H,Y),_&&b(B,_,r,u,I,H)),this.validateItem(B,P,r,u,I,H)),r+=I+x}return n},validateItem:function(B,n,O,e,r,u){var W,H,M=this,Z=n.element;Z&&!C(Z)?(O-=M.tx(),e-=M.ty(),(H=n._layoutRect)&&H.width===r&&H.height===u&&H.x===O&&H.y===e?Z.invalidate&&Z.invalidate():(R(Z,O,e,r,u),n._layoutRect={x:O,y:e,width:r,height:u})):B&&(C(n)?W=n:C(n.element)&&(W=n.element),W&&(B.save(),B.beginPath(),B.rect(O,e,r,u),B.clip(),U(B,W,M.getLabelFont(n),M.getLabelColor(n),O+M._labelHPadding,e-M._labelVPadding,r-2*M._labelHPadding,u-2*M._labelVPadding,M.getLabelAlign(n),M.getLabelVAlign(n)),B.restore()))}}),function(B){this.f=B,this.addListeners()});u(_j,B,{ms_listener:1,getView:function(){return this.f._view},handle_mousedown:function(B){M(B)&&this.handle_touchstart(B)},handleWindowMouseMove:function(B){this.handleWindowTouchMove(B)},handleWindowMouseUp:function(B){this.handleWindowTouchEnd(B)},handle_touchstart:function(B){var n,O=this,e=O.f,r=B.target;O.isV(B)?n="v":O.isH(B)?n="h":r!==O.getView()&&r!==e._contentDiv&&!e._79O.contains(r)&&r!==e._canvas||(n="p"),(O.s=n)&&(O.cp=f(B),O.tx=e.tx(),O.ty=e.ty(),E(B),I(O,B))},handle_mousemove:function(B){var n=this.f;this.isV(B)&&n._43o(),this.isH(B)&&n._42o()},handleWindowTouchMove:function(B){var n=this,O=n.f,e=n.s,r=n.tx,u=n.ty,n=n.cp,B=f(B),W=O._29I;"p"===e?O.setTranslate(r+B.x-n.x,u+B.y-n.y):"v"===e?O.ty(u+(n.y-B.y)*O._59I/W.height):"h"===e&&O.tx(r+(n.x-B.x)*O._91I/W.width)},handleWindowTouchEnd:function(B){},handle_mousewheel:function(B){this.h(B,B.wheelDelta/40,B.wheelDelta!==B.wheelDeltaX)},handle_DOMMouseScroll:function(B){this.h(B,-B.detail,1)},h:function(B,n,O){var e=this.f;E(B),q.closePopup(),O&&e._41o()?e.translate(0,10*n):e._40o()&&e.translate(10*n,0)},isV:function(B){var n=this.f,O=n._29I;return n._41o()&&O.x+O.width-n.lp(B).x<a},isH:function(B){var n=this.f,O=n._29I;return n._40o()&&O.y+O.height-n.lp(B).y<a}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);