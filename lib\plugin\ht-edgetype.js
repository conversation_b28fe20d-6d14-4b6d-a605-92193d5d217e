!function(P){"use strict";function $(P,m){return p(P,m).width}function Z(P,m){return p(P,m).height}function f(P,m){return k.getEdgeHostPosition(P,m,"source")}function n(P,m){return k.getEdgeHostPosition(P,m,"target")}function e(P,m){var k=P.s(b),_=P.getEdgeGroup();if(_){var t=0;if(_.eachSiblingEdge(function(P){m.isVisible(P)&&P.s(b)==k&&t++}),1<t)return P.s(G)*(t-1)/2}return 0}function c(k,_){var t=k.s(b),P=k.isLooped();if(!k.getEdgeGroup())return P?k.s(G):0;var w,F=0,D=0,B=0;return k.getEdgeGroup().getSiblings().each(function(P){P.each(function(P){var m;P._40I===k._40I&&P.s(b)==t&&_.isVisible(P)&&(m=P.s(G),B=(w?D+=B/2+m/2:w=P,m),P===k&&(F=D))})}),P?D-F+B:F-D/2}function T(P,m){return m=m.s("edge.corner.radius"),N.toRoundedCorner(P,m)}function F(P,t,w,m,k){if(m.sort(function(P,m){var k=P.getSourceAgent()===t?P.getTargetAgent():P.getSourceAgent(),_=m.getSourceAgent()===t?m.getTargetAgent():m.getSourceAgent(),k=k.p(),_=_.p();if(w===v||w===W){if(k.y>_.y)return 1;if(k.y<_.y)return-1}else{if(k.x>_.x)return 1;if(k.x<_.x)return-1}return N.sortFunc(P.getId(),m.getId())}),k){for(var _,F,D,B=P.getSourceAgent(),b=P.getTargetAgent(),L=0;L<m.size();L++){var E=m.get(L);E.getSourceAgent()===B&&E.getTargetAgent()===b||E.getTargetAgent()===B&&E.getSourceAgent()===b?(F=F||new Q).add(E,0):(F?D=D||new Q:_=_||new Q).add(E)}m.empty(),_&&m.addAll(_),F&&m.addAll(F),D&&m.addAll(D)}var k=m.indexOf(P),g=m.size(),P=P.s(G);return{side:w,index:k,size:g,offset:-P*(g-1)/2+P*k}}function I(m,P,k,_,t){var w=P.s(b);return F(P,k,_,k.getAgentEdges().toList(function(P){return m.isVisible(P)&&P.s(b)===w}),t)}function w(P,m,k){var _=m.getSourceAgent()===k?m.getTargetAgent():m.getSourceAgent(),k=k instanceof h.Edge?S(P,k,m):k.p(),m=(P=_ instanceof h.Edge?S(P,_,m):_.p()).x-k.x,_=P.y-k.y;return 0<m&&x(_)<=m?W:m<0&&x(_)<=-m?v:0<_&&x(m)<=_?q:M}function H(m,P,k){var _=P.s(b),t=w(m,P,k);return F(P,k,t,k.getAgentEdges().toList(function(P){return m.isVisible(P)&&P.s(b)===_&&w(m,P,k)===t}))}function D(P,m,k){var _=m.getSourceAgent()===k,t=_?m.getTargetAgent():m.getSourceAgent(),w=k instanceof h.Edge,F=t instanceof h.Edge,w=w?S(P,k,m):k.p(),k=F?S(P,t,m):t.p();return _?w.y>k.y?M:q:w.x<k.x?W:v}function t(m,P,k){var _=P.s(b),t=D(m,P,k);return F(P,k,t,k.getAgentEdges().toList(function(P){return m.isVisible(P)&&P.s(b)===_&&D(m,P,k)===t}),t===W||t===q)}function B(P,m,k){var _=m.getSourceAgent()===k,t=_?m.getTargetAgent():m.getSourceAgent(),w=k instanceof h.Edge,F=t instanceof h.Edge,w=w?S(P,k,m):k.p(),k=F?S(P,t,m):t.p();return _?w.x<k.x?W:v:w.y>k.y?M:q}function L(m,P,k){var _=P.s(b),t=B(m,P,k);return F(P,k,t,k.getAgentEdges().toList(function(P){return m.isVisible(P)&&P.s(b)===_&&B(m,P,k)===t}),t===W||t===q)}function _(P,m,k){var _=P.getSourceAgent(),t=P.getTargetAgent(),w=_.getId()>t.getId(),F=w?t:_,_=w?_:t,t=F instanceof h.Edge,D=_ instanceof h.Edge,B=t?S(m,F,P):F.p(),b=D?S(m,_,P):_.p(),L=k(m,P,F),k=k(m,P,_),E=(I=P.s(R))||t?0:$(m,F)/2,g=I||D?0:$(m,_)/2,t=I||t?0:Z(m,F)/2,F=I||D?0:Z(m,_)/2,I=L.offset,D=k.offset,m=L.side,_=k.side,L=new Q;return m===M?(L.add({x:B.x+I,y:B.y-t}),L.add({x:B.x+I,y:b.y+D}),_===v?L.add({x:b.x-g,y:b.y+D}):L.add({x:b.x+g,y:b.y+D})):m===q?(L.add({x:B.x+I,y:B.y+t}),L.add({x:B.x+I,y:b.y+D}),_===v?L.add({x:b.x-g,y:b.y+D}):L.add({x:b.x+g,y:b.y+D})):m===v?(L.add({x:B.x-E,y:B.y+I}),L.add({x:b.x+D,y:B.y+I}),_===q?L.add({x:b.x+D,y:b.y+F}):L.add({x:b.x+D,y:b.y-F})):m===W&&(L.add({x:B.x+E,y:B.y+I}),L.add({x:b.x+D,y:B.y+I}),_===q?L.add({x:b.x+D,y:b.y+F}):L.add({x:b.x+D,y:b.y-F})),w&&L.reverse(),T(L,P)}var h=P.ht,P=Math,r=P.max,u=P.min,x=P.abs,s=P.atan2,y=(P.cos,P.sin,P.ceil),N=h.Default,k=N.getInternal(),Q=h.List,X=k.Mat,p=k.getNodeRect,j=k.intersectionLineRect,V=N.getDistance,P=N.setEdgeType,m=(N.unionRect,N._edgeProtectMethod),E=m.getStraightLinePoints,g=m.getPercentPosition,v="left",W="right",M="top",q="bottom",b="edge.type",G="edge.gap",R="edge.center",K="edge.extend",S=function(P,m,k){P=P.getDataUI(m),m=E(P),P=g(m,50);return P||(k.iv(),{})};k.addMethod(h.Style,{"edge.ripple.elevation":-20,"edge.ripple.size":1,"edge.ripple.both":!1,"edge.ripple.straight":!1,"edge.ripple.length":-1,"edge.corner.radius":-1,"edge.ortho":.5,"edge.flex":20,"edge.extend":20},!0),P("boundary",function(P,m,k,_){_||(m=-m);var _=f(k,P),t=n(k,P),w=P.getSource(),F=P.getTarget(),D=P.getSource()instanceof h.Edge,B=P.getTarget()instanceof h.Edge,b=new X(s(t.y-_.y,t.x-_.x)),L=V(_,t),E=_.x,g=_.y,I=b.tf(0,m),_={x:I.x+E,y:I.y+g},t={x:(I=b.tf(L,m)).x+E,y:I.y+g};return D?_={x:(I=S(k,w,P)).x,y:I.y}:(b=p(k,w),(I=j(_,t,b))&&(_={x:I[0],y:I[1]})),B?t={x:(I=S(k,F,P)).x,y:I.y}:(L=p(k,F),(I=j(_,t,L))&&(t={x:I[0],y:I[1]})),{points:new Q([_,t])}}),P("ripple",function(P,m,k,_){_||(m=-m);var t=f(k,P),k=n(k,P),w=V(t,k),F=u(P.s("edge.offset"),w/2),D=P.s("edge.ripple.size"),B=P.s("edge.ripple.length"),b=P.s("edge.ripple.both"),L=P.s(R),E=P.s("edge.ripple.elevation"),g=new Q,I=P.s("edge.ripple.straight")?null:new Q,r=new X(s(k.y-t.y,k.x-t.x)),c=(_||(E=-E),(w-=2*F)/(D=0<B?y(w/B):D));I&&I.add(1);for(var N=0;N<D;N++)I&&I.add(3),0===N?g.add({x:F+c*N,y:L?0:m}):g.add({x:F+c*N,y:m}),g.add({x:F+c*N+c/2,y:E+m}),b&&(E=-E);for(g.add({x:F+w,y:L?0:m}),N=0;N<g.size();N++){var e=r.tf(g.get(N));e.x+=t.x,e.y+=t.y,g.set(N,e)}return{points:g,segments:I}}),P("h.v",function(P,m,k){m=c(P,k);var _=new Q,t=P.s(R),w=f(k,P),F=w.x,w=w.y,D=n(k,P),B=D.x,D=D.y,b=P.getSource()instanceof h.Edge,L=P.getTarget()instanceof h.Edge,E=0,g=0,I=B-F,r=D-w;return t||(E=b?0:$(k,P.getSource())/2,g=L?0:Z(k,P.getTarget())/2),0<=I&&r<=0?(_.add({x:F+E,y:w+m}),_.add({x:B+m,y:w+m}),_.add({x:B+m,y:D+g})):I<=0&&0<=r?(_.add({x:F-E,y:w+m}),_.add({x:B+m,y:w+m}),_.add({x:B+m,y:D-g})):0<=I&&0<=r?(_.add({x:F+E,y:w+m}),_.add({x:B-m,y:w+m}),_.add({x:B-m,y:D-g})):(_.add({x:F-E,y:w+m}),_.add({x:B-m,y:w+m}),_.add({x:B-m,y:D+g})),T(_,P)}),P("v.h",function(P,m,k){m=c(P,k);var _=new Q,t=P.s(R),w=f(k,P),F=w.x,w=w.y,D=n(k,P),B=D.x,D=D.y,b=P.getSource()instanceof h.Edge,L=P.getTarget()instanceof h.Edge,E=0,g=0,I=B-F,r=D-w;return t||(E=L?0:$(k,P.getTarget())/2,g=b?0:Z(k,P.getSource())/2),0<=I&&r<=0?(_.add({x:F+m,y:w-g}),_.add({x:F+m,y:D+m}),_.add({x:B-E,y:D+m})):I<=0&&0<=r?(_.add({x:F+m,y:w+g}),_.add({x:F+m,y:D+m}),_.add({x:B+E,y:D+m})):0<=I&&0<=r?(_.add({x:F-m,y:w+g}),_.add({x:F-m,y:D+m}),_.add({x:B-E,y:D+m})):(_.add({x:F-m,y:w-g}),_.add({x:F-m,y:D+m}),_.add({x:B+E,y:D+m})),T(_,P)}),P("ortho",function(P,m,k){var _=new Q,t=P.s(R),w=P.s("edge.ortho"),F=P.getSource(),D=P.getTarget(),B=f(k,P),b=B.x,B=B.y,L=n(k,P),E=L.x,L=L.y,g=E-b,I=L-B,r=F instanceof h.Edge,c=D instanceof h.Edge,N=t||r?0:$(k,F)/2,r=t||r?0:Z(k,F)/2,F=t||c?0:$(k,D)/2,t=t||c?0:Z(k,D)/2,c=(g-(N+F)*(0<g?1:-1))*w,k=(I-(r+t)*(0<I?1:-1))*w;return x(g)<x(I)?0<=g&&I<=0?(_.add({x:b+m,y:B-r}),_.add({x:b+m,y:B+k+m-r}),_.add({x:E+m,y:B+k+m-r}),_.add({x:E+m,y:L+t})):g<=0&&0<=I?(_.add({x:b+m,y:B+r}),_.add({x:b+m,y:B+k+m+r}),_.add({x:E+m,y:B+k+m+r}),_.add({x:E+m,y:L-t})):0<=g&&0<=I?(_.add({x:b+m,y:B+r}),_.add({x:b+m,y:B+k-m+r}),_.add({x:E+m,y:B+k-m+r}),_.add({x:E+m,y:L-t})):(_.add({x:b+m,y:B-r}),_.add({x:b+m,y:B+k-m-r}),_.add({x:E+m,y:B+k-m-r}),_.add({x:E+m,y:L+t})):0<=g&&I<=0?(_.add({x:b+N,y:B+m}),_.add({x:b+c+m+N,y:B+m}),_.add({x:b+c+m+N,y:L+m}),_.add({x:E-F,y:L+m})):g<=0&&0<=I?(_.add({x:b-N,y:B+m}),_.add({x:b+c+m-N,y:B+m}),_.add({x:b+c+m-N,y:L+m}),_.add({x:E+F,y:L+m})):0<=g&&0<=I?(_.add({x:b+N,y:B+m}),_.add({x:b+c-m+N,y:B+m}),_.add({x:b+c-m+N,y:L+m}),_.add({x:E-F,y:L+m})):(_.add({x:b-N,y:B+m}),_.add({x:b+c-m-N,y:B+m}),_.add({x:b+c-m-N,y:L+m}),_.add({x:E+F,y:L+m})),T(_,P)}),P("flex",function(P,m,k){var _=new Q,t=P.s("edge.flex")+e(P,k),w=P.s(R),F=P.getSource(),D=P.getTarget(),B=f(k,P),b=B.x,B=B.y,L=n(k,P),E=L.x,L=L.y,g=F instanceof h.Edge,I=D instanceof h.Edge,r=E-b,c=L-B,N=w||g?0:$(k,F)/2,g=w||g?0:Z(k,F)/2,F=w||I?0:$(k,D)/2,w=w||I?0:Z(k,D)/2,I=0<r?t:-t,k=0<c?t:-t;return x(r)<x(c)?0<=r&&c<=0?(_.add({x:b+m,y:B-g}),_.add({x:b+m,y:B+k+m-g}),_.add({x:E+m,y:L-k+m+w}),_.add({x:E+m,y:L+w})):r<=0&&0<=c?(_.add({x:b+m,y:B+g}),_.add({x:b+m,y:B+k+m+g}),_.add({x:E+m,y:L-k+m-w}),_.add({x:E+m,y:L-w})):0<=r&&0<=c?(_.add({x:b+m,y:B+g}),_.add({x:b+m,y:B+k-m+g}),_.add({x:E+m,y:L-k-m-w}),_.add({x:E+m,y:L-w})):(_.add({x:b+m,y:B-g}),_.add({x:b+m,y:B+k-m-g}),_.add({x:E+m,y:L-k-m+w}),_.add({x:E+m,y:L+w})):0<=r&&c<=0?(_.add({x:b+N,y:B+m}),_.add({x:b+I+m+N,y:B+m}),_.add({x:E-I+m-F,y:L+m}),_.add({x:E-F,y:L+m})):r<=0&&0<=c?(_.add({x:b-N,y:B+m}),_.add({x:b+I+m-N,y:B+m}),_.add({x:E-I+m+F,y:L+m}),_.add({x:E+F,y:L+m})):0<=r&&0<=c?(_.add({x:b+N,y:B+m}),_.add({x:b+I-m+N,y:B+m}),_.add({x:E-I-m-F,y:L+m}),_.add({x:E-F,y:L+m})):(_.add({x:b-N,y:B+m}),_.add({x:b+I-m-N,y:B+m}),_.add({x:E-I-m+F,y:L+m}),_.add({x:E+F,y:L+m})),T(_,P)}),P("extend.east",function(P,m,k){var _=new Q,t=P.s(K)+e(P,k),w=P.s(R),F=f(k,P),D=P.getSource()instanceof h.Edge,B=P.getTarget()instanceof h.Edge,D=F.x+(w||D?0:$(k,P.getSource())/2),F=F.y,b=n(k,P),w=b.x+(w||B?0:$(k,P.getTarget())/2),B=b.y,k=r(D,w)+t;return B<F?(_.add({x:D,y:F+m}),_.add({x:k+m,y:F+m}),_.add({x:k+m,y:B-m}),_.add({x:w,y:B-m})):(_.add({x:D,y:F-m}),_.add({x:k+m,y:F-m}),_.add({x:k+m,y:B+m}),_.add({x:w,y:B+m})),T(_,P)}),P("extend.west",function(P,m,k){var _=new Q,t=P.s(K)+e(P,k),w=P.s(R),F=P.getSource()instanceof h.Edge,D=P.getTarget()instanceof h.Edge,B=f(k,P),F=B.x-(w||F?0:$(k,P.getSource())/2),B=B.y,b=n(k,P),w=b.x-(w||D?0:$(k,P.getTarget())/2),D=b.y,k=u(F,w)-t;return D<B?(_.add({x:F,y:B+m}),_.add({x:k-m,y:B+m}),_.add({x:k-m,y:D-m}),_.add({x:w,y:D-m})):(_.add({x:F,y:B-m}),_.add({x:k-m,y:B-m}),_.add({x:k-m,y:D+m}),_.add({x:w,y:D+m})),T(_,P)}),P("extend.north",function(P,m,k){var _=new Q,t=P.s(K)+e(P,k),w=P.s(R),F=P.getSource()instanceof h.Edge,D=P.getTarget()instanceof h.Edge,B=f(k,P),b=B.x,B=B.y-(w||F?0:Z(k,P.getSource())/2),F=n(k,P),L=F.x,F=F.y-(w||D?0:Z(k,P.getTarget())/2),w=u(B,F)-t;return L<b?(_.add({y:B,x:b+m}),_.add({y:w-m,x:b+m}),_.add({y:w-m,x:L-m}),_.add({y:F,x:L-m})):(_.add({y:B,x:b-m}),_.add({y:w-m,x:b-m}),_.add({y:w-m,x:L+m}),_.add({y:F,x:L+m})),T(_,P)}),P("extend.south",function(P,m,k){var _=new Q,t=P.s(K)+e(P,k),w=P.s(R),F=P.getSource()instanceof h.Edge,D=P.getTarget()instanceof h.Edge,B=f(k,P),b=B.x,B=B.y+(w||F?0:Z(k,P.getSource())/2),F=n(k,P),L=F.x,F=F.y+(w||D?0:Z(k,P.getTarget())/2),w=r(B,F)+t;return L<b?(_.add({y:B,x:b+m}),_.add({y:w+m,x:b+m}),_.add({y:w+m,x:L-m}),_.add({y:F,x:L-m})):(_.add({y:B,x:b-m}),_.add({y:w+m,x:b-m}),_.add({y:w+m,x:L+m}),_.add({y:F,x:L+m})),T(_,P)});P("ortho2",function(P,m,k,_){var t,w,F=P.s(R),D=P.s("edge.ortho"),B=P.getSourceAgent(),b=P.getTargetAgent(),L=B.getId()>b.getId(),E=L?b:B,B=L?B:b,b=E instanceof h.Edge,g=B instanceof h.Edge,I=b?S(k,E,P):E.p(),r=g?S(k,B,P):B.p(),c=H(k,P,E),N=H(k,P,B),e=F||b?0:$(k,E)/2,b=F||b?0:Z(k,E)/2,E=F||g?0:$(k,B)/2,F=F||g?0:Z(k,B)/2,g=new Q,k=c.offset,B=N.offset,N=c.side;return N===W?(t=r.y>I.y?-k:k,w=I.x+e+(r.x-E-I.x-e)*D,g.add({x:I.x+e,y:I.y+k}),g.add({x:w+t,y:I.y+k}),g.add({x:w+t,y:r.y+B}),g.add({x:r.x-E,y:r.y+B})):N===v?(t=r.y>I.y?-k:k,w=I.x-e-(I.x-e-r.x-E)*D,g.add({x:I.x-e,y:I.y+k}),g.add({x:w-t,y:I.y+k}),g.add({x:w-t,y:r.y+B}),g.add({x:r.x+E,y:r.y+B})):N===q?(t=r.x>I.x?-k:k,w=I.y+b+(r.y-F-I.y-b)*D,g.add({x:I.x+k,y:I.y+b}),g.add({x:I.x+k,y:w+t}),g.add({x:r.x+B,y:w+t}),g.add({x:r.x+B,y:r.y-F})):N===M&&(t=r.x>I.x?-k:k,w=I.y-b-(I.y-b-r.y-F)*D,g.add({x:I.x+k,y:I.y-b}),g.add({x:I.x+k,y:w-t}),g.add({x:r.x+B,y:w-t}),g.add({x:r.x+B,y:r.y+F})),L&&g.reverse(),T(g,P)},!0),P("flex2",function(P,m,k,_){var t,w=P.getSourceAgent(),F=P.getTargetAgent(),D=w.getId()>F.getId(),B=D?F:w,w=D?w:F,F=B instanceof h.Edge,b=w instanceof h.Edge,L=F?S(k,B,P):B.p(),E=b?S(k,w,P):w.p(),g=H(k,P,B),I=H(k,P,w),r=P.s(R),c=P.s("edge.flex")+(g.size-1)/2*P.s(G),N=r||F?0:$(k,B)/2,F=r||F?0:Z(k,B)/2,B=r||b?0:$(k,w)/2,r=r||b?0:Z(k,w)/2,b=new Q,k=g.offset,w=I.offset,I=g.side;return I===W?(t=E.y>L.y?-k:k,b.add({x:L.x+N,y:L.y+k}),b.add({x:L.x+N+c+t,y:L.y+k}),b.add({x:E.x-B-c+t,y:E.y+w}),b.add({x:E.x-B,y:E.y+w})):I===v?(t=E.y>L.y?-k:k,b.add({x:L.x-N,y:L.y+k}),b.add({x:L.x-N-c-t,y:L.y+k}),b.add({x:E.x+B+c-t,y:E.y+w}),b.add({x:E.x+B,y:E.y+w})):I===q?(t=E.x>L.x?-k:k,b.add({x:L.x+k,y:L.y+F}),b.add({x:L.x+k,y:L.y+F+c+t}),b.add({x:E.x+w,y:E.y-r-c+t}),b.add({x:E.x+w,y:E.y-r})):I===M&&(t=E.x>L.x?-k:k,b.add({x:L.x+k,y:L.y-F}),b.add({x:L.x+k,y:L.y-F-c-t}),b.add({x:E.x+w,y:E.y+r+c-t}),b.add({x:E.x+w,y:E.y+r})),D&&b.reverse(),T(b,P)},!0),P("extend.north2",function(P,m,k){var _=P.getSourceAgent(),t=P.getTargetAgent(),w=_.getId()>t.getId(),F=w?t:_,_=w?_:t,t=F instanceof h.Edge,D=_ instanceof h.Edge,B=t?S(k,F,P):F.p(),b=D?S(k,_,P):_.p(),L=I(k,P,F,M),E=I(k,P,_,M,!0),g=P.s(R),t=g||t?0:Z(k,F)/2,F=g||D?0:Z(k,_)/2,g=L.offset,D=E.offset,k=P.s(K)+(L.size-1)/2*P.s(G),_=u(B.y-t,b.y-F)-k+(B.x<b.x?g:-g),E=new Q;return E.add({x:B.x+g,y:B.y-t}),E.add({x:B.x+g,y:_}),E.add({x:b.x+D,y:_}),E.add({x:b.x+D,y:b.y-F}),w&&E.reverse(),T(E,P)},!0),P("extend.south2",function(P,m,k){var _=P.getSourceAgent(),t=P.getTargetAgent(),w=_.getId()>t.getId(),F=w?t:_,_=w?_:t,t=F instanceof h.Edge,D=_ instanceof h.Edge,B=t?S(k,F,P):F.p(),b=D?S(k,_,P):_.p(),L=I(k,P,F,q),E=I(k,P,_,q,!0),g=P.s(R),t=g||t?0:Z(k,F)/2,F=g||D?0:Z(k,_)/2,g=L.offset,D=E.offset,k=P.s(K)+(L.size-1)/2*P.s(G),_=r(B.y+t,b.y+F)+k+(B.x>b.x?g:-g),E=new Q;return E.add({x:B.x+g,y:B.y+t}),E.add({x:B.x+g,y:_}),E.add({x:b.x+D,y:_}),E.add({x:b.x+D,y:b.y+F}),w&&E.reverse(),T(E,P)},!0),P("extend.west2",function(P,m,k){var _=P.getSourceAgent(),t=P.getTargetAgent(),w=_.getId()>t.getId(),F=w?t:_,_=w?_:t,t=F instanceof h.Edge,D=_ instanceof h.Edge,B=t?S(k,F,P):F.p(),b=D?S(k,_,P):_.p(),L=I(k,P,F,M),E=I(k,P,_,M,!0),g=P.s(R),t=g||t?0:$(k,F)/2,F=g||D?0:$(k,_)/2,g=L.offset,D=E.offset,k=P.s(K)+(L.size-1)/2*P.s(G),_=u(B.x-t,b.x-F)-k+(B.y<b.y?g:-g),E=new Q;return E.add({x:B.x-t,y:B.y+g}),E.add({x:_,y:B.y+g}),E.add({x:_,y:b.y+D}),E.add({x:b.x-F,y:b.y+D}),w&&E.reverse(),T(E,P)},!0),P("extend.east2",function(P,m,k){var _=P.getSourceAgent(),t=P.getTargetAgent(),w=_.getId()>t.getId(),F=w?t:_,_=w?_:t,t=F instanceof h.Edge,D=_ instanceof h.Edge,B=t?S(k,F,P):F.p(),b=D?S(k,_,P):_.p(),L=I(k,P,F,M),E=I(k,P,_,M,!0),g=P.s(R),t=g||t?0:$(k,F)/2,F=g||D?0:$(k,_)/2,g=L.offset,D=E.offset,k=P.s(K)+(L.size-1)/2*P.s(G),_=r(B.x+t,b.x+F)+k+(B.y>b.y?g:-g),E=new Q;return E.add({x:B.x+t,y:B.y+g}),E.add({x:_,y:B.y+g}),E.add({x:_,y:b.y+D}),E.add({x:b.x+F,y:b.y+D}),w&&E.reverse(),T(E,P)},!0),P("v.h2",function(P,m,k){return _(P,k,t)},!0),P("h.v2",function(P,m,k){return _(P,k,L)},!0)}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);