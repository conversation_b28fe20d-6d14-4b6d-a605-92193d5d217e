
<!DOCTYPE html>
<html>
    <head>
        <title>Icons 3D</title>
        <meta charset="UTF-8">
        <style>
            html, body {
                padding: 0;
                margin: 0;
            }
            .main {
                background: black;
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
            .formpane {
                top: 10px;
                right: 10px;
                background: rgba(230, 230, 230, 0.5);
            }
        </style>

        <script src="../../../../lib/core/ht.js"></script>
        <script src="../../../../lib/plugin/ht-form.js"></script>
        <script>

            ht.Default.setImage('book0', 'data:image/jpeg;base64,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');
            ht.Default.setImage('book1', 'data:image/jpeg;base64,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');
            ht.Default.setImage('book2', 'data:image/jpeg;base64,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');

            function init(){
                dataModel = new ht.DataModel();
                g3d = new ht.graph3d.Graph3dView(dataModel);
                g3d.setGridVisible(true);

                view = g3d.getView();
                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function (e) {
                    g3d.iv();
                }, false);

                createFormPane();
                formPane.getView().className = 'formpane';
                document.body.appendChild(formPane.getView());

                createModel();

                g3d.walk(400, true);
                update();
            }

            function createModel(){
                node = new ht.Node();
                node.p3(-200, 100, -60);
                node.s3(150, 50, 80);
                node.s({
                    'shape': 'rect',
                    'all.transparent': true,
                    'all.reverse.cull': true,
                    'all.opacity': 0.7,
                    'wf.visible': true,
                    'wf.color': 'yellow'
                });

                n1 = new ht.Node();
                n2 = new ht.Node();
                n1.p3(0, 100, -60);
                n2.p3(300, 100, -60);
                n1.s3(16, 16, 16);
                n2.s3(16, 16, 16);

                edge1 = new ht.Edge(n1, n2);
                edge1.s({
                    'edge.gap': 80,
                    'edge.center': true
                });

                edge2 = new ht.Edge(n1, n2);
                edge2.s({
                    'edge.gap': 80,
                    'edge.center': true
                });

                n3 = new ht.Node();
                n4 = new ht.Node();
                n3.p3(-200, 50, 120);
                n4.p3(200, 50, 120);
                n3.s3(16, 16, 16);
                n4.s3(16, 16, 16);

                edge3 = new ht.Edge(n3, n4);
                edge3.s({
                    'edge.center': false,
                    'edge.offset': 20,
                    'edge.type': 'points',
                    'edge.points': [{x: -100, y: 160, e: 50}, {x: 100, y: 160, e: 50}]
                });

                polyline = new ht.Polyline();
                polyline.setPoints([
                    {x: -200, y: 320},
                    {x: -100, y: 260},
                    {x: 100, y: 260},
                    {x: 200, y: 320}
                ]);

                dataModel.add(node);
                dataModel.add(n1);
                dataModel.add(n2);
                dataModel.add(n3);
                dataModel.add(n4);
                dataModel.add(edge1);
                dataModel.add(edge2);
                dataModel.add(edge3);
                dataModel.add(polyline);
            }

            function update(){
                dataModel.each(function(data){
                    if(data === n1 || data === n2 || data === n3 || data === n4){
                        return;
                    }
                    data.addStyleIcon('books', {
                        names: ['book0', 'book1', 'book2'],
                        face: formPane.v('face'),
                        position: formPane.v('position'),
                        t3: [formPane.v('tx'), formPane.v('ty'), formPane.v('tz')],
                        r3: [formPane.v('rx'), formPane.v('ry'), formPane.v('rz')],
                        rotationMode: formPane.v('rotationMode'),
                        autorotate: formPane.v('autorotate'),
                        gap: formPane.v('gap'),
                        direction: formPane.v('direction'),
                        textureScale: formPane.v('texScale'),
                        width: 24,
                        height: 30
                    });
                });
            }

            function createFormPane(){
                formPane = new ht.widget.FormPane();
                formPane.setWidth(230);
                formPane.setHeight(354);
                formPane.setLabelAlign('right');

                formPane.addRow([
                    'face',
                    {
                        id: 'face',
                        comboBox: {
                            value: 'front',
                            values: ['front', 'back', 'left', 'right', 'top', 'bottom', 'center'],
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'position',
                    {
                        id: 'position',
                        slider: {
                            min: 1,
                            max: 55,
                            value: 17,
                            step: 1,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'texScale',
                    {
                        id: 'texScale',
                        slider: {
                            min: 1,
                            max: 8,
                            value: 5,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'tx',
                    {
                        id: 'tx',
                        slider: {
                            min: -100,
                            max: 100,
                            value: 0,
                            step: 1,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'ty',
                    {
                        id: 'ty',
                        slider: {
                            min: -100,
                            max: 100,
                            value: 0,
                            step: 1,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'tz',
                    {
                        id: 'tz',
                        slider: {
                            min: -100,
                            max: 100,
                            value: 0,
                            step: 1,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'rx',
                    {
                        id: 'rx',
                        slider: {
                            min: -Math.PI,
                            max: Math.PI,
                            value: 0,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'ry',
                    {
                        id: 'ry',
                        slider: {
                            min: -Math.PI,
                            max: Math.PI,
                            value: 0,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'rz',
                    {
                        id: 'rz',
                        slider: {
                            min: -Math.PI,
                            max: Math.PI,
                            value: 0,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'rm',
                    {
                        id: 'rotationMode',
                        comboBox: {
                            value: 'xzy',
                            values: ['xyz', 'xzy', 'yxz', 'yzx', 'zxy', 'zyx'],
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'autorotate',
                    {
                        id: 'autorotate',
                        comboBox: {
                            value: false,
                            values: [false, 'y', true],
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'gap',
                    {
                        id: 'gap',
                        slider: {
                            min: 0,
                            max: 50,
                            value: 2,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'direction',
                    {
                        id: 'direction',
                        comboBox: {
                            value: 'east',
                            values: ['west', 'east', 'north', 'south'],
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
