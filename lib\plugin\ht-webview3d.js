!function(Y){"use strict";var J=Y.ht,g=<PERSON><PERSON>,u=g.getInternal(),A=u.superCall,Y=J.graph3d.Graph3dView,R=Y.prototype.validateImpl;function M(o){var Q,s,t,m,d,c,j=[],D=0,$=g.createDiv(!0),R=o.getView(),V=(R.insertBefore($,o.getCanvas()),(Q=J.Default.createDiv()).style.WebkitTransformStyle="preserve-3d",Q.style.MozTransformStyle="preserve-3d",Q.style.transformStyle="preserve-3d",$.appendChild(Q),[{event:"mousedown",style:"none"},{event:"mouseup",style:"auto"},{event:"touchstart",style:"none"},{event:"touchend",style:"auto"}].forEach(function(Y){var A=Y.style;u.addEventListener(R,Y.event,function(){o.isDragProtectForWebView()&&($.style.pointerEvents=A)})}),new J.Math.Vector3(0,0,1)),l=new J.Math.Vector3(0,0,1),p=new J.Math.Quaternion,v=new J.Math.Matrix4,O=J.Math.randomFloat,b=new J.Math.Euler,T=new J.Math.Matrix4,I=new J.Math.Matrix4,K=new J.Math.Matrix4,G=(this.updateWebView=function(){if(o.getWidth()&&o.getHeight()){var Y,A,R,u,M,n=o.getCanvas(),v=+n.style.width.slice(0,-2),n=+n.style.height.slice(0,-2),v=(j[0]===v&&j[1]===n||(j[0]=v,j[1]=n,$.style.width=v+"px",$.style.height=n+"px",Q.style.width=v+"px",Q.style.height=n+"px"),o._projectMatrix[5]*j[1]/2),G=(v!==s&&(s=v,$.style.WebkitPerspective=s+"px",$.style.MozPerspective=s+"px",$.style.perspective=s+"px"),[]),g=(o.dm().each(function(Y){Y.isWebView&&G.push(Y)}),Y=G.length,100),_=o.getCamera();do{for(M=!0,a=0;a<=Y;a++)if((a===Y?_:G[a]).getQuaternion(p),A&&p.premultiply(A),R=l.set(1,0,0).applyQuaternion(p).dot(V),u=l.set(0,1,0).applyQuaternion(p).dot(V),Math.abs(Math.abs(R)-1)<.1||Math.abs(Math.abs(u)-1)<.1){M=!1,c?(A=c,c=null):(A=A||new J.Math.Quaternion,b.set(0,O(-Math.PI,Math.PI),0),A.setFromEuler(b));break}}while(g--&&!M);for(var q,n=o._viewMatrix,n=(A&&(n=T.makeRotationFromQuaternion(A.inverse()).premultiply(n).toArray(),T.makeRotationFromQuaternion(A.inverse())),I.fromArray(n),I.setPosition(l.set(0,0,-100)),K.copy(I).invert().multiply(n),n=I.toArray(),(d="translateZ("+s+"px)"+("matrix3d("+e((v=n)[0])+","+e(-v[1])+","+e(v[2])+","+e(v[3])+","+e(v[4])+","+e(-v[5])+","+e(v[6])+","+e(v[7])+","+e(v[8])+","+e(-v[9])+","+e(v[10])+","+e(v[11])+","+e(v[12])+","+e(-v[13])+","+e(v[14])+","+e(v[15])+")"))+"translate("+j[0]/2+"px,"+j[1]/2+"px)"),z=(t===n||m||(Q.style.WebkitTransform=n,Q.style.MozTransform=n,Q.style.transform=n,t=n),D++,G.forEach(function(Y){f(Y,A?T:null,K)}),Q.children),i=[],a=0,k=z.length;a<k;a++)(q=z[a])._isHtWebView&&q._renderCookie!==D&&i.push(q);i.length&&i.forEach(function(Y){Q.removeChild(Y)}),c=A}},new Array(16)),f=function(Y,A,R){var u,M,n=Y.getAttach();n&&((M=o.isVisible(Y))&&n.parentNode!==Q?Q.appendChild(n):!M&&n.parentNode&&g.removeHTML(n),M&&(n._renderCookie=D,M=Y.getFinalScale3d(),(u=Y._prefrenceSize)&&u[0]?u[1]||(u[1]=u[0]/M[0]*M[1]):u=[M[0],M[1]],n.style.width=u[0]+"px",n.style.height=u[1]+"px",M=1/u[0],u=1/u[1],Y=Y.mat,G[0]=Y[0]*M,G[1]=Y[1]*M,G[2]=Y[2]*M,G[3]=Y[3]*M,G[4]=Y[4]*u,G[5]=Y[5]*u,G[6]=Y[6]*u,G[7]=Y[7]*u,G[8]=+Y[8],G[9]=+Y[9],G[10]=+Y[10],G[11]=+Y[11],G[12]=Y[12],G[13]=Y[13],G[14]=Y[14],G[15]=Y[15],A&&(G=v.multiplyMatrices(A,G).toArray()),M=function(Y,A){Y="matrix3d("+e(Y[0])+","+e(Y[1])+","+e(Y[2])+","+e(Y[3])+","+e(-Y[4])+","+e(-Y[5])+","+e(-Y[6])+","+e(-Y[7])+","+e(Y[8])+","+e(Y[9])+","+e(Y[10])+","+e(Y[11])+","+e(Y[12])+","+e(Y[13])+","+e(Y[14])+","+e(Y[15])+")";return"translate(-50%,-50%)"+(A||"")+Y}(G=R?v.multiplyMatrices(R,G).toArray():G,m?"translate("+j[0]/2+"px,"+j[1]/2+"px)"+d:""),n.$a2!==M&&(n.$a2=M,n.style.WebkitTransform=M,n.style.MozTransform=M,n.style.transform=M)))}}function e(Y){return Math.abs(Y)<1e-10?0:Y}Y.prototype.validateImpl=function(){R.call(this),(this._webViewRenderer||(this._webViewRenderer=new M(this))).updateWebView()},Y.prototype.isDragProtectForWebView=function(){return this._dragProtectForWebView},Y.prototype.setDragProtectForWebView=function(Y){this._dragProtectForWebView=Y},J.Default.setShader("htWebView3d",["attribute vec3 aPosition;","uniform mat4 uModelViewMatrix ;","uniform mat4 uProjectMatrix;","void main(void) {","gl_Position = uProjectMatrix * uModelViewMatrix  * vec4(aPosition, 1.0);","}","// FS","void main(void) {","gl_FragColor = vec4(0.0);","}"].join("\n"));var n={type:"htWebView3d"},v=J.WebView3d=function(){A(v,this),this.s({shape3d:"billboard","shape3d.reverse.flip":!0})};g.def("ht.WebView3d",J.Node,{ms_ac:["attach"],isWebView:!0,attachDOM:function(Y,A,R){if(!Y)return this.detachDOM();"string"==typeof Y&&((u=document.createElement("iframe")).src=Y,Y=u);var u=Y.style;u.position="absolute",u.outline=0,u.margin=0,Y._isHtWebView=!0,this.setAttach(Y),this._prefrenceSize=[A,R],this.s("shape3d.material",n)},detachDOM:function(){this.setAttach(null),this.s("shape3d.material",void 0)}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);