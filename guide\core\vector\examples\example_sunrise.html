<!DOCTYPE html>
<html>
    <head>
        <title>Sun Rise</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script>   
        <script>
            function init() {
                var dataModel = new ht.DataModel(),
                        graphView = new ht.graph.GraphView(dataModel),
                        view = graphView.getView();

                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function(e) {
                    graphView.invalidate();
                }, false);

                ht.Default.setImage('sunrise', {
                    width: 220,
                    height: 150,
                    comps: [
                        {
                            type: 'shape',
                            points: [10, 110, 10, 10, 210, 10, 210, 110],
                            segments: [1, 4],
                            background: 'yellow',
                            gradient: 'linear.north'
                        },
                        {
                            type: 'shape',
                            shadow: true,
                            points: [
                                30, 10,
                                30, 110,
                                30, 60,
                                90, 60,
                                90, 10,
                                90, 110,
                                130, 10,
                                190, 10,
                                160, 10,
                                160, 110
                            ],
                            segments: [
                                1, 2, 1, 2, 1, 2,
                                1, 2, 1, 2
                            ],
                            borderWidth: 10,
                            borderColor: '#1ABC9C',
                            borderCap: 'round'
                        },
                        {
                            type: 'shape',
                            points: [
                                10, 130,
                                35, 120,
                                60, 130,
                                85, 140,
                                110, 130,
                                135, 120,
                                160, 130,
                                185, 140,
                                210, 130
                            ],
                            segments: [
                                1, 3, 3, 3, 3
                            ],
                            borderWidth: 2,
                            borderColor: '#3498DB'
                        }
                    ]
                });

                var node = new ht.Node();
                node.setPosition(160, 110);
                node.setImage('sunrise');
                dataModel.add(node);

                ht.Default.setImage('group-sunrise', {
                    width: 240,
                    height: 160,
                    clip: true,
                    color: 'red',
                    comps: [
                        {
                            type: 'image',
                            name: 'sunrise',                            
                            rect: [0, 0, 120, 80],
                            opacity: 0.3
                        },
                        {
                            type: 'image',
                            name: 'sunrise',
                            rect: [120, 0, 120, 80],
                            rotation: Math.PI / 4                            
                        },
                        {
                            type: 'image',
                            name: 'sunrise',
                            rect: [0, 80, 120, 80],
                            shadow: true
                        },
                        {
                            type: 'image',
                            name: 'sunrise',
                            rect: [120, 80, 120, 80]
                        }
                    ]
                });

                node = new ht.Node();
                node.setPosition(460, 100);
                node.setImage('group-sunrise');
                dataModel.add(node);

                graphView.setEditable(true);
                graphView.getSelectionModel().setSelection(node);
            }


        </script>
    </head>
    <body onload="init();">
    </body>
</html>
