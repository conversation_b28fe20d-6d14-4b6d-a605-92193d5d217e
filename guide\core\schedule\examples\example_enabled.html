
<!DOCTYPE html>
<html>
    <head>
        <title>Enable Schedule Task</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0;
                margin: 0;                
            }            
            .formpane {
                top: 10px;
                right: 10px;   
                background: rgba(155, 155, 155, 0.3);
            }    
        </style>    
                               
        <script src="../../../../lib/core/ht.js"></script> 
        <script src="../../../../lib/plugin/ht-form.js"></script> 
                             
        <script>                                        

            function init(){
                ht.Default.setImage('alarm-triangle', {
                    width: 100,
                    height: 100,
                    visible: { func: '<EMAIL>'},
                    comps: [
                        {
                            type: 'triangle',
                            rect: [2, 2, 96, 96],
                            background: { 
                                value : 'gray',
                                func: '<EMAIL>'
                            }
                        },
                        {
                            type: 'rect',
                            rect: [45, 30, 10, 40],
                            background: 'white'
                        },                        
                        {
                            type: 'circle',
                            rect: [40, 72, 20, 20],
                            background: 'white'
                        }
                    ]
                });   
                
                ht.Default.setImage('alarm-circle', {
                    width: 100,
                    height: 100,
                    comps: [
                        {
                            type: 'circle',
                            rect: [1, 1, 98, 98],
                            background: { 
                                value : 'gray',
                                func: '<EMAIL>'
                            }
                        },
                        {
                            type: 'rect',
                            rect: [40, 10, 20, 50],
                            background: 'white'
                        },
                        {
                            type: 'circle',
                            rect: [35, 65, 30, 30],
                            background: 'white'
                        }                        
                    ]
                });                
                
                dataModel = new ht.DataModel();
                                
                g2d = new ht.graph.GraphView(dataModel);                                            
                g2d.addToDOM();
                
                formPane = new ht.widget.FormPane();  
                formPane.getView().className = 'formpane';
                document.body.appendChild(formPane.getView());                                                              
                
                blinkTask = {
                    interval: 300,
                    action: function(data){
                        if(data.a('blink.enabled')){
                            data.a('blink.visible', !data.a('blink.visible'));
                        }
                    }        
                };
                dataModel.addScheduleTask(blinkTask); 

                scaleTask = {
                    interval: 10,
                    action: function(data){   
                        var scale = data.s('label.scale');
                        if(g2d.isSelected(data)){
                            var shrink = data.a('scale.shrink');
                            if(shrink){
                                scale -= 0.015;
                                if(scale < 0.85){
                                    scale = 0.85;
                                    data.a('scale.shrink', false);
                                }                            
                            }else{
                                scale += 0.015;
                                if(scale > 1.15){
                                    scale = 1.15;
                                    data.a('scale.shrink', true);
                                } 
                            }
                            data.s('label.scale', scale); 
                        }else{
                            if(scale !== 1){
                                data.s('label.scale', 1);
                            }                            
                        }
                    }
                };
                dataModel.addScheduleTask(scaleTask); 
                
                rotationTask = {
                    interval: 50,
                    action: function(data){
                        if(data.a('rotation.enabled')){
                            data.a('rotation.angle', data.a('rotation.angle') + Math.PI/20);
                        }
                    }        
                };
                dataModel.addScheduleTask(rotationTask);                
                
                flowTask = {
                    interval: 40,
                    action: function(data){   
                        if(data instanceof ht.Edge){
                            data.s('edge.dash.offset', data.s('edge.dash.offset')-1);
                        }
                    }
                };
                dataModel.addScheduleTask(flowTask);                                 
                
                initDataModel();                
                initFormPane();                 
            }            

            function createNode(name, x){
                var node = new ht.Node();
                node.setName(name);
                node.setPosition(x, 70);
                node.s('2d.move.mode', 'x');
                dataModel.add(node); 
                return node;
            };
            function initDataModel(){
                
                var blinkNode = createNode('Blink Node', 80);
                blinkNode.a({
                    'blink.enabled': true, 
                    'blink.visible': true,
                    'alarm.color': 'red'
                });
                blinkNode.addStyleIcon('alarm', {
                    position: 24,
                    width: 18,
                    height: 18,
                    names: ['alarm-triangle']
                });                                        
                
                var rotationNode = createNode('Rotation Node', 300);
                rotationNode.a({                    
                    'rotation.enabled': true,
                    'rotation.angle': 0,
                    'alarm.color': 'orange'
                });
                rotationNode.addStyleIcon('alarm', {
                    position: 17,
                    width: 18,
                    height: 18,
                    names: ['alarm-circle'],
                    rotation: { func: '<EMAIL>'}
                });
                           
                for(var i=0; i<2; i++){
                    var edge = new ht.Edge(blinkNode, rotationNode);
                    edge.s({                     
                        'edge.width': 6,
                        'edge.dash': true,
                        'edge.dash.color': 'yellow'
                    });
                    dataModel.add(edge);                    
                }
                        
                 dataModel.sm().ss(rotationNode);       
            }

            function initFormPane(){ 
                formPane.setWidth(150);
                formPane.setHeight(90);
                
                formPane.addRow([
                    {
                        checkBox: {
                            label: 'Enable Blink',                 
                            selected: true,
                            onValueChanged: function(){
                                var selected = this.isSelected();
                                dataModel.each(function(data){
                                    if(data.getName() === 'Blink Node'){
                                        data.a('blink.enabled', selected);
                                        if(!selected){
                                            data.a('blink.visible', true);
                                        }
                                    }
                                });
                            }
                        }
                    }
                ], [0.1]);                  
                
                formPane.addRow([
                    {
                        checkBox: {
                            label: 'Enable Rotation',                 
                            selected: true,
                            onValueChanged: function(){
                                var selected = this.isSelected();
                                dataModel.each(function(data){
                                    if(data.getName() === 'Rotation Node'){
                                        data.a('rotation.enabled', selected);
                                        if(!selected){
                                            data.a('rotation.angle', 0);
                                        }
                                    }
                                });
                            }
                        }
                    }
                ], [0.1]);                 

                formPane.addRow([
                    {
                        checkBox: {
                            label: 'Enable Flow',                 
                            selected: true,
                            onValueChanged: function(){
                                flowTask.enabled = this.isSelected();
                            }
                        }
                    }
                ], [0.1]); 

            }                                                            
            
        </script>
    </head>
    <body onload="init();">
    </body>
</html>
