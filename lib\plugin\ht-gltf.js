!function(O,t,X){"use strict";var I=O.ht,n=<PERSON><PERSON>,l=n.getInternal(),$=(n.clone,n.def),G=I.WebGLConstants,k=I.Math.Matrix4,f=I.Math.Vector3;function R($){if("undefined"!=typeof TextDecoder)return(new TextDecoder).decode($);for(var G="",m=0,b=$.length;m<b;m++)G+=String.fromCharCode($[m]);try{return decodeURIComponent(escape(G))}catch($){return G}}var y=new RegExp("[\\[\\]\\.:\\/]","g");function W(){this.userData={},this.matrix=new k,this.matrixWorld=new k,W.superClass.constructor.apply(this,arguments)}function N($){for(var G in this.texture=!0,$)this[G]=$[G]}function Z($,G){this.mat=G,this.mesh=$,this.userData={},Z.superClass.constructor.apply(this,arguments)}function z(){this.uuid=I.Math.generateUUID(),this.name="",this.index=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null}$(W,I.Data,{add:function($){this.addChild($)},traverse:function($){n.traverse(this,$)},applyMatrix4:function($){this.matrix.premultiply($),this.getParent()?this.matrixWorld.multiplyMatrices(this.getParent().matrixWorld,this.matrix):this.matrixWorld.copy(this.matrix)}});var v=function(){this.type="pbr",this.setValues=function($){if($){for(var G in $)this[G]=$[G];return this}},this.clone=function(){var $,G=new v;for($ in this)this[$]&&(G[$]=this[$]);return G}},m=($(Z,W,{normalizeSkinWeights:function(){var $=new I.Math.Vector4,G=this.mesh.attributes.skinWeight;if(G)for(var m=0,b=G.count;m<b;m++){$.fromBufferAttribute(G,m);var C=1/$.manhattanLength();C!=1/0?$.multiplyScalar(C):$.set(1,0,0,0),G.setXYZW(m,$.x,$.y,$.z,$.w)}}}),new I.Math.Matrix4);function V($,G,m){this.name="",this.array=$,this.itemSize=G,this.count=$!==X?$.length/G:0,this.normalized=!0===m,this.usage=I.WebGLConstants.STATIC_DRAW,this.updateRange={offset:0,count:-1},this.version=0}$(z,t,{setIndex:function($){return Array.isArray($)?this.index=new(65535<arrayMax($)?K:c)($,1):this.index=$,this},setAttribute:function($,G){return this.attributes[$]=G,this},deleteAttribute:function($){return delete this.attributes[$],this},hasAttribute:function($){return this.attributes[$]!==X},applyMatrix4:function($){var G=this.attributes.position,G=(G!==X&&(G.applyMatrix4($),G.needsUpdate=!0),this.attributes.normal),m=(G!==X&&(m=(new I.Math.Matrix3).getNormalMatrix($),G.applyNormalMatrix(m),G.needsUpdate=!0),this.attributes.tangent);return m!==X&&(m.transformDirection($),m.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this},applyQuaternion:function($){return m.makeRotationFromQuaternion($),this.applyMatrix4(m),this},addGroup:function($,G,m){this.groups.push({start:$,count:G,materialIndex:m||0})},setFromPoints:function($){for(var G=[],m=0,b=$.length;m<b;m++){var C=$[m];G.push(C.x,C.y,C.z||0)}return this.setAttribute("position",new U(G,3)),this}}),t.defineProperty(V.prototype,"needsUpdate",{set:function($){!0===$&&this.version++}});var b=new f;function C($,G,m){V.call(this,new Int8Array($),G,m)}function u($,G,m){V.call(this,new Uint8Array($),G,m)}function p($,G,m){V.call(this,new Uint8ClampedArray($),G,m)}function e($,G,m){V.call(this,new Int16Array($),G,m)}function c($,G,m){V.call(this,new Uint16Array($),G,m)}function M($,G,m){V.call(this,new Int32Array($),G,m)}function K($,G,m){V.call(this,new Uint32Array($),G,m)}function U($,G,m){V.call(this,new Float32Array($),G,m)}function d($,G,m){V.call(this,new Float64Array($),G,m)}t.assign(V.prototype,{isBufferAttribute:!0,onUploadCallback:function(){},setUsage:function($){return this.usage=$,this},copy:function($){return this.name=$.name,this.array=new $.array.constructor($.array),this.itemSize=$.itemSize,this.count=$.count,this.normalized=$.normalized,this.usage=$.usage,this},copyAt:function($,G,m){$*=this.itemSize,m*=G.itemSize;for(var b=0,C=this.itemSize;b<C;b++)this.array[$+b]=G.array[m+b];return this},copyArray:function($){return this.array.set($),this},copyColorsArray:function($){for(var G=this.array,m=0,b=0,C=$.length;b<C;b++){var t=$[b];t===X?(G[m++]=1,G[m++]=1,G[m++]=1):(G[m++]=t.r,G[m++]=t.g,G[m++]=t.b)}return this},copyVector2sArray:function($){for(var G=this.array,m=0,b=0,C=$.length;b<C;b++){var t=$[b];t===X&&(t=new Vector2),G[m++]=t.x,G[m++]=t.y}return this},copyVector3sArray:function($){for(var G=this.array,m=0,b=0,C=$.length;b<C;b++){var t=$[b];t===X&&(t=new f),G[m++]=t.x,G[m++]=t.y,G[m++]=t.z}return this},copyVector4sArray:function($){for(var G=this.array,m=0,b=0,C=$.length;b<C;b++){var t=$[b];t===X&&(t=new Vector4),G[m++]=t.x,G[m++]=t.y,G[m++]=t.z,G[m++]=t.w}return this},applyMatrix3:function($){for(var G=0,m=this.count;G<m;G++)b.x=this.getX(G),b.y=this.getY(G),b.z=this.getZ(G),b.applyMatrix3($),this.setXYZ(G,b.x,b.y,b.z);return this},applyMatrix4:function($){for(var G=0,m=this.count;G<m;G++)b.x=this.getX(G),b.y=this.getY(G),b.z=this.getZ(G),b.applyMatrix4($),this.setXYZ(G,b.x,b.y,b.z);return this},applyNormalMatrix:function($){for(var G=0,m=this.count;G<m;G++)b.x=this.getX(G),b.y=this.getY(G),b.z=this.getZ(G),b.applyNormalMatrix($),this.setXYZ(G,b.x,b.y,b.z);return this},transformDirection:function($){for(var G=0,m=this.count;G<m;G++)b.x=this.getX(G),b.y=this.getY(G),b.z=this.getZ(G),b.transformDirection($),this.setXYZ(G,b.x,b.y,b.z);return this},set:function($,G){return this.array.set($,G=G===X?0:G),this},getX:function($){return this.array[$*this.itemSize]},setX:function($,G){return this.array[$*this.itemSize]=G,this},getY:function($){return this.array[$*this.itemSize+1]},setY:function($,G){return this.array[$*this.itemSize+1]=G,this},getZ:function($){return this.array[$*this.itemSize+2]},setZ:function($,G){return this.array[$*this.itemSize+2]=G,this},getW:function($){return this.array[$*this.itemSize+3]},setW:function($,G){return this.array[$*this.itemSize+3]=G,this},setXY:function($,G,m){return $*=this.itemSize,this.array[$+0]=G,this.array[$+1]=m,this},setXYZ:function($,G,m,b){return $*=this.itemSize,this.array[$+0]=G,this.array[$+1]=m,this.array[$+2]=b,this},setXYZW:function($,G,m,b,C){return $*=this.itemSize,this.array[$+0]=G,this.array[$+1]=m,this.array[$+2]=b,this.array[$+3]=C,this},onUpload:function($){return this.onUploadCallback=$,this},clone:function(){return new this.constructor(this.array,this.itemSize).copy(this)},toJSON:function(){return{itemSize:this.itemSize,type:this.array.constructor.name,array:Array.prototype.slice.call(this.array),normalized:this.normalized}}}),(C.prototype=t.create(V.prototype)).constructor=C,(u.prototype=t.create(V.prototype)).constructor=u,(p.prototype=t.create(V.prototype)).constructor=p,(e.prototype=t.create(V.prototype)).constructor=e,c.prototype=t.create(V.prototype),c.prototype.constructor=c,(M.prototype=t.create(V.prototype)).constructor=M,K.prototype=t.create(V.prototype),K.prototype.constructor=K,(U.prototype=t.create(V.prototype)).constructor=U,(d.prototype=t.create(V.prototype)).constructor=d;var o={5:"triangleStrip",6:"triangleFan",1:"lines",2:"lineLoop",3:"lineStrip"},q=(o[G.POINTS]="points",{position:"vs",normal:"ns"}),E=new k,T=function($,G,m,b,C,t){var u=G.mat;if(u){var p=C[u.name||(u.name="material_"+t.next())]={};for(O in u)"name"!==O&&"setValues"!==O&&"clone"!==O&&(p[O]=u[O]);$.mat=u.name}var e=G.mesh;if(e){$.mesh=n={},e.index&&(n.is=e.index.array);var n,O,l=e.attributes;for(O in l){var N=q[O]||O;m.ignoreNormal&&"ns"===N||(n[N]=l[O].array)}}G.matrix&&!G.matrix.equals(E)?decomposeTRS($,G.matrix):(G.position&&($.position=G.position),(G.rotation||G.quaternion)&&($.quaternion=G.rotation||G.quaternion),G.scale&&($.scale=G.scale)),G.userData&&(e=G.userData.transformData)&&(e.rotationOffset||e.rotationPivot||e.scalingOffset||e.scalingPivot)&&((W=$.transformData={}).translation=e.translation||[0,0,0],e.rotation?((z=e.rotation.map(degToRad)).push(e.eulerOrder),W.rotation=z):W.rotation=[0,0,0,1],W.scale=e.scale||[1,1,1],copyIfExists(W,e,"rotationOffset"),copyIfExists(W,e,"rotationPivot"),copyIfExists(W,e,"scalingOffset"),copyIfExists(W,e,"scalingPivot")),G.matrixWorld=(new k).composeFromArray($.position,$.quaternion,$.scale),G._matrixWorldNeedUpdate=!0;G.isBone&&($.isBone=!0),$.name=G.name,$.ID=G.ID;var R,z=G.skeleton,W=(z&&($.skeleton=z),G.animations),Z=(W&&($.animations=W),G.getChildren().getArray());if(Z){$.comps=[];for(var v=0,c=Z.length;v<c;v++)$.comps.push(R={}),T(R,Z[v],m,b,C,t)}};function i($,u,p){var e=I.Math.generateUUID();n.addPendingPlaceholder(e),new g(u).parse($,"",function($){var G,z={model3d:!0,matDef:{}},m=$.parser.sourceBufferCache,m=(I.Default.isEmptyObject(m)||(z.sourceBufferMap=m),$.scene),b=(T(z,m,u,{},z.matDef,I.Math.newIdGenerator()),n.traverse(z,null,function($){var G=z.matDef,m=$.mat;if(m&&(m=G[m])&&m.map){G=m.map.uvMatrix;if(G){m=$.mesh;if(m){var b=m.uv;if(b){for(var C=new Float32Array(b.length),t=G[0],u=G[1],p=G[3],e=G[4],n=G[6],O=G[7],l=0;l<b.length;l+=2){var N=b[l],R=b[l+1];C[l]=t*N+p*R+n,C[l+1]=u*N+e*R+O}m.uv=C}}}}},"comps"),$.animations),b=(b&&(z.animations=b),$.assets),C=(b&&(z.assets=b),{}),t={};n.traverse(z,null,function($){if(C[$.name]){for($.displayName=$.name;G=$.name+"_"+I.Math.generateIncreasingID(),C[G];);$.name=G}C[$.name]=$.ID,t[$.ID]=$.name,delete $.ID},"comps"),z.animations&&z.animations.forEach(function($){$.tracks.forEach(function($){var G=$.name.split(".");G[0]&&t[G[0]]&&($.name=t[G[0]]+"."+G.slice(1).join("."))})}),n.traverse(z,null,function($){var G=$.skeleton;if(G){for(var m=G[0],b=m.length,C=new Array(b),t=0;t<b;t++)C[t]=m[t].name;$.skeleton={bones:C,boneMatrixInverses:G[1],bindMatrix:G[2].toArray(),bindMatrixInverse:(new k).copy(G[2]).invert().toArray()}}},"comps"),n.traverse(z,null,function($){var G=$.comps;if(G)for(var m=G.length-1;0<=m;m--){var b=G[m];b.mesh||b.isBone||b.comps&&b.comps.length||G.splice(m,1)}},"comps"),$.externalAssetURIs&&(z.externalAssetURIs=$.externalAssetURIs),u.batchByMaterial&&l.batchModel3dByMaterial(z,u),l.completeCubeCenterOfModel3d(z,{scene:m,cube:u.cube,center:u.center,preferBox3:u.box3,rotationMode:u.rotationMode,t3:u.t3,r3:u.r3,s3:u.s3}),l.completeCommonPropertiesOfModel3d(z,u),p&&p(z),n.removePendingPlaceholder(e,z)},function($){console.error($),p&&p(null),n.removePendingPlaceholder(e,null)})}function j($,G,m,b){if("string"!=typeof $||0<=$.indexOf("data:")||0<=$.indexOf("blob:"))return b($,!0);if(0<=$.indexOf(Q))return b($.slice(Q.length),!0);if(G){G=G[$];if(G)return b(G)}n.convertGltfAssetsURL?n.convertGltfAssetsURL($,m,b):b(m+$)}l.addMethod(n,{loadGltf:function(b,C){if(!(C=C||{}).path)for(var $=b.length-1;0<=$;$--){var G=b.charAt($);if("/"===G||"\\"===G){C.path=b.substring(0,$+1);break}}C.responseType="arraybuffer",n.xhrLoad(b,function($){var m;$=$,m=C.finishFunc,$?i($,C,function($){var G=C.shape3d;$?m&&m($,b,G):m&&m(null)}):m(null)},C)},parseGltf:i,convertGltfAssetsURL:null});var Q="binary:",g=function($){$=$||{},this.dracoLoader=null,this.ktx2Loader=null,this.meshoptDecoder=null,this.pluginCallbacks=[],this.assetsURIMap=$.assetsURIMap,this.path=$.path||"",this.binaryImagePath=$.binaryImagePath,this.register(function($){return new r($)}),this.register(function($){return new $P($)}),this.register(function($){return new GP($)}),this.register(function($){return new H($)}),this.register(function($){return new h($)}),this.register(function($){return new w($)}),this.register(function($){return new L($)}),this.register(function($){return new S($)}),this.register(function($){return new s($)}),this.register(function($){return new mP($)})};function _(){var m={};return{get:function($){return m[$]},add:function($,G){m[$]=G},remove:function($){delete m[$]},removeAll:function(){m={}}}}$(g,t,{getDRACOLoader:function(){var $=this.dracoLoader;return $||(($=(new cP).setDecoderPath(n.dracoLibraryPath||"")).getDracoWorkerSourceURL=n.getDracoWorkerSourceURL,this.setDRACOLoader($),$)},setDRACOLoader:function($){return this.dracoLoader=$,this},setDDSLoader:function(){throw new Error('GLTFLoader: use "KHR_texture_basisu" instead.')},setKTX2Loader:function($){return this.ktx2Loader=$,this},setMeshoptDecoder:function($){return this.meshoptDecoder=$,this},register:function($){return-1===this.pluginCallbacks.indexOf($)&&this.pluginCallbacks.push($),this},unregister:function($){return-1!==this.pluginCallbacks.indexOf($)&&this.pluginCallbacks.splice(this.pluginCallbacks.indexOf($),1),this},parse:function($,G,m,b){var C,t={},u={};if("string"==typeof $)C=$;else if(R(new Uint8Array($,0,4))===bP){try{t[F.KHR_BINARY_GLTF]=new uP($)}catch($){return void(b&&b($))}C=t[F.KHR_BINARY_GLTF].content}else C=R(new Uint8Array($));var p=JSON.parse(C);if(p.asset===X||p.asset.version[0]<2)b&&b(new Error("GLTFLoader: only glTF versions >=2.0 are supported."));else{for(var e=new ZP(p,{assetsURIMap:this.assetsURIMap,path:G||this.resourcePath||this.path||"",crossOrigin:this.crossOrigin,requestHeader:this.requestHeader,ktx2Loader:this.ktx2Loader,meshoptDecoder:this.meshoptDecoder,binaryImagePath:this.binaryImagePath}),n=0;n<this.pluginCallbacks.length;n++){var O=this.pluginCallbacks[n](e);t[(u[O.name]=O).name]=!0}if(p.extensionsUsed)for(n=0;n<p.extensionsUsed.length;++n){var l=p.extensionsUsed[n],N=p.extensionsRequired||[];switch(l){case F.KHR_MATERIALS_UNLIT:t[l]=new J;break;case F.KHR_DRACO_MESH_COMPRESSION:t[l]=new pP(p,this.getDRACOLoader());break;case F.KHR_TEXTURE_TRANSFORM:t[l]=new eP;break;case F.KHR_MESH_QUANTIZATION:t[l]=new nP;break;default:0<=N.indexOf(l)&&u[l]===X&&console.warn('GLTFLoader: Unknown extension "'+l+'".')}}e.setExtensions(t),e.setPlugins(u),e.parse(m,b)}},parseAsync:function(m,b){var C=this;return new Promise(function($,G){C.parse(m,b,$,G)})}});var F={KHR_BINARY_GLTF:"KHR_binary_glTF",KHR_DRACO_MESH_COMPRESSION:"KHR_draco_mesh_compression",KHR_LIGHTS_PUNCTUAL:"KHR_lights_punctual",KHR_MATERIALS_CLEARCOAT:"KHR_materials_clearcoat",KHR_MATERIALS_IOR:"KHR_materials_ior",KHR_MATERIALS_SHEEN:"KHR_materials_sheen",KHR_MATERIALS_SPECULAR:"KHR_materials_specular",KHR_MATERIALS_TRANSMISSION:"KHR_materials_transmission",KHR_MATERIALS_UNLIT:"KHR_materials_unlit",KHR_MATERIALS_VOLUME:"KHR_materials_volume",KHR_TEXTURE_BASISU:"KHR_texture_basisu",KHR_TEXTURE_TRANSFORM:"KHR_texture_transform",KHR_MESH_QUANTIZATION:"KHR_mesh_quantization",EXT_TEXTURE_WEBP:"EXT_texture_webp",EXT_MESHOPT_COMPRESSION:"EXT_meshopt_compression"},s=function($){this.parser=$,this.name=F.KHR_LIGHTS_PUNCTUAL,this.cache={refs:{},uses:{}}},J=($(s,t,{_markDefs:function(){},createNodeAttachment:function(){return null}}),function(){this.name=F.KHR_MATERIALS_UNLIT}),S=($(J,t,{getMaterialType:function(){return B},extendParams:function($,G,m){var b,C=[],G=($.diffuse=[1,1,1],$.opacity=1,G.pbrMetallicRoughness);return G&&(Array.isArray(G.baseColorFactor)&&(b=G.baseColorFactor,$.diffuse=b,$.opacity=b[3]),G.baseColorTexture!==X&&C.push(m.assignTexture($,"map",G.baseColorTexture))),Promise.all(C)}}),function($){this.parser=$,this.name=F.KHR_MATERIALS_SPECULAR}),G=($(S,t,{getMaterialType:function($){$=this.parser.json.materials[$];return $.extensions&&$.extensions[this.name]?B:null},extendParams:function($,G,m){if(!(G=(m=this.parser).json.materials[materialIndex]).extensions||!G.extensions[this.name])return Promise.resolve();var b=[],G=G.extensions[this.name],C=($.specularIntensity=G.specularFactor!==X?G.specularFactor:1,G.specularTexture!==X&&b.push(m.assignTexture($,"specularIntensityMap",G.specularTexture)),G.specularColorFactor||[1,1,1]);return $.specularColor=[C[0],C[1],C[2]],G.specularColorTexture!==X&&b.push(m.assignTexture($,"specularColorMap",G.specularColorTexture)),Promise.all(b)}}),{getMaterialType:function($){$=this.parser.json.materials[$];return $.extensions&&$.extensions[this.name]?B:null},extendParams:function(){return Promise.resolve()}}),r=function($){this.parser=$,this.name=F.KHR_MATERIALS_CLEARCOAT},H=($(r,t,G),function($){this.parser=$,this.name=F.KHR_MATERIALS_SHEEN}),h=($(H,t,G),function($){this.parser=$,this.name=F.KHR_MATERIALS_TRANSMISSION}),w=($(h,t,G),function($){this.parser=$,this.name=F.KHR_MATERIALS_VOLUME}),L=($(w,t,G),function($){this.parser=$,this.name=F.KHR_MATERIALS_IOR}),$P=($(L,t,G),function($){this.parser=$,this.name=F.KHR_TEXTURE_BASISU}),GP=($($P,t,{loadTexture:function($){var G=this.parser,m=G.json,b=m.textures[$];if(!b.extensions||!b.extensions[this.name])return null;var b=b.extensions[this.name],C=G.options.ktx2Loader;if(C)return G.loadTextureImage($,b.source,C);if(m.extensionsRequired&&0<=m.extensionsRequired.indexOf(this.name))throw new Error("GLTFLoader: setKTX2Loader must be called");return null}}),function($){this.parser=$,this.name=F.EXT_TEXTURE_WEBP,this.isSupported=null}),mP=($(GP,t,{loadTexture:function(G){var m=this.name,b=this.parser,C=b.json,$=C.textures[G];if(!$.extensions||!$.extensions[m])return null;var $=$.extensions[m],t=C.images[$.source],u=b.textureLoader;return this.detectSupport().then(function($){if($)return b.loadTextureImage(G,t,u);if(C.extensionsRequired&&0<=C.extensionsRequired.indexOf(m))throw new Error("GLTFLoader: WebP required by asset but unsupported.");return b.loadTexture(G)})},detectSupport:function(){return this.isSupported||(this.isSupported=new Promise(function($){var G=new Image;G.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",G.onload=G.onerror=function(){$(1===G.height)}})),this.isSupported}}),function($){this.parser=$,this.name=F.EXT_MESHOPT_COMPRESSION}),bP=($(mP,t,{loadBufferView:function($){var G=this.parser.json,$=G.bufferViews[$];if($.extensions&&$.extensions[this.name]){var u=$.extensions[this.name],$=this.parser.getDependency("buffer",u.buffer),p=this.parser.options.meshoptDecoder;if(p&&p.supported)return Promise.all([$,p.ready]).then(function($){var G=u.byteOffset||0,m=u.byteLength||0,b=u.count,C=u.byteStride,t=new ArrayBuffer(b*C),$=new Uint8Array($[0],G,m);return p.decodeGltfBuffer(new Uint8Array(t),b,C,$,u.mode,u.filter),t});if(G.extensionsRequired&&0<=G.extensionsRequired.indexOf(this.name))throw new Error("GLTFLoader: setMeshoptDecoder must be called before loading compressed files");return null}return null}}),"glTF"),CP=1313821514,tP=5130562,uP=function($){this.name=F.KHR_BINARY_GLTF,this.content=null,this.body=null;var G=new DataView($,0,12);if(this.header={magic:R(new Uint8Array($.slice(0,4))),version:G.getUint32(4,!0),length:G.getUint32(8,!0)},this.header.magic!==bP)throw new Error("GLTFLoader: Unsupported glTF-Binary header.");if(this.header.version<2)throw new Error("GLTFLoader: Legacy binary file detected.");for(var m=this.header.length-12,b=new DataView($,12),C=0;C<m;){var t,u=b.getUint32(C,!0),p=(C+=4,b.getUint32(C,!0));C+=4,p===CP?(t=new Uint8Array($,12+C,u),this.content=R(t)):p===tP&&(this.body=$.slice(t=12+C,t+u)),C+=u}if(null===this.content)throw new Error("GLTFLoader: JSON content not found.")},pP=function($,G){if(!G)throw new Error("GLTFLoader: No DRACOLoader instance provided.");this.name=F.KHR_DRACO_MESH_COMPRESSION,this.json=$,this.dracoLoader=G,this.dracoLoader.preload()},eP=($(pP,t,{decodePrimitive:function($,G){var m,b=this.json,C=this.dracoLoader,t=$.extensions[this.name].bufferView,u=$.extensions[this.name].attributes,p={},e={},n={};for(m in u){var O=x[m]||m.toLowerCase();p[O]=u[m]}for(m in $.attributes){var l,N,O=x[m]||m.toLowerCase();u[m]!==X&&(l=b.accessors[$.attributes[m]],N=a[l.componentType],n[O]=N.name,e[O]=!0===l.normalized)}return G.getDependency("bufferView",t).then(function($){return new Promise(function(b){C.decodeDracoFile($,function($){for(var G in $.attributes){var m=$.attributes[G],G=e[G];G!==X&&(m.normalized=G)}b($)},p,n)})})}}),function(){this.name=F.KHR_TEXTURE_TRANSFORM}),nP=($(eP,t,{extendTexture:function($,G){if(G.texCoord!==X&&console.warn('GLTFLoader: Custom UV sets in "'+this.name+'" extension not yet supported.'),G.offset===X&&G.rotation===X&&G.scale===X)return $;G=l.makeUVMatrixArray(G.rotation,G.offset,G.scale);return $.uvMatrix=G.slice(0),$}}),function(){this.name=F.KHR_MESH_QUANTIZATION}),a={5120:Int8Array,5121:Uint8Array,5122:Int16Array,5123:Uint16Array,5125:Uint32Array,5126:Float32Array},OP={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},x={POSITION:"position",NORMAL:"normal",TANGENT:"tangent",TEXCOORD_0:"uv",TEXCOORD_1:"uv2",COLOR_0:"color",WEIGHTS_0:"skinWeight",JOINTS_0:"skinIndex"},lP={scale:"scale",translation:"position",rotation:"quaternion",weights:"morphTargetInfluences"},NP="OPAQUE",RP="MASK",zP="BLEND",B=function($){var G=(new v).setValues({type:"pbr"});return $&&G.setValues($),G};function P($,G,m){for(var b in m.extensions)$[b]===X&&G.userData&&(G.userData.gltfExtensions=G.userData.gltfExtensions||{},G.userData.gltfExtensions[b]=m.extensions[b])}function Y($,G){var m,G=G.extras;G!==X&&("object"==typeof G?((m=$.userData)||($.userData=m={}),t.assign(m,G)):console.warn("GLTFLoader: Ignoring primitive type .extras, "+G))}function WP($){for(var G="",m=t.keys($).sort(),b=0,C=m.length;b<C;b++)G+=m[b]+":"+$[m[b]]+";";return G}function A($){switch($){case Int8Array:return 1/127;case Uint8Array:return 1/255;case Int16Array:return 1/32767;case Uint16Array:return 1/65535;default:throw new Error("GLTFLoader: Unsupported normalized accessor component type.")}}var ZP=function($,G){G=G||{},this.json=$=$||{},this.extensions={},this.plugins={},this.options=G,this.cache=new _,this.associations=new Map,this.primitiveCache={},this.assetsURIMap=G.assetsURIMap,this.path=G.path,this.meshCache={refs:{},uses:{}},this.cameraCache={refs:{},uses:{}},this.lightCache={refs:{},uses:{}},this.sourceCache={},this.sourceBufferCache={},this.textureCache={},this.nodeNamesUsed={},this.assets=[],this.externalAssetURIs=[],this.nameIdGenerator=I.Math.newIdGenerator()};function vP(m,$,b){var G,C=$.attributes,t=[];for(G in C){var u=x[G]||G.toLowerCase();u in m.attributes||t.push(function($,G){return b.getDependency("accessor",$).then(function($){m.setAttribute(G,$)})}(C[G],u))}$.indices===X||m.index||(p=b.getDependency("accessor",$.indices).then(function($){m.setIndex($)}),t.push(p)),Y(m,$);var p=m,e=b,n=$.attributes,O=new I.Math.Box3;if(n.POSITION!==X){var l=(c=e.json.accessors[n.POSITION]).min,N=c.max;if(l===X||N===X)console.warn("GLTFLoader: Missing min/max properties for accessor POSITION.");else{O.set(new f(l[0],l[1],l[2]),new f(N[0],N[1],N[2])),c.normalized&&(k=A(a[c.componentType]),O.min.multiplyScalar(k),O.max.multiplyScalar(k));var R=$.targets;if(R!==X){for(var z=new f,W=new f,Z=0,v=R.length;Z<v;Z++){var c,k,M=R[Z];M.POSITION!==X&&(l=(c=e.json.accessors[M.POSITION]).min,N=c.max,l!==X&&N!==X?(W.setX(Math.max(Math.abs(l[0]),Math.abs(N[0]))),W.setY(Math.max(Math.abs(l[1]),Math.abs(N[1]))),W.setZ(Math.max(Math.abs(l[2]),Math.abs(N[2]))),c.normalized&&(k=A(a[c.componentType]),W.multiplyScalar(k)),z.max(W)):console.warn("GLTFLoader: Missing min/max properties for accessor POSITION."))}O.expandByPoint(z)}p.boundingBox=O;n=new I.Math.Sphere;O.getCenter(n.center),n.radius=O.min.distanceTo(O.max)/2,p.boundingSphere=n}}return Promise.all(t).then(function(){return m})}$(ZP,t,{setExtensions:function($){this.extensions=$},setPlugins:function($){this.plugins=$},parse:function(m,$){var b=this,C=this.json,t=this.extensions,u=this.assets;this.cache.removeAll(),this._invokeAll(function($){return $._markDefs&&$._markDefs()}),Promise.all(this._invokeAll(function($){return $.beforeRoot&&$.beforeRoot()})).then(function(){return Promise.all([b.getDependencies("scene"),b.getDependencies("animation")])}).then(function($){var G={scene:$[0][C.scene||0],scenes:$[0],animations:$[1],cameras:$[2],asset:C.asset,parser:b,userData:{},externalAssetURIs:b.externalAssetURIs};u.length&&(G.assets=u),P(t,G,C),Y(G,C),Promise.all(b._invokeAll(function($){return $.afterRoot&&$.afterRoot(G)})).then(function(){m(G)})}).catch($)},_markDefs:function(){for(var $=this.json.nodes||[],G=this.json.skins||[],m=this.json.meshes||[],b=0,C=G.length;b<C;b++)for(var t=G[b].joints,u=0,p=t.length;u<p;u++)$[t[u]].isBone=!0;for(var e=0,n=$.length;e<n;e++){var O=$[e];O.mesh!==X&&(this._addNodeRef(this.meshCache,O.mesh),O.skin!==X&&(m[O.mesh].isSkinnedMesh=!0)),O.camera!==X&&this._addNodeRef(this.cameraCache,O.camera)}},_addNodeRef:function($,G){G!==X&&($.refs[G]===X&&($.refs[G]=$.uses[G]=0),$.refs[G]++)},_getNodeRef:function($,G,m){if($.refs[G]<=1)return m;var b,C,t=new Z(m.geometry,m.material);for(b in m)"geometry"!==b&&"material"!==b&&"_id"!==b&&(C=m[b],t[b]=C&&C.clone?C.clone():C);var u=this,p=function($,G){var m=u.associations.get($);null!=m&&u.associations.set(G,m);for(var b=0,m=$.children,C=m?m.length:0;b<C;b++)p($.children[b],G.children[b])};return p(m,t),t.name+="_instance_"+$.uses[G]++,t},_invokeOne:function($){var G=t.values(this.plugins);G.push(this);for(var m=0;m<G.length;m++){var b=$(G[m]);if(b)return b}return null},_invokeAll:function($){for(var G=t.values(this.plugins),m=(G.unshift(this),[]),b=0;b<G.length;b++){var C=$(G[b]);C&&m.push(C)}return m},getDependency:function($,G){var m=$+":"+G,b=this.cache.get(m);if(!b){switch($){case"scene":b=this.loadScene(G);break;case"node":b=this.loadNode(G);break;case"mesh":b=this._invokeOne(function($){return $.loadMesh&&$.loadMesh(G)});break;case"accessor":b=this.loadAccessor(G);break;case"bufferView":b=this._invokeOne(function($){return $.loadBufferView&&$.loadBufferView(G)});break;case"buffer":b=this.loadBuffer(G);break;case"material":b=this._invokeOne(function($){return $.loadMaterial&&$.loadMaterial(G)});break;case"texture":b=this._invokeOne(function($){return $.loadTexture&&$.loadTexture(G)});break;case"skin":b=this.loadSkin(G);break;case"animation":b=this.loadAnimation(G);break;case"camera":b=this.loadCamera(G);break;default:throw new Error("Unknown type: "+$)}this.cache.add(m,b)}return b},getDependencies:function(m){var b,$,G=this.cache.get(m);return G||($=(b=this).json[m+("mesh"===m?"es":"s")]||[],G=Promise.all($.map(function($,G){return b.getDependency(m,G)})),this.cache.add(m,G)),G},loadBuffer:function($){var C=this.json.buffers[$];if(C.type&&"arraybuffer"!==C.type)throw new Error("GLTFLoader: "+C.type+" buffer type is not supported.");if(C.uri===X&&0===$)return Promise.resolve(this.extensions[F.KHR_BINARY_GLTF].body);var G=this.path,t=this.assets,u=this.externalAssetURIs,p=this.assetsURIMap;return new Promise(function(m,b){var $=C.uri;$.indexOf("data:")<0&&u.indexOf($)<0&&u.push($),j($,p,G,function($,G){G||t.push($),I.Default.xhrLoad($,function($){$?m($):b(new Error('GLTFLoader: Failed to load buffer "'+C.uri+'".'))},{responseType:"arraybuffer"})})})},loadBufferView:function($){var b=this.json.bufferViews[$];return this.getDependency("buffer",b.buffer).then(function($){var G=b.byteLength||0,m=b.byteOffset||0;return $.slice(m,m+G)})},loadAccessor:function($){var k=this.json,M=this.json.accessors[$];if(M.bufferView===X&&M.sparse===X)return Promise.resolve(null);$=[];return M.bufferView!==X?$.push(this.getDependency("bufferView",M.bufferView)):$.push(null),M.sparse!==X&&($.push(this.getDependency("bufferView",M.sparse.indices.bufferView)),$.push(this.getDependency("bufferView",M.sparse.values.bufferView))),Promise.all($).then(function($){var G,m=$[0],b=OP[M.type],C=a[M.componentType],t=C.BYTES_PER_ELEMENT,u=M.byteOffset||0,p=M.bufferView!==X?k.bufferViews[M.bufferView].byteStride:X,e=!0===M.normalized;if(p&&p!==t*b){for(var n=new C(M.count*b),O=Math.round(p/t),l=u%p/t,N=new C(m,Math.floor(u/p)*p,M.count*p/t),R=0;R<M.count;R++)for(var z=0;z<b;z++){var W=R*O+z+l;n[R*b+z]=N[W]}G=new V(n,b,e)}else G=new V(n=null===m?new C(M.count*b):new C(m,u,M.count*b),b,e);if(M.sparse!==X){var p=a[M.sparse.indices.componentType],t=M.sparse.indices.byteOffset||0,u=M.sparse.values.byteOffset||0,Z=new p($[1],t,M.sparse.count*OP.SCALAR),v=new C($[2],u,M.sparse.count*b);null!==m&&(G=new V(G.array.slice(),G.itemSize,G.normalized));for(var R=0,c=Z.length;R<c;R++){W=Z[R];if(G.setX(W,v[R*b]),2<=b&&G.setY(W,v[R*b+1]),3<=b&&G.setZ(W,v[R*b+2]),4<=b&&G.setW(W,v[R*b+3]),5<=b)throw new Error("GLTFLoader: Unsupported itemSize in sparse BufferAttribute.")}}return G})},loadTexture:function($){var G=this.json.textures[$].source,m=this.textureLoader;return this.loadTextureImage($,G,m)},loadTextureImage:function(m,$,G){var b=this,C=this.json,t=C.textures[m],u=C.images[$],u=(u.uri||u.bufferView)+":"+t.sampler;if(this.textureCache[u])return this.textureCache[u];$=this.loadImageSource($,G).then(function($){$.flipY=!1,t.name&&($.name=t.name);var G=(C.samplers||{})[t.sampler]||{};return G.magFilter!==X&&($.magFilter=G.magFilter),G.minFilter!==X&&($.minFilter=G.minFilter),G.wrapS!==X&&($.wrapS=G.wrapS),G.wrapT!==X&&($.wrapT=G.wrapT),b.associations.set($,{textures:m}),$}).catch(function(){return null});return this.textureCache[u]=$},loadImageSource:function($,G){var C=this,m=this.json;if(this.sourceCache[$]!==X)return this.sourceCache[$].then(function($){$.clone()});var t=m.images[$],u=O.URL||O.webkitURL,p=t.uri,e=("string"==typeof p&&p.indexOf("data:")<0?(m=this.externalAssetURIs).indexOf(p)<0&&m.push(p):p=p||"",!1);if(t.bufferView!==X)p=C.getDependency("bufferView",t.bufferView).then(function($){var G,m,b=C.options.binaryImagePath;return null==b&&u?(e=!0,u.createObjectURL(new Blob([$],{type:t.mimeType}))):(G=t.name||I.Math.generateUUID(),m=t.mimeType||"image/png",p=(b||"")+G+"."+m.slice(m.indexOf("/")+1),C.sourceBufferCache[p]=$,Q+p)});else if(t.uri===X)throw new Error("GLTFLoader: Image "+$+" is missing URI and bufferView");var b=this.assetsURIMap,n=this.path,m=Promise.resolve(p).then(function(m){return new Promise(function(G,$){j(m,b,n,function($){G($)})})}).then(function(C){return new Promise(function($,G){var m,b=decodeURIComponent(C);e?((m=new Image).src=b,m.onload=function(){$(new N({image:m,mimeType:t.mimeType}))}):$(new N({image:b}))})}).then(function($){return!0===e&&u.revokeObjectURL(p),$}).catch(function($){throw console.error("GLTFLoader: Couldn't load texture",p),$});return this.sourceCache[$]=m},assignTexture:function(b,C,t){var u=this;return this.getDependency("texture",t.index).then(function($){var G,m;return t.texCoord===X||0==t.texCoord||"aoMap"===C&&1==t.texCoord||console.warn("GLTFLoader: Custom UV set "+t.texCoord+" for texture "+C+" not yet supported."),u.extensions[F.KHR_TEXTURE_TRANSFORM]&&(G=t.extensions!==X?t.extensions[F.KHR_TEXTURE_TRANSFORM]:X)&&(m=u.associations.get($),$=u.extensions[F.KHR_TEXTURE_TRANSFORM].extendTexture($,G),u.associations.set($,m)),b[C]=$})},assignFinalMaterial:function($){var G,m,b=$.mesh,C=$.mat,t=b.attributes.tangent===X,u=b.attributes.color!==X,p=b.attributes.normal===X;(t||u||p)&&(G="ClonedMaterial:"+C.uuid+":",C.isGLTFSpecularGlossinessMaterial&&(G+="specular-glossiness:"),t&&(G+="derivative-tangents:"),u&&(G+="vertex-colors:"),p&&(G+="flat-shading:"),(m=this.cache.get(G))||(m=C.clone(),u&&(m.vertexColors=!0),p&&(m.flatShading=!0),t&&(m.normalScale&&(m.normalScale.y*=-1),m.clearcoatNormalScale&&(m.clearcoatNormalScale.y*=-1)),this.cache.add(G,m),this.associations.set(m,this.associations.get(C))),C=m),C.aoMap&&b.attributes.uv2===X&&b.attributes.uv!==X&&b.setAttribute("uv2",b.attributes.uv),$.material=C},getMaterialType:function(){return B},loadMaterial:function(G){var m,b=this,$=this.json,C=this.extensions,t=$.materials[G],u={},$=t.extensions||{},p=[],e=($[F.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS]?(e=C[F.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS],m=e.getMaterialType(),p.push(e.extendParams(u,t,b))):$[F.KHR_MATERIALS_UNLIT]?(e=C[F.KHR_MATERIALS_UNLIT],m=e.getMaterialType(),p.push(e.extendParams(u,t,b))):($=t.pbrMetallicRoughness||{},u.diffuse=[1,1,1],u.opacity=1,Array.isArray($.baseColorFactor)&&(e=$.baseColorFactor,u.diffuse=e,u.opacity=e[3]),$.baseColorTexture!==X&&p.push(b.assignTexture(u,"map",$.baseColorTexture)),u.metalness=$.metallicFactor!==X?$.metallicFactor:1,u.roughness=$.roughnessFactor!==X?$.roughnessFactor:1,$.metallicRoughnessTexture!==X&&(p.push(b.assignTexture(u,"metalnessMap",$.metallicRoughnessTexture)),p.push(b.assignTexture(u,"roughnessMap",$.metallicRoughnessTexture))),m=this._invokeOne(function($){return $.getMaterialType&&$.getMaterialType(G)}),p.push(Promise.all(this._invokeAll(function($){return $.extendMaterialParams&&$.extendMaterialParams(G,u)})))),!0===t.doubleSided&&(u.cullFace=!1),t.alphaMode||NP);return e===zP?(u.transparent=!0,u.depthWrite=!1):(u.transparent=!1,e===RP&&(u.alphaTest=t.alphaCutoff!==X?t.alphaCutoff:.5)),t.normalTexture!==X&&(p.push(b.assignTexture(u,"normalMap",t.normalTexture)),u.normalScale=[1,1],t.normalTexture.scale!==X&&($=t.normalTexture.scale,u.normalScale=[$,$])),t.occlusionTexture!==X&&(p.push(b.assignTexture(u,"aoMap",t.occlusionTexture)),t.occlusionTexture.strength!==X&&(u.aoMapIntensity=t.occlusionTexture.strength)),t.emissiveFactor!==X&&(u.emissive=t.emissiveFactor),t.emissiveTexture!==X&&p.push(b.assignTexture(u,"emissiveMap",t.emissiveTexture)),Promise.all(p).then(function(){var $=(new m).setValues(u);return t.name&&($.name=t.name),Y($,t),b.associations.set($,{materials:G}),t.extensions&&P(C,$,t),$.roughnessMap&&($.roughnessChannel="G"),$.metalnessMap&&($.metalnessChannel="B"),$.aoMap&&($.aoChannel="R",$.aoUvChannel=2),$})},createUniqueName:function($){for(var G=($||"").replace(/\s/g,"_").replace(y,""),m=G,b=1;this.nodeNamesUsed[m];++b)m=G+"_"+b;return this.nodeNamesUsed[m]=!0,m},loadGeometries:function($){var m=this,b=this.extensions,G=this.primitiveCache;for(var C=[],t=0,u=$.length;t<u;t++){var p=$[t],e=(n=void 0,n=(n=(e=p).extensions&&e.extensions[F.KHR_DRACO_MESH_COMPRESSION])?"draco:"+n.bufferView+":"+n.indices+":"+WP(n.attributes):e.indices+":"+WP(e.attributes)+":"+e.mode),n=G[e];n?C.push(n.promise):(n=p.extensions&&p.extensions[F.KHR_DRACO_MESH_COMPRESSION]?function(G){return b[F.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(G,m).then(function($){return vP($,G,m)})}(p):vP(new z,p,m),G[e]={primitive:p,promise:n},C.push(n))}return Promise.all(C)},loadMesh:function(O){for(var l=this,$=this.json,N=this.extensions,R=$.meshes[O],z=R.primitives,G=[],m=0,b=z.length;m<b;m++){var C=z[m].material===X?((C=this.cache).DefaultMaterial===X&&(C.DefaultMaterial=B()),C.DefaultMaterial):this.getDependency("material",z[m].material);G.push(C)}return G.push(l.loadGeometries(z)),Promise.all(G).then(function($){for(var G=$.slice(0,$.length-1),m=$[$.length-1],b=[],C=0,t=m.length;C<t;C++){var u=m[C],p=z[C],e=G[C],u=new Z(u,e);p.mode!==X&&(e.renderMode=o[p.mode]||"triangles"),u.name=l.createUniqueName(R.name||"mesh_"+O),Y(u,R),p.extensions&&P(N,u,p),l.assignFinalMaterial(u),u.normalizeSkinWeights(),b.push(u)}for(C=0,t=b.length;C<t;C++)l.associations.set(b[C],{meshes:O,primitives:C});if(1===b.length)return b[0];var n=new W;l.associations.set(n,{meshes:O});for(C=0,t=b.length;C<t;C++)n.add(b[C]);return n})},loadCamera:function($){return Promise.resolve(new I.Camera)},loadSkin:function($){var $=this.json.skins[$],G={joints:$.joints};return $.inverseBindMatrices===X?Promise.resolve(G):this.getDependency("accessor",$.inverseBindMatrices).then(function($){return G.inverseBindMatrices=$,G})},loadAnimation:function(f){for(var V=this.json.animations[f],$=[],G=[],m=[],b=[],C=[],K=this.nameIdGenerator,t=0,u=V.channels.length;t<u;t++){var p=V.channels[t],U=V.samplers[p.sampler],p=p.target,e=p.node,n=V.parameters!==X?V.parameters[U.input]:U.input,O=V.parameters!==X?V.parameters[U.output]:U.output;$.push(this.getDependency("node",e)),G.push(this.getDependency("accessor",n)),m.push(this.getDependency("accessor",O)),b.push(U),C.push(p)}return Promise.all([Promise.all($),Promise.all(G),Promise.all(m),Promise.all(b),Promise.all(C)]).then(function($){for(var G=$[0],m=$[1],b=$[2],C=$[4],t=[],u=0,p=G.length;u<p;u++){var e=G[u],n=m[u],O=b[u],l=C[u];if(e!==X){var N=U.interpolation,R="CUBICSPLINE"===N?"cubicSpline":"STEP"===N?"discrete":"linear";"rotation"===l.path&&("cubicSpline"===R?R="cubicSplineQuaternion":"linear"===R&&(R="quaternion"));var N=(N=e.name)||(e.name="node"+K.next()),z=[],W=(l.path,z.push(N),O.array);if(O.normalized){for(var Z=A(W.constructor),v=new Float32Array(W.length),c=0,k=W.length;c<k;c++)v[c]=W[c]*Z;W=v}for(c=0,k=z.length;c<k;c++){var M={name:z[c]+"."+lP[l.path],times:n.array,values:W,type:R};t.push(M)}}}var $=V.name||"animation_"+f,I=0;return t.forEach(function($){I=Math.max(I,$.times[$.times.length-1])}),{name:$,duration:I,tracks:t}})},createNodeMesh:function($){var G=this.json,m=this,b=G.nodes[$];return b.mesh===X?null:m.getDependency("mesh",b.mesh).then(function($){$=m._getNodeRef(m.meshCache,b.mesh,$);return b.weights!==X&&$.traverse(function($){if($ instanceof Z)for(var G=0,m=b.weights.length;G<m;G++)$.morphTargetInfluences[G]=b.weights[G]}),$})},loadNode:function(e){var G,$=this.json,n=this.extensions,O=this,l=this.nameIdGenerator,N=$.nodes[e],R=N.name?O.createUniqueName(N.name):"";return G=[],($=O._invokeOne(function($){return $.createNodeMesh&&$.createNodeMesh(e)}))&&G.push($),N.camera!==X&&G.push(O.getDependency("camera",N.camera).then(function($){return O._getNodeRef(O.cameraCache,N.camera,$)})),O._invokeAll(function($){return $.createNodeAttachment&&$.createNodeAttachment(e)}).forEach(function($){G.push($)}),Promise.all(G).then(function($){var G,m,b,C,t;if(!0===N.isBone?(G=new W).isBone=!0:G=!(1<$.length)&&1===$.length?$[0]:new W,G!==$[0])for(var u=0,p=$.length;u<p;u++)G.add($[u]);return N.name&&(G.userData&&(G.userData.name=N.name),G.name=R),G.isBone&&!G.name&&(G.name="node"+l.next()),Y(G,N),N.extensions&&P(n,G,N),N.matrix!==X?((m=new I.Math.Matrix4).fromArray(N.matrix),b=new f,C=new f,t=new I.Math.Quaternion,m.decompose(b,t,C),G.position=b.toArray(),G.scale=C.toArray(),G.quaternion=t.toArray()):(N.translation!==X&&(G.position=N.translation),N.rotation!==X&&(G.quaternion=N.rotation),N.scale!==X&&(G.scale=N.scale)),O.associations.has(G)||O.associations.set(G,{}),O.associations.get(G).nodes=e,G})},loadScene:function($){for(var G=this.json,m=this.extensions,$=this.json.scenes[$],C=this,b=new W,t=($.name&&(b.name=C.createUniqueName($.name)),Y(b,$),$.extensions&&P(m,b,$),$.nodes||[]),u=[],p=0,e=t.length;p<e;p++)u.push(function u($,p,e,n){var O=e.nodes[$];return n.getDependency("node",$).then(function($){return O.skin===X?$:n.getDependency("skin",O.skin).then(function($){for(var G=[],m=0,b=(p=$).joints.length;m<b;m++)G.push(n.getDependency("node",p.joints[m]));return Promise.all(G)}).then(function(u){return $.traverse(function($){if($ instanceof Z){for(var G=[],m=[],b=0,C=u.length;b<C;b++){var t=u[b];t?(G.push(t),t=new k,p.inverseBindMatrices!==X&&t.fromArray(p.inverseBindMatrices.array,16*b),t.toArray(m,m.length)):console.warn("Joint %s could not be found.",p.joints[b])}$.skeleton=[G,m,$.matrixWorld]}}),$});var p}).then(function($){p.add($);var G=[];if(O.children)for(var m=O.children,b=0,C=m.length;b<C;b++){var t=m[b];G.push(u(t,$,e,n))}return Promise.all(G)})}(t[p],b,G,C));return Promise.all(u).then(function(){return C.associations=function($){var G,m=new Map;for(G in C.associations){var b=C.associations[G];(G instanceof v||G instanceof N)&&m.set(G,b)}return $.traverse(function($){var G=C.associations.get($);null!=G&&m.set($,G)}),m}(b),b})}});var D=new WeakMap,cP=function(){this.decoderPath="",this.decoderConfig={},this.decoderBinary=null,this.decoderPending=null,this.workerLimit=4,this.workerPool=[],this.workerNextTaskID=1,this.workerSourceURL="",this.defaultAttributeIDs={position:"POSITION",normal:"NORMAL",color:"COLOR",uv:"TEX_COORD"},this.defaultAttributeTypes={position:"Float32Array",normal:"Float32Array",color:"Float32Array",uv:"Float32Array"}};t.assign(cP.prototype,{setDecoderPath:function($){return this.decoderPath=$,this},setDecoderConfig:function($){return this.decoderConfig=$,this},setWorkerLimit:function($){return this.workerLimit=$,this},load:function($,G,m,b){var C=this,t=C.path+$;I.Default.xhrLoad(t,function($){$?C.decodeDracoFile($,G).catch(b):b("Failed to load "+t)},{responseType:"arraybuffer"})},decodeDracoFile:function($,G,m,b){b={attributeIDs:m||this.defaultAttributeIDs,attributeTypes:b||this.defaultAttributeTypes,useUniqueIDs:!!m};return this.decodeGeometry($,b).then(G)},decodeGeometry:function(m,b){var $=JSON.stringify(b);if(D.has(m)){var G=D.get(m);if(G.key===$)return G.promise;if(0===m.byteLength)throw new Error("DRACOLoader: Unable to decode a buffer.")}var C,t=this,u=t.workerNextTaskID++,G=m.byteLength,G=t._getWorker(u,G).then(function($){return C=$,new Promise(function($,G){C._callbacks[u]={resolve:$,reject:G},C.postMessage({type:"decode",id:u,taskConfig:b,buffer:m},[m])})}).then(function($){return t._createGeometry($.geometry)});return G.catch(function(){return!0}).then(function(){C&&u&&t._releaseTask(C,u)}),D.set(m,{key:$,promise:G}),G},_createGeometry:function($){var G=new z;$.index&&G.setIndex(new V($.index.array,1));for(var m=0;m<$.attributes.length;m++){var b=$.attributes[m],C=b.name,t=b.array,b=b.itemSize;G.setAttribute(C,new V(t,b))}return G},_loadLibrary:function($,b){var C=this.decoderPath+$;return new Promise(function(G,m){I.Default.xhrLoad(C,function($){$?G($):m("Failed to load "+C)},{responseType:b})})},preload:function(){return this._initDecoder(),this},_initDecoder:function(){var m=this;if(m.decoderPending)return m.decoderPending;var b="object"!=typeof WebAssembly||"js"===m.decoderConfig.type,$=[];return b?$.push(m._loadLibrary("draco_decoder.js","text")):($.push(m._loadLibrary("draco_wasm_wrapper.js","text")),$.push(m._loadLibrary("draco_decoder.wasm","arraybuffer"))),m.decoderPending=Promise.all($).then(function($){var G=$[0],$=(b||(m.decoderConfig.wasmBinary=$[1]),"("+function(){var m,G;function e($,G,m,b){var C,t=b.attributeIDs,u=b.attributeTypes,p=G.GetEncodedGeometryType(m);if(p===$.TRIANGULAR_MESH)C=new $.Mesh,k=G.DecodeBufferToMesh(m,C);else{if(p!==$.POINT_CLOUD)throw new Error("DRACOLoader: Unexpected geometry type.");C=new $.PointCloud,k=G.DecodeBufferToPointCloud(m,C)}if(!k.ok()||0===C.ptr)throw new Error("DRACOLoader: Decoding failed: "+k.error_msg());var e,n,O,l,N,R,z,W,Z,v,c,k,M,I,f,V={index:null,attributes:[]};for(e in t){var K,U,X=self[u[e]];if(b.useUniqueIDs)U=t[e],K=G.GetAttributeByUniqueId(C,U);else{if(-1===(U=G.GetAttributeId(C,$[t[e]])))continue;K=G.GetAttribute(C,U)}V.attributes.push((n=$,O=G,l=C,N=e,X=X,c=v=Z=W=z=void 0,z=(R=K).num_components(),W=l.num_points()*z,Z=W*X.BYTES_PER_ELEMENT,v=function($,G){switch(G){case Float32Array:return $.DT_FLOAT32;case Int8Array:return $.DT_INT8;case Int16Array:return $.DT_INT16;case Int32Array:return $.DT_INT32;case Uint8Array:return $.DT_UINT8;case Uint16Array:return $.DT_UINT16;case Uint32Array:return $.DT_UINT32}}(n,X),c=n._malloc(Z),O.GetAttributeDataArrayForAllPoints(l,R,v,Z,c),O=new X(n.HEAPF32.buffer,c,W).slice(),n._free(c),{name:N,array:O,itemSize:z}))}return p===$.TRIANGULAR_MESH&&(V.index=(m=$,k=G,M=3*(p=C).num_faces(),I=4*M,f=m._malloc(I),k.GetTrianglesUInt32Array(p,I,f),k=new Uint32Array(m.HEAPF32.buffer,f,M).slice(),m._free(f),{array:k,itemSize:1})),$.destroy(C),V}return function($){var t=$.data;switch(t.type){case"init":m=t.decoderConfig,G=new Promise(function(G){m.onModuleLoaded=function($){G({draco:$})},DracoDecoderModule(m)});break;case"decode":var u=t.buffer,p=t.taskConfig;G.then(function($){var $=$.draco,G=new $.Decoder,m=new $.DecoderBuffer;m.Init(new Int8Array(u),u.byteLength);try{var b=e($,G,m,p),C=b.attributes.map(function($){return $.array.buffer});b.index&&C.push(b.index.array.buffer),self.postMessage({type:"decode",id:t.id,geometry:b},C)}catch($){console.error($),self.postMessage({type:"error",id:t.id,error:$.message})}finally{$.destroy(m),$.destroy(G)}})}}}.toString()+")()"),G=["/* draco decoder */",G,"","/* worker */",""].join("\n"),G=m.getDracoWorkerSourceURL?m.getDracoWorkerSourceURL(G,$):URL.createObjectURL(new Blob([G,"onmessage = "+$]));m.workerSourceURL=G}),m.decoderPending},_getWorker:function($,G){var b=this;return b._initDecoder().then(function(){b.workerPool.length<b.workerLimit?((m=new Worker(b.workerSourceURL))._callbacks={},m._taskCosts={},m._taskLoad=0,m.postMessage({type:"init",decoderConfig:b.decoderConfig}),m.addEventListener("message",function($){var G=$.data;switch(G.type){case"decode":m._callbacks[G.id].resolve(G);break;case"error":m._callbacks[G.id].reject(G);break;default:console.error('DRACOLoader: Unexpected message, "'+G.type+'"')}}),b.workerPool.push(m)):b.workerPool.sort(function($,G){return $._taskLoad>G._taskLoad?-1:1});var m=b.workerPool[b.workerPool.length-1];return m._taskCosts[$]=G,m._taskLoad+=G,m})},_releaseTask:function($,G){$._taskLoad-=$._taskCosts[G],delete $._callbacks[G],delete $._taskCosts[G]},debug:function(){},dispose:function(){for(var $=0;$<this.workerPool.length;++$)this.workerPool[$].terminate();return this.workerPool.length=0,this}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);