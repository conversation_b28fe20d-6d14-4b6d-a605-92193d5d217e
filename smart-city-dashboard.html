<!DOCTYPE html>
<html>
<head>
    <title>Hightopo Smart City Dashboard</title>
    <meta charset="UTF-8">
    <style>
        html, body {
            padding: 0px;
            margin: 0px;
            background: #0a0a0a;
            font-family: 'Arial', sans-serif;
            color: #ffffff;
            overflow: hidden;
        }
        
        .dashboard-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
        }
        
        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(0, 20, 40, 0.8);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid #00d4ff;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
        
        .main-3d-view {
            position: absolute;
            top: 80px;
            left: 300px;
            right: 300px;
            bottom: 200px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid #00d4ff;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .left-panel {
            position: absolute;
            top: 80px;
            left: 0;
            width: 300px;
            bottom: 0;
            background: rgba(0, 20, 40, 0.9);
            backdrop-filter: blur(10px);
            border-right: 1px solid #00d4ff;
            overflow-y: auto;
        }
        
        .right-panel {
            position: absolute;
            top: 80px;
            right: 0;
            width: 300px;
            bottom: 0;
            background: rgba(0, 20, 40, 0.9);
            backdrop-filter: blur(10px);
            border-left: 1px solid #00d4ff;
            overflow-y: auto;
        }
        
        .bottom-panel {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: rgba(0, 20, 40, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid #00d4ff;
            display: flex;
        }
        
        .panel-section {
            margin: 20px;
            padding: 15px;
            background: rgba(0, 50, 100, 0.3);
            border: 1px solid #00d4ff;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }
        
        .stat-label {
            font-size: 12px;
            color: #a0a0a0;
        }
        
        .stat-value {
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
        }
        
        .chart-container {
            flex: 1;
            margin: 10px;
            background: rgba(0, 50, 100, 0.3);
            border: 1px solid #00d4ff;
            border-radius: 8px;
            padding: 15px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(0, 212, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #0099cc);
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .control-button {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid #00d4ff;
            color: #00d4ff;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            background: rgba(0, 212, 255, 0.4);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
        
        .mini-chart {
            width: 100%;
            height: 60px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .chart-line {
            position: absolute;
            bottom: 0;
            width: 2px;
            background: #00d4ff;
            transition: height 0.3s ease;
        }
    </style>
    <script src="lib/core/ht.js"></script>
    <script src="lib/plugin/ht-form.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <h1>Hightopo Smart City</h1>
        </div>
        
        <!-- Left Panel -->
        <div class="left-panel">
            <div class="panel-section">
                <div class="panel-title">City Equipment Operation</div>
                <div class="stat-item">
                    <span class="stat-label">Online</span>
                    <span class="stat-value">100</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Offline</span>
                    <span class="stat-value">30</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Warning</span>
                    <span class="stat-value">5</span>
                </div>
                <div class="mini-chart" id="equipmentChart"></div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">Real-time Statistics</div>
                <div class="stat-item">
                    <span class="stat-label">A-region</span>
                    <span class="stat-value">80%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 80%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">B-region</span>
                    <span class="stat-value">65%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 65%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">C-region</span>
                    <span class="stat-value">90%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 90%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">D-region</span>
                    <span class="stat-value">75%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 75%"></div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">City Warning</div>
                <div class="stat-item">
                    <span class="stat-label">Level</span>
                    <span class="stat-value">5550.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Count</span>
                    <span class="stat-value">22</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Level</span>
                    <span class="stat-value">1243.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Count</span>
                    <span class="stat-value">163425</span>
                </div>
            </div>
        </div>
        
        <!-- Main 3D View -->
        <div class="main-3d-view" id="main3dView"></div>
        
        <!-- Right Panel -->
        <div class="right-panel">
            <div class="panel-section">
                <div class="panel-title">Real Traffic Analysis</div>
                <div class="stat-item">
                    <span class="stat-label">Max of today</span>
                    <span class="stat-value">80% 140.54km/h</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">View</span>
                    <span class="stat-value">19% 228/min</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Max of the month</span>
                    <span class="stat-value">80% 140.54km/h</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">View</span>
                    <span class="stat-value">14% 228/min</span>
                </div>
                <div class="mini-chart" id="trafficChart"></div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">Access Control List</div>
                <div class="stat-item">
                    <span class="stat-label">2102</span>
                    <span class="stat-value">A-2</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">10:30:00</span>
                    <span class="stat-value">Normal</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">10:30:00</span>
                    <span class="stat-value">Normal</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">11:30:00</span>
                    <span class="stat-value">Warning</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">12:30:00</span>
                    <span class="stat-value">Normal</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">13:30:00</span>
                    <span class="stat-value">Normal</span>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">Industry View Distribution</div>
                <div class="stat-item">
                    <span class="stat-label">Cloud Computing</span>
                    <span class="stat-value">20.1%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 20%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">E-commerce</span>
                    <span class="stat-value">38.1%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 38%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Development</span>
                    <span class="stat-value">32.1%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 32%"></div>
                </div>
            </div>
        </div>
        
        <!-- Bottom Panel -->
        <div class="bottom-panel">
            <div class="chart-container">
                <div class="panel-title">Utilization Statistics</div>
                <div id="utilizationChart" style="width: 100%; height: 120px;"></div>
            </div>
            <div class="chart-container">
                <div class="panel-title">Special Statistics</div>
                <div id="specialChart" style="width: 100%; height: 120px;"></div>
            </div>
            <div class="chart-container">
                <div class="panel-title">Utilization Statistics</div>
                <div id="utilizationChart2" style="width: 100%; height: 120px;"></div>
            </div>
        </div>
    </div>

    <script>
        // Initialize HT for Web
        var dataModel, g3d, borderPane;

        function init() {
            // Create data model
            dataModel = new ht.DataModel();

            // Create 3D graph view
            g3d = new ht.graph3d.Graph3dView(dataModel);
            g3d.getView().style.background = 'transparent';
            g3d.setGridVisible(false);
            g3d.setOriginAxisVisible(false);
            g3d.setCenterAxisVisible(false);

            // Set camera position for city view
            g3d.setEye([0, 2000, 3000]);
            g3d.setCenter([0, 0, 0]);
            g3d.setUp([0, 1, 0]);

            // Add to main 3D view container
            var container = document.getElementById('main3dView');
            container.appendChild(g3d.getView());

            // Create smart city scene
            createSmartCityScene();

            // Initialize charts
            initializeCharts();

            // Start animations
            startAnimations();
        }

        function createSmartCityScene() {
            // Create ground/base
            var ground = new ht.Node();
            ground.s({
                'shape3d': 'rect',
                'shape3d.color': '#1a1a2e',
                'shape3d.transparent': true,
                'shape3d.opacity': 0.8
            });
            ground.p3([0, -10, 0]);
            ground.s3([4000, 20, 4000]);
            dataModel.add(ground);

            // Create buildings
            createBuildings();

            // Create roads
            createRoads();

            // Create lights and effects
            createLights();

            // Create data visualization elements
            createDataElements();
        }

        function createBuildings() {
            var buildingData = [
                {pos: [-800, 0, -800], size: [200, 400, 200], color: '#00d4ff'},
                {pos: [800, 0, -800], size: [150, 600, 150], color: '#0099cc'},
                {pos: [-800, 0, 800], size: [180, 350, 180], color: '#00ffcc'},
                {pos: [800, 0, 800], size: [220, 500, 220], color: '#0066ff'},
                {pos: [0, 0, -1200], size: [300, 800, 300], color: '#00d4ff'},
                {pos: [-400, 0, 0], size: [120, 300, 120], color: '#0099cc'},
                {pos: [400, 0, 0], size: [140, 450, 140], color: '#00ffcc'},
                {pos: [0, 0, 1200], size: [250, 600, 250], color: '#0066ff'}
            ];

            buildingData.forEach(function(building, index) {
                var node = new ht.Node();
                node.s({
                    'shape3d': 'rect',
                    'shape3d.color': building.color,
                    'shape3d.transparent': true,
                    'shape3d.opacity': 0.8,
                    'shape3d.reverse.cull': true
                });
                node.p3([building.pos[0], building.size[1]/2, building.pos[2]]);
                node.s3(building.size);
                node.setToolTip('Smart Building ' + (index + 1));
                dataModel.add(node);

                // Add building lights
                for(var i = 0; i < 5; i++) {
                    var light = new ht.Node();
                    light.s({
                        'shape3d': 'rect',
                        'shape3d.color': '#ffff00',
                        'shape3d.transparent': true,
                        'shape3d.opacity': 0.9
                    });
                    light.p3([
                        building.pos[0] + (Math.random() - 0.5) * building.size[0] * 0.8,
                        building.size[1] * 0.2 + i * building.size[1] * 0.15,
                        building.pos[2] + (Math.random() - 0.5) * building.size[2] * 0.8
                    ]);
                    light.s3([20, 20, 5]);
                    dataModel.add(light);
                }
            });
        }

        function createRoads() {
            // Main roads
            var roads = [
                {pos: [0, 1, 0], size: [4000, 2, 100]},
                {pos: [0, 1, -600], size: [4000, 2, 100]},
                {pos: [0, 1, 600], size: [4000, 2, 100]},
                {pos: [-600, 1, 0], size: [100, 2, 4000]},
                {pos: [600, 1, 0], size: [100, 2, 4000]}
            ];

            roads.forEach(function(road) {
                var node = new ht.Node();
                node.s({
                    'shape3d': 'rect',
                    'shape3d.color': '#333333',
                    'shape3d.transparent': true,
                    'shape3d.opacity': 0.9
                });
                node.p3(road.pos);
                node.s3(road.size);
                dataModel.add(node);
            });
        }

        function createLights() {
            // Add ambient lighting
            var ambientLight = new ht.Light();
            ambientLight.s({
                'light.type': 'ambient',
                'light.color': '#404040'
            });
            dataModel.add(ambientLight);

            // Add directional light
            var dirLight = new ht.Light();
            dirLight.s({
                'light.type': 'directional',
                'light.color': '#ffffff',
                'light.direction': [1, -1, -1]
            });
            dataModel.add(dirLight);

            // Add point lights for city atmosphere
            var pointLights = [
                {pos: [-1000, 500, -1000], color: '#00d4ff'},
                {pos: [1000, 500, -1000], color: '#0099cc'},
                {pos: [-1000, 500, 1000], color: '#00ffcc'},
                {pos: [1000, 500, 1000], color: '#0066ff'}
            ];

            pointLights.forEach(function(light) {
                var pointLight = new ht.Light();
                pointLight.s({
                    'light.type': 'point',
                    'light.color': light.color,
                    'light.range': 2000
                });
                pointLight.p3(light.pos);
                dataModel.add(pointLight);
            });
        }

        function createDataElements() {
            // Create floating data visualization elements
            var dataPoints = [
                {pos: [-600, 200, -600], value: '85%', label: 'Traffic'},
                {pos: [600, 200, -600], value: '92%', label: 'Energy'},
                {pos: [-600, 200, 600], value: '78%', label: 'Water'},
                {pos: [600, 200, 600], value: '96%', label: 'Network'}
            ];

            dataPoints.forEach(function(point) {
                var node = new ht.Node();
                node.s({
                    'shape3d': 'sphere',
                    'shape3d.color': '#00d4ff',
                    'shape3d.transparent': true,
                    'shape3d.opacity': 0.7,
                    'note': point.label + ': ' + point.value,
                    'note.background': '#00d4ff',
                    'note.color': '#000000',
                    'note.font': '16px Arial',
                    'note.position': 6,
                    'note.t3': [0, 50, 0],
                    'note.expanded': true,
                    'note.autorotate': true
                });
                node.p3(point.pos);
                node.s3([50, 50, 50]);
                node.setToolTip(point.label + ': ' + point.value);
                dataModel.add(node);
            });
        }

        function initializeCharts() {
            // Initialize mini charts with animated bars
            createMiniChart('equipmentChart', [80, 65, 90, 75, 85, 70, 95]);
            createMiniChart('trafficChart', [60, 75, 80, 65, 90, 85, 70]);

            // Initialize bottom charts
            createBottomChart('utilizationChart');
            createBottomChart('specialChart');
            createBottomChart('utilizationChart2');
        }

        function createMiniChart(containerId, data) {
            var container = document.getElementById(containerId);
            if (!container) return;

            container.innerHTML = '';
            var maxValue = Math.max.apply(Math, data);

            data.forEach(function(value, index) {
                var bar = document.createElement('div');
                bar.className = 'chart-line';
                bar.style.left = (index * (100 / data.length)) + '%';
                bar.style.height = (value / maxValue * 100) + '%';
                bar.style.background = 'linear-gradient(to top, #00d4ff, #00ffcc)';
                container.appendChild(bar);
            });
        }

        function createBottomChart(containerId) {
            var container = document.getElementById(containerId);
            if (!container) return;

            // Create simple line chart visualization
            var canvas = document.createElement('canvas');
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            container.appendChild(canvas);

            var ctx = canvas.getContext('2d');

            // Draw chart background
            ctx.fillStyle = 'rgba(0, 212, 255, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw chart lines
            ctx.strokeStyle = '#00d4ff';
            ctx.lineWidth = 2;
            ctx.beginPath();

            var points = 20;
            for (var i = 0; i <= points; i++) {
                var x = (i / points) * canvas.width;
                var y = canvas.height - (Math.sin(i * 0.5) * 0.3 + 0.5 + Math.random() * 0.2) * canvas.height;

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
        }

        function startAnimations() {
            // Rotate camera around the city
            var angle = 0;
            var radius = 3000;
            var height = 2000;

            function animate() {
                angle += 0.005;
                var x = Math.cos(angle) * radius;
                var z = Math.sin(angle) * radius;
                g3d.setEye([x, height, z]);
                g3d.setCenter([0, 0, 0]);

                requestAnimationFrame(animate);
            }

            animate();

            // Update statistics periodically
            setInterval(updateStatistics, 3000);
        }

        function updateStatistics() {
            // Update progress bars with random values
            var progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(function(bar) {
                var newWidth = Math.random() * 100;
                bar.style.width = newWidth + '%';
            });

            // Update stat values
            var statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(function(stat) {
                if (stat.textContent.includes('%')) {
                    stat.textContent = Math.floor(Math.random() * 100) + '%';
                } else if (stat.textContent.includes('km/h')) {
                    stat.textContent = Math.floor(Math.random() * 200) + 'km/h';
                }
            });
        }

        // Initialize when page loads
        window.onload = function() {
            init();
        };
    </script>
</body>
</html>
