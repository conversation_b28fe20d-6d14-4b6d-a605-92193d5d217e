<!DOCTYPE html>
<html>
<head>
    <title>Hightopo Smart City Dashboard</title>
    <meta charset="UTF-8">
    <style>
        html, body {
            padding: 0px;
            margin: 0px;
            background: #0a0a0a;
            font-family: 'Arial', sans-serif;
            color: #ffffff;
            overflow: hidden;
        }
        
        .dashboard-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
        }
        
        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(0, 20, 40, 0.8);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid #00d4ff;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
        
        .main-3d-view {
            position: absolute;
            top: 80px;
            left: 300px;
            right: 300px;
            bottom: 200px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid #00d4ff;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .left-panel {
            position: absolute;
            top: 80px;
            left: 0;
            width: 300px;
            bottom: 0;
            background: rgba(0, 20, 40, 0.9);
            backdrop-filter: blur(10px);
            border-right: 1px solid #00d4ff;
            overflow-y: auto;
        }
        
        .right-panel {
            position: absolute;
            top: 80px;
            right: 0;
            width: 300px;
            bottom: 0;
            background: rgba(0, 20, 40, 0.9);
            backdrop-filter: blur(10px);
            border-left: 1px solid #00d4ff;
            overflow-y: auto;
        }
        
        .bottom-panel {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: rgba(0, 20, 40, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid #00d4ff;
            display: flex;
        }
        
        .panel-section {
            margin: 20px;
            padding: 15px;
            background: rgba(0, 50, 100, 0.3);
            border: 1px solid #00d4ff;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }
        
        .stat-label {
            font-size: 12px;
            color: #a0a0a0;
        }
        
        .stat-value {
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
        }
        
        .chart-container {
            flex: 1;
            margin: 10px;
            background: rgba(0, 50, 100, 0.3);
            border: 1px solid #00d4ff;
            border-radius: 8px;
            padding: 15px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(0, 212, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #0099cc);
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .control-button {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid #00d4ff;
            color: #00d4ff;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            background: rgba(0, 212, 255, 0.4);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
        
        .mini-chart {
            width: 100%;
            height: 60px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .chart-line {
            position: absolute;
            bottom: 0;
            width: 2px;
            background: #00d4ff;
            transition: height 0.3s ease;
        }
    </style>
    <script src="lib/core/ht.js"></script>
    <script src="lib/plugin/ht-form.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <h1>Hightopo Smart City</h1>
        </div>
        
        <!-- Left Panel -->
        <div class="left-panel">
            <div class="panel-section">
                <div class="panel-title">City Equipment Operation</div>
                <div class="stat-item">
                    <span class="stat-label">Online</span>
                    <span class="stat-value">100</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Offline</span>
                    <span class="stat-value">30</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Warning</span>
                    <span class="stat-value">5</span>
                </div>
                <div class="mini-chart" id="equipmentChart"></div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">Real-time Statistics</div>
                <div class="stat-item">
                    <span class="stat-label">A-region</span>
                    <span class="stat-value">80%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 80%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">B-region</span>
                    <span class="stat-value">65%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 65%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">C-region</span>
                    <span class="stat-value">90%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 90%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">D-region</span>
                    <span class="stat-value">75%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 75%"></div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">City Warning</div>
                <div class="stat-item">
                    <span class="stat-label">Level</span>
                    <span class="stat-value">5550.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Count</span>
                    <span class="stat-value">22</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Level</span>
                    <span class="stat-value">1243.00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Count</span>
                    <span class="stat-value">163425</span>
                </div>
            </div>
        </div>
        
        <!-- Main 3D View -->
        <div class="main-3d-view" id="main3dView"></div>
        
        <!-- Right Panel -->
        <div class="right-panel">
            <div class="panel-section">
                <div class="panel-title">Real Traffic Analysis</div>
                <div class="stat-item">
                    <span class="stat-label">Max of today</span>
                    <span class="stat-value">80% 140.54km/h</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">View</span>
                    <span class="stat-value">19% 228/min</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Max of the month</span>
                    <span class="stat-value">80% 140.54km/h</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">View</span>
                    <span class="stat-value">14% 228/min</span>
                </div>
                <div class="mini-chart" id="trafficChart"></div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">Access Control List</div>
                <div class="stat-item">
                    <span class="stat-label">2102</span>
                    <span class="stat-value">A-2</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">10:30:00</span>
                    <span class="stat-value">Normal</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">10:30:00</span>
                    <span class="stat-value">Normal</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">11:30:00</span>
                    <span class="stat-value">Warning</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">12:30:00</span>
                    <span class="stat-value">Normal</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">13:30:00</span>
                    <span class="stat-value">Normal</span>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">Industry View Distribution</div>
                <div class="stat-item">
                    <span class="stat-label">Cloud Computing</span>
                    <span class="stat-value">20.1%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 20%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">E-commerce</span>
                    <span class="stat-value">38.1%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 38%"></div>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Development</span>
                    <span class="stat-value">32.1%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 32%"></div>
                </div>
            </div>
        </div>
        
        <!-- Camera Controls -->
        <div style="position: absolute; top: 90px; right: 310px; z-index: 1000;">
            <button class="control-button" onclick="setCameraMode(0)">Orbit View</button>
            <button class="control-button" onclick="setCameraMode(1)">Fly Through</button>
            <button class="control-button" onclick="setCameraMode(2)">Overview</button>
            <button class="control-button" onclick="toggleLights()">Toggle Lights</button>
            <button class="control-button" onclick="resetCamera()">Reset Camera</button>
        </div>

        <!-- Bottom Panel -->
        <div class="bottom-panel">
            <div class="chart-container">
                <div class="panel-title">Utilization Statistics</div>
                <div id="utilizationChart" style="width: 100%; height: 120px;"></div>
            </div>
            <div class="chart-container">
                <div class="panel-title">Special Statistics</div>
                <div id="specialChart" style="width: 100%; height: 120px;"></div>
            </div>
            <div class="chart-container">
                <div class="panel-title">Real-time Analytics</div>
                <div id="utilizationChart2" style="width: 100%; height: 120px;"></div>
            </div>
        </div>
    </div>

    <script>
        // Initialize HT for Web
        var dataModel, g3d, borderPane;

        function init() {
            // Create data model
            dataModel = new ht.DataModel();

            // Create 3D graph view
            g3d = new ht.graph3d.Graph3dView(dataModel);
            g3d.getView().style.background = 'transparent';
            g3d.setGridVisible(false);
            g3d.setOriginAxisVisible(false);
            g3d.setCenterAxisVisible(false);

            // Set camera position for city view
            g3d.setEye([0, 2000, 3000]);
            g3d.setCenter([0, 0, 0]);
            g3d.setUp([0, 1, 0]);

            // Add to main 3D view container
            var container = document.getElementById('main3dView');
            container.appendChild(g3d.getView());

            // Create smart city scene
            createSmartCityScene();

            // Initialize charts
            initializeCharts();

            // Start animations
            startAnimations();
        }

        function createSmartCityScene() {
            // Create ground/base
            var ground = new ht.Node();
            ground.s({
                'shape3d': 'rect',
                'shape3d.color': '#1a1a2e',
                'shape3d.transparent': true,
                'shape3d.opacity': 0.8
            });
            ground.p3([0, -10, 0]);
            ground.s3([4000, 20, 4000]);
            dataModel.add(ground);

            // Create buildings
            createBuildings();

            // Create roads
            createRoads();

            // Create lights and effects
            createLights();

            // Create data visualization elements
            createDataElements();
        }

        function createBuildings() {
            var buildingData = [
                {pos: [-800, 0, -800], size: [200, 400, 200], color: '#00d4ff', type: 'office'},
                {pos: [800, 0, -800], size: [150, 600, 150], color: '#0099cc', type: 'residential'},
                {pos: [-800, 0, 800], size: [180, 350, 180], color: '#00ffcc', type: 'commercial'},
                {pos: [800, 0, 800], size: [220, 500, 220], color: '#0066ff', type: 'mixed'},
                {pos: [0, 0, -1200], size: [300, 800, 300], color: '#00d4ff', type: 'skyscraper'},
                {pos: [-400, 0, 0], size: [120, 300, 120], color: '#0099cc', type: 'office'},
                {pos: [400, 0, 0], size: [140, 450, 140], color: '#00ffcc', type: 'residential'},
                {pos: [0, 0, 1200], size: [250, 600, 250], color: '#0066ff', type: 'commercial'},
                {pos: [-1200, 0, -400], size: [160, 380, 160], color: '#ff6600', type: 'industrial'},
                {pos: [1200, 0, -400], size: [180, 420, 180], color: '#ff3366', type: 'hospital'},
                {pos: [-1200, 0, 400], size: [200, 320, 200], color: '#66ff00', type: 'school'},
                {pos: [1200, 0, 400], size: [170, 480, 170], color: '#9966ff', type: 'shopping'}
            ];

            buildingData.forEach(function(building, index) {
                var node = new ht.Node();
                node.s({
                    'shape3d': 'rect',
                    'shape3d.color': building.color,
                    'shape3d.transparent': true,
                    'shape3d.opacity': 0.85,
                    'shape3d.reverse.cull': true,
                    'shape3d.top.color': building.color,
                    'shape3d.side.color': ht.Default.darker(building.color, 0.3)
                });
                node.p3([building.pos[0], building.size[1]/2, building.pos[2]]);
                node.s3(building.size);
                node.setToolTip('Smart Building ' + (index + 1) + ' - ' + building.type);
                dataModel.add(node);

                // Add animated building lights (windows)
                var lightsPerFloor = 8;
                var floors = Math.floor(building.size[1] / 50);

                for(var floor = 0; floor < floors; floor++) {
                    for(var lightIndex = 0; lightIndex < lightsPerFloor; lightIndex++) {
                        var light = new ht.Node();
                        var lightColor = Math.random() > 0.3 ? '#ffff99' : '#ffffff';

                        light.s({
                            'shape3d': 'rect',
                            'shape3d.color': lightColor,
                            'shape3d.transparent': true,
                            'shape3d.opacity': Math.random() * 0.5 + 0.5
                        });

                        // Position lights on building faces
                        var face = Math.floor(Math.random() * 4);
                        var x, z;

                        switch(face) {
                            case 0: // Front face
                                x = building.pos[0] + (Math.random() - 0.5) * building.size[0] * 0.8;
                                z = building.pos[2] + building.size[2] / 2 + 2;
                                break;
                            case 1: // Back face
                                x = building.pos[0] + (Math.random() - 0.5) * building.size[0] * 0.8;
                                z = building.pos[2] - building.size[2] / 2 - 2;
                                break;
                            case 2: // Left face
                                x = building.pos[0] - building.size[0] / 2 - 2;
                                z = building.pos[2] + (Math.random() - 0.5) * building.size[2] * 0.8;
                                break;
                            case 3: // Right face
                                x = building.pos[0] + building.size[0] / 2 + 2;
                                z = building.pos[2] + (Math.random() - 0.5) * building.size[2] * 0.8;
                                break;
                        }

                        light.p3([x, 30 + floor * 50, z]);
                        light.s3([15, 15, 3]);
                        light.buildingIndex = index;
                        light.lightIndex = lightIndex;
                        dataModel.add(light);
                    }
                }

                // Add rooftop elements
                var rooftop = new ht.Node();
                rooftop.s({
                    'shape3d': 'cylinder',
                    'shape3d.color': '#ff0000',
                    'shape3d.transparent': true,
                    'shape3d.opacity': 0.9
                });
                rooftop.p3([building.pos[0], building.size[1] + 20, building.pos[2]]);
                rooftop.s3([20, 40, 20]);
                rooftop.setToolTip('Communication Tower');
                dataModel.add(rooftop);
            });
        }

        function createRoads() {
            // Main roads with lane markings
            var roads = [
                {pos: [0, 1, 0], size: [4000, 2, 100], type: 'main'},
                {pos: [0, 1, -600], size: [4000, 2, 100], type: 'main'},
                {pos: [0, 1, 600], size: [4000, 2, 100], type: 'main'},
                {pos: [-600, 1, 0], size: [100, 2, 4000], type: 'main'},
                {pos: [600, 1, 0], size: [100, 2, 4000], type: 'main'},
                // Secondary roads
                {pos: [-300, 1, -300], size: [2000, 2, 60], type: 'secondary'},
                {pos: [300, 1, -300], size: [2000, 2, 60], type: 'secondary'},
                {pos: [-300, 1, 300], size: [2000, 2, 60], type: 'secondary'},
                {pos: [300, 1, 300], size: [2000, 2, 60], type: 'secondary'}
            ];

            roads.forEach(function(road, index) {
                var node = new ht.Node();
                var roadColor = road.type === 'main' ? '#2a2a2a' : '#404040';

                node.s({
                    'shape3d': 'rect',
                    'shape3d.color': roadColor,
                    'shape3d.transparent': true,
                    'shape3d.opacity': 0.95
                });
                node.p3(road.pos);
                node.s3(road.size);
                node.setToolTip('Road ' + (index + 1));
                dataModel.add(node);

                // Add lane markings
                if (road.size[0] > road.size[2]) { // Horizontal road
                    for(var i = -road.size[0]/2; i < road.size[0]/2; i += 200) {
                        var marking = new ht.Node();
                        marking.s({
                            'shape3d': 'rect',
                            'shape3d.color': '#ffffff',
                            'shape3d.transparent': true,
                            'shape3d.opacity': 0.8
                        });
                        marking.p3([road.pos[0] + i, road.pos[1] + 1, road.pos[2]]);
                        marking.s3([80, 1, 5]);
                        dataModel.add(marking);
                    }
                } else { // Vertical road
                    for(var i = -road.size[2]/2; i < road.size[2]/2; i += 200) {
                        var marking = new ht.Node();
                        marking.s({
                            'shape3d': 'rect',
                            'shape3d.color': '#ffffff',
                            'shape3d.transparent': true,
                            'shape3d.opacity': 0.8
                        });
                        marking.p3([road.pos[0], road.pos[1] + 1, road.pos[2] + i]);
                        marking.s3([5, 1, 80]);
                        dataModel.add(marking);
                    }
                }
            });

            // Add traffic lights
            var trafficLightPositions = [
                [-600, 0, -600], [600, 0, -600], [-600, 0, 600], [600, 0, 600],
                [0, 0, -600], [0, 0, 600], [-600, 0, 0], [600, 0, 0]
            ];

            trafficLightPositions.forEach(function(pos, index) {
                createTrafficLight(pos, index);
            });

            // Add street lights
            for(var x = -1800; x <= 1800; x += 300) {
                for(var z = -1800; z <= 1800; z += 300) {
                    if (Math.abs(x) > 100 || Math.abs(z) > 100) {
                        createStreetLight([x, 0, z]);
                    }
                }
            }
        }

        function createTrafficLight(position, index) {
            // Traffic light pole
            var pole = new ht.Node();
            pole.s({
                'shape3d': 'cylinder',
                'shape3d.color': '#666666',
                'shape3d.transparent': true,
                'shape3d.opacity': 0.9
            });
            pole.p3([position[0], 100, position[2]]);
            pole.s3([8, 200, 8]);
            dataModel.add(pole);

            // Traffic light box
            var lightBox = new ht.Node();
            lightBox.s({
                'shape3d': 'rect',
                'shape3d.color': '#333333',
                'shape3d.transparent': true,
                'shape3d.opacity': 0.9
            });
            lightBox.p3([position[0], 180, position[2]]);
            lightBox.s3([30, 60, 15]);
            dataModel.add(lightBox);

            // Traffic lights (red, yellow, green)
            var lightColors = ['#ff0000', '#ffff00', '#00ff00'];
            lightColors.forEach(function(color, i) {
                var light = new ht.Node();
                light.s({
                    'shape3d': 'sphere',
                    'shape3d.color': color,
                    'shape3d.transparent': true,
                    'shape3d.opacity': i === (index % 3) ? 1.0 : 0.3
                });
                light.p3([position[0], 195 - i * 15, position[2] + 8]);
                light.s3([8, 8, 8]);
                light.trafficLightIndex = index;
                light.lightPosition = i;
                dataModel.add(light);
            });
        }

        function createStreetLight(position) {
            // Street light pole
            var pole = new ht.Node();
            pole.s({
                'shape3d': 'cylinder',
                'shape3d.color': '#555555',
                'shape3d.transparent': true,
                'shape3d.opacity': 0.9
            });
            pole.p3([position[0], 150, position[2]]);
            pole.s3([6, 300, 6]);
            dataModel.add(pole);

            // Street light
            var light = new ht.Node();
            light.s({
                'shape3d': 'sphere',
                'shape3d.color': '#ffffaa',
                'shape3d.transparent': true,
                'shape3d.opacity': 0.8
            });
            light.p3([position[0], 280, position[2]]);
            light.s3([25, 25, 25]);
            dataModel.add(light);

            // Add point light for illumination
            var pointLight = new ht.Light();
            pointLight.s({
                'light.type': 'point',
                'light.color': '#ffffaa',
                'light.range': 400,
                'light.intensity': 0.5
            });
            pointLight.p3([position[0], 280, position[2]]);
            dataModel.add(pointLight);
        }

        function createLights() {
            // Add ambient lighting
            var ambientLight = new ht.Light();
            ambientLight.s({
                'light.type': 'ambient',
                'light.color': '#404040'
            });
            dataModel.add(ambientLight);

            // Add directional light
            var dirLight = new ht.Light();
            dirLight.s({
                'light.type': 'directional',
                'light.color': '#ffffff',
                'light.direction': [1, -1, -1]
            });
            dataModel.add(dirLight);

            // Add point lights for city atmosphere
            var pointLights = [
                {pos: [-1000, 500, -1000], color: '#00d4ff'},
                {pos: [1000, 500, -1000], color: '#0099cc'},
                {pos: [-1000, 500, 1000], color: '#00ffcc'},
                {pos: [1000, 500, 1000], color: '#0066ff'}
            ];

            pointLights.forEach(function(light) {
                var pointLight = new ht.Light();
                pointLight.s({
                    'light.type': 'point',
                    'light.color': light.color,
                    'light.range': 2000
                });
                pointLight.p3(light.pos);
                dataModel.add(pointLight);
            });
        }

        function createDataElements() {
            // Create floating data visualization elements
            var dataPoints = [
                {pos: [-600, 200, -600], value: '85%', label: 'Traffic'},
                {pos: [600, 200, -600], value: '92%', label: 'Energy'},
                {pos: [-600, 200, 600], value: '78%', label: 'Water'},
                {pos: [600, 200, 600], value: '96%', label: 'Network'}
            ];

            dataPoints.forEach(function(point) {
                var node = new ht.Node();
                node.s({
                    'shape3d': 'sphere',
                    'shape3d.color': '#00d4ff',
                    'shape3d.transparent': true,
                    'shape3d.opacity': 0.7,
                    'note': point.label + ': ' + point.value,
                    'note.background': '#00d4ff',
                    'note.color': '#000000',
                    'note.font': '16px Arial',
                    'note.position': 6,
                    'note.t3': [0, 50, 0],
                    'note.expanded': true,
                    'note.autorotate': true
                });
                node.p3(point.pos);
                node.s3([50, 50, 50]);
                node.setToolTip(point.label + ': ' + point.value);
                dataModel.add(node);
            });
        }

        function initializeCharts() {
            // Initialize mini charts with animated bars
            createMiniChart('equipmentChart', [80, 65, 90, 75, 85, 70, 95]);
            createMiniChart('trafficChart', [60, 75, 80, 65, 90, 85, 70]);

            // Initialize bottom charts
            createBottomChart('utilizationChart');
            createBottomChart('specialChart');
            createBottomChart('utilizationChart2');
        }

        function createMiniChart(containerId, data) {
            var container = document.getElementById(containerId);
            if (!container) return;

            container.innerHTML = '';
            var maxValue = Math.max.apply(Math, data);

            data.forEach(function(value, index) {
                var bar = document.createElement('div');
                bar.className = 'chart-line';
                bar.style.left = (index * (100 / data.length)) + '%';
                bar.style.height = (value / maxValue * 100) + '%';
                bar.style.background = 'linear-gradient(to top, #00d4ff, #00ffcc)';
                container.appendChild(bar);
            });
        }

        function createBottomChart(containerId) {
            var container = document.getElementById(containerId);
            if (!container) return;

            // Create simple line chart visualization
            var canvas = document.createElement('canvas');
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            container.appendChild(canvas);

            var ctx = canvas.getContext('2d');

            // Draw chart background
            ctx.fillStyle = 'rgba(0, 212, 255, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw chart lines
            ctx.strokeStyle = '#00d4ff';
            ctx.lineWidth = 2;
            ctx.beginPath();

            var points = 20;
            for (var i = 0; i <= points; i++) {
                var x = (i / points) * canvas.width;
                var y = canvas.height - (Math.sin(i * 0.5) * 0.3 + 0.5 + Math.random() * 0.2) * canvas.height;

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
        }

        function startAnimations() {
            // Enhanced camera rotation with multiple modes
            var angle = 0;
            var radius = 3000;
            var height = 2000;
            var cameraMode = 0; // 0: orbit, 1: fly-through, 2: static overview
            var flyThroughPoints = [
                {eye: [0, 500, 2000], center: [0, 200, 0]},
                {eye: [1500, 800, 1500], center: [0, 0, 0]},
                {eye: [0, 1200, -2000], center: [0, 0, 0]},
                {eye: [-1500, 600, 1000], center: [500, 0, -500]},
                {eye: [2000, 1500, 0], center: [0, 0, 0]}
            ];
            var flyThroughIndex = 0;
            var transitionProgress = 0;

            function animate() {
                switch(cameraMode) {
                    case 0: // Orbital camera
                        angle += 0.008;
                        var x = Math.cos(angle) * radius;
                        var z = Math.sin(angle) * radius;
                        var y = height + Math.sin(angle * 2) * 300;
                        g3d.setEye([x, y, z]);
                        g3d.setCenter([0, 0, 0]);
                        break;

                    case 1: // Fly-through camera
                        transitionProgress += 0.01;
                        if (transitionProgress >= 1) {
                            transitionProgress = 0;
                            flyThroughIndex = (flyThroughIndex + 1) % flyThroughPoints.length;
                        }

                        var currentPoint = flyThroughPoints[flyThroughIndex];
                        var nextPoint = flyThroughPoints[(flyThroughIndex + 1) % flyThroughPoints.length];

                        var eyeX = currentPoint.eye[0] + (nextPoint.eye[0] - currentPoint.eye[0]) * transitionProgress;
                        var eyeY = currentPoint.eye[1] + (nextPoint.eye[1] - currentPoint.eye[1]) * transitionProgress;
                        var eyeZ = currentPoint.eye[2] + (nextPoint.eye[2] - currentPoint.eye[2]) * transitionProgress;

                        var centerX = currentPoint.center[0] + (nextPoint.center[0] - currentPoint.center[0]) * transitionProgress;
                        var centerY = currentPoint.center[1] + (nextPoint.center[1] - currentPoint.center[1]) * transitionProgress;
                        var centerZ = currentPoint.center[2] + (nextPoint.center[2] - currentPoint.center[2]) * transitionProgress;

                        g3d.setEye([eyeX, eyeY, eyeZ]);
                        g3d.setCenter([centerX, centerY, centerZ]);
                        break;

                    case 2: // Static overview
                        g3d.setEye([0, 2500, 2500]);
                        g3d.setCenter([0, 0, 0]);
                        break;
                }

                requestAnimationFrame(animate);
            }

            animate();

            // Change camera mode every 15 seconds
            setInterval(function() {
                cameraMode = (cameraMode + 1) % 3;
                angle = 0;
                transitionProgress = 0;
                flyThroughIndex = 0;
            }, 15000);

            // Animate building lights
            setInterval(animateBuildingLights, 2000);

            // Animate traffic lights
            setInterval(animateTrafficLights, 3000);

            // Update statistics periodically
            setInterval(updateStatistics, 2000);

            // Update real-time data
            setInterval(updateRealTimeData, 1000);
        }

        function animateBuildingLights() {
            dataModel.each(function(data) {
                if (data.lightIndex !== undefined) {
                    var newOpacity = Math.random() > 0.7 ? Math.random() * 0.5 + 0.5 : 0.1;
                    data.s('shape3d.opacity', newOpacity);

                    // Occasionally change light color
                    if (Math.random() > 0.9) {
                        var colors = ['#ffff99', '#ffffff', '#ffccaa', '#aaffff'];
                        var newColor = colors[Math.floor(Math.random() * colors.length)];
                        data.s('shape3d.color', newColor);
                    }
                }
            });
        }

        function animateTrafficLights() {
            dataModel.each(function(data) {
                if (data.trafficLightIndex !== undefined) {
                    var isActive = data.lightPosition === (data.trafficLightIndex % 3);
                    data.s('shape3d.opacity', isActive ? 1.0 : 0.3);
                }
            });
        }

        function updateStatistics() {
            // Update progress bars with realistic fluctuations
            var progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(function(bar, index) {
                var currentWidth = parseFloat(bar.style.width) || 50;
                var change = (Math.random() - 0.5) * 10;
                var newWidth = Math.max(10, Math.min(95, currentWidth + change));
                bar.style.width = newWidth + '%';

                // Update corresponding stat value
                var statItem = bar.closest('.stat-item');
                if (statItem) {
                    var statValue = statItem.querySelector('.stat-value');
                    if (statValue && statValue.textContent.includes('%')) {
                        statValue.textContent = Math.floor(newWidth) + '%';
                    }
                }
            });

            // Update mini charts
            updateMiniChart('equipmentChart');
            updateMiniChart('trafficChart');

            // Update bottom charts
            updateBottomCharts();
        }

        function updateRealTimeData() {
            // Update equipment operation numbers
            var onlineCount = 95 + Math.floor(Math.random() * 10);
            var offlineCount = 35 - Math.floor(Math.random() * 10);
            var warningCount = Math.floor(Math.random() * 8);

            // Update traffic analysis
            var maxSpeed = 120 + Math.floor(Math.random() * 50);
            var avgSpeed = 60 + Math.floor(Math.random() * 40);
            var trafficFlow = 200 + Math.floor(Math.random() * 100);

            // Update city warning levels
            var warningLevel1 = 5000 + Math.floor(Math.random() * 1000);
            var warningLevel2 = 1000 + Math.floor(Math.random() * 500);
            var warningCount1 = 20 + Math.floor(Math.random() * 10);
            var warningCount2 = 160000 + Math.floor(Math.random() * 10000);

            // Apply updates to DOM
            var statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(function(stat) {
                var text = stat.textContent;
                var label = stat.closest('.stat-item').querySelector('.stat-label').textContent;

                switch(label) {
                    case 'Online':
                        stat.textContent = onlineCount;
                        break;
                    case 'Offline':
                        stat.textContent = offlineCount;
                        break;
                    case 'Warning':
                        stat.textContent = warningCount;
                        break;
                    case 'Max of today':
                        stat.textContent = Math.floor(Math.random() * 100) + '% ' + maxSpeed + 'km/h';
                        break;
                    case 'Max of the month':
                        stat.textContent = Math.floor(Math.random() * 100) + '% ' + avgSpeed + 'km/h';
                        break;
                    case 'View':
                        stat.textContent = Math.floor(Math.random() * 30) + '% ' + trafficFlow + '/min';
                        break;
                    case 'Level':
                        if (text.includes('5')) {
                            stat.textContent = warningLevel1.toFixed(2);
                        } else {
                            stat.textContent = warningLevel2.toFixed(2);
                        }
                        break;
                    case 'Count':
                        if (text.includes('22')) {
                            stat.textContent = warningCount1;
                        } else {
                            stat.textContent = warningCount2;
                        }
                        break;
                }
            });

            // Update time stamps
            var now = new Date();
            var timeString = now.getHours().toString().padStart(2, '0') + ':' +
                           now.getMinutes().toString().padStart(2, '0') + ':' +
                           now.getSeconds().toString().padStart(2, '0');

            var timeLabels = document.querySelectorAll('.stat-label');
            timeLabels.forEach(function(label) {
                if (label.textContent.includes(':')) {
                    label.textContent = timeString;
                }
            });
        }

        function updateMiniChart(containerId) {
            var container = document.getElementById(containerId);
            if (!container) return;

            var bars = container.querySelectorAll('.chart-line');
            bars.forEach(function(bar) {
                var currentHeight = parseFloat(bar.style.height) || 50;
                var change = (Math.random() - 0.5) * 20;
                var newHeight = Math.max(10, Math.min(100, currentHeight + change));
                bar.style.height = newHeight + '%';
            });
        }

        function updateBottomCharts() {
            ['utilizationChart', 'specialChart', 'utilizationChart2'].forEach(function(chartId) {
                var container = document.getElementById(chartId);
                if (!container) return;

                var canvas = container.querySelector('canvas');
                if (!canvas) return;

                var ctx = canvas.getContext('2d');

                // Clear canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Draw updated chart background
                ctx.fillStyle = 'rgba(0, 212, 255, 0.1)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw updated chart lines
                ctx.strokeStyle = '#00d4ff';
                ctx.lineWidth = 2;
                ctx.beginPath();

                var points = 20;
                for (var i = 0; i <= points; i++) {
                    var x = (i / points) * canvas.width;
                    var y = canvas.height - (Math.sin(i * 0.5 + Date.now() * 0.001) * 0.3 + 0.5 + Math.random() * 0.2) * canvas.height;

                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();

                // Add data points
                ctx.fillStyle = '#00d4ff';
                for (var i = 0; i <= points; i += 4) {
                    var x = (i / points) * canvas.width;
                    var y = canvas.height - (Math.sin(i * 0.5 + Date.now() * 0.001) * 0.3 + 0.5 + Math.random() * 0.2) * canvas.height;
                    ctx.beginPath();
                    ctx.arc(x, y, 3, 0, 2 * Math.PI);
                    ctx.fill();
                }
            });
        }

        // Camera control functions
        var currentCameraMode = 0;
        var lightsEnabled = true;

        function setCameraMode(mode) {
            currentCameraMode = mode;
            cameraMode = mode;
            angle = 0;
            transitionProgress = 0;
            flyThroughIndex = 0;
        }

        function toggleLights() {
            lightsEnabled = !lightsEnabled;
            dataModel.each(function(data) {
                if (data.lightIndex !== undefined || data.s('light.type')) {
                    data.s('shape3d.opacity', lightsEnabled ? data.s('shape3d.opacity') : 0.1);
                }
            });
        }

        function resetCamera() {
            g3d.setEye([0, 2000, 3000]);
            g3d.setCenter([0, 0, 0]);
            g3d.setUp([0, 1, 0]);
            setCameraMode(0);
        }

        // Add mouse interaction for manual camera control
        function addCameraControls() {
            var view = g3d.getView();
            var isDragging = false;
            var lastX, lastY;
            var currentEye = [0, 2000, 3000];
            var currentCenter = [0, 0, 0];

            view.addEventListener('mousedown', function(e) {
                if (e.button === 0) { // Left mouse button
                    isDragging = true;
                    lastX = e.clientX;
                    lastY = e.clientY;
                    setCameraMode(-1); // Manual mode
                }
            });

            view.addEventListener('mousemove', function(e) {
                if (isDragging) {
                    var deltaX = e.clientX - lastX;
                    var deltaY = e.clientY - lastY;

                    // Rotate camera around center
                    var eye = g3d.getEye();
                    var center = g3d.getCenter();

                    var dx = eye[0] - center[0];
                    var dz = eye[2] - center[2];
                    var radius = Math.sqrt(dx * dx + dz * dz);
                    var angle = Math.atan2(dz, dx);

                    angle += deltaX * 0.01;
                    var newEye = [
                        center[0] + Math.cos(angle) * radius,
                        eye[1] + deltaY * 5,
                        center[2] + Math.sin(angle) * radius
                    ];

                    g3d.setEye(newEye);

                    lastX = e.clientX;
                    lastY = e.clientY;
                }
            });

            view.addEventListener('mouseup', function(e) {
                isDragging = false;
            });

            view.addEventListener('wheel', function(e) {
                e.preventDefault();
                var eye = g3d.getEye();
                var center = g3d.getCenter();
                var direction = [
                    center[0] - eye[0],
                    center[1] - eye[1],
                    center[2] - eye[2]
                ];
                var length = Math.sqrt(direction[0] * direction[0] + direction[1] * direction[1] + direction[2] * direction[2]);
                direction[0] /= length;
                direction[1] /= length;
                direction[2] /= length;

                var zoomFactor = e.deltaY > 0 ? 100 : -100;
                var newEye = [
                    eye[0] + direction[0] * zoomFactor,
                    eye[1] + direction[1] * zoomFactor,
                    eye[2] + direction[2] * zoomFactor
                ];

                g3d.setEye(newEye);
                setCameraMode(-1); // Manual mode
            });
        }

        // Initialize when page loads
        window.onload = function() {
            init();
            addCameraControls();
        };
    </script>
</body>
</html>
