<!DOCTYPE html>
<html>
    <head>
        <title>Label</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script>   
        <script>                                       
            
            function init(){                                
                dataModel = new ht.DataModel();
                graphView = new ht.graph.GraphView(dataModel);
                view = graphView.getView();            
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    graphView.invalidate();
                }, false);  
                                   
                var node = createNode(150, 150);
                node.setSize(50, 50);
                node.setStyle('shape', 'circle');
                node.setStyle('shape.background', 'red');
                node.setStyle('shape.gradient', 'radial.center');                
                node.setStyle('label.max', 260);
                node.setStyle('label.position', 17);
                node.setStyle('label.font', '15px Arial'); 
                node.setStyle('note', 'Drag me to rotate label');                   

                var angle = Math.PI/3;
                node.setStyle('label.rotation', angle);

                graphView.getLabel = function(data){
                    if(data === node){
                        var p = data.getPosition();
                        return 'Position x:' + p.x + ', y:' + p.y + ', Pretty long long long long long label';                        
                    }
                    return data.getName();
                };       
                
                graphView.addInteractorListener(function(e){
                    if(graphView.isSelected(node) && e.kind === 'betweenMove'){
                        angle -= Math.PI/20;
                        node.setStyle('label.rotation', angle);
                    }                    
                });
                
                n1 = createNode(500, 100, 'source');               
                n2 = createNode(300, 200, 'target');   
                n3 = createNode(800, 100, 'source');  
                n4 = createNode(600, 200, 'target');  
                
                createEdge(n1, n2, 'HT', 'yellow');
                createEdge(n1, n2, 'HT for Web', 'yellow');
                createEdge(n1, n2, 'www.hightopo.com', 'yellow');
                
                createEdge(n3, n4, 'HT', 'pink', true);
                createEdge(n3, n4, 'HT for Web', 'pink', true);
                createEdge(n3, n4, 'www.hightopo.com', 'pink', true);                
                
            }
                 
            function createNode(x, y, name){
                var node = new ht.Node();
                node.setPosition(x, y);
                node.setName(name);
                dataModel.add(node);
                return node;
            }
                 
            function createEdge(n1, n2, name, background, fixed){
                var edge = new ht.Edge(n1, n2);
                edge.setName(name);
                edge.setStyle('edge.gap', 30);
                edge.setStyle('label.position.fixed', fixed);
                edge.setStyle('label.background', background);
                dataModel.add(edge);   
                return edge;
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
