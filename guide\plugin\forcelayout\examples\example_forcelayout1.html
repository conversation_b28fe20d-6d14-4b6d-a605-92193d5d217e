<!DOCTYPE html>
<html>
    <head>
        <title>ForceLayout</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>    
                 
        <script src="../../../../lib/core/ht.js"></script> 
        <script src="../../../../lib/plugin/ht-forcelayout.js"></script>   
        <script>
            function init(){                
                items = [ 
                    {
                        label: 'Force Layout',
                        selected: true,
                        type: 'toggle',
                        action: function(){
                            if(this.selected){
                                forceLayout.start();                            
                            }else{
                                forceLayout.stop();
                            }                            
                        }                 
                    },                                          
                    {
                        label: 'Editable',
                        type: 'check',
                        selected: false,
                        action: function(item){
                            graphView.setEditable(item.selected);
                        }
                    }                    
                ];

                dataModel = new ht.DataModel();                   
                graphView = new ht.graph.GraphView(dataModel); 
                toolbar = new ht.widget.Toolbar(items);
                borderPane = new ht.widget.BorderPane();
                borderPane.setTopView(toolbar);
                borderPane.setCenterView(graphView);
                        
                view = borderPane.getView();
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    borderPane.invalidate();
                }, false);                                                                                
                          
                initDataModel();
    
                graphView.setZoom(0.4);
                forceLayout = new ht.layout.ForceLayout(graphView);     
                forceLayout.start();
            }

            function createNode(name){
                var node = new ht.Node();
                node.setId(name);
                node.setName(name);
                node.setPosition(600, 400);
                dataModel.add(node);
                return node;
            }
            
            function createEdge(sourceNode, targetNode){
                var edge = new ht.Edge(sourceNode, targetNode);
                edge.s({
                    'edge.width': 1,
                    'edge.color': 'red'
                });
                dataModel.add(edge);
                return edge;
            }

            function initDataModel(){                      
                var ip = "192.168.1.";
                var count = 0;
                iNode = createNode(ip + count++);
                
                for (var j = 0; j < 3; j++) {
                    jNode = createNode(ip + count++);
                    createEdge(iNode, jNode);
                    
                    for (var z = 0; z < 3; z++) {
                        zNode = createNode(ip + count++);
                        createEdge(jNode, zNode);
                    }    
                }
                
                var x = 5;
                var y = 3;                
                var w = 30, h = 30, wg = 60, hg = 60;
                for (var j = 0; j < y; j++) {
                    for (var i = 0; i < x; i++) {
                        var node = createNode(i + " - " + j);                         
                        node.setPosition(800 + i * (w + wg), 300 + j * (h + hg));
                    }
                }
                for (var j = 0; j < y; j++) {
                    for (var i = 0; i < x; i++) {
                        if (i > 0) {
                            createEdge(dataModel.getDataById(i + " - " + j), dataModel.getDataById((i - 1) + " - " + j));                            
                        }
                        if (j > 0) {
                            createEdge(dataModel.getDataById(i + " - " + j), dataModel.getDataById(i + " - " + (j - 1)));
                        }
                    }
                }               
            }            

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
