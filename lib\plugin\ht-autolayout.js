!function(z,G,v){"use strict";function b(){throw"Oops!"}function i(z){return Fs===z?new is:us===z?new $s:"hierarchical"===z?new hT:z===zT||"towardsouth"===z||z===GT||z===bT?new Ws:NT}function A(z){return z===bT?new Z(Xs):z===GT?new Z(-Xs):z===zT?new Z(N):NT}function Z(z){this.s=cs(z),this.c=ns(z)}function ST(z,G){this.x=z,this.y=G}function I(z,G){this.width=z,this.height=G}function y(z,G){this.x=z,this.y=G}function d(){var z=arguments;2===z.length?(d.superClass.constructor.call(this,z[1].width,z[1].height),this.x=z[0].x,this.y=z[0].y):(d.superClass.constructor.call(this,z[2],z[3]),this.x=z[0],this.y=z[1])}function g(z,G){g.a2(z.x,G.x)?(this._a=1,this._b=0,this._c=-z.x):(this._b=-1,G=(G.y-z.y)/(G.x-z.x),z=z.y-z.x*G,this._a=G,this._c=z)}function S(z){if(this._a=new m,z)for(var G=0;G<z.size();G++)this._a.aa(z.get(G))}function f(z,G){this.x=z,this.y=G}function h(z,G){this.x=z||0,this.y=G||0}function H(z){this._c=new m,z?(this.ac(z.a8().b()),this.ad(z.a9().b())):(this.ac(new h),this.ad(new h))}function k(z){k.superClass.constructor.call(this,z)}function K(){var z,G;2===arguments.length?(G=arguments[0],z=arguments[1],this._s=!1,this._w=30,this._h=30,this._x=G-this._w/2,this._y=z-this._h/2):(this._s=(G=arguments[0])._s,this._w=G._w,this._h=G._h,this._x=G._x,this._y=G._y)}function C(z){z?C.superClass.constructor.call(this,z):C.superClass.constructor.call(this,0,0)}function a(){this._c=0,this._d=0,this._e=0,this._b=!0,this._f=!1}function s(z){this._a=z}function Q(z){this._a=z}function n(z){this._a=z}function c(){this._a=0,this._c=0}function o(){this._a=0}function X(z,G){this._h=!1,this._i=z,this._g=G}function J(z,G){this._c=!1,this._d=z,this._b=G}function j(z){this._bb=z,this.i4()}function M(z){M.superClass.constructor.call(this,z)}function F(){this._c=0}function u(z,G){this._p=z,this._j=G,this._o=z._o[G]}function zs(z,G,b,R){this._r=z,this._s=G,this._q=b,this._p=R}function Gs(){}function bs(z,G,b,R,E,a,x){this._g=0,z.xt(this,G,b,R,E,a,x)}function Es(z){this._j=0,this._h=z,this.i4()}function as(){this._a=Rs._A,this._b=Rs._A,this._c=new e}function xs(){this._x=0,this._y=0,this._w=0,this._h=0}function Bs(z,G,b,R){this._m=z,this._n=G,this._l=b,this._k=R}function Os(z,G){this._b=z,this._r=G,this._a=[];for(var b=this._b-1;0<=b;b--)this._a.push(b);this._c=new e}function Ys(z){this._id=m.id(),this._p=0,z.xs(this)}function ls(z){ls.superClass.constructor.call(this,z)}function vs(z){this._o=z,this._c=z._a}function Zs(z){this._d=z,Zs.superClass.constructor.call(this)}function q(z){this._a=z,this._b=new PT,this._c=new tT}function Is(){var z=arguments;this._g=z[0],this._f=this._g.xk(),this._h=this._g.xk(),this._d=new m,this._e=0,1!==z.length&&this.a(z[1],z[2],z[3],z[4])}function Ts(){var z,G,b,R=arguments;2===R.length?(this._a=new m,this._b=new m,z=R[this._c=0],b=new t(z._j2.gj(G=R[1])/2,0),this._a.ac(b),b=new t(z._j2.gj(G)/2,0),this._b.ac(b)):(this._a=R[1],this._b=R[2],this._c=R[3])}function R(){this._cx=!0,this._cs=new ZT,this._ct=new lT,this._cw=new vT}function Ws(){Ws.superClass.constructor.call(this),this._jv=20,this._jw=40,this._jx=function(z,G){var z=z.a3(),G=G.a3(),b=z._g;return D(100*(b.g5(z)-b.g5(G)))}}function fs(z){this._d=0,this._e=0,this._f=0,this._a=0,this._b=0,this._g=z,this._c=new m}function hs(){hs.superClass.constructor.call(this),this._kl=340,this._km=360,this._kk=40,this._ko=.5}function gs(){}function Ss(z){this._a=z}function rs(){rs.superClass.constructor.call(this),this._kq=!1,this._kp=90}function Ns(z){this._a=z}function Ps(){}function ts(){ts.superClass.constructor.call(this),this._jo=30,this._jp=new TT,this._jt=5}function is(){is.superClass.constructor.call(this),this._jm=new ts,this._jk=new rs}function As(){this._a=(new Date).getTime()}function ys(){this._v=new F,this._x=new F,this._z=new Os(3,5),this._w=new Os(3,5),this._y=!1,this._u=!1,this._t={}}function Hs(){Hs.superClass.constructor.call(this)}function ks(){ks.superClass.constructor.call(this),this.a(new C,new k)}function ps(){ps.superClass.constructor.call(this),this._ap=this.xk(),this._as=this.xl()}function Us(z,G,b){this._a={},Us.superClass.constructor.call(this);for(var R=new e,E=0,a=G.size();E<a;E++){var x,B,O,Y=G.get(E);Y instanceof ss?R.add(Y):(Y instanceof r&&Y.setExpanded(!0),x=z.getNodeSize(Y),Y instanceof r&&Y.setExpanded(!1),x&&(B=this.xm(),O=z._repulsion,b!==Fs||Y instanceof r?b===Fs?O*=1.1:b===us&&(O*=.9):O*=.6,b===GT||b===bT?this.s7(B,x.height*O,x.width*O):this.s7(B,x.width*O,x.height*O),B.node=Y,this._a[Y.getId()]=B))}for(E=0,a=R.size();E<a;E++){var l=R.get(E),v=l.getSourceAgent(),l=l.getTargetAgent(),v=this._a[v.getId()],l=this._a[l.getId()];v&&l&&v!==l&&this.xo(v,l)}}function B(z){this._a=z,this._b=!1}function ds(z,G,b,R,E){this._o=0,this._l=0,this._i=0,this._d=0,this._f=0,this._b=z,this._a=1e-4,this._r=G,this._p=1,this._e=(E.gj(z)+E.g9(z))/4,G=.45*b*$(R),this._k=Rs.l(-G,G),this._h=Rs.l(-G,G),this._g=Rs.l(-G,G)}function Ds(){this._a=0,this._c=0,this._b=0}function $s(){$s.superClass.constructor.call(this),this._dj=0,this._dh=0,this._dq=0,this._dp=0,this._dt=0,this._de=0,this._d3=0,this._dr=0,this._ed=0,this._dw=.65,this._ea=1,this._dl=80,this._dx=3,this._d8=!0,this._eb=3e5,this._ee=2,this._di=2,this._df=1e3}function Ls(z,G){this._e=z,this._f=G,this._c={}}function O(){}function E(){E.superClass.constructor.call(this),this.c0()}function Vs(){this._m1=20,this._m2=60,this._m3=5,this._m4=0}function ms(z,G){ms.superClass.constructor.call(this)}function _s(z,G){this._b=20,this._a=G,this._d=z,this._f={}}function rT(z,G,b,R){this._k=20,this._r=.5,this._d=z,this._c=G,this._j=b,this._m=R,this._i=z.xc("A")!=NT||z.xc("B")!=NT,this._t=new gT(z,G,b,R),this._b=new _s(z,this)}function Y(){this._af=0,this._b=0}var qs,x=z.ht||module.parent.exports.ht,NT=null,Ks=x.Default,z=Ks.def,e=x.List,Cs=x.Node,ss=x.Edge,r=x.Group,l=Math,D=l.floor,p=l.ceil,$=l.sqrt,L=l.max,V=l.min,Qs=l.abs,ns=l.cos,cs=l.sin,N=l.PI,os=2*N,Xs=N/2,Js=l.atan,es=l.atan2,ws=l.random,js=l.pow,P=Number.MAX_VALUE,Ms=Number.MIN_VALUE,Fs="circular",us="symmetric",zT="towardnorth",GT="towardeast",bT="towardwest",U=(Z.prototype.tf=function(z,G){return 1===arguments.length&&(G=z.y,z=z.x),{x:this.c*z-this.s*G,y:this.s*z+this.c*G}},z(ST,G,{equals:function(z){return this===z||z instanceof ST&&(z.x===this.x&&z.y===this.y)}}),z(I,G,{}),z(y,G,{}),z(d,I,{}),z(g,G,{a3:function(){return this._a},a4:function(){return this._b},a5:function(){return this._c}}),g.a6=function(z,G){if(g.a1(z.a3())&&g.a1(G.a3()))return NT;if(g.a1(z.a4())&&g.a1(G.a4()))return NT;g.a1(G.a4())&&(R=z,z=G,G=R);var b,R=z.a3(),E=z.a4(),a=-z.a5(),G=g.a1(z.a3())?(b=G.a4(),-G.a5()):(b=G.a4()-G.a3()/z.a3()*z.a4(),-G.a5()-G.a3()/z.a3()*-z.a5()),z=G/b;return new ST((a-z*E)/R,z)},g.a1=function(z){return g.a2(z,0)},g.a2=function(z,G){return Qs(z-G)<1e-5},z(S,G,{c:function(){return this._a.ah()},d:function(){return this._a.ah()},a:function(){for(var z=new e,G=this.c();G.i1();G.i2())z.add(G.i6(),0);return new S(z)},b:function(){return this._a.ay()}}),z(f,G,{a:function(z,G){this.x=z,this.y=G}}),z(h,G,{b:function(){return new h(this.x,this.y)},a:function(z){this.z=z},c:function(){return this.x},d:function(){return this.y},f:function(z,G){this.x=z,this.y=G}}),z(H,G,{a6:function(){return this.a5(this)},ac:function(z){z.a(this),this._a=z},ad:function(z){z.a(this),this._b=z},a8:function(){return this._a},a9:function(){return this._b},a1:function(z,G){return this.a4(z,G,this.aa())},a2:function(){return this._c.ay()},a7:function(z){return this._c.ak(z)},aa:function(){return 0===this._c.ay()?NT:this._c.as()},a3:function(){this._c.af()},i2:function(z){z=this.a7(z);return z?new ST(z.x,z.y):NT},i1:function(){return this.a2()},i6:function(){var z=this.a8();return new ST(z.c(),z.d())},i7:function(){var z=this.a9();return new ST(z.c(),z.d())},i8:function(z){this.a8().f(z.x,z.y)},i9:function(z){this.a9().f(z.x,z.y)},i3:function(z,G,b){z=this.a7(z);z&&z.a(G,b)},i4:function(z,G){this.a1(z,G)},i5:function(){this.a3()}}),z(k,H,{a5:function(z){return new k(z)},a4:function(z,G,b){z=new f(z,G);return this.ab(z,b),z},ab:function(z,G){this._c.an(z,this._c.al(G))}}),z(K,G,{m3:function(){return this.m2(this)},m4:function(){return this._x+this._w/2},m5:function(){return this._y+this._h/2},m6:function(z,G){this._x=z-this._w/2,this._y=G-this._h/2},i1:function(){return this._x},i2:function(){return this._y},i5:function(z,G){this._x=z,this._y=G},i3:function(){return this._w},i4:function(){return this._h},i6:function(z,G){var b=(this._w-z)/2,R=(this._h-G)/2;this._x+=b,this._y+=R,this._w=z,this._h=G},m1:function(z){var G,b,R,E=z.width<=0?(G=this._x,b=this._x+this._w,R=this._y,this._y+this._h):(G=V(this._x,z.x),b=L(this._x+this._w,z.x+z.width),R=V(this._y,z.y),L(this._y+this._h,z.y+z.height));z.x=G,z.y=R,z.width=b-G,z.height=E-R}}),z(C,K,{m2:function(z){return new C(z)}}),{a2:function(z){var G=W.a2(w.a(z.xa()));return U.a4(z,G,U.a3(z,G))},a3:function(z,G){for(var b=z.x9();b.i1();b.i2())G.i7(b.i9(),-1);for(var R=0,E=new xT(z.xa()),a=z.x9();a.i1();a.i2()){var x=a.i9();-1===G.i2(x)&&U.a(x,E,G,R++)}return R},a6:function(z){for(var G=new PT,b=U.a2(z),R=0;R<b.length-1;R++){var E=z.xo(b[R].x2(),b[R+1].x3());G.aa(E)}return G},a4:function(z,G,b){for(var R=[],E=0,a=z.x9();E<b;E++)R[E]=new tT;for(;a.i1();a.i2())R[G.i2(a.i9())].ae(a.i9());return R},a:function(z,G,b,R){for(G.c(z),b.i7(z,R);!G.a();){for(var E=(z=G.b()).ag();E;E=E.a8()){var a=E.a3();-1===b.i2(a)&&(b.i7(a,R),G.c(a))}for(var x=z.ae();x;x=x.a7()){var B=x.a2();-1===b.i2(B)&&(b.i7(B,R),G.c(B))}}},a1:function(z,G,b){G=new ET(G,b);return G.a8(z),G._i},a5:function(z,G,b){for(var R=[],E=0;E<b;E++)R[E]=new PT;for(var a=z.xf();a.i1();a.i2())R[G.i2(a.i8())].aa(a.i8());return R},a7:function(z){var G=new PT,b=W.a3(w.b(z.xa())),R=W.a4(w.a(z.xh())),E=U.a1(z,R,b),a=U.a5(z,R,E);if(1<a.length){for(var x=new tT,B=0;B<a.length;B++){var O=a[B],Y=NT;if(1===O.ay()){var l=O.c2();1===l.a2().ad()?Y=l.a2():1===l.a3().ad()&&(Y=l.a3())}else{for(var v=O.c1();v.i1();v.i2()){var Z=v.i8();if(b.i4(Z.a2()))if(Y){if(Y!==Z.a2()){Y=NT;break}}else Y=Z.a2();if(b.i4(Z.a3()))if(Y){if(Y!==Z.a3()){Y=NT;break}}else Y=Z.a3()}Y&&(Y=(l=O.c2()).a2()!==Y?l.a2():l.a3())}Y&&x.aa(Y)}for(var I,T=x.x4();!x.ar();T=I)I=x.x4(),G.ac(z.xo(T,I))}return G}}),T=(z(a,G,{a6:function(z){this._f=z},a7:function(z){this._b=z},a8:function(z){0!==z.x0()&&this.a9(z,z.x9().i9())},a9:function(z,G){if(this._xx=z.xk(),this._c=z.xl(),this._d=0,this._e=0,this.a0(G),this._b)for(var b=z.x9();b.i1();b.i2()){var R=b.i9();this._xx.i1(R)||(this.a1(R),this.a0(R))}z.xi(this._xx),z.xj(this._c)},a0:function(z){var G=++this._d;this._xx.z1(z,a._B),this.a5(z,G);for(var b=this._f?z.ap():z.af();b.i1();b.i2()){var R,E=b.i8();this._c.i4(E)||(this._c.i7(E,!0),R=E.a1(z),this._xx.i1(R)?this.a3(E,R,!1):(this.a3(E,R,!0),this.a0(R),this.a2(E,R)))}this.a4(z,G,++this._e),this._xx.z1(z,a._C)},a5:function(z,G){},a4:function(z,G,b){},a3:function(z,G,b){},a2:function(z,G){},a1:function(z){}}),a._B={},a._C={},z(s,a,{a5:function(z,G){var b=this._a._ah.i2(z);this._a._ad[b].ae(z)}}),z(Q,a,{a2:function(z,G){var b=z.a1(G),b=this._a[b.al()],G=this._a[G.al()];G._a+1>b._a?(b._c=b._a,b._b=b._d,b._a=G._a+1,b._d=z):G._a+1>b._c&&(b._c=G._a+1,b._b=z)}}),z(n,a,{a3:function(z,G,b){b&&z.a2()===G&&this._a.ac(z)}}),z(c,G,{}),z(o,G,{a1:function(z,G){this._a=0;for(var b=G.length-1;0<=b;b--)G[b]=-1;for(var R=z.x9();R.i1();R.i2()){var E=R.i9();if(0===E.ak()){this.a2(E,E.al(),G);break}}for(var a=z.x9();a.i1();a.i2()){var x=a.i9(),B=x.al();-1===G[B]&&this.a2(x,B,G)}},a2:function(z,G,b){b[G]=-2;for(var R=z.ag();R;){var E=R.a3(),a=E.al();-1===b[a]&&this.a2(E,a,b),R=R.a8()}b[G]=this._a++}}),{a1:function(z){var G=new RT;return G.a8(z),G._n},a2:function(z){var G,b=z.x9(),R=0;for(b.i4();b.i1();b.i2())0===b.i9().ak()&&(G=b.i9(),R++);if(1===R)return G;for(R=0,b.i4();b.i1();b.i2())0===b.i9().ao()&&(G=b.i9(),R++);return 1===R?G:T.a8(z)},a8:function(z){var G=w.a(z.x0()),G=W.a2(G);return T.a6(z,G)},a6:function(z,G){var b=z.xd(),R=w.d(1),E=w.a(z.x0(),-1),a=T.a4(z,b);T.a7(b,G,R,E,-1);for(var x=a.c1();x.i1();x.i2())z.x3(x.i8());return R[0]},a7:function(z,G,b,R,E){for(var a=0,x=z.ag();x;x=x.a8()){var B=x.a3(),O=T.a7(B,G,b,R,E);E<O&&(E=O),a+=R[B.al()]}for(var Y=a*(z._g.xa()-1-a),l=z.ag();l;l=l.a8())for(var v=l.a3(),Z=l.a8();Z;Z=Z.a8()){var I=Z.a3();Y+=R[v.al()]*R[I.al()]}return G.i7(z,Y),R[z.al()]=a+1,E<Y&&(E=Y,b[0]=z),E},a4:function(z,G){var b=new PT,R=new n(b);R.a6(!1),R.a9(z,G);for(var E=b.c1();E.i1();E.i2())z.x3(E.i8());return b},a3:function(z){return T.a4(z,T.a2(z))}}),RT=function(){this._n=!0,this.a6(!1)},ET=(z(RT,a,{a3:function(z,G,b){b||(this._n=!1)},a1:function(z){this._n=!1}}),function(z,G){this._i=0,this._m=G,this._j=z,this._l=!1}),m=(z(ET,a,{a8:function(z){this._h=w.a(z.x0()),this._k=w.a(z.x0()),this._g=new xT(z.xh()),ET.superClass.a8.call(this,z)},a5:function(z,G){this._k[z.al()]=this._h[z.al()]=G},a3:function(z,G,b){this._g.c(z),b||(b=z.a1(G),this._h[b.al()]=V(this._h[b.al()],this._k[G.al()]))},a1:function(z){this._l=!1},a2:function(z,G){var b=z.a1(G);if(this._h[G.al()]>=this._k[b.al()]){for(;this._g.d()!==z;this._j.i5(this._g.b(),this._i));this._j.i5(this._g.b(),this._i),this._i++,!this._g.a()||this._l?this._m.i5(b,!0):this._l=!0}this._h[b.al()]=V(this._h[b.al()],this._h[G.al()])}}),z(X,G,{z1:function(z,G){z._c[this._i]=G},i1:function(z){return z._c[this._i]},i5:function(z,G){z._c[this._i]=G},i4:function(z){return z._c[this._i]},i7:function(z,G){z._c[this._i]=G},i2:function(z){z=z._c[this._i];return z||0},i6:function(z,G){z._c[this._i]=G},i3:function(z){z=z._c[this._i];return z||0},c:function(){return this._h},d:function(){this._h=!0}}),z(J,G,{i8:function(z,G){z._c[this._d]=G},i1:function(z){return z._c[this._d]},i7:function(z,G){z._c[this._d]=G},i4:function(z){z=z._c[this._d];return z||!1},i5:function(z,G){z._c[this._d]=G},i2:function(z){z=z._c[this._d];return z||0},i6:function(z,G){z._c[this._d]=G},i3:function(z){z=z._c[this._d];return z||0},a:function(){return this._c},b:function(){this._c=!0}}),z(j,G,{i1:function(){return this._aa!=NT},i2:function(){this._aa=this._aa._a},i3:function(){this._aa=this._aa._b},i4:function(){this._aa=this._bb._b},i5:function(){this._aa=this._bb._c},i7:function(){return this._bb.ay()},i6:function(){return this._aa._c}}),z(M,j,{i8:function(){return this.i6()}}),function(z){if(this._id=m.id(),this._a=0,z)for(z.i4();z.i1();z.i2())this.ae(z.i6())}),PT=(z(m,G,{ac:function(z){z=this.ag(z);return this._b?((this._b._b=z)._a=this._b,this._b=z):this._b=this._c=z,this._a++,z},ae:function(z){z=this.ag(z);return this._c?((this._c._a=z)._b=this._c,this._c=z):this._b=this._c=z,this._a++,z},z1:function(z){z._b=NT,z._a=NT,this._c?((this._c._a=z)._b=this._c,this._c=z):this._b=this._c=z,this._a++},ad:function(z){z._b=NT,z._a=NT,this._b?((this._b._b=z)._a=this._b,this._b=z):this._b=this._c=z,this._a++},aa:function(z){return this.ae(z),!0},ab:function(z){for(;z.i1();z.i2())this.ae(z.i6())},ao:function(z,G){return G===this._b?this.ac(z):G?(b=this.ag(z),this.aq(b,G),b):this.ae(z);var b},aq:function(z,G){var b;!G||G===this._b?this.ad(z):(this._c?(b=G._b,(G._b=z)._a=G,(b._a=z)._b=b):(z._b=NT,z._a=NT,this._b=this._c=z),this._a++)},ap:function(z,G){var b;!G||G===this._c?this.z1(z):(this._b?(b=G._a,(((G._a=z)._a=b)._b=z)._b=G):(z._b=NT,z._a=NT,this._b=this._c=z),this._a++)},an:function(z,G){return G===this._c?this.ae(z):G?(b=this.ag(z),this.ap(b,G),b):this.ac(z);var b},ay:function(){return this._a},ar:function(){return 0===this._a},af:function(){this._b=this._c=NT,this._a=0},am:function(){return this._b._c},at:function(){var z=this.am();return this.aw(this._b),z},as:function(){return this._c._c},au:function(){return this.aw(this._c)},ak:function(z){for(var G=0,b=this._b;b;){if(z===G)return b._c;b=b._a,G++}return NT},aj:function(z){return z._a||this._b},ai:function(z){return z._b||this._c},aw:function(z){return z!==this._b?z._b._a=z._a:this._b=z._a,z!==this._c?z._a._b=z._b:this._c=z._b,this._a--,z._c},av:function(z){return this.aw(z._aa)},ah:function(){return new j(this)},al:function(z){for(var G=this._b;G;){if(!G._c&&!z)return G;if(G._c===z)return G;G=G._a}return NT},a0:function(){for(var z=w.d(this._a),G=0,b=this._b;b;)z[G]=b._c,b=b._a,G++;return z},ax:function(){for(var z=this._b;z;z=z._b){var G=z._a;z._a=z._b,z._b=G}var b=this._b;this._b=this._c,this._c=b},a1:function(z){var G=this.a0(),b=0;G.sort(z);for(var R=this._b;R;)R._c=G[b],R=R._a,b++},a2:function(){for(var z=this.a0(),G=(z.sort(w.c),0),b=this._b;b;)b._c=z[G],b=b._a,G++},az:function(z){this._b?z._b&&(this._c._a=z._b,z._b._b=this._c,this._c=z._c):(this._b=z._b,this._c=z._c),this._a+=z._a,z._b=z._c=NT,z._a=0},ag:function(z){return new aT(z)}}),m.id=(qs=0,function(){return++qs}),function(z){PT.superClass.constructor.call(this,z)}),aT=(z(PT,m,{c1:function(){return new M(this)},c2:function(){return this.am()},c3:function(){return this.at()}}),z(F,G,{a:function(z){this._c++,z._b=this._b,z._a=NT,this._b?(this._b._a=z,this._b=z):this._b=this._a=z},b:function(z,G){var b;G?((b=G._b)?b._a=z:this._a=z,z._b=b,(z._a=G)._b=z,this._c++):this.a(z)},c:function(z){var G=z._a,z=z._b;this._c--,G?G._b=z:this._b=z,z?z._a=G:this._a=G}}),z(u,G,{i1:function(){return this._o!=NT},i2:function(){this._o=this._o._k[this._j]},i3:function(){this._o=this._o._f[this._j]},i4:function(){this._o=this._p._o[this._j]},i5:function(){this._o=this._p._q[this._j]},i7:function(){return this._p._n[this._j]},i6:function(){return this._o},i8:function(){return this._o}}),function(z){this._c=z}),xT=(z(aT,G,{a:function(){return this._a},b:function(){return this._b},c:function(z){this._c=z},d:function(){return this._c}}),z(zs,G,{i1:function(z){return this._p[z.a5()]},i3:function(z){return this._r[z.a5()]},i2:function(z){return this._s[z.a5()]},i4:function(z){return this._q[z.a5()]},i8:function(z,G){this._p[z.a5()]=G},i6:function(z,G){this._r[z.a5()]=G},i5:function(z,G){this._s[z.a5()]=G},i7:function(z,G){this._q[z.a5()]=G}}),function(z){this._a=w.d(z),this._b=-1}),BT=(z(xT,G,{d:function(){return this._a[this._b]},b:function(){return this._a[this._b--]},c:function(z){this._a[++this._b]=z},a:function(){return this._b<0}}),z(Gs,G,{a0:function(z){this._c=w.d(z)}}),z(bs,Gs,{a5:function(){return this._h._u&&this._h.b1(),this._g},a2:function(){return this._d},a3:function(){return this._e},a1:function(z){return this._d!==z?this._d:this._e},a4:function(){for(var z=0;z<=1;z++)this._k[z]=NT,this._f[z]=NT},a8:function(){return this._k[0]},a7:function(){return this._k[1]},a6:function(z,G,b,R){this.a0(R),this._h=z,this._k=w.d(2),this._f=w.d(2),this._d=G,this._e=b}}),z(Es,G,{i2:function(){this._k=this._k._k[this._j],this._k||0!==this._j||(this._k=this._h._o[1],this._j=1)},i3:function(){this._k=this._k._f[this._j],this._k||1!==this._j||(this._k=this._h._q[0],this._j=0)},i4:function(){this._k=this._h._o[0],this._k?this._j=0:(this._k=this._h._o[1],this._j=1)},i5:function(){this._k=this._h._q[1],this._k?this._j=1:(this._k=this._h._q[0],this._j=0)},i1:function(){return!!this._k},i6:function(){return this._k},i8:function(){return this._k},i7:function(){return this._h.ad()}}),z(as,G,{i1:function(){return this._c.size()},i2:function(z){return this._c.get(z)},i3:function(z,G,b){this._c.set(z,new ST(G,b))},i4:function(z,G){this._c.add(new ST(z,G))},i5:function(){this._c.empty()},i6:function(){return this._a},i7:function(){return this._b},i8:function(z){this._a=z},i9:function(z){this._b=z}}),z(xs,G,{i5:function(z,G){this._x=z,this._y=G},i6:function(z,G){this._w=z,this._h=G},i4:function(){return this._h},i3:function(){return this._w},i1:function(){return this._x},i2:function(){return this._y}}),z(Bs,G,{i1:function(z){return this._k[z.al()]},i3:function(z){return this._m[z.al()]},i2:function(z){return this._n[z.al()]},i4:function(z){return this._l[z.al()]},z1:function(z,G){this._k[z.al()]=G},i6:function(z,G){this._m[z.al()]=G},i7:function(z,G){this._n[z.al()]=G},i5:function(z,G){this._l[z.al()]=G}}),z(Os,G,{a1:function(z){var G;if(0===this._a.length){this.a2(z,this._b,this._b+this._r);for(var b=this._b+this._r-1;b>this._b;b--)this._a.push(b);G=this._b,this._b+=this._r}else G=this._a.pop();return G},b:function(z){var G=this.a1(z),b=new X(G,this);return this._c.add(b),this.a4(z,G),b},c:function(z){var G=this.a1(z),b=new J(G,this);return this._c.add(b),this.a4(z,G),b},a2:function(z,G,b){for(var R=z._a;R;R=R._a){var E=w.d(b);w.f(R._c,E,G),R._c=E}},a3:function(z,G,b){b=w.d(b);w.f(z._c,b,G),z._c=b},a4:function(z,G){for(var b=z._a;b;b=b._a)b._c[G]=NT},a5:function(z,G){if(z instanceof X){var b=z;if(b.c())throw"";b.d();b=z._i;this._a.indexOf(b)<0&&(this.a4(G,b),this._a.push(b),this._c.remove(z))}},a6:function(z,G){if(z instanceof J){var b=z;if(b.a())throw"";b.b();b=b._d;this._a.indexOf(b)<0&&(this.a4(G,b),this._a.push(b),this._c.remove(z))}}}),z(Ys,Gs,{ad:function(){return this._n[0]+this._n[1]},ak:function(){return this._n[1]},ao:function(){return this._n[0]},al:function(){return this._g._y&&this._g.c(),this._p},ag:function(){return this._o[0]},ae:function(){return this._o[1]},af:function(){return new Es(this)},am:function(){return new u(this,1)},ap:function(){return new u(this,0)},an:function(){return new OT(this)},aq:function(){return new BT(this,1)},aw:function(){return new BT(this,0)},ah:function(z){for(var G=this._o[0];G;G=G._k[0])if(G.a3()===z)return G;return NT},ai:function(z){for(var G=this._o[1];G;G=G._k[1])if(G.a2()===z)return G;return NT},aj:function(z){return this.ah(z)||this.ai(z)},au:function(z){this.at(z,1,w.d(this.ak()))},av:function(z){this.at(z,0,w.d(this.ao()))},as:function(z,G){this.a0(G),this._g=z,this._o=w.d(2),this._q=w.d(2),this._n=w.a(2)},ab:function(z,G,b,R,E){var a;G?(a=G._d===G._e?R:this!==G._d?1:0,0===E?(E=G._k[a],z._f[R]=G,z._k[R]=E,G._k[a]=z,E?E._d===E._e?E._f[R]=z:E._f[this!==E._d?1:0]=z:this._q[b]=z):(E=G._f[a],z._k[R]=G,z._f[R]=E,G._f[a]=z,E?E._d===E._e?E._k[R]=z:E._k[this!==E._d?1:0]=z:this._o[b]=z),this._n[b]++):this.aa(z,b,R)},aa:function(z,G,b){var R=this._q[G];z._k[b]=NT,R?(z._f[b]=R)._d===R._e?R._k[b]=z:R._k[this!==R._d?1:0]=z:(this._o[G]=z)._f[b]=NT,this._q[G]=z,this._n[G]++},ar:function(z,G,b){var R=z._k[b],z=z._f[b];R?R._f[R._d!==this?1:0]=z:this._q[G]=z,z?z._k[z._d!==this?1:0]=R:this._o[G]=R,this._n[G]--},ac:function(){for(var z=0;z<=1;z++)this._o[z]=NT,this._q[z]=NT,this._n[z]=0},at:function(z,G,b){if(!(this._n[G]<2)){for(var R=this._n[G],E=0,a=this._o[G];a;a=a._k[G])b[E]=a,E++;w.s(b,R,z);var x=1,B=this._o[G]=b[0];for(B._f[G]=NT;x<R;)((a=b[x])._f[G]=B)._k[G]=a,x++,B=a;(this._q[G]=a)._k[G]=NT}}}),function(z,G){BT.superClass.constructor.call(this,z,G),this._h=1!==G?1:0}),OT=(z(BT,u,{i6:function(){return this.i9()},i9:function(){return 0!==this._h?this._o._e:this._o._d}}),function(z){OT.superClass.constructor.call(this,z)}),tT=(z(OT,Es,{i6:function(){return this._k.a1(this._h)},i9:function(){return this._k.a1(this._h)}}),z(ls,j,{i9:function(){return this.i6()}}),z(vs,G,{i1:function(){return this._c!=NT},i2:function(){this._c=this._c._a},i3:function(){this._c=this._c._b},i5:function(){this._c=this._o._b},i4:function(){this._c=this._o._a},i7:function(){return this._o._c},i6:function(){return this._c},i9:function(){return this._c},i8:function(){return this._c}}),function(z){if(z&&z.length){tT.superClass.constructor.call(this);for(var G=0;G<z.length;G++)this.ae(z[G])}else tT.superClass.constructor.call(this,z)}),W=(z(tT,m,{x1:function(){return new ls(this)},x2:function(){return this.am()},x3:function(){return this.as()},x4:function(){return this.at()}}),z(Zs,tT,{}),z(q,G,{a:function(){for(var z=this._a.x9();z.i1();z.i2())this.e(z.i9())},b:function(){this.c(),this.d()},c:function(){for(;!this._c.ar();){var z=this._c.x4();this._a.xq(z)||this.g(z)}},d:function(){for(;!this._b.ar();){var z=this._b.c3();this._a.xp(z)||this.f(z)}},e:function(z){for(var G=z.af();G.i1();G.i2())this._b.ac(G.i8()),this._a.h1(G.i8());this._c.ac(z),this._a.h2(z)},f:function(z){this._a.u1(z)},g:function(z){this._a.h3(z)}}),q.h=function(z,G){for(G.i4();G.i1();G.i2()){var b=G.i8();z.xq(b.a2())||z.h3(b.a2()),z.xq(b.a3())||z.h3(b.a3()),z.xp(b)||z.u1(b)}},q.i=function(z,G){for(G.i4();G.i1();G.i2()){var b=G.i8();z.xp(b)&&z.h1(b),0===b.a2().ad()&&z.h2(b.a2()),0===b.a3().ad()&&z.h2(b.a3())}},z(Is,G,{a:function(z,G,b,R){for(var E=w.d(b-G+1),a=G,x=0;a<=b;a++)E[a]=new Zs(a);for(var B=this._g.x9();B.i1();B.i2()){var O=B.i9();R&&!R.i4(O)||(this._f.z1(O,E[z.i2(O)-G].ac(O)),this._e++)}for(;x<E.length;x++)for(var Y=E[x],l=this._d.ae(Y),v=Y.x1();v.i1();v.i2())this._h.z1(v.i9(),l)},c:function(){this._g.xi(this._h),this._g.xi(this._f)},e:function(){return 0===this._e},g:function(){for(;this._d.am().ar();this._d.at());this._e--;var z=this._d.am().x4();return this._h.z1(z,NT),this._f.z1(z,NT),z},f:function(){for(;this._d.as().ar();this._d.au());this._e--;var z=this._d.as().x4();return this._h.z1(z,NT),this._f.z1(z,NT),z},d:function(z){var G=this._f.i1(z),b=this._h.i1(z),R=b.d(),E=NT,b=b.a();b?(E=b.d(),this._h.z1(z,b)):(E=new Zs(R._d+1),this._h.z1(z,this._d.ae(E))),R.aw(G),this._f.z1(z,E.ac(z))},b:function(z){var G=this._f.i1(z),b=this._h.i1(z),R=b.d(),E=NT,b=b.b();b?(E=b.d(),this._h.z1(z,b)):(E=new Zs(R._d-1),this._h.z1(z,this._d.ac(E))),R.aw(G),this._f.z1(z,E.ac(z))}}),{a1:function(z){return new Bs(z,NT,NT,NT)},a2:function(z){return new Bs(NT,z,NT,NT)},a3:function(z){return new Bs(NT,NT,z,NT)},a4:function(z){return new zs(NT,z,NT,NT)},a5:function(z){return new zs(NT,NT,z,NT)},a6:function(z){return new zs(NT,NT,NT,z)}}),t=(z(Ts,G,{}),function(z,G){this._b=z,this._a=G}),YT=(z(t,G,{}),z(R,G,{i5:function(z){this._cx=z},k:function(){var z=new IT(this);return this._cx&&(this._cs.w1(z),z=this._cs),this._cw.w1(z),z=this._cw,this._ct.w1(z),z=this._ct},i2:function(z){this.k().i2(z)},i1:function(z){return this.k().i1(z)}}),z(Ws,R,{i4:function(z){return T.a1(z)},i3:function(z){if(!this.i4(z))throw"";var G,b,R=T.a3(z);for(this._j2=z,this._j3=new YT(z),_.c(z),this._jy=z.xk(),z.xb()||(this.bu(),G=this._j3.c1(),this.f(G),this.b(this._j3),this.c(this._j3));!R.ar();z.x3(b))b=R.c3(),_.b(z.g2(b))},bu:function(){if(this._jx)for(var z=this._j2.x9();z.i1();z.i2())z.i9().av(this._jx)},c:function(z){for(var G=this.a2(z),b=w.a(G.length),R=0;R<G.length;R++){for(var E=0,a=G[R].ah();a.i1();a.i2())var x=a.i6(),E=L(E,this._j2.g9(x));b[R]=E}for(var B=-this._jw,O=0;O<G.length;O++){B+=this._jw+b[O];for(var Y=G[O].ah();Y.i1();Y.i2()){var l=Y.i6();this._j2.s2(l,this._j2.g5(l),B-b[O]/2)}}},a2:function(z){for(var G=w.d(z.b()),b=0,R=z.b();b<R;b++)G[b]=new m;return z.c1(),this.a1(z.c1(),0,G),G},a1:function(z,G,b){b[G].ae(z);for(var R=z.aw();R.i1();R.i2())this.a1(R.i9(),G+1,b)},b:function(z){z=z.c1();this._j2.s2(z,0,this._j2.g6(z)),this.g(z)},g:function(z){for(var G=z.aw();G.i1();G.i2()){var b=G.i9(),R=this._jy.i1(b);this._j2.s2(b,this._j2.g5(z)+R._c,this._j2.g6(b)),this.g(b)}},f:function(z){if(this._j3.c2(z))this._jy.z1(z,new Ts(this,z));else{var G=z.aw(),b=G.i9(),R=(G.i2(),this.f(b),this._jy.i1(b)),E=new Ts(this,R._a,R._b,0);if(!G.i1())return E._a.ac(new t(this._j2.gj(z)/2,0)),E._b.ac(new t(this._j2.gj(z)/2,0)),void this._jy.z1(z,E);for(;G.i1();){b=G.i9(),G.i2(),this.f(b);for(var R=this._jy.i1(b),a=E._b.ah(),x=R._a.ah(),B=2147483647,O=0,Y=0;a.i1()&&x.i1();){var l=a.i6(),v=(a.i2(),x.i6());x.i2(),Y+=l._a,O+=v._a,B=V(B,O-Y-l._b-v._b)}if(R._c=this._jv-B,O+=R._c,R._b.am()._a=R._c,a.i1()&&!x.i1())for(var Z=Y-this.a3(R._b);a.i1();Z=0){var I=a.i6();a.i2(),R._b.ae(new t(I._b,I._a+Z))}else if(!a.i1()&&x.i1())for(var T=O-(T=this.a3(E._a));x.i1();T=0){var W=x.i6();x.i2(),E._a.ae(new t(W._b,W._a+T))}E._b=R._b}this._jy.z1(z,E);for(var f=-R._c/2,h=z.aw();h.i1();){var g=h.i9(),g=(h.i2(),this._jy.i1(g)),S=(g._c+=f,g._b.am());S._a+=f,(S=g._a.am())._a+=f}E._a.ac(new t(this._j2.gj(z)/2,0)),E._b.ac(new t(this._j2.gj(z)/2,0))}},a3:function(z){for(var G=0,b=z.ah();b.i1();b.i2())G+=b.i6()._a;return G}}),function(z){this._b=z,this.a()}),lT=(z(YT,G,{c1:function(){return this._a||this.a(),this._a},b:function(){return this._a?this.d(this._a):-1},d:function(z){for(var G=0,b=z.aw();b.i1();b.i2())G=L(G,this.d(b.i9()));return G+1},c2:function(z){return 0===z.ao()},a:function(){for(var z=this._b.x9();z.i1();z.i2())if(0===z.i9().ak())return void(this._a=z.i9())}}),z(fs,G,{a:function(){return this._d+this._e+this._f}}),z(hs,R,{ic:function(){return this._km},ia:function(){return this._kl},i9:function(){return this._ko},i3:function(z){if(!T.a1(z))throw"";this._a=z;var G=this.i8(),b=T.a4(z,G);_.c(z),this._kn=w.d(z.x0());for(var R,E=z.x9();E.i1();E.i2()){var a=E.i9();a!==G?this.aa(a,new fs(this._kk+this.q(a.aq().i9()))):this.aa(a,new fs(this._kk))}for(this.s(G),z.s2(G,0,0),this.t(G);!b.ar();z.x3(R))R=b.c3()},i4:function(z){return T.a1(z)},i0:function(z){return this._kn[z.al()]},i8:function(){return T.a2(this._a)},i7:function(z){for(var G=this.ib(z);!((a=this.i6(z))<=G);)for(var b=z.aw();b.i1();b.i2()){var R=b.i9();this.i0(R)._g*=1+this._ko}for(var E=(G-a)/(2*z.ao()),a=0,x=z.aw();x.i1();x.i2()){var B=this.i0(x.i9());B._d+=E,B._e+=E,a+=B._d+B._e}this.id(z)},id:function(z){for(var G=w.d(z.ao()),b=0,R=z.ap();R.i1();)G[b]=R.i8(),R.i2(),b++;var E=this;G.sort(function(z,G){z=z.a3(),G=G.a3(),z=E.i0(z).a()-E.i0(G).a();return 0<z?1:0<=z?0:-1});for(var a=0;a<G.length;a++)this._a.h1(G[a]);for(var x=0;x<G.length;x+=2)this._a.u1(G[x]);for((b=G.length-1)%2==0&&b--;0<b;b-=2)this._a.u1(G[b])},ib:function(z){return 0===z.ak()?this._km:2===z.ao()?V(180,this._kl):this._kl},i6:function(z){for(var G=0,b=z.ap();b.i1();b.i2()){for(var R,E,a=b.i8().a3(),a=this.i0(a),x=-a._g,B=a._b,O=a._c,Y=0,l=Y+1,v=O._b,Z=v.d();Y<l;l=(R.y-B)/(R.x-x))R=Z,Y=((Z=(v=O.ai(v)).d()).y-R.y)/(Z.x-R.x);for(a._d=180*-Js(l)/N,l=(Y=0)-1,Z=(v=O._b).d();v.a().d().x===Z.x;Z=v.d())v=v.a();for(;l<Y;l=(E.y-B)/(E.x-x))E=Z,Y=((Z=(v=O.aj(v)).d()).y-E.y)/(Z.x-E.x);a._e=180*Js(l)/N,G+=a._d+a._e}return G},aa:function(z,G){this._kn[z.al()]=G},p:function(z){var G=this.i0(z),b=new m,z=2*this.q(z);b.aa(new ST(0,0)),b.aa(new ST(0,z)),b.aa(new ST(z,z)),b.aa(new ST(z,0)),G._c=b,G._a=z/2,G._b=z/2},r:function(z){if(0===z.ao())this.p(z);else{var G=this.i0(z),b=this.q(z),R=new m;R.aa(new ST(-b,-b)),R.aa(new ST(-b,b)),R.aa(new ST(b,-b)),R.aa(new ST(b,b));for(var E=z.aw();E.i1();E.i2()){var a=this.i0(E.i9());R.az(a._c)}for(var b=Rs.h(R),x=P,B=P,O=Ms,Y=Ms,l=b.ah();l.i1();l.i2()){var v=l.i6();v.x<x&&(x=v.x),v.x>O&&(O=v.x),v.y<B&&(B=v.y),v.y>Y&&(Y=v.y)}for(var Z=new m,I=b.ah();I.i1();I.i2()){var T=I.i6();Z.aa(new ST(T.x-x,T.y-B))}G._c=Z,G._a=-x,G._b=-B}},s:function(z){if(0===z.ao())this.r(z);else{for(var G=z.aw();G.i1();G.i2())this.s(G.i9());this.i7(z);for(var b=0,R=z.aw();R.i1();R.i2()){for(var E=R.i9(),a=this.i0(E),E=180-(360-this.ib(z))/2-b-(a._e+a._f),x=(b+=a.a(),cs(E=E/180*N)),B=ns(E),O=a._c._b;O;O=O.a()){var Y=O.d(),l=Y.x+a._g,Y=Y.y-a._b,l=new ST(l*B-x*Y,l*x+B*Y);O.c(l)}E=a._a+a._g;a._a=E*B,a._b=E*x}this.r(z)}},t:function(z){var G,b=this._a.g4(z),R=0;0<z.ak()&&(G=z.aq().i9(),G=this._a.g4(G),R=N+es(G.y-b.y,G.x-b.x));for(var E=z.aw();E.i1();E.i2()){var a,x,B,O=E.i9(),Y=this.i0(O);0!==R&&(a=ns(R),B=cs(R),x=Y._a*a-B*Y._b,B=Y._a*B+a*Y._b,Y._a=x,Y._b=B),this._a.s2(O,b.x+Y._a,b.y+Y._b),this.t(O)}},q:function(z){return L(this._a.gj(z),this._a.g9(z))/2*1.41}}),z(gs,G,{i2:function(z){return z.ad()},i1:b,i3:b,i4:b}),z(Ss,G,{i2:function(z){for(var G=0,b=z.an();b.i1();b.i2())this._a.i1(b.i9())&&G++;return G},i4:function(z){return this._a.i1(z)==NT},i1:b,i3:b}),z(rs,hs,{a:function(z,G){this._kr=G,this._ks=z,this._kq=!0},i7:function(z){if(this.u(z))for(var G=this.i9(),b=this.ib(z),R=(360-b)/2+b,E=new PT(z.ap());;){for(var a,x,B=this.i6(z),O=E._b,B=(360-b)/2;O;O=O.a()){var Y=O.d(),l=Y.a3(),l=this.i0(l),Y=this._ks.i3(Y),v=Y-(B+l._e);if(0<=v&&Y+l._d>=R&&(v=B+l.a()<=R?R-B-l.a():2*(R-(Y+l._d))),(l._f=0)<=v)l._f=v,a=O,x=l;else{for(-v>l._d+l._e?v=(l._d+l._e)/2:v/=-2,(B-=v)<=R&&B+l.a()>R&&(B=(B+=v)-(v=B+l.a()-R));a&&v>x._f;x=this.i0(a.d().a3()))if(v-=x._f,x._f=0,!(a=a.b())){x=NT;break}a?x._f-=v:B+=v}B+=l.a()}if(B<=R){for(var Z=0,I=(360-b)/2,T=z.ap();T.i1();T.i2()){var W=T.i8(),f=W.a3(),W=this._ks.i3(W),f=this.i0(f),h=I+f._f+f._e;Z<Qs(h-W)&&(Z=Qs(h-W)),I+=f.a()}if(Z<=this._kp)break}for(var g=z.aw();g.i1();g.i2()){var S=g.i9();this.i0(S)._g*=1+G}}else rs.superClass.i7.call(this,z)},ib:function(z){return this.u(z)?0===z.ak()?this.ic():this.ia():rs.superClass.ib.call(this,z)},u:function(z){return!(!this._kq||0===z.ao())&&this._ks.i1(z.ag())!=NT}}),z(Ns,G,{i1:function(z){return this._a.i1(z)},i2:b,i3:b,i4:b}),z(Ps,G,{w1:function(z){this._bb=z},w2:function(){return this._bb},w4:function(z){this._bb&&this._bb.i2(z)},w3:function(z){return!this._bb||this._bb.i1(z)}}),function(){this._cg=45,this._ce=400,this._ch=400,this._cf=0}),vT=(z(lT,Ps,{i1:function(z){if(this.w2()){for(var G=!0,b=z.xk(),R=U.a3(z,b),E=w.d(R),a=w.d(R),x=0;x<R;x++)E[x]=new tT,a[x]=new PT;for(var B=z.xf();B.i1();B.i2()){var O=B.i8();a[b.i2(O.a2())].aa(O),z.h1(O)}for(var Y=z.x9();Y.i1();Y.i2()){var l=Y.i9();E[b.i2(l)].aa(l),z.h2(Y.i9())}for(var v=0;v<R;v++){for(var Z=E[v].x1();Z.i1();Z.i2())z.h3(Z.i9());for(var I=a[v].c1();I.i1();I.i2())z.u1(I.i8());for(var G=this.w3(z),T=a[v].c1();T.i1();T.i2())z.h1(T.i8());for(var W=E[v].x1();W.i1();W.i2())z.h2(W.i9());if(!G)break}for(var f=0;f<R;f++)for(var h=E[f].x1();h.i1();h.i2())z.h3(h.i9());for(var g=0;g<R;g++)for(var S=a[g].c1();S.i1();S.i2())z.u1(S.i8());return z.xi(b),G}return!0},i2:function(z){if(!z.xb()){for(var G=z.xk(),b=U.a3(z,G),R=w.d(b),E=w.d(b),a=w.d(b),x=w.d(b),B=0;B<b;B++)R[B]=new tT,E[B]=new PT;for(var O=z.xf();O.i1();O.i2()){var Y=O.i8();E[G.i2(Y.a2())].aa(Y),z.h1(Y)}for(var l=z.x9();l.i1();l.i2()){var v=l.i9();R[G.i2(v)].aa(v),z.h2(l.i9())}for(var Z=0;Z<b;Z++){for(var I=R[Z].x1();I.i1();I.i2())z.h3(I.i9());for(var T=E[Z].c1();T.i1();T.i2())z.u1(T.i8());this.w4(z);var W,f,h=z.g3(),g=(a[Z]=new d(h.x,h.y,h.width,h.height),{});x[Z]=g,0<this._cf?(W=this._cg+p((h.width+1)/this._cf)*this._cf,f=this._cg+p((h.height+1)/this._cf)*this._cf,g.x=h.x,g.y=h.y,g.width=W,g.height=f):(g.x=h.x,g.y=h.y,g.width=h.width+this._cg,g.height=h.height+this._cg);for(var S=E[Z].c1();S.i1();S.i2())z.h1(S.i8());for(var r=R[Z].x1();r.i1();r.i2())z.h2(r.i9())}for(var N=0;N<b;N++)for(var P=R[N].x1();P.i1();P.i2())z.h3(P.i9());for(var t=0;t<b;t++)for(var i=E[t].c1();i.i1();i.i2())z.u1(i.i8());if(_.a(x,NT,this._ce/this._ch),this._cf<=0)for(var A=0;A<x.length;A++)this.w5(z,R[A],E[A],new ST(x[A].x,x[A].y),a[A]);else for(var y=0;y<x.length;y++){var H=D((x[y].x-a[y].x)/this._cf)*this._cf,k=D((x[y].y-a[y].y)/this._cf)*this._cf,H=a[y].x+H,k=a[y].y+k;this.w5(z,R[y],E[y],new ST(H,k),a[y])}z.xi(G)}},w5:function(z,G,b,R,E){for(var a=-E.x+R.x,x=-E.y+R.y,B=G.x1();B.i1();B.i2()){var O=z.ga(B.i9());z.s4(B.i9(),new ST(O.x+a,O.y+x))}for(var Y=b.c1();Y.i1();Y.i2()){for(var l=Y.i8(),v=new e,Z=z.gp(l).c();Z.i1();Z.i2()){var I=Z.i6();v.add(new ST(I.x+a,I.y+x))}z.s5(l,new S(v))}}}),function(){}),ZT=(z(vT,Ps,{i1:function(z){return this.w3(z)},i2:function(z){this.w7(z),this.w2()&&this.w4(z),this.w6(z)},w7:function(z){this.e(z),this.k(z),this.i(z)},e:function(z){for(var G=z.x9();G.i1();G.i2()){var b=z.g4(G.i9());z.s1(G.i9(),b)}},w6:function(z){this.l(z),this.j(z),this.f(z)},l:function(z){for(var G=z.x9();G.i1();G.i2()){var b=z.g4(G.i9());z.s1(G.i9(),b)}},j:function(z){for(var G=z.xf();G.i1();G.i2()){var b=z.g7(G.i8()),R=b.i6();b.i8(R),R=b.i7(),b.i9(R);for(var E=0;E<b.i1();E++){var a=b.i2(E);b.i3(E,a.x,a.y)}}},k:function(z){for(var G=z.xf();G.i1();G.i2()){var b=z.g7(G.i8()),R=b.i6();b.i8(R),R=b.i7(),b.i9(R);for(var E=0;E<b.i1();E++){var a=b.i2(E);b.i3(E,a.x,a.y)}}},f:function(z){this._ca&&(z.x1("A",this._ca),this._ca=NT,this._b6=NT),this._b8&&(z.x1("B",this._b8),this._b8=NT,this._b9=NT)},i:function(z){this._ca=z.xc("A"),this._ca&&(this._b6=new Ns(this._ca),z.x1("A",this._b6)),this._b8=z.xc("B"),this._b8&&(this._b9=new Ns(this._b8),z.x1("B",this._b9))}}),function(){this._a=new PT,this._c=10}),IT=(z(ZT,Ps,{i2:function(z){this._b=z.xl(),this.w9(z),this.w4(z),this.c(z),this.w8(z,this._b),z.xj(this._b)},i1:function(z){var G;return!this.w2()||(this._b=z.xl(),this.w9(z),G=this.w3(z),this.c(z),z.xj(this._b),G)},w8:function(z,G){for(var b=z.xf();b.i1();b.i2()){var R=b.i8();G.i1(R)&&_.g(z,R,G.i1(R),this._c)}},w9:function(z){for(var G=z.xk(),b=z.x9();b.i1();b.i2()){for(var R=b.i9(),E=R.af();E.i1();E.i2()){var a=E.i8(),x=a.a1(R),B=G.i1(x);B!==a&&(B?(this._b.i1(B)||this._b.i8(B,new PT),this._b.i1(B).aa(a),this._a.ac(a),z.h1(a)):G.z1(x,a))}for(var O=R.af();O.i1();O.i2()){var Y=O.i8().a1(R);G.z1(Y,NT)}}z.xi(G)},c:function(z){for(;!this._a.ar();z.u1(this._a.c3()));}}),function(z){this._a=z}),Rs=(z(IT,G,{i2:function(z){this._a.i3(z)},i1:function(z){return this._a.i4(z)}}),z(ts,R,{i4:function(z){return!0},i3:function(z){this._ju=z,_.c(z);for(var G=this._jp.i1(z),b=0,R=z.x9();R.i1();R.i2())b=L(b,this.e(R.i9()));b<this._jt&&(b=this._jt),this.a(G,b)},a:function(z,G){var b=z.i7(),R=2*N/b,E=0,a=w.a(b),x=0;for(z.i4();x<b;)a[x]=this.e(z.i9())+this._jo,E+=a[x],x++,z.i2();for(var B=E/b,O=E/os,Y=(O<G&&(O=G),z.i4(),0),l=0;l<b;){var v=R/B*a[l],Z=ns(Y+=v/2)*O,I=cs(Y)*O;Y+=v/2,this._ju.s2(z.i9(),Z,I),l++,z.i2()}return O},e:function(z){var G=this._ju.gj(z),z=this._ju.g9(z);return G<=z?z:G}}),z(is,R,{i4:function(z){return!0},i3:function(z){if(!(z.x0()<2)){this._jn=z,_.c(this._jn),_.e(this._jn);var G=new WT(this._jn),z=(G.a1(),G.h(),new q(this._jn));z.a();for(var b=G.x9();b.i1();b.i2()){var R,E=b.i9(),a=G.c2(E);1<a.ay()?(R=G.d1(E),q.h(this._jn,R.c1()),this._jm.i3(this._jn),R=this._jn.g3(),G.s7(E,R.width,R.height)):1===a.ay()?(R=a.x2(),G.s8(E,this._jn.gm(R)),this._jn.s2(R,0,0)):G.s7(E,1,1),q.i(this._jn,this._jn.xf())}z.b();var z=this.a7(G),x=(T.a4(G,z),G.xk()),B=G.xl();this.a2(G,B,x),this.a1(G,B),this.a3(G,z,B),this._jk.a(B,x),this._jk.i3(G),this.a5(G,z,x);for(var O=G.x9();O.i1();O.i2())for(var Y=O.i9(),l=G.g4(Y),v=G.c2(Y).x1();v.i1();v.i2()){var Z=v.i9();this._jn.s2(Z,l.x+this._jn.g5(Z),l.y+this._jn.g6(Z))}}},a7:function(z){for(var G,b=-1,R=z.x9();R.i1();R.i2()){var E=R.i9();z.c2(E).ay()>b&&(b=z.c2(G=E).ay())}return G},a1:function(z,b){for(var G=function(z,G){z=b.i3(z)-b.i3(G);return 0<z?1:0<=z?0:-1},R=z.x9();R.i1();R.i2())R.i9().av(G)},a2:function(z,G,b){for(var R=w.a(this._jn.x0()),E=z.x9();E.i1();E.i2())for(var a=E.i9(),x=z.c2(a).x1();x.i1();x.i2())R[x.i9().al()]=a.al();this.a4(z,T.a2(z),R,G,b)},a3:function(z,G,b){if(1<z.c2(G).ay()){for(var R=0,E=0,a=0,x=G.ap();x.i1();x.i2()){var B=x.i8(),B=b.i3(B);E<B-R&&(E=B-R,a=(R+B)/2),R=B}this.a6(z,G,a=E<360-R?(360+R)/2:a);for(var O=G.ap();O.i1();O.i2()){var Y=O.i8(),l=b.i3(Y);for(l-=a;l<0;l+=360);b.i6(Y,l)}G.av(function(z,G){z=b.i3(z)-b.i3(G);return 0<z?1:0<=z?0:-1})}},a4:function(z,G,b,R,E){for(var a=G.al(),x=E.i3(G),B=G.ap();B.i1();B.i2()){for(var O,Y=B.i8(),l=0,v=0,Z=0,I=0,T=z.b(Y).c1();T.i1();T.i2()){var W,f=T.i8(),f=b[f.a2().al()]===a?(W=f.a2(),f.a3()):(W=f.a3(),f.a2());Z-=this._jn.g5(W),I+=this._jn.g6(W),l-=this._jn.g5(f),v+=this._jn.g6(f)}if(0!==Z||0!==I){for(var h=180*es(I,Z)/N-x;h<0;h+=360);R.i6(Y,h)}0!==l&&0!==v&&((O=180*es(v,l)/N)<0&&(O+=360),E.i6(Y.a3(),O)),this.a4(z,Y.a3(),b,R,E)}},a5:function(z,G,b){for(var R=z.g4(G),E=G.ap();E.i1();E.i2()){var a=E.i8().a3(),x=z.g4(a),x=180*es(x.y-R.y,x.x-R.x)/N;b.i1(a)&&(x+=b.i3(a)),this.a6(z,a,x),this.a5(z,a,b)}},a6:function(z,G,b){b=b/180*N;z=z.c2(G);if(!(z.ay()<=1))for(var R=z.x1();R.i1();R.i2()){var E=R.i9(),a=this._jn.g5(E),x=this._jn.g6(E),B=ns(b),O=cs(b);this._jn.s2(E,a*B-O*x,a*O+B*x)}}}),z(As,G,{b:function(){return(new Date).getTime()-this._a}}),{_A:new ST(0,0),b:function(z,G,b){return Rs.c(z.x,z.y,G.x,G.y,b.x,b.y)},c:function(z,G,b,R,E,a){E=(E-=z)*(R-=G)-(a-=G)*(b-=z);return 0<=E?E<=0?0:-1:1},d:function(z,G,b){return 0<Rs.b(z,G,b)},f:function(z,G,b){return Rs.b(z,G,b)<0},g:function(z,G,b){return 0===Rs.b(z,G,b)},h:function(z){return Rs.i(z)},i:function(z){var G=new m(z.ah()),b=new m;if(G.a2(),G.ar())return b;var R=G.at();for(b.ae(R);!G.ar()&&R.equals(G.am());G.at());if(G.ar())return b;for(var R=G.at(),E=b.ae(R),a=G.ah();a.i1();a.i2()){var x=a.i6();if(!x.equals(R))if(R=x,2===b.ay()&&Rs.g(b.am(),b.as(),x))E.c(x);else{for(var B,O=E;!Rs.f(b.ai(O).d(),O.d(),x);O=b.ai(O));for(B=E;!Rs.d(b.aj(B).d(),B.d(),x);B=b.aj(B));for(;B!==b.aj(O);b.aw(b.aj(O)));E=b.an(x,O)}}return b},j:function(){return Rs.k(P)},k:function(z){return D(ws()*z)},l:function(z,G){return ws()*(G-z)+z}}),TT=function(){},WT=(z(TT,G,{i1:function(z){this._b=z;var G=new PT;(G=U.a6(z)).az(U.a7(z));for(var b=this.a1();!G.ar();z.x5(G.c3()));return b.x1()},a1:function(){if(this._b.x0()<3)return new tT(this._b.x9());for(var z=this._b.xk(),G=this._b.xk(),b=this._b.xl(),R=new Is(this._b,new gs,0,this.a3(this._b)),E=this._b.x0(),a=new PT,x=new PT,B=new q(this._b);3<E;E--){for(var O=R.g(),Y=O.an();Y.i1();Y.i2())z.z1(Y.i9(),E),G.i5(Y.i9(),!1);for(var l=O.an();l.i1();l.i2())for(var v=l.i9().ap();v.i1();v.i2()){var Z=v.i8();z.i2(Z.a3())===E&&(x.aa(Z),G.i5(Z.a2(),!0),G.i5(Z.a3(),!0))}if(x.ay()<O.ad()-1){for(var I=NT,T=O.an();T.i1();T.i2()){var W,f=T.i9();z.i2(f)!==E||G.i4(f)||(I=I?(W=this._b.xo(I,f),b.i7(W,!0),x.aa(W),NT):f)}if(I)for(var h=O.an();h.i1();h.i2()){var g=h.i9();if(g!==I&&!g.aj(I)){g=this._b.xo(I,g);b.i7(g,!0),x.aa(g);break}}if(x.ay()<O.ad()-1){for(var S,r=2147483647,N=O.an();N.i1();N.i2()){var m=N.i9();m.ad()<r&&(r=(S=m).ad())}for(var P=O.an();P.i1();P.i2()){var t=P.i9();if(!S.aj(t)&&S!==t){t=this._b.xo(S,t);if(b.i7(t,!0),x.aa(t),x.ay()>=O.ad()-1)break}}}}for(var i=O.an();i.i1();i.i2())R.b(i.i9());for(var A=x.c1();A.i1();A.i2()){var y=A.i8();b.i4(y)&&(R.d(y.a2()),R.d(y.a3()))}a.az(x),B.e(O)}B.b(),R.c();for(var H=a.c1();H.i1();H.i2()){var k=H.i8();k._h&&(b.i4(k)?this._b.x5(k):this._b.h1(k))}var p=this.a4(this._b),U=new tT,d=p.ak(0),_=p.ak(1);$=d.a2()===_.a2()||d.a2()===_.a3()?d.a3():d.a2(),U.aa($);for(var D=p.c1();D.i1();D.i2()){var $=D.i8().a1($);U.aa($)}for(var L=a.c1();L.i1();L.i2()){var V=L.i8();b.i4(V)||V._h||this._b.u1(V)}return this._b.xi(G),this._b.xj(b),this._b.xi(z),this.a2(U),U},a2:function(z){if(z.ay()<this._b.x0()){for(var G=this._b.xk(),b=z._b;b;b=b.a()){var R=b.d();G.z1(R,b)}for(var E=new Is(this._b,new Ss(G),0,z.ay(),new Ss(G));!E.e();){for(var a=E.f(),x=a.an();x.i1();x.i2()){var B=x.i9();if(G.i1(B)){var B=G.i1(B),O=z.ai(B).d(),O=a.aj(O)?z.ao(a,B):z.an(a,B);G.z1(a,O);break}}for(var Y=a.an();Y.i1();Y.i2()){var l=Y.i9();G.i1(l)||E.d(l)}}this._b.xi(G),E.c()}},a3:function(z){for(var G=0,b=z.x9();b.i1();b.i2())G=L(G,b.i9().ad());return G},a4:function(z){for(var G=[],b=0,R=z.x0();b<R;b++)G[b]=new c;for(var E,a=new Q(G),x=(a.a6(!1),a.a8(z),-1),B=z.x9();B.i1();B.i2()){var O=B.i9(),Y=G[O.al()];Y._a+Y._c>x&&(x=Y._a+Y._c,E=O)}for(var l=new PT,v=E,Z=G[v.al()]._d;Z;Z=G[v.al()]._d)l.ac(Z),v=Z.a1(v);for(var I=G[(v=E).al()]._b;I;I=G[v.al()]._d)l.ae(I),v=I.a1(v);return l}}),z(ys,G,{xm:function(){return new Ys(this)},xo:function(z,G){return this.xn(z,NT,G,NT,0,0)},xn:function(z,G,b,R,E,a){return new bs(this,z,G,b,R,E,a)},x4:function(z){this.b3(z)},b3:function(z){for(var G;G=z._o[0];)this.x5(G);for(;G=z._o[1];)this.x5(G);this._v.c(z),z._g=NT,this._y=!0},x5:function(z){this.a11(z)},a11:function(z){if(z._h!==this)throw"";this.a12(z,z.a2(),z.a3()),this._x.c(z),z._h=NT,this._u=!0},x7:function(z){z._p=this._v._c,z._g=this,z.ac(),z._c.length<this._z._b&&this._z.a3(z,z._c.length,this._z._b),this._v.a(z),this._y=!0},x8:function(z){if(z._h)throw"";z._c.length<this._w._b&&this._w.a3(z,z._c.length,this._w._b),z._a&&z._a._h===this?this._x.b(z,z._a):this._x.a(z),z._h=this,z.a4(),this.b2(z,z.a2(),NT,z.a3(),NT,0,0),this._u=!0},xr:function(z,G,b){var R=z.a2(),E=z.a3();z._h?(R!==G&&(R.ar(z,0,0),(z._d=G).ab(z,NT,0,0,0)),E!==b&&(E.ar(z,1,1),(z._e=b).ab(z,NT,1,1,0))):(z._d=G,z._e=b)},x3:function(z){this.xr(z,z.a3(),z.a2())},h1:function(z){this.a11(z)},u1:function(z){this.x8(z)},h2:function(z){this.x4(z)},h3:function(z){this.x7(z)},xa:function(){return this._v._c},x0:function(){return this._v._c},xh:function(){return this._x._c},xg:function(){return this._x._c},xb:function(){return 0===this._v._c},xq:function(z){return z._g===this},xp:function(z){return z._h===this},xd:function(){return this._v._a},x9:function(){return new vs(this._v)},xf:function(){return new vs(this._x)},x2:function(z,G){var b=w.d(this.xh());if(z&&G)for(var R=this.x9();R.i1();R.i2())R.i9().at(z,1,b),R.i9().at(G,0,b);else if(!G&&z)for(var E=this.x9();E.i1();E.i2())E.i9().at(z,1,b);else if(G&&!z)for(var a=this.x9();a.i1();a.i2())a.i9().at(G,0,b)},xk:function(){return this._z.b(this._v)},xl:function(){return this._w.c(this._x)},xi:function(z){this._z.a5(z,this._v)},xj:function(z){this._w.a6(z,this._x)},xc:function(z){return this._t[z]},x1:function(z,G){this._t[z]=G},x6:function(z){delete this._t[z]},b2:function(z,G,b,R,E,a,x){G.ab(z,b,0,0,a),R.ab(z,E,1,1,x)},a12:function(z,G,b){G.ar(z,0,0),b.ar(z,1,1)},c:function(){for(var z=0,G=this.x9();G.i1();G.i2())G.i9()._p=z++;this._y=!1},b1:function(){for(var z=0,G=this.xf();G.i1();G.i2())G.i8()._g=z++;this._u=!1},xs:function(z){z.as(this,this._z._b),z._p=this._v._c,this._v.a(z)},xt:function(z,G,b,R,E,a,x){z.a6(this,G,R,this._w._b),z._g=this._x._c,this._x.a(z),this.b2(z,z.a2(),b,z.a3(),E,a,x)}}),z(Hs,ys,{gb:function(z){return this.g1(z)},g7:function(z){return this.g2(z)},g5:function(z){z=this.g1(z);return z.i1()+z.i3()/2},g6:function(z){z=this.g1(z);return z.i2()+z.i4()/2},g4:function(z){return new ST(this.g5(z),this.g6(z))},gi:function(z){return this.g1(z).i1()},gh:function(z){return this.g1(z).i2()},ga:function(z){z=this.g1(z);return new ST(z.i1(),z.i2())},gj:function(z){return this.g1(z).i3()},g9:function(z){return this.g1(z).i4()},gm:function(z){return new I(this.gj(z),this.g9(z))},s1:function(z,G){this.s2(z,G.x,G.y)},s2:function(z,G,b){z=this.g1(z);z.i5(G-z.i3()/2,b-z.i4()/2)},s7:function(z,G,b){this.g1(z).i6(G,b)},s8:function(z,G){this.s7(z,G.width,G.height)},s3:function(z,G,b){this.g1(z).i5(G,b)},s4:function(z,G){this.s3(z,G.x,G.y)},gp:function(z){for(var G=this.g2(z),b=new e,R=0;R<G.i1();R++)b.add(G.i2(R));return new S(b)},gf:function(z){for(var G=this.g2(z),b=new m,R=0;R<G.i1();R++)b.aa(G.i2(R));return b},gc:function(z){var G=new e;G.add(this.gs(z));for(var b=this.gp(z).d();b.i1();b.i2())G.add(b.i6());return G.add(this.gl(z)),new S(G)},gd:function(z){var G=new m;G.aa(this.gs(z));for(var b=this.gp(z).d();b.i1();b.i2())G.aa(b.i6());return G.aa(this.gl(z)),G},m1:function(z,G){var b=this.g2(z),R=(b.i5(),G.ah()),E=R.i6(),a=(this.gx(z,E),G.as());for(R.i2();R.i6()!==a;R.i2()){var x=R.i6();b.i4(x.x,x.y)}this.gy(z,a)},s5:function(z,G){var b=this.g2(z);b.i5();for(var R=G.d();R.i1();R.i2()){var E=R.i6();b.i4(E.x,E.y)}},s6:function(z,G){var b=this.g2(z);b.i5();for(var R=G.ah();R.i1();R.i2()){var E=R.i6();b.i4(E.x,E.y)}},m2:function(z,G,b){this.gx(z,G),this.gy(z,b)},gn:function(z){return this.g2(z).i6()},gk:function(z){return this.g2(z).i7()},gt:function(z,G){this.g2(z).i8(G)},gz:function(z,G){this.g2(z).i9(G)},gs:function(z){var G=this.g2(z).i6();return G?new ST(this.g5(z.a2())+G.x,this.g6(z.a2())+G.y):this.g4(z.a2())},gl:function(z){var G=this.g2(z).i7();return G?new ST(this.g5(z.a3())+G.x,this.g6(z.a3())+G.y):this.g4(z.a3())},gx:function(z,G){this.g2(z).i8(new ST(G.x-this.g5(z.a2()),G.y-this.g6(z.a2())))},gy:function(z,G){this.g2(z).i9(new ST(G.x-this.g5(z.a3()),G.y-this.g6(z.a3())))},g8:function(){for(var z=new PT,G=this.xf();G.i1();G.i2())z.aa(G.i8());return z},g3:function(){for(var z=a=P,G=x=Ms,b=this.x9();b.i1();b.i2())var R=this.ga(b.i9()),E=this.gm(b.i9()),z=V(R.x,z),a=V(R.y,a),G=L(R.x+E.width,G),x=L(R.y+E.height,x);for(var B=this.xf();B.i1();B.i2())for(var O=this.gp(B.i8()).c();O.i1();O.i2()){var Y=O.i6();z=V(Y.x,z),a=V(Y.y,a),G=L(Y.x,G),x=L(Y.y,x)}return{x:D(z),y:D(a),width:D(G-z),height:D(x-a)}}}),z(ks,Hs,{a:function(z,G){this._a3=z,this._a4=G},xo:function(z,G){return this.l2(z,G,this._a4.a6())},l2:function(z,G,b){return this.l1(z,NT,G,NT,0,0,b)},xn:function(z,G,b,R,E,a){return this.l1(z,G,b,R,E,a,this._a4.a6())},l1:function(z,G,b,R,E,a,x){z=new bs(this,z,G,b,R,E,a);return z._l=x,z},xm:function(){var z=new Ys(this);return z._r=this._a3.m3(),z},g3:function(){for(var z={x:0,y:0,width:-1,height:-1},G=this.x9();G.i1();G.i2())G.i9()._r.m1(z);return z},g1:function(z){return z._r},g2:function(z){return z._l},g5:function(z){return z._r.m4()},g6:function(z){return z._r.m5()},gi:function(z){return z._r.i1()},gh:function(z){return z._r.i2()},gj:function(z){return z._r.i3()},g9:function(z){return z._r.i4()},s2:function(z,G,b){z._r.m6(G,b)},s7:function(z,G,b){z._r.i6(G,b)},s3:function(z,G,b){z._r.i5(G,b)}}),z(ps,Hs,{g1:function(z){var G=this._ap.i1(z);return G||(G=new xs,this._ap.z1(z,G)),G},g2:function(z){var G=this._as.i1(z);return G||(G=new as,this._as.i8(z,G)),G}}),function(z){WT.superClass.constructor.call(this),this._ay=z,this._a0=this.xk(),this._au=this.xl()}),_=(z(WT,ps,{c2:function(z){return this._a0.i1(z)},a2:function(z,G){this._a0.z1(z,G)},h:function(){this._az||(this._az=this.xk());for(var z=w.a(this._ay.x0()+1),G=1,b=this.x9();b.i1();){for(var R=this.c2(b.i9()),E=R.x1();E.i1();E.i2())z[E.i9().al()]=G;for(var a=new PT,x=R.x1();x.i1();x.i2())for(var B=x.i9(),O=z[B.al()],Y=B.ap();Y.i1();Y.i2()){var l=Y.i8();z[l.a3().al()]===O&&a.ac(l)}this._az.z1(b.i9(),a),b.i2(),G++}},d1:function(z){return this._az.i1(z)},b:function(z){return this._au.i1(z)},a3:function(z,G){this._au.i8(z,G)},a1:function(){var z=this._ay.xk(),G=W.a4(w.a(this._ay.xh())),G=U.a5(this._ay,G,U.a1(this._ay,G,z));this.d2(z,G),this._ay.xi(z)},c1:function(z){for(var G,b=-1,R=0,E=z.length;R<E;R++){var a=z[R];a.ay()>b&&(b=(G=a).ay())}return G},d2:function(z,G){for(var b=this._ay.xl(),R=this._ay.xk(),E=G.length,a=0;a<E;a++)for(var m=G[a],x=m.c1();x.i1();x.i2())b.i8(x.i8(),m);for(var B=this.c1(G),O=(this.a4(B,z,b,new e,R),{}),E=G.length,Y=0;Y<E;Y++){var _,q=G[Y];1<q.ay()&&(_=this.xm(),O[q._id]=_)}for(var l=this._ay.x9();l.i1();l.i2()){var v,K,Z=l.i9();z.i4(Z)&&!R.i1(Z)&&(v=this.xm(),O[Z._id]=v,(K=new tT).aa(Z),this.a2(v,K))}var I=w.d(2),T=0;for(E=G.length;T<E;T++){var W=G[T];if(1===W.ay()){W=W.c2();I[0]=W.a2(),I[1]=W.a3();for(var f=0;f<2;f++){var h,C,g=I[f];1===g.ad()&&(h=this.xm(),O[g._id]=h,(C=new tT).aa(g),this.a2(h,C))}}}for(var S,r,N=this._ay.x9();N.i1();N.i2()){var P=N.i9();if(R.i1(P))for(var s=R.i1(P),Q=O[s._id],t=P.af();t.i1();t.i2()){var i,A,y,H=t.i8();b.i1(H)!==s&&((i=O[b.i1(H)._id])||(y=H.a1(P),i=(A=R.i1(y))?O[A._id]:O[y._id]),(y=(A=Q.aj(i))?this.b(A):(A=this.xo(Q,i),new PT)).aa(H),this.a3(A,y))}else if(z.i4(P))for(var n=O[P._id],k=P.af();k.i1();k.i2()){var c,o=k.i8(),p=O[o.a1(P)._id];p&&!n.aj(p)&&(p=this.xo(n,p),(c=new PT).aa(o),this.a3(p,c))}}2===this._ay.x0()&&1===this._ay.xg()&&(S=O[(B=this._ay.xf().i8()).a2()._id],(r=O[B.a3()._id])&&S&&!r.aj(S)&&(S=this.xo(S,r),(r=new PT).aa(B),this.a3(S,r)));var U=w.a(this._ay.x0());E=G.length;for(var d=0;d<E;d++){var D=G[d],$=O[D._id];if($){var L=this.c2($);L||(L=new tT,this.a2($,L));for(var X=D.c1();X.i1();X.i2()){var J=X.i8(),V=J.a2();1===U[V.al()]||z.i4(V)&&R.i1(V)!==D||(U[V.al()]=1,L.aa(V)),1===U[(V=J.a3()).al()]||z.i4(V)&&R.i1(V)!==D||(U[V.al()]=1,L.aa(V))}}}this._ay.xj(b),this._ay.xi(R)},a4:function(z,G,b,R,E){if(!R.contains(z)){R.add(z);for(var a=[],x=z.c1();x.i1();x.i2()){var B=x.i8();a[0]=B.a2(),a[1]=B.a3();for(var O=0;O<2;O++){var Y=a[O];if(G.i4(Y)&&!E.i1(Y)){1<z.ay()&&E.z1(Y,z);for(var l=Y.af();l.i1();l.i2())this.a4(b.i1(l.i8()),G,b,R,E)}}}}}}),z(Us,ks,{}),{_D:new S,_E:new ST(0,0),b:function(z){if(0<z.i1()){for(var G=new e,b=z.i1()-1;0<=b;b--)G.add(z.i2(b));z.i5();for(var R=0,E=G.size();R<E;R++){var a=G.get(R);z.i4(a.x,a.y)}}var x=z.i6();z.i8(z.i7()),z.i9(x)},c:function(z){_.d(z,!0)},d:function(z,G){if(G)for(var b=z.xf();b.i1();b.i2()){var R=b.i8();z.gt(R,_._E),z.gz(R,_._E),z.s5(R,_._D)}else for(var E=z.xf();E.i1();E.i2())z.s5(E.i8(),_._D)},e:function(z){for(var G=new ST(0,0),b=z.xf();b.i1();b.i2()){var R=b.i8();z.gt(R,G),z.gz(R,G)}},f:function(z,G,b,R){for(var E=z.gc(G).b(),a=w.d(E),x=0,B=z.gc(G).c();B.i1();B.i2()){var O=B.i6();(x<=0||!O.equals(a[x-1]))&&(a[x]=new ST(O.x,O.y),x++)}if(!((E=x)<2)){for(var Y=new e,l=_.i(new y(a[1].x-a[0].x,a[1].y-a[0].y)),v=(l.x*=R,l.y*=R,_.h(a[0],l)),l=_.h(a[1],l),Z=new g(v,l),I=1;I<E-1;I++){var T=Z,W=_.i(new y(a[I+1].x-a[I].x,a[I+1].y-a[I].y)),f=(W.x*=R,W.y*=R,_.h(a[I],W)),W=_.h(a[I+1],W),Z=new g(f,W),f=g.a6(T,Z);f&&Y.add(new ST(f.x,f.y))}var l=new y(a[E-1].x-a[E-2].x,a[E-1].y-a[E-2].y),l=((l=_.i(l)).x*=R,l.y*=R,_.h(a[E-1],l)),h=new S(Y);G.a2()===b.a2()?(z.s5(b,h),z.m2(b,v,l)):(z.s5(b,h.a()),z.m2(b,l,v))}},g:function(z,G,b,R){for(var E=R,a=b.c1();a.i1();a.i2()){var x=a.i8();_.f(z,G,x,E),E<0&&(E-=R),E=-E}},a:function(z,G,b){return _.j(z,G,b,1)},l:function(z,G,b){if(!z||z.length<1)return G&&(G.x=0,G.y=0,G.width=0,G.height=0),{width:0,height:0};for(var R=0,E=0,a=0;a<z.length;a++)var x=z[a],R=L(R,x.width),E=L(E,x.height);var B,O,Y=R*E*z.length,Y=Y/$(Y/b),b=D(Y/R),Y=p(Y/R),l=p(z.length/b),v=p(z.length/Y),Z=b*l<Y*v?(B=b,l):(B=Y,v),I=0,T=0,W=0,f=0;if(E<R)for(var h=0;h<z.length;h++)(O=z[h]).x=T*R,O.y=I*E,W=L(W,O.x+O.width),f=L(f,O.y+O.height),++T>=B&&(I++,T=0);else for(var g=0;g<z.length;g++)(O=z[g]).x=T*R,O.y=I*E,W=L(W,O.x+O.width),f=L(f,O.y+O.height),++I>=Z&&(T++,I=0);return G&&(G.x=0,G.y=0,G.width=W,G.height=f),{width:Z,height:B}},j:function(z,G,b,R){if(!z||z.length<1)return G&&(G.x=0,G.y=0,G.width=0,G.height=0),0;for(var E=Y=z[0].width,a=l=z[0].height,x=z.length,B=1;B<x;B++)var O=z[B].width,E=V(E,O),Y=L(Y,O),O=z[B].height,a=V(a,O),l=L(l,O);if(.95<a/l&&.95<E/Y)return _.l(z,G,b).width;for(var v=new m,Z=0,I=0;I<x;I++){var T=z[I];v.aa(z[I]),Z=D(Z+T.width*T.height)}v.a1(function(z,G){var b=D(G.height)-D(z.height);return 0==b?D(G.width)-D(z.width):b});var W=0,f=0,h=D(b*$(Z/b)),g=0,S=new m;do{var r,N,P=new m,t=r=N=0;S.aa(P);for(var i=v.ah();i.i1();i.i2()){var A=i.i6(),t=t+A.width>h&&0<P.ay()?(N=L(N,t),(P=new m).aa(A),S.aa(P),D(A.width)):(P.aa(A),D(t+A.width));1===P.ay()&&(r=D(r+P.am().height))}}while((N=L(N,t))<b*r&&g!==N&&(S.af(),h=D(1.1*h),g=N),S.ar());for(var y=0,H=S.ah();H.i1();H.i2()){for(var k=0,p=H.i6(),U=p.ah();U.i1();U.i2()){var d=U.i6();d.x=k,d.y=y,k+=d.width}W=L(W,k),y+=_.k(p),f=L(f,y)}return G&&(G.x=0,G.y=0,G.width=W,G.height=f),S.ay()},k:function(z){for(var G=0,b=z.ah();b.i1();b.i2())G=L(b.i6().height,G);return G},h:function(z,G){return new ST(z.x+G.x,z.y+G.y)},i:function(z){var G=$(z.x*z.x+z.y*z.y);return new y(-z.y/G,z.x/G)}}),fT=(z(B,G,{a:function(){return this._b},b:function(){return this._a},c:function(){return 1===this._a},d:function(){return 2===this._a},e:function(){return 4===this._a},f:function(){return 8===this._a},g:function(){return 0===this._a}}),B.h=function(z,G){z=z.xc("A");return z?z.i1(G):NT},B.i=function(z,G){z=z.xc("B");return z?z.i1(G):NT},B.j=function(z){switch(z){case 1:return B.k;case 2:return B.l}return NT},B.k=new B(1),B.l=new B(2),z(ds,G,{}),z(Ds,G,{}),z($s,R,{i4:function(z){return!0},i3:function(z){if(z&&(this._d5=z,this.s(z))){var G=new Ds,b=0,R=D(this._dx*(this._dz.length*this._dz.length)+20*this._dz.length),R=L(R,1e4),E=this._ea*this._ea*this._dz.length,a=this._df;try{for(;this._dj>E&&b<R;b++){var x=this.b(2147483647&b),B=(0==a--&&(this._dy.b()>this._eb&&(b=R),a=this._df),this.h(x,G),this.d(x,G),this.i(x,G),this._d8?(this.g(x,G),this.j(x,G)):(this.f(x,G),this.c(x,G)),$(G._a*G._a+G._c*G._c+G._b*G._b));this.ac(x,G,B),this.aa(x,G,B)}}finally{this.r()}}},s:function(z){if(!z||z.xa()<1)return!1;this._d5=z,this._dp=1,this._dy=new As,this._dt=z.x0(),this._d2=w.d(this._dt),this._df=1+1e5/this._dt,this._ed=1/(2*this._di),this._de=this._ed*this._ee/(.05*this._dl),this._d3=js(this._dl,-1)*this._ed,this._dr=js(this._dl,3)*this._ed,this._dj=0,this._du=new Ds,this._dq=L(20*this._dl,10);var G=L(.1,V(this._dw*this._dl,this._dq)),b=this._dt;_.c(z),this._dz=w.d(b);for(var R=z.x9();R.i1();R.i2()){var E=R.i9(),a=new ds(E,G,this._dl,this._dt,z);this._dz[--b]=a,this._dj+=a._r,this._dh+=a._r*a._r,this._du._a+=a._k,this._du._c+=a._h,this._du._b+=a._g,this._d2[E.al()]=a}return this._d8=!1,0<this._dz.length},b:function(z){var G=this._dz.length,z=G-z%G-1,G=Rs.k(1+z),b=this._dz[G];return this._dz[G]=this._dz[z],this._dz[z]=b},f:function(z,G){for(var b,R,E=b=R=0,a=z._b.ae();a;a=a.a7()){var x=this._d2[a.a2().al()],B=x._k-z._k,O=x._h-z._h,Y=x._g-z._g,l=$(B*B+O*O+Y*Y),x=l-(x._e+z._e);x<=0||(E+=B*(B=x*x*this._d3/l),b+=O*B,R+=Y*B)}for(var v=z._b.ag();v;v=v.a8()){var Z=this._d2[v.a3().al()],I=Z._k-z._k,T=Z._h-z._h,W=Z._g-z._g,f=$(I*I+T*T+W*W),Z=f-(Z._e+z._e);Z<=0||(E+=I*(I=Z*Z*this._d3/f),b+=T*I,R+=W*I)}G._a+=E,G._c+=b,G._b+=R},g:function(z,G){var b,R,E=b=R=0;this._dp++,z._f=this._dp;for(var a=z._b.ae();a;a=a.a7()){var x=this._d2[a.a2().al()],B=(x._f=this._dp,x._k-z._k),O=x._h-z._h,Y=x._g-z._g,l=$(B*B+O*O+Y*Y);0!==l&&(x=L(1e-6,l-(z._e+x._e)),E+=B*(B=((B=-this._ef[a.a5()]/(x*x))+x*x*this._d1[a.a5()])/l),b+=O*B,R+=Y*B)}for(var v=z._b.ag();v;v=v.a8()){var Z=this._d2[v.a3().al()],I=(Z._f=this._dp,Z._k-z._k),T=Z._h-z._h,W=Z._g-z._g,f=$(I*I+T*T+W*W);0!==f&&(Z=L(1e-6,f-(z._e+Z._e)),E+=I*(I=((I=-this._ef[v.a5()]/(Z*Z))+Z*Z*this._d1[v.a5()])/f),b+=T*I,R+=W*I)}G._a+=E,G._c+=b,G._b+=R},j:function(z,G){for(var b,R,E=b=R=0,a=this._dt-1;0<=a;a--){var x,B,O,Y,l=this._d2[a];l._f===z._f||0!=(O=(Y=z._k-l._k)*Y+(x=z._h-l._h)*x+(B=z._g-l._g)*B)&&(O=$(O),l=L(1e-6,O-(z._e+l._e)),E+=Y*(Y=this._dr/(l*l*O)),b+=x*Y,R+=B*Y)}G._a+=E,G._c+=b,G._b+=R},c:function(z,G){for(var b,R,E=b=R=0,a=this._dt-1;0<=a;a--){var x=this._d2[a],B=z._k-x._k,O=z._h-x._h,Y=z._g-x._g,l=B*B+O*O+Y*Y;0!=l&&(E+=B*(x=(l=(B=$(l))-(z._e+x._e))<=0?this._dr/(1e-8*B):this._dr/(l*l*B)),b+=O*x,R+=Y*x)}G._a+=E,G._c+=b,G._b+=R},i:function(z,G){z=this._du._b/this._dt-z._g;G._b+=z*this._dl*this._dt/this._dh},d:function(z,G){var b,R;0!==this._de&&(b=this._du._a/this._dt-z._k,R=this._du._c/this._dt-z._h,z=this._du._b/this._dt-z._g,G._a+=b*this._de,G._c+=R*this._de,G._b+=z*this._de)},h:function(z,G){z=.05*(z._r+2);0<z&&(G._a=Rs.l(-z,z),G._c=Rs.l(-z,z),G._b=Rs.l(-z,z))},ac:function(z,G,b){0!==b&&0!==z._a&&(G=(G._a*z._o+G._c*z._l+G._b*z._i)/(b*z._a),this._dh-=z._r*z._r,this._dj-=z._r,0<z._p*G?z._r+=.45*G:z._r+=.15*G,z._r>this._dq?z._r=this._dq:z._r<.1&&(z._r=.1),this._dj+=z._r,this._dh+=z._r*z._r,z._p=G)},aa:function(z,G,b){var R,E,a;0<b&&(a=z._r/b,R=G._a*a,E=G._c*a,a=G._b*a,z._k+=R,z._h+=E,z._g+=a,this._du._a+=R,this._du._c+=E,this._du._b+=a,z._a=b,z._o=G._a,z._l=G._c,z._i=G._b)},r:function(){for(var z=this._d2.length-1;0<=z;z--){var G=this._d2[z];this._d5.s2(G._b,G._k,G._h)}}}),z(Ls,G,{r:function(){for(var z in this._c){z=this._c[z];z.g.setExpanded(z.b)}},p:function(){for(var R=new e,z=new e,E=new e,G=0,b=this._f.size();G<b;G++){var a=this._f.get(G);a instanceof ss?a.isLooped()||R.add(a):a instanceof Cs&&(a.getParent()instanceof r?a instanceof r||z.add(a):(R.add(a),a instanceof r&&(E.add(a),this.l(a))))}return z.each(function(z){for(var G=!0,b=0;b<E.size();b++)if(z.isDescendantOf(E.get(b))){G=!1;break}G&&R.add(z)}),R},l:function(z){if(!this._c[z.getId()]){var G=this._e,b=G.getType(z),R=i(b);if(R){this._c[z.getId()]={g:z,b:z.isExpanded()},z.setExpanded(!0);for(var E,a=new e,x=z.getChildren(),B=0,O=(x=z instanceof r&&G.isCascadeGroup()?x.toList(function(z){return G.isLayoutable(z)}):x).size();B<O;B++)if((E=x.get(B))instanceof r&&(this.l(E),E.setExpanded(!1)),E instanceof ss||a.contains(E)||a.add(E),E instanceof Cs){var Y=E.getEdges();if(Y)for(var l=0,v=Y.size();l<v;l++){var Z=Y.get(l);a.contains(Z)||a.add(Z)}}var I=new Us(this._e,a,b);try{R.i2(I);var T,W=A(b);for(T in I._a){var f=I._a[T],h=I.g4(f);f.node.p(W?W.tf(h):h)}}catch(z){}z.eachChild(function(z){z instanceof r&&z.setExpanded(!0)})}}}}),z(O,G,{i1:function(z,G,b){var R=this.a1(z,G);return this.a2(z,G,b),R},a1:function(z,G){for(var z=O.i4(z),b=(z.ax(),0),R=z.x1();R.i1();R.i2())G.i7(R.i9(),-1);for(var E=z.x1();E.i1();E.i2()){for(var a=E.i9(),x=-1,B=a.aq();B.i1();B.i2())x=L(x,G.i2(B.i9()));G.i7(a,x+1),b=L(b,x+1)}return b+1},a2:function(z,G,b){b.az(O.i3(z,G))}}),O.i3=function(z,G){for(var b=new PT,R=z.xf();R.i1();R.i2()){var E=R.i8();G.i2(E.a2())>G.i2(E.a3())&&(z.x3(E),b.ac(E))}return b},O.i4=function(z){var G=w.a(z.xa());return(new o).a1(z,G),O.i2(z,G)},O.i2=function(z,G){for(var b=w.d(z.x0()),R=z.x9();R.i1();R.i2()){var E=R.i9();b[G[E.al()]]=E}return new tT(b)},z(E,PT,{c0:function(){this._bc=1,this._bd=0}}),z(Vs,G,{i4:function(z){this._m3=z},i5:function(z){this._m4=z},i3:function(z){this._m1=z},i6:function(z){this._m2=z},i2:function(z){this._m5=z},t1:function(){return this._m2},a1:function(z,G){for(var b=w.d(G.length),R=0;R<G.length;R++)b[R]=G[R].x1();this.a2(z,b)},a2:function(z,G){for(var b=w.a(G.length),R=0,E=0;E<G.length;E++){var a=0,x=G[E];for(x.i4();x.i1();x.i2())a=L(a,z.g9(x.i9()));for(b[E]=a,x.i4();x.i1();x.i2()){var B=(b[E]-z.g9(x.i9()))/2;z.s4(x.i9(),new ST(z.gi(x.i9()),R+B))}R+=b[E]+this.t1(),x.i4()}},i1:function(z,G,b){this._m6=z,this.t2(G,b)}}),z(ms,Vs,{t2:function(z,G){var b=this._m6;this._a=b.xc("D"),this._h=b.xc("C"),this.a1(b,z),this.tg(b,z),this.tf(z,W.a5(this._e),this._m5,this._l),this.tb(b,this._f[0]),this.ta(z),this.th(b,this._f[0],z),this.b(z),this.tb(b,this._f[1]),this.ta(z),this.th(b,this._f[1],z),this.b(z),this.a11(this._f[1]),this.a12(z),this.tb(b,this._f[2]),this.ta(z),this.th(b,this._f[2],z),this.b(z),this.tb(b,this._f[3]),this.ta(z),this.th(b,this._f[3],z),this.b(z),this.a11(this._f[3]),this.a12(z),this.tc(b),this.tj()},a11:function(z){for(var G=0;G<z.length;G++)z[G]=-z[G]},b:function(z){for(var G=0;G<z.length;G++)z[G].ax();for(var b=0;b<z.length;b++)for(var R,E=0,a=z[b].x1();a.i1();a.i2()){var x=a.i9(),B=x.al();this._l[B]=E++,this._b[B]=R,this._k[B]=NT,R&&(this._k[R.al()]=x),R=x}var O=this._a;this._a=this._h,this._h=O;for(var Y=this._m6.xf();Y.i1();Y.i2()){var l=Y.i8(),v=this._m6.gn(l),v=(this._m6.gt(l,new ST(-v.x,v.y)),this._m6.gk(l));this._m6.gz(l,new ST(-v.x,v.y))}var Z=this._l;this._m6.x2(function(z,G){return!z&&G?1:z&&!G?-1:z||G?Z[z.a2().al()]-Z[G.a2().al()]:0},function(z,G){return!z&&G?1:z&&!G?-1:z||G?Z[z.a3().al()]:0})},a12:function(z){for(var G=this._m6.xf();G.i1();G.i2()){var b=G.i8(),R=(this._m6.x3(b),this._m6.gn(b)),E=this._m6.gk(b);this._m6.gz(b,R),this._m6.gt(b,E)}for(var a=new m,x=0,B=0;x<z.length;x++)a.ae(z[x]);for(;B<z.length;B++)z[B]=a.au();var O=this._l;this._m6.x2(function(z,G){return!z&&G?1:z&&!G?-1:z||G?O[z.a2().al()]-O[G.a2().al()]:0},function(z,G){return!z&&G?1:z&&!G?-1:z||G?O[z.a3().al()]:0})},tg:function(z,G){var b=z.x0(),R=z.xg(),E=0;for(this._l=w.a(b),this._b=w.d(b),this._k=w.d(b),this._m=w.d(b),this._i=w.d(b),this._o=w.d(b),this._f=w.e(4,b),this._c=w.a(b),this._g=w.a(b),this._j=w.a(b),this._d=w.b(b),this._e=w.b(R);E<G.length;E++)for(var a,x=0,B=G[E].x1();B.i1();B.i2()){var O=B.i9(),Y=O.al();this._l[Y]=x++,this._b[Y]=a,this._k[Y]=NT,a&&(this._k[a.al()]=O),a=O}var l=this._l;z.x2(function(z,G){return!z&&G?1:z&&!G?-1:z||G?l[z.a2().al()]-l[G.a2().al()]:0},function(z,G){return!z&&G?1:z&&!G?-1:z||G?l[z.a3().al()]:0})},tb:function(z,G){for(var b=z.x9();b.i1();b.i2()){var R=b.i9(),E=R.al();this._m[E]=R,this._i[E]=R,G[E]=P,this._o[E]=R,this._c[E]=P,this._d[E]=!1,this._j[E]=this._g[E]=0}},ta:function(z){for(var G=1;G<z.length;G++)for(var b=-1,R=z[G]._b;R;R=R.a()){var E=R.d(),a=E.al(),x=E.ak();if(0!==x){for(var B=D((x+1)/2),O=p((x+1)/2),Y=1,l=E.ae(),v=!1;Y<B;l=l.a7())Y++;for(;Y<=O&&!v;Y++){var Z=this._m6.g2(l),I=l.a2().al();this._i[a]===E&&!this._e[l.a5()]&&b<this._l[I]&&(b=this._l[I],this._i[I]=E,this._m[a]=this._m[I],this._i[a]=this._m[a],v=!0,this._j[I]=Z.i6().x,this._g[a]=Z.i7().x),l=l.a7()}}}},th:function(z,G,b){for(var R=z.x9();R.i1();R.i2()){var E=R.i9(),a=E.al();this._m[a]===E&&this.td(z,E,G)}for(var x,B,O=0;O<b.length;O++)b[O].x1().i1()&&(B=(x=b[O].x1().i9()).al(),this._o[this._m[B].al()]===x&&this.tk(z,x,G));for(var Y=z.x9();Y.i1();Y.i2()){var l=Y.i9().al(),v=this._c[this._o[this._m[l].al()].al()];v<P&&(G[l]+=v)}},td:function(z,G,b){var R=G.al();if(b[R]===P){var E=G,a=b[R]=0;do{var x,B,O,Y=E.al()}while(Y!==R&&(a-=this._g[Y]),0<this._l[Y]&&(x=this._b[Y],O=(B=this._m[this._b[Y].al()]).al(),this.td(z,B,b),this._o[R]===G&&(this._o[R]=this._o[O]),this._o[R]===this._o[O]&&(b[R]=L(b[R],b[x.al()]+this.ti(z,x,E)-a))),a+=this._j[Y],(E=this._i[Y])!==G);a=0,E=G;do{var l=E.al()}while(l!==R&&(a-=this._g[l]),b[l]=b[R]+a,a+=this._j[l],(E=this._i[l])!==G)}},tk:function(z,G,b){var R=G.al();if(!this._d[R]){this._d[R]=!0;var E=G;do{var a,x,B=E.al(),O=this._k[B]}while(O&&(a=O.al(),(x=this._o[this._m[a].al()])!==this._o[R]?(O=b[a]-b[R]-this.ti(z,E,O),this._c[x.al()]!==P&&(O+=this._c[x.al()]),this._c[this._o[R].al()]=V(this._c[this._o[R].al()],O)):this.tk(z,this._m[a],b)),(E=this._i[B])!==G)}},tc:function(z){for(var G=w.a(4),b=w.a(4),R=z.x9();R.i1();R.i2()){var E=R.i9().al();b[0]+=this._f[0][E],b[1]+=this._f[1][E],b[2]+=this._f[2][E],b[3]+=this._f[3][E]}b[0]/=z.xa(),b[1]/=z.xa(),b[2]/=z.xa(),b[3]/=z.xa();for(var a=z.x9();a.i1();a.i2()){var x=a.i9(),B=x.al(),O=z.g4(x);G[0]=this._f[0][B]-b[0],G[1]=this._f[1][B]-b[1],G[2]=this._f[2][B]-b[2],G[3]=this._f[3][B]-b[3],G.sort(w.n),z.s1(x,new ST((G[1]+G[2])/2,O.y))}},ti:function(z,G,b){var R=z.gj(G),z=z.gj(b),R=1<R&&1<z?this._m1+(R+z)/2:this._m3+(R+z)/2;return this._l[G.al()]<this._l[b.al()]?(this._a&&(R+=this._a.i3(b)),this._h&&(R+=this._h.i3(G))):(this._a&&(R+=this._a.i3(G)),this._h&&(R+=this._h.i3(b))),R},tj:function(){this._l=this._b=this._k=this._e=this._m=this._i=this._f=this._c=this._o=this._d=this._j=this._g=NT},tf:function(z,G,b,R){for(var E=z.length,a=2;a<E-1;a++)for(var x=-1,B=0,O=0,Y=z[a].x1(),l=z[a].x1();l.i1();l.i2()){var v,Z=l.i9(),I=!1;if(1===Z.ak()&&(v=Z.ae().a2(),b.i1(v)&&b.i1(Z)&&(I=!0)),O===z[a].ay()-1||I){for(var T=I?R[v.al()]:z[a-1].ay();B<=O;B++){for(var W=Y.i9().am();W.i1();W.i2()){var f=R[W.i8().a2().al()];(f<x||T<f)&&G.i7(W.i8(),!0)}Y.i2()}x=T}O++}}}),z(_s,G,{a3:function(z){this._b=z},a4:function(z,G,b,R,E){this.a2(z)&&((z=this.b2(z))._o=G,z._m=E,z._n=R,z._f=b)},b2:function(z){var G=this._f[z._id];return G||(G=new fT,this._f[z._id]=G),G},a2:function(z){return!!this._f[z._id]},c:function(){for(var z=W.a1(w.a(this._d.xa())),G=W.a1(w.a(this._d.xa())),b=this._d.x9();b.i1();b.i2()){var R,E=b.i9();this.a2(E)&&(R=this.b2(E),z.i6(E,this._b*(R.c()-1)),G.i6(E,this._b*(R.b()-1)))}this._d.x1("D",z),this._d.x1("C",G)},g:function(){this._d.x6("D"),this._d.x6("C")},f:function(){for(var z=this._d.x9();z.i1();z.i2()){var G=z.i9();if(this.a2(G)){var b=this._d.gi(G),R=this._d.gh(G),E=this._d.gj(G),a=this._d.g9(G),x=this.b2(G),G=x._q.ay()+x._b.ay()+x._f,B=x._d.ay()+x._g.ay()+x._o,O=x._i.ay()+x._l.ay()+x._n,Y=x._h.ay()+x._k.ay()+x._m,l=this._a.a7(E,G),v=this._a.a7(E,B),Z=this._a.a7(a,Y),I=this._a.a7(a,O);x.a2(this._a.a8(E,G,l),this._a.a8(E,B,v),this._a.a8(a,Y,Z),this._a.a8(a,O,I));for(var T=x._j.c1();T.i1();T.i2()){var W=T.i8(),f=this.a1(W),h=this.b1(W),g=new m;f.b()===h.b()?(f.c()?(g.aa(new ST(b+x._g._bd*v+x._c,R)),g.aa(new ST(b+x._g._bd*v+x._c,R-this._b)),x._g._bd++,g.aa(new ST(b+x._g._bd*v+x._c,R-this._b)),g.aa(new ST(b+x._g._bd*v+x._c,R)),x._g._bd++,x._g._bc=L(x._g._bc,2)):f.d()?(g.aa(new ST(b+x._b._bd*l+x._p,R+a)),g.aa(new ST(b+x._b._bd*l+x._p,R+a+this._b)),x._b._bd++,g.aa(new ST(b+x._b._bd*l+x._p,R+a+this._b)),g.aa(new ST(b+x._b._bd*l+x._p,R+a)),x._b._bd++,x._b._bc=L(x._b._bc,2)):f.f()?(g.aa(new ST(b,R+x._i._bd*I+x._a)),g.aa(new ST(b-this._b,R+x._i._bd*I+x._a)),x._i._bd++,g.aa(new ST(b-this._b,R+x._i._bd*I+x._a)),g.aa(new ST(b,R+x._i._bd*I+x._a)),x._i._bd++,x._i._bc=L(x._i._bc,2)):f.e()&&(g.aa(new ST(b+E,R+x._h._bd*Z+x._e)),g.aa(new ST(b+E+this._b,R+x._h._bd*Z+x._e)),x._h._bd++,g.aa(new ST(b+E+this._b,R+x._h._bd*Z+x._e)),g.aa(new ST(b+E,R+x._h._bd*Z+x._e)),x._h._bd++,x._h._bc=L(x._h._bc,2)),this._d.m1(W,g)):f.c()||h.c()?f.e()||h.e()?(g.aa(new ST(b+E-x._d._bd*v-x._c,R)),g.aa(new ST(b+E-x._d._bd*v-x._c,R-this._b*x._d._bc)),g.aa(new ST(b+E+this._b*x._h._bc,R-this._b*x._d._bc)),g.aa(new ST(b+E+this._b*x._h._bc,R+x._h._bd*Z+x._e)),g.aa(new ST(b+E,R+x._h._bd*Z+x._e)),x._d._bd++,x._d._bc++,x._h._bd++,x._h._bc++,h.c()&&g.ax(),this._d.m1(W,g)):f.f()||h.f()?(g.aa(new ST(b+x._g._bd*v+x._c,R)),g.aa(new ST(b+x._g._bd*v+x._c,R-this._b*x._g._bc)),g.aa(new ST(b-this._b*x._i._bc,R-this._b*x._g._bc)),g.aa(new ST(b-this._b*x._i._bc,R+x._i._bd*I+x._a)),g.aa(new ST(b,R+x._i._bd*I+x._a)),x._g._bd++,x._g._bc++,x._i._bd++,x._i._bc++,h.c()&&g.ax(),this._d.m1(W,g)):(f.d()||h.d())&&(g.aa(new ST(b+E-x._d._bd*v-x._c,R)),g.aa(new ST(b+E-x._d._bd*v-x._c,R-this._b*x._d._bc)),g.aa(new ST(b+E+this._b*x.b(),R-this._b*x._d._bc)),g.aa(new ST(b+E+this._b*x.b(),R+a+this._b*x._q._bc)),g.aa(new ST(b+E-x._q._bd*l-x._p,R+a+this._b*x._q._bc)),g.aa(new ST(b+E-x._q._bd*l-x._p,R+a)),x._d._bd++,x._d._bc++,x._k._bc++,x._h._bc++,x._q._bc++,x._q._bd++,h.c()&&g.ax(),this._d.m1(W,g)):f.d()||h.d()?f.e()||h.e()?(g.aa(new ST(b+E-x._q._bd*l-x._p,R+a)),g.aa(new ST(b+E-x._q._bd*l-x._p,R+a+this._b*x._q._bc)),g.aa(new ST(b+E+this._b*x._k._bc,R+a+this._b*x._q._bc)),g.aa(new ST(b+E+this._b*x._k._bc,R+a-x._k._bd*Z-x._e)),g.aa(new ST(b+E,R+a-x._k._bd*Z-x._e)),x._q._bd++,x._q._bc++,x._k._bd++,x._k._bc++,h.d()&&g.ax(),this._d.m1(W,g)):(f.f()||h.f())&&(g.aa(new ST(b+x._b._bd*l+x._p,R+a)),g.aa(new ST(b+x._b._bd*l+x._p,R+a+this._b*x._b._bc)),g.aa(new ST(b-this._b*x._l._bc,R+a+this._b*x._b._bc)),g.aa(new ST(b-this._b*x._l._bc,R+a-x._l._bd*I-x._a)),g.aa(new ST(b,R+a-x._l._bd*I-x._a)),x._b._bd++,x._b._bc++,x._l._bd++,x._l._bc++,h.d()&&g.ax(),this._d.m1(W,g)):(g.aa(new ST(b,R+a-x._l._bd*I-x._a)),g.aa(new ST(b-this._b*x._l._bc,R+a-x._l._bd*I-x._a)),g.aa(new ST(b-this._b*x._l._bc,R+a+this._b*x.a1())),g.aa(new ST(b+E+this._b*x._k._bc,R+a+this._b*x.a1())),g.aa(new ST(b+E+this._b*x._k._bc,R+a-x._k._bd*Z-x._e)),g.aa(new ST(b+E,R+a-x._k._bd*Z-x._e)),x._l._bd++,x._l._bc++,x._b._bc++,x._q._bc++,x._k._bc++,x._k._bd++,h.f()&&g.ax(),this._d.m1(W,g))}}}},a5:function(z,G){for(var b=0;b<z.length;b++)for(var R=z[b],E=G[b],a=R.x1();a.i1();a.i2()){var x=a.i9();this.a2(x)&&(x=this.b2(x),E._g=L(E._g,this._b*(x.d()-1)),E._j=L(E._j,this._b*(x.a1()-1)))}},a1:function(z){var G,b=this._d.xc("A");if(!(G=b?b.i1(z):G)||G.g()){b=this._d.xc("B");if(!b)return B.j(1);b=b.i1(z);if(!b||b.g())return B.j(1);if(b.c())return B.j(8);if(b.f())return B.j(1);if(b.d())return B.j(4);if(b.e())return B.j(2)}return G},b1:function(z){var G,b=this._d.xc("B");if(!(G=b?b.i1(z):G)||G.g()){b=this._d.xc("A");if(!b)return B.j(8);b=b.i1(z);if(!b||b.g())return B.j(8);if(b.c())return B.j(8);if(b.f())return B.j(1);if(b.d())return B.j(4);if(b.e())return B.j(2)}return G}}),function(){this._o=0,this._f=0,this._n=0,this._m=0,this._c=0,this._p=0,this._e=0,this._a=0,this._j=new PT,this._g=new E,this._d=new E,this._b=new E,this._q=new E,this._h=new E,this._k=new E,this._i=new E,this._l=new E}),hT=(z(fT,G,{a1:function(){return L(this._q._bc,this._b._bc)},d:function(){return L(this._d._bc,this._g._bc)},b:function(){return L(this._k._bc,this._h._bc)},c:function(){return L(this._l._bc,this._i._bc)},a2:function(z,G,b,R){this._c=G,this._a=R,this._p=z,this._e=b,this._g.c0(),this._d.c0(),this._b.c0(),this._q.c0(),this._k.c0(),this._h.c0(),this._l.c0(),this._i.c0()}}),z(rT,G,{a6:function(z){this._k=z,this._t.a1(z),this._b.a3(z)},g1:function(){return this._k},a9:function(z){return this.c1(),z},a5:function(z){return this.a1(),z},b3:function(z){return this.c1(),z=this.c4(z),this._b.c(),z},g2:function(z){return this._b.g(),z},e2:function(z){z=this.f(z),this._b.f()},e1:function(){this._t.d(),this._n&&this._d.xi(this._n),this.a1(),this._d=NT},a1:function(){this._i&&(this._q&&(this._d.x1("A",this._q),this._q=NT),this._p&&(this._d.x1("B",this._p),this._p=NT),this._h&&(this._d.xj(this._h),this._h=NT),this._l&&(this._d.xj(this._l),this._l=NT))},c1:function(){if(this._i){this._h||(this._h=this._d.xl()),this._l||(this._l=this._d.xl());for(var z=this._d.xf();z.i1();z.i2()){var G,b=z.i8(),R=this._j.i1(b.a2()),E=this._j.i1(b.a3());R&&!E?(G=this._j.i1(b.a2()),this._m.i4(G)?this._l.i8(b,B.h(this._d,G)):this._l.i8(b,B.i(this._d,G))):!R&&E?(G=this._j.i1(b.a3()),this._m.i4(G)?this._h.i8(b,B.i(this._d,G)):this._h.i8(b,B.h(this._d,G))):R||E||(this._m.i4(b)?(this._h.i8(b,B.i(this._d,b)),this._l.i8(b,B.h(this._d,b))):(this._h.i8(b,B.h(this._d,b)),this._l.i8(b,B.i(this._d,b))))}this._q=this._d.xc("A"),this._p=this._d.xc("B"),this._d.x1("A",this._h),this._d.x1("B",this._l)}},c4:function(z){this._n=this._d.xk(),this._a=this._d.xl(),this._g=this._d.xl();for(var G=new PT,b=new PT,m=new PT,_=new PT,R=new PT,E=new PT,a=new PT,x=new PT,B=new PT,O=this._d.xk(),Y=0;Y<z.length;Y++)for(var q=0,l=z[Y].x1();l.i1();)O.i6(l.i9(),q),l.i2(),q++;for(var K=function(z,G){z=O.i3(z.a3())-O.i3(G.a3());return z<=0?0<=z?0:-1:1},C=function(z,G){z=O.i3(z.a2())-O.i3(G.a2());return z<=0?0<=z?0:-1:1},v=0;v<z.length;v++)for(var Z=z[v],I=Z._b;I;I=I.a()){var T=I.d();T.av(K),T.au(C),G.af(),b.af(),m.af(),_.af(),R.af(),E.af(),a.af(),x.af(),B.af();for(var W=T.ap();W.i1();){var f=W.i8(),h=this.b1(f);!h||h.d()||h.g()?m.aa(f):h.e()?G.aa(f):h.f()?(b.aa(f),B.aa(f)):h.c()&&(x.aa(f),B.aa(f)),W.i2(),0}for(var s=T.am();s.i1();){var g=s.i8(),S=this.a2(g);!S||S.c()||S.g()?_.aa(g):S.e()?G.aa(g):S.f()?(b.aa(g),B.aa(g)):S.d()&&(E.aa(g),B.aa(g)),s.i2(),0}var r=O.i3(T);if(!B.ar())for(var Q=.1/B.ay(),n=r-.4;!B.ar();n+=Q){var N,P=B.c3();P.a2()===T?(N=this._d.xm(),this._n.z1(N,P.a2()),this._d.s7(N,1,1),this._c.z1(N,this._c.i1(T)),O.i6(N,n),this._a.i8(P,this._d.gn(P)),this._d.gt(P,Rs._A),this._d.xr(P,N,P.a3()),Z.ao(N,I)):(N=this._d.xm(),this._n.z1(N,P.a3()),this._d.s7(N,1,1),this._c.z1(N,this._c.i1(T)),O.i6(N,n),this._g.i8(P,this._d.gk(P)),this._d.gz(P,Rs._A),this._d.xr(P,P.a2(),N),Z.ao(N,I))}if(!G.ar())for(var c=.1/G.ay(),o=r+.1;!G.ar();o+=c)var t,i=G.c3(),I=i.a2()===T?(t=this._d.xm(),this._n.z1(t,i.a2()),this._d.s7(t,1,1),this._c.z1(t,this._c.i1(T)),O.i6(t,o),this._a.i8(i,this._d.gn(i)),this._d.gt(i,Rs._A),this._d.xr(i,t,i.a3()),Z.an(t,I)):(t=this._d.xm(),this._n.z1(t,i.a3()),this._d.s7(t,1,1),this._c.z1(t,this._c.i1(T)),O.i6(t,o),this._g.i8(i,this._d.gk(i)),this._d.gz(i,Rs._A),this._d.xr(i,i.a2(),t),Z.an(t,I));var r=rT._z,A=(r=this._b.a2(T)?this._b.b2(T):r)._b.ay()+E.ay()+T.ao()+R.ay()+r._q.ay();if(0<A)for(var X=this._d.g9(T)/2,y=this._d.gj(T),J=this.a7(y,A),e=-.5*y+this.a8(this._d.gj(T),A,J)+J*(r._b.ay()+E.ay()),w=T.ap();w.i1();w.i2()){var j=w.i8();this.c2(j)||this._j.i1(j.a2())||(this._d.g2(j).i8(new ST(e,X)),e+=J)}var y=this._t.a3(T),H=0,M=0,F=0,u=0;if(y&&(H=y._e.ay(),M=y._c.ay(),F=y._b.ay(),u=y._d.ay()),0<(A=r._g.ay()+H+x.ay()+T.ak()+a.ay()+M+r._d.ay())){for(var k=this._d.gj(T),p=this.a7(k,A),A=this.a8(k,A,p),zs=-.5*k+A+p*(r._g.ay()+H+x.ay()),U=-this._d.g9(T)/2,Gs=T.am();Gs.i1();Gs.i2()){var bs=Gs.i8();this.d1(bs)||this._j.i1(bs.a3())||(this._d.g2(bs).i9(new ST(zs,U)),zs+=p)}if(y){for(var d=-.5*k+A+p*(r._g.ay()+x.ay()+y._e.ay()-1),D=y._e.c1();D.i1();D.i2()){var $=D.i8();this._d.u1($),$.a2()!==T||this.c2($)?this.d1($)||(this._d.g2(D.i8()).i9(new ST(d,U)),d-=p):(this._d.g2(D.i8()).i8(new ST(d,U)),d-=p),this._d.h1($)}for(var d=.5*k-A-p*(r._d.ay()+a.ay()),L=y._c.c1();L.i1();L.i2()){var V=L.i8();this._d.u1(V),V.a2()!==T||this.c2(V)?this.d1(V)||(this._d.g2(L.i8()).i9(new ST(d,U)),d-=p):(this._d.g2(L.i8()).i8(new ST(d,U)),d-=p),this._d.h1(V)}}}this._b.a2(T)&&this._b.a4(T,H+x.ay()+T.ak()+a.ay()+M,E.ay()+T.ao()+R.ay(),F+b.ay(),u+G.ay())}return this._d.xi(O),z},a7:function(z,G){return G<=1?0:z/(G-1+2*this._r)},a8:function(z,G,b){return G<=1?.5*z:.5*(z-b*(G-1))},f:function(z){var m=this.g1(),_=0;for(this._f=this._d.xk();_<z.length;_++)for(var G=z[_]._b;G;){var b=G.d();if(this._n.i1(b)||this._t.b2(b))G=G.a();else{var q=new tT,K=new tT,C=new tT,s=new tT,Q=new tT,n=new tT,c=new PT,o=new PT,X=new AT(q,K,C,s,Q,n,c,o);this._f.z1(b,X),c.ab(b.am()),o.ab(b.ap());for(var J=G.b();J&&this._n.i1(J.d())===b;J=J.b()){var e=J.d(),w=this.c3(e);w.f()?K.ac(e):w.c()?s.ac(e):w.d()&&n.ac(e)}for(var R=G.a();R&&this._n.i1(R.d())===b;R=R.a()){var j=R.d(),M=this.c3(j);M.e()?q.aa(j):M.c()?C.aa(j):M.d()&&Q.aa(j)}G=R}}for(var F=this.d2(z),u=0,E=0;E<z.length;E++){var a=F[E];0<E&&(u+=F[E-1]._j+F[E-1]._h+F[E-1]._b),u+=a._g+a._f+a._a+a._d;for(var zs=z[E].x1();zs.i1();zs.i2()){var Gs=zs.i9();this._d.s3(Gs,this._d.gi(Gs),this._d.gh(Gs)+u)}a._c+=u,a._i+=u}for(var bs=0;bs<z.length;bs++)for(var Rs=z[bs],Es=Rs.x1();Es.i1();Es.i2()){var as=Es.i9();this._n.i1(as)&&Rs.av(Es)}for(var x=this,xs=function(z,G){return x.a3(z)?x.a3(G)&&x._d.gi(z)>=x._d.gi(G)?-1:1:!x.a3(G)&&x._d.gi(z)>=x._d.gi(G)?1:-1},Bs=function(z,G){return x.a3(z)?!x.a3(G)||x._d.gi(z)>=x._d.gi(G)?1:-1:x.a3(G)||x._d.gi(z)>=x._d.gi(G)?-1:1},Os=0;Os<z.length;Os++)for(var B=F[Os],Ys=z[Os].x1();Ys.i1();Ys.i2()){var O=Ys.i9();if(!this._t.b2(O)){var Y=this._f.i1(O),l=Y._d,v=Y._a,ls=Y._b,Z=Y._h,vs=Y._f,I=Y._c,Zs=Y._g,Is=Y._e,Ts=0,Ws=0,Y=0,fs=0,hs=O.ao(),T=O.ak(),W=this._d.gi(O),f=this._d.gh(O),h=this._d.gj(O),g=this._d.g9(O),S=this._t.a3(O),r=rT._z;if(this._b.a2(O)&&(r=this._b.b2(O)),S){if(Ts=S._d.ay(),Ws=S._b.ay(),Y=S._e.ay(),fs=S._c.ay(),0<Ts)for(var gs=r._h.ay()+l.ay()+Ts+r._k.ay(),Ss=this.a7(g,gs),rs=f+this.a8(g,gs,Ss)+Ss*(r._h.ay()+this.a4(l)),Ns=S._d.c1();Ns.i1();Ns.i2()){var N=Ns.i8();this._d.u1(N),N.a2()===O?this.c2(N)||this._d.gx(N,new ST(W+h,rs)):(this.d1(N),this._d.gy(N,new ST(W+h,rs))),rs+=Ss,this._d.h1(N)}if(0<Ws)for(var gs=r._i.ay()+v.ay()+Ws+r._l.ay(),Ps=this.a7(g,gs),ts=f+this.a8(g,gs,Ps)+Ps*(r._i.ay()+this.a4(v)),is=S._b.c1();is.i1();is.i2()){var P=is.i8();this._d.u1(P),P.a2()===O?this.c2(P)||this._d.gx(P,new ST(W,ts)):this.d1(P)||this._d.gy(P,new ST(W,ts)),ts+=Ps,this._d.h1(P)}}if(0<l.ay()){l.a1(xs);for(var S=r._h.ay()+l.ay()+Ts+r._k.ay(),As=this.a7(g,S),t=f+this.a8(g,S,As)+As*r._h.ay(),ys=!0;!l.ar();){var i,A,y,H,Hs=l.x4();this.a3(Hs)?(ys&&(ys=!1,t+=As*Ts),y=Hs.ag(),A=(H=this._d.gd(y)).at(),H.ac(new ST(A.x,B.b())),this.c2(y)?(i=this._a.i1(y),H.ac(new ST(A.x,i.y+this._d.g6(O))),H.ac(new ST(i.x+this._d.g5(O),i.y+this._d.g6(O)))):(H.ac(new ST(A.x,t)),H.ac(new ST(W+h,t))),this._d.xr(y,O,y.a3()),this._d.m1(y,H)):(i=Hs.ae(),y=(A=this._d.gd(i)).au(),A.ae(new ST(y.x,B.a())),this.d1(i)?(H=this._g.i1(i),A.ae(new ST(y.x,H.y+this._d.g6(O))),A.ae(new ST(H.x+this._d.g5(O),H.y+this._d.g6(O)))):(A.ae(new ST(y.x,t)),A.ae(new ST(W+h,t))),this._d.xr(i,i.a2(),O),this._d.m1(i,A)),this._d.x4(Hs),t+=As}}if(0<v.ay()){v.a1(Bs);for(var S=r._i.ay()+v.ay()+Ws+r._l.ay(),ks=this.a7(g,S),ps=f+this.a8(g,S,ks)+ks*r._i.ay(),Us=!0;!v.ar();){var k,p,U,d,ds=v.x4();this.a3(ds)?(Us&&(Us=!1,ps+=ks*Ws),U=ds.ag(),p=(d=this._d.gd(U)).at(),d.ac(new ST(p.x,B.b())),this.c2(U)?(k=this._a.i1(U),d.ac(new ST(p.x,k.y+this._d.g6(O))),d.ac(new ST(k.x+this._d.g5(O),k.y+this._d.g6(O)))):(d.ac(new ST(p.x,ps)),d.ac(new ST(W,ps))),this._d.xr(U,O,U.a3()),this._d.m1(U,d)):(k=ds.ae(),U=(p=this._d.gd(k)).au(),p.ae(new ST(U.x,B.a())),this.d1(k)?(d=this._g.i1(k),p.ae(new ST(U.x,d.y+this._d.g6(O))),p.ae(new ST(d.x+this._d.g5(O),d.y+this._d.g6(O)))):(p.ae(new ST(U.x,ps)),p.ae(new ST(W,ps))),this._d.xr(k,k.a2(),O),this._d.m1(k,p)),this._d.x4(ds),ps+=ks}}f=r._g.ay()+r._d.ay()+T+Z.ay()+ls.ay()+Y+fs,h=this._d.gj(O),g=this.a7(h,f),S=this.a8(h,f,g),f=r._b.ay()+r._q.ay()+hs+I.ay()+vs.ay(),T=this.a7(h,f),Y=this.a8(h,f,T);if(0<Z.ay())for(var Ds=g,$s=m,Ls=this._d.gi(O)+S+Ds*(r._g.ay()+Z.ay()-1),Vs=this._d.gh(O),ms=B._c-B._g-Z.ay()*$s;!Z.ar();this._d.x4(_s)){var _s,qs=(_s=Z.x4()).ag(),D=this._d.gd(qs),Ks=D.at();D.ac(new ST(Ks.x,B.b())),D.ac(new ST(Ks.x,ms)),this.c2(qs)?(Ks=this._a.i1(qs),D.ac(new ST(Ks.x+this._d.g5(O),ms)),D.ac(new ST(Ks.x+this._d.g5(O),Ks.y+this._d.g6(O)))):(D.ac(new ST(Ls,ms)),D.ac(new ST(Ls,Vs)),Ls-=Ds),ms+=$s,this._d.xr(qs,O,qs.a3()),this._d.m1(qs,D)}if(0<ls.ay())for(var Cs=g,ss=m,Qs=this._d.gi(O)+this._d.gj(O)-S-Cs*r._d.ay(),ns=this._d.gh(O),cs=B._c-B._g-ss;!ls.ar();this._d.x4(os)){var os,Xs=(os=ls.x4()).ag(),$=this._d.gd(Xs),Js=$.at();$.ac(new ST(Js.x,B.b())),$.ac(new ST(Js.x,cs)),this.c2(Xs)?(Js=this._a.i1(Xs),$.ac(new ST(Js.x+this._d.g5(O),cs)),$.ac(new ST(Js.x+this._d.g5(O),Js.y+this._d.g6(O)))):($.ac(new ST(Qs,cs)),$.ac(new ST(Qs,ns)),Qs-=Cs),cs-=ss,this._d.xr(Xs,O,Xs.a3()),this._d.m1(Xs,$)}if(0<I.ay())for(var es=T,ws=m,js=this._d.gi(O)+Y+es*(r._b.ay()+I.ay()-1),Ms=this._d.gh(O)+this._d.g9(O),Fs=Ms+I.ay()*ws;!I.ar();this._d.x4(us)){var us,zT=(us=I.x4()).ae(),L=this._d.gd(zT),GT=L.au();L.ae(new ST(GT.x,B.a())),L.ae(new ST(GT.x,Fs)),this.d1(zT)?(GT=this._g.i1(zT),L.ae(new ST(GT.x+this._d.g5(O),Fs)),L.ae(new ST(GT.x+this._d.g5(O),GT.y+this._d.g6(O)))):(L.ae(new ST(js,Fs)),L.ae(new ST(js,Ms)),js-=es),Fs-=ws,this._d.xr(zT,zT.a2(),O),this._d.m1(zT,L)}if(0<vs.ay())for(var bT=T,RT=m,ET=this._d.gi(O)+this._d.gj(O)-Y-T*r._q.ay(),aT=this._d.gh(O)+this._d.g9(O),xT=aT+RT;!vs.ar();this._d.x4(BT)){var BT,OT=(BT=vs.x4()).ae(),V=this._d.gd(OT),YT=V.au();V.ae(new ST(YT.x,B.a())),V.ae(new ST(YT.x,xT)),this.d1(OT)?(YT=this._g.i1(OT),V.ae(new ST(YT.x+this._d.g5(O),xT)),V.ae(new ST(YT.x+this._d.g5(O),YT.y+this._d.g6(O)))):(V.ae(new ST(ET,xT)),V.ae(new ST(ET,aT)),ET-=bT),xT+=RT,this._d.xr(OT,OT.a2(),O),this._d.m1(OT,V)}for(;!Is.ar();){var lT=Is.c3(),vT=this._d.gl(lT);B.a()+12<vT.y&&this._d.g7(lT).i4(vT.x,B.a())}for(;!Zs.ar();){var ZT,IT=Zs.c3(),TT=this._d.gs(IT);B.b()-12>TT.y&&((ZT=this._d.gf(IT)).ac(new ST(TT.x,B.b())),this._d.s6(IT,ZT))}}}for(var WT=0;WT<z.length;WT++)for(var fT=z[WT],hT=fT._b;hT;hT=hT.a()){var gT=hT.d(),gT=this._t.a3(gT);gT&&gT._a!=NT&&(this._d.x4(gT._a),fT.aw(hT.b()))}return this._d.xi(this._f),this._d.xj(this._a),this._d.xj(this._g),z},c3:function(z){return this.a3(z)?this.b1(z.ag()):this.a2(z.ae())},b1:function(z){return this._h?this._h.i1(z):rT.s},a2:function(z){return this._l?this._l.i1(z):rT.u},c2:function(z){return!!z&&((z=this.b1(z))!=NT&&z.a())},d1:function(z){return!!z&&((z=this.a2(z))!=NT&&z.a())},a3:function(z){return 1===z.ao()},b2:function(z){return 1===z.ak()},a4:function(z){for(var G=0,b=z._b;b;b=b.a())this.b2(b.d())&&G++;return G},d2:function(z){for(var G=this._k,b=w.d(z.length+1),R=0;R<z.length;R++){var E=z[R],a=new iT;(b[R]=a)._c=P,a._i=Ms;for(var x=E.x1();x.i1();x.i2()){var B=x.i9(),B=this._d.gb(B);a._c=V(a._c,B.i2()),a._i=L(a._i,B.i2()+B.i4())}}this._b.a5(z,b);for(var O=0;O<z.length;O++)for(var Y=b[O],l=z[O].x1();l.i1();l.i2()){var v=l.i9(),v=this._f.i1(v);v&&(Y._h=L(Y._h,L(v._f.ay()*G,v._c.ay()*G)),Y._f=L(Y._f,L(v._b.ay()*G,v._h.ay()*G)))}return b}}),rT.s=B.j(2),rT.u=B.j(1),rT._z=new fT,z(Y,G,{ib:function(z){this._af=z},ia:function(z,G,b){this.a6(z,G,b),this.b2(!1);var R=this.g();if(this.o()&&0<R){for(var E=this.r(),a=0;a<20&&0<R&&this.o();a++){this.b2(!0);var x=this.g();x<R&&(this.a7(E),R=x)}this.b3(E),this.b1()}return this.c()},a6:function(z,G,b){this._b=(new Date).getTime(),this._ac=z,this._ah=G;var R=this;this._p=function(z,G){z=R._n[z.al()]-R._n[G.al()];return 0<z?1:0<=z?0:-1},this._ad=w.d(b);for(var E=0;E<this._ad.length;E++)this._ad[E]=new tT;this._ab=w.a(this._ac.x0()),this._f=w.d(this._ac.x0()),this._n=w.a(this._ac.x0()+1);var x=this._ab;this._o=function(z,G){if(!z&&G)return 1;if(z&&!G)return-1;if(!z&&!G)return 0;var b,R=z._h,E=z.a2(),a=G.a2(),E=x[E.al()]-x[a.al()];return 0==E?0==(a=Y.b(B.h(R,z),R.gn(z))-Y.b(B.h(R,G),R.gn(G)))?0==(b=x[z.a3().al()]-x[G.a3().al()])?Y.a(B.i(R,z),R.gk(z))-Y.a(B.i(R,G),R.gk(G)):b:a:E},this._l=function(z,G){if(!z&&G)return 1;if(z&&!G)return-1;if(!z&&!G)return 0;var b,R=z._h,E=z.a3(),a=G.a3(),E=x[E.al()]-x[a.al()];return 0==E?0==(a=Y.a(B.i(R,z),R.gk(z))-Y.a(B.i(R,G),R.gk(G)))?0==(b=x[z.a2().al()]-x[G.a2().al()])?Y.b(B.h(R,z),R.gn(z))-Y.b(B.h(R,G),R.gn(G)):b:a:E},this._z=function(z,G){if(!z&&G)return 1;if(z&&!G)return-1;if(!z&&!G)return 0;var b=z._h;return Y.b(B.h(b,z),b.gn(z))-Y.b(B.h(b,G),b.gn(G))},this._e=function(z,G){if(!z&&G)return 1;if(z&&!G)return-1;if(!z&&!G)return 0;var b=z._h;return Y.a(B.i(b,z),b.gk(z))-Y.a(B.i(b,G),b.gk(G))},this._ac.x2(this._e,this._z)},c:function(){this._ah=NT,this._aa=NT,this._f=NT,this._n=NT,this._p=NT,this._o=NT,this._l=NT,this._ac=NT;var z=this._ad;return this._ad=NT,z},o:function(){return(new Date).getTime()-this._b<=this._af},m:function(){for(var b=this,z=function(z,G){return p(b._n[z.a3().al()])-p(b._n[G.a3().al()])},G=this._ac.x9();G.i1();G.i2()){for(var R=G.i9().aw();R.i1();R.i2())this._n[R.i9().al()]=Rs.j();G.i9().av(z)}},b2:function(z){for(var G=0;G<this._ad.length;G++)this._ad[G].af();if(z){this.m();for(var b=0,R=this._ab.length;b<R;b++)this._ab[b]=0;this._ac.x2(NT,this._z)}var E=this._ac.xm();this._ah.i7(E,0);for(var a=this._ac.x9();a.i1();a.i2())0===a.i9().ak()&&a.i9()!==E&&this._ac.xo(E,a.i9());z=new s(this);z.a6(!0),z.a9(this._ac,E),this._ad[0].at(),this._ac.x4(E),this.d()},a1:function(){this._ac.x2(this._o,this._l);for(var z=0,G=1;G<this._ad.length;G++)z+=this.a2(this._ad[G-1],this._ad[G]);return z},a2:function(z,G){var b=z.ah(),R=G.ah(),E=new m,a=new m,x=0;for(this._aa=w.d(this._ac.x0());b.i1()&&R.i1();R.i2())x=(x+=this.a8(b.i6(),E,a,!0))+this.a8(R.i6(),a,E,!1),b.i2();for(;b.i1();b.i2())x+=this.a8(b.i6(),E,a,!0);for(;R.i1();R.i2())x+=this.a8(R.i6(),a,E,!1);return x},a8:function(z,G,b,R){var E=0,a=0,x=0;if(this._aa[z.al()])for(var B=this._aa[z.al()].a(),O=G._b;O!==B;O=O.a())O._c===z?(E++,x+=a,G.aw(O)):a++;var Y=E*b.ay()+x;if(R)for(var l=z.ag();l;l=l.a8()){var v=l.a3();this._ab[v.al()]>=this._ab[z.al()]&&(this._aa[v.al()]=b.ae(v))}else for(var Z=z.ae();Z;Z=Z.a7()){var I=Z.a2();this._ab[I.al()]>this._ab[z.al()]&&(this._aa[I.al()]=b.ae(I))}return Y},g:function(){for(var z=this.r(),G=this.a1(),b=0;b<3&&this.o()&&0<G;){var R=this.k();R<G?(this.a7(z),G=R):b++,0}if(this.b3(z),this.b1(),0<G){for(var E=1;1===E&&0<G;0){this.e(),this.i();var a=this.a1();a<G?(E=1,this.a7(z)):E=-1,G=a}this.b3(z),this.b1()}return G},e:function(){for(var z=this.l(),G=this.r(),b=w.d(this._ac.x0()),R=this._ad.length-1;0<=R;R--)for(var E=this._ad[R].ah();E.i1();E.i2()){var a=E.i6();if(1===a.ak()&&1===a.ao()){var x=z.i1(a.ag());if(x&&!b[x.al()])for(var a=this.a4(a,x),B=b[x.al()]=w.d(a+1),O=B.length-1;0<=O;O--)B[O]=new m}}for(var Y=0;Y<this._ad.length;Y++)for(var l=this._ad[Y].ah();l.i1();l.i2()){var v=l.i6();if(1===v.ak()&&1===v.ao()){var Z,I=z.i1(v.ag());I&&(Z=I.al(),I=this.a4(v,I)-1,b[Z][I].ae(v.ae()))}else for(var T=v.ae();T;T=T.a7()){var W=z.i1(T);W&&b[W.al()][this.a4(v,W)-1].ae(T)}}for(var f=this._ac.x9();f.i1();f.i2()){var h=f.i9();if(b[h.al()])for(var g=h.ag();g;g=g.a8()){var S=z.i1(g);if(S)for(var r=b[S.al()];0<r[0].ay();){for(var N=0;;){var P,t=(P=r[N].am()).a3();if(1!==t.ak()||1!==t.ao())break;N++}for(var i=r[N].at().a3(),A=(N--,i=P.a2(),(P=r[N].at()).a3());0<=N;)G[i.al()]!==G[A.al()]&&(this._ab[i.al()]=G[A.al()]),i=i.ae().a2(),0<=--N&&(A=r[N].at().a3())}}}this.b1(),this._ac.xj(z)},i:function(){for(var z=this.f(),G=this.r(),b=w.d(this._ac.x0()),R=0;R<this._ad.length;R++)for(var E=this._ad[R].ah();E.i1();E.i2()){var a=E.i6();if(1===a.ak()&&1===a.ao()){var x=z.i1(a.ae());if(x&&!b[x.al()])for(var a=this.a4(x,a),B=b[x.al()]=w.d(a+1),O=B.length-1;0<=O;O--)B[O]=new m}}for(var Y=this._ad.length-1;0<=Y;Y--)for(var l=this._ad[Y].ah();l.i1();l.i2()){var v=l.i6();if(1===v.ak()&&1===v.ao()){var Z=z.i1(v.ae());Z&&b[Z.al()][this.a4(Z,v)-1].ae(v.ag())}else for(var I=v.ag();I;I=I.a8()){var T=z.i1(I);T&&b[T.al()][this.a4(T,v)-1].ae(I)}}for(var W=this._ac.x9();W.i1();W.i2()){var f=W.i9();if(b[f.al()])for(var h=f.ae();h;h=h.a7()){var g=z.i1(h);if(g)for(var S=b[g.al()];0<S[0].ay();){for(var r=0;;){var N,P=(N=S[r].am()).a2();if(1!==P.ak()||1!==P.ao())break;r++}for(var t=S[r].at().a2(),i=(r--,t=N.a3(),(N=S[r].at()).a2());0<=r;)G[t.al()]!==G[i.al()]&&(this._ab[t.al()]=G[i.al()]),t=t.ag().a3(),0<=--r&&(i=S[r].at().a2())}}}this.b1(),this._ac.xj(z)},a4:function(z,G){return this._ah.i2(z)-this._ah.i2(G)},l:function(){for(var z=W.a6(w.d(this._ac.xg())),G=this._ac.x9();G.i1();G.i2()){var b=G.i9();if(1<b.ao()){for(var R=0,E=b.ag();E;E=E.a8()){var a=E.a3();1===a.ak()&&1===a.ao()&&R++}if(1<R)for(var x=b.ag();x;x=x.a8()){var B=x,O=B.a3();if(1===O.ak()&&1===O.ao()){for(;1===O.ak()&&1===O.ao();O=B.a3())z.i8(B,b),B=O.ag();z.i8(B,b)}}}}return z},f:function(){for(var z=W.a6(w.d(this._ac.xg())),G=this._ac.x9();G.i1();G.i2()){var b=G.i9();if(1<b.ak()){for(var R=0,E=b.ae();E;E=E.a7()){var a=E.a2();1===a.ak()&&1===a.ao()&&R++}if(1<R)for(var x=b.ae();x;x=x.a7()){var B=x,O=B.a2();if(1===O.ak()&&1===O.ao()){for(;1===O.ak()&&1===O.ao();O=B.a2())z.i8(B,b),B=O.ae();z.i8(B,b)}}}}return z},k:function(){for(var z=1;z<this._ad.length;z++){for(var G=this._ad[z],b=G.ah();b.i1();b.i2()){var R=b.i6();this._n[R.al()]=this.a5(R,G.ay(),R.am(),this._ad[z-1].ay()),this._n[R.al()]+=this._ab[R.al()]/(3*this._ad[z-1].ay())}this.a3(G,this._p)}return this.a1()},a5:function(z,G,b,R){var E=0;if(0===b.i7())E=R*this._ab[z.al()]/G;else{for(;b.i1();b.i2()){var a=b.i8();a.a2()===z?E+=this._ab[a.a3().al()]:E+=this._ab[a.a2().al()]}E/=b.i7()}return E},a7:function(z){w.f(this._ab,z,z.length)},b3:function(z){w.f(z,this._ab,z.length)},r:function(){var z=w.a(this._ab.length);return this.a7(z),z},d:function(){for(var z=0;z<this._ad.length;z++)for(var G=0,b=this._ad[z].ah();b.i1();)this._ab[b.i6().al()]=G,b.i2(),G++},b1:function(){for(var z=0;z<this._ad.length;z++){for(var G=this._ad[z],b=G._b;b;b=b.a()){var R=b.d();this._f[this._ab[R.al()]]=R}for(var E=0,a=G._b;a;)a.c(this._f[E]),a=a.a(),E++}},a3:function(z,G){for(var b=z.ah(),R=0;R<z.ay();b.i2())this._f[R]=b.i6(),R++;w.s(this._f,z.ay(),G);for(var E=0,a=z._b;a;)a.c(this._f[E]),this._ab[this._f[E].al()]=E,a=a.a(),E++}}),Y.b=function(z,G){if(!z)return 0;var b=z.a()?D(G.x):0,G=z.a()?D(G.y):0;return z.e()?1e4-G:z.f()?-1e4+G:z.c()?-2e4-b:b},Y.a=function(z,G){if(!z)return 0;var b=z.a()?D(G.x):0,G=z.a()?D(G.y):0;return z.e()?1e4+G:z.f()?-1e4-G:z.d()?-2e4-b:b},function(){hT.superClass.constructor.call(this),this._i6=0,this._i3=2147483647,this._i0=60,this._iz=20,this._i2=20,this._i4=20,this.i5(!1),this._i7=new O,this._i1=new Y,this._i8=new ms}),gT=(z(hT,R,{j2:function(){return this._i2},i4:function(z){return!0},i3:function(z){this._i6=(new Date).getTime(),_.d(z,!1);for(var G=z.xk(),b=z.xk(),R=z.xl(),E=new PT,a=new rT(z,G,b,R),x=(a.a6(this.j2()),this._i8.i3(this._iz),this._i8.i6(this._i0),this._i8.i4(this._i2),this._i8.i5(this._i4),this._i8.i2(b),this._i7.i1(z,G,E)),B=E.c1();B.i1();B.i2()){var O=B.i8(),Y=(R.i7(O,!0),z.gn(O));z.gt(O,z.gk(O)),z.gz(O,Y)}this.a2(z,G,b);x=a.a9(x),x=this.j1(z,G,x),x=a.a5(x);x=a.b3(x),this._i8.i1(z,x,G),x=a.g2(x),a.e2(x),this.b(z,b),this.w(z),this.a1(z,E),a.e1(),z.xj(R),z.xi(b),z.xi(G)},j1:function(z,G,b){var R,E;return this._i1 instanceof Y&&(R=this._i1,E=(new Date).getTime()-this._i6,R.ib(this._i3-E)),this._i1.ia(z,G,b)},a1:function(z,G){for(var b=G.c1();b.i1();b.i2()){var R=b.i8(),E=z.gs(R),a=z.gl(R),x=(z.x3(R),z.gp(R));z.s5(R,x.a()),z.gy(R,E),z.gx(R,a)}},b:function(z,G){for(var b=z.x9();b.i1();b.i2()){var R=b.i9(),E=G.i1(R);if(E&&!z.xp(E)){for(var a=R.am().i8().a2();G.i1(a);a=R.am().i8().a2())R=a;z.u1(E);for(var x=R.ae(),B=new m;G.i1(x.a3());x=x.a3().ag()){var O=z.gs(x),Y=(B.aa(O),B.az(z.gf(x)),z.gl(x));Y.equals(O)||B.aa(Y)}var l=z.gs(x),v=(B.aa(l),B.az(z.gf(x)),z.gl(x));v.equals(l)||B.aa(v),z.m1(E,B)}}for(var Z=z.x9();Z.i1();Z.i2())G.i1(Z.i9())&&z.x4(Z.i9())},w:function(z){for(var G=z.xf();G.i1();G.i2()){var b=G.i8(),R=z.g2(b);if(0<R.i1()){var E=new e,a=z.gc(b).c(),x=a.i6(),B=(a.i2(),x.x),O=x.y;if(a.i1()){var Y=a.i6(),l=Y.x,v=Y.y;for(a.i2();a.i1();a.i2()){var Z=a.i6(),I=Z.x,T=Z.y;1<=Qs((B-I)*(v-T)/(O-T)+I-l)&&(E.add(Y),B=l,O=v),Y=Z,l=I,v=T}}E.size()<R.i1()&&z.s5(b,new S(E))}}},a2:function(z,G,b){var R=z.g8().c1();for(R.i5();R.i1();R.i3()){var E=R.i8().a2(),a=R.i8().a3(),x=G.i2(a)-G.i2(E);if(1<x){for(var B,O,Y=E;1<x;x--)B=z.xm(),z.s7(B,1,1),z.s4(B,Rs._A),O=z.xo(Y,B),Y===E&&z.gt(O,z.gn(R.i8())),G.i7(B,G.i2(Y)+1),b.z1(B,R.i8()),Y=B;O=z.xo(B,a),z.gz(O,z.gk(R.i8())),z.h1(R.i8())}}}}),function(z,G,b,R){this._i=20,this._j=z,this._g=G,this._a=b,this._h=R}),iT=(z(gT,G,{a1:function(z){this._i=z},b2:function(z){return!!this._e&&this._e.i4(z)},a3:function(z){return this._f?this._f.i1(z):NT},d:function(){this._j.xi(this._f),this._j.xi(this._e)}}),function(){this._c=0,this._i=0,this._g=0,this._j=0,this._f=0,this._h=0,this._d=0,this._e=0,this._a=0,this._b=0}),AT=(z(iT,G,{a:function(){return this._c-this._g-this._f-this._a},b:function(){return this._i+this._j+this._h+this._b}}),z(function(){this._d=new PT,this._b=new PT,this._c=new PT,this._e=new PT},G,{}),function(z,G,b,R,E,a,x,B){this._d=z,this._a=G,this._b=b,this._h=R,this._f=E,this._c=a,this._e=x,this._g=B}),w=(z(AT,G,{}),{a:function(z,G){for(var b=[],R=0;R<z;R++)b[R]=G||0;return b},b:function(z){for(var G=[],b=0;b<z;b++)G[b]=!1;return G},c:function(z,G){if(z instanceof ST)return z.x<G.x?-1:z.x>G.x?1:z.y<G.y?-1:z.y<=G.y?0:1;if(z instanceof I)return G.width>z.width?-1:G.width<z.width?1:G.height>z.height?-1:G.height>=z.height?0:1;if(z instanceof d)return z.x<G.x?-1:z.x>G.x?1:z.y<G.y?-1:z.y>G.y?1:G.width>z.width?-1:G.width<z.width?1:G.height>z.height?-1:G.height>=z.height?0:1;throw""},d:function(z){for(var G=[],b=0;b<z;b++)G[b]=NT;return G},e:function(z,G){for(var b=[],R=0;R<z;R++)b[R]=w.a(G);return b},f:function(z,G,b){for(var R=0;R<b;R++)G[R]=z[R]},s:function(z,G,b){var R=[];w.f(z,R,G),R.sort(b),w.f(R,z,G)},n:function(z,G){return z-G}});x.layout.AutoLayout=function(z,G){z instanceof x.DataModel?this.dm=z:this.gv=z,this.options=G||{}},z("ht.layout.AutoLayout",G,{_repulsion:1,_type:NT,_offsetX:NT,_offsetY:NT,_xf:0,_yf:0,_animate:!0,_frames:NT,_interval:NT,_duration:NT,_easing:NT,_cascadeGroup:!1,isCascadeGroup:function(){return this._cascadeGroup},setCascadeGroup:function(z){this._cascadeGroup=z},isAnimate:function(){return this._animate},setAnimate:function(z){this._animate=z},getFrames:function(){return this._frames},setFrames:function(z){this._frames=z},getInterval:function(){return this._interval},setInterval:function(z){this._interval=z},getDuration:function(){return this._duration},setDuration:function(z){this._duration=z},getEasing:function(){return this._easing},setEasing:function(z){this._easing=z},getRepulsion:function(){return this._repulsion},setRepulsion:function(z){this._repulsion=z},getOffsetX:function(){return this._offsetX},setOffsetX:function(z){this._offsetX=z},getOffsetY:function(){return this._offsetY},setOffsetY:function(z){this._offsetY=z},getNodeSize:function(z){var G,b,R=this.gv;return z.getChildrenRect&&(G=z.getChildrenRect())&&Ks.grow(G,15),(G=!G&&R&&R.getDataUIBounds?R.getDataUIBounds(z):G)||(G=z.getRect(),Ks.grow(G,15)),R=z.s("autolayout.gap"),b=z.s("autolayout.hgap"),z=z.s("autolayout.vgap"),z=R!==v||b!==v||z!==v?(b=(b||0)+(R||0),(z||0)+(R||0)):(b=(R=this.options.gap||0)+(this.options.hgap||0),R+(this.options.vgap||0)),b&&(G.x-=b,G.width=G.width+2*b),z&&(G.y-=z,G.height=G.height+2*z),G},isLayoutable:function(z){var G=this.gv;return!(G&&!G.isVisible(z))&&(!1!==z.s("layoutable")&&(z instanceof ss?!(!z.getSourceAgent()||!z.getTargetAgent()||z.isLooped()):z instanceof Cs&&(!z.getHost()&&(!G||G.isMovable(z)))))},getType:function(z){return this._type},getLayoutDatas:function(){var G=this,z=G.gv,b=G.dm,R=!1,E=new e,z=z?1<z.sm().size()?(R=!0,z.sm().getSelection()):z.dm().getDatas():1<b.sm().size()?(R=!0,b.sm().getSelection()):b.getDatas();return G._xf=G._yf=P,z.each(function(z){G.isLayoutable(z)&&(E.add(z),R&&z instanceof Cs&&((z=z.p()).x<G._xf&&(G._xf=z.x),z.y<G._yf&&(G._yf=z.y)))}),R||(G._xf=G._offsetX==NT?50:G._offsetX,G._yf=G._offsetY==NT?50:G._offsetY),E},getLayoutResult:function(z){var G={};return this.layoutImpl(z,NT,G),G},layout:function(z,G){return this.layoutImpl(z,G)},layoutImpl:function(z,G,b){this._type=z;var R=this,E={},a=i(z),x=R.getLayoutDatas(),B=new Ls(R,x);if(!a)return!1;var x=B.p(),O=new Us(R,x,z);try{a.i2(O)}catch(z){return B.r(),G&&G(),!1}var Y,l,v,Z,I=O._a;for(Y in I)l=I[Y],Z=O.g4(l),E[Y]={x:Z.x+R._xf,y:Z.y+R._yf};if(z===GT||z===bT||z===zT){var T,W=A(z),f=P,h=P,g=2*R._repulsion;for(Y in E)l=I[Y],v=E[Y],Z=W.tf(v),v.x=Z.x,v.y=Z.y,z===GT||z===bT?((T=Z.x-O.g9(l)/g)<f&&(f=T),(T=Z.y-O.gj(l)/g)<h&&(h=T)):((T=Z.x-O.gj(l)/g)<f&&(f=T),(T=Z.y-O.g9(l)/g)<h&&(h=T));for(Y in E)l=I[Y],(v=E[Y]).x=v.x-f+R._xf,v.y=v.y-h+R._yf}if(!b&&R._animate){var S=R.gv,r={};for(Y in E)r[Y]=I[Y].node.p();S&&(S._autoLayouting=1),Ks.startAnim({duration:R._duration,frames:R._frames,interval:R._interval,easing:R._easing,finishFunc:function(){B.r(),G&&G(),S&&(delete S._autoLayouting,S.onAutoLayoutEnded())},action:function(z){for(Y in E){var G=r[Y],b=E[Y];I[Y].node.p(G.x+(b.x-G.x)*z,G.y+(b.y-G.y)*z)}}})}else{for(Y in E)l=I[Y],v=E[Y],b?b[l.node.getId()]=v:l.node.p(v);B.r(),G&&G()}return!0}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);