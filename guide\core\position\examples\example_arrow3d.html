
<!DOCTYPE html>
<html>
    <head>
        <title>Arrow 3D</title>
        <meta charset="UTF-8">
        <style>
            html, body {
                padding: 0;
                margin: 0;
            }
            .main {
                background: black;
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
            .formpane {
                top: 10px;
                right: 10px;
                background: rgba(230, 230, 230, 0.5);
            }
        </style>

        <script src="../../../../lib/core/ht.js"></script>
        <script src="../../../../lib/plugin/ht-form.js"></script>
        <script src="../../../../lib/plugin/ht-modeling.js"></script>
        <script>


            ht.Default.setImage('arrow', {
                width: 100,
                height: 50,
                comps: [
                    {
                        type: 'shape',
                        points: [2, 25, 30, 25],
                        borderWidth: 4,
                        borderColor: 'red'
                    },
                    {
                        type: 'shape',
                        points: [15, 5, 30, 25, 15, 45, 50, 25, 15, 5],
                        background: 'yellow'
                    }
                ]
            });

            ht.Default.setShape3dModel('arrow', [
                {
                    shape3d: ht.Default.createCylinderModel(8, 0, 8, false, false, true, true),
                    r3: [0, 0, Math.PI / 2],
                    t3: [-75, 0, 0],
                    s3: [10, 50, 10],
                    color: 'red'
                },
                {
                    shape3d: ht.Default.createConeModel(5, 0, 5, false, false, true),
                    r3: [0, 0, -Math.PI / 2],
                    t3: [-25, 0, 0],
                    s3: [25, 50, 25],
                    color: 'yellow'
                }
            ]);



            function init(){
                dataModel = new ht.DataModel();
                g3d = new ht.graph3d.Graph3dView(dataModel);
                g3d.setGridVisible(true);

                view = g3d.getView();
                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function (e) {
                    g3d.iv();
                }, false);

                createFormPane();
                formPane.getView().className = 'formpane';
                document.body.appendChild(formPane.getView());

                createModel();

                g3d.walk(400, true);
                update();
            }

            function createModel(){
                node = new ht.Node();
                node.p3(-200, 100, -60);
                node.s3(150, 50, 80);
                node.s({
                    'shape': 'rect',
                    'all.transparent': true,
                    'all.reverse.cull': true,
                    'all.opacity': 0.7,
                    'wf.visible': true,
                    'wf.color': 'yellow'
                });

                n1 = new ht.Node();
                n2 = new ht.Node();
                n1.p3(0, 100, -60);
                n2.p3(300, 100, -60);
                n1.s3(16, 16, 16);
                n2.s3(16, 16, 16);

                edge1 = new ht.Edge(n1, n2);
                edge1.s({
                    'edge.gap': 80,
                    'edge.center': true
                });

                edge2 = new ht.Edge(n1, n2);
                edge2.s({
                    'edge.gap': 80,
                    'edge.center': true
                });

                n3 = new ht.Node();
                n4 = new ht.Node();
                n3.p3(-200, 50, 120);
                n4.p3(200, 50, 120);
                n3.s3(16, 16, 16);
                n4.s3(16, 16, 16);

                edge3 = new ht.Edge(n3, n4);
                edge3.s({
                    'edge.center': false,
                    'edge.offset': 20,
                    'edge.type': 'points',
                    'edge.points': [{x: -100, y: 160, e: 50}, {x: 100, y: 160, e: 50}],
                    'edge.width': 5,
                    'shape3d': 'cylinder',
                    'shape3d.top.cap': 'round',
                    'shape3d.bottom.cap': 'round'
                });

                polyline = new ht.Polyline();
                polyline.setPoints([
                    {x: -200, y: 320},
                    {x: -100, y: 260},
                    {x: 100, y: 260},
                    {x: 200, y: 320}
                ]);
                polyline.setThickness(5);
                polyline.s({
                    'shape3d': 'cylinder',
                    'shape3d.top.cap': 'round',
                    'shape3d.bottom.cap': 'round'
                });

                dataModel.add(node);
                dataModel.add(n1);
                dataModel.add(n2);
                dataModel.add(n3);
                dataModel.add(n4);
                dataModel.add(edge1);
                dataModel.add(edge2);
                dataModel.add(edge3);
                dataModel.add(polyline);
            }

            function update(){
                dataModel.each(function(data){
                    if(data === n1 || data === n2 || data === n3 || data === n4){
                        return;
                    }
                    data.addStyleIcon('arrow', {
                        names: ['arrow'],
                        shape3d: formPane.v('3d') ? 'arrow' : null,
                        face: formPane.v('face'),
                        position: formPane.v('position'),
                        t3: [formPane.v('tx'), formPane.v('ty'), formPane.v('tz')],
                        r3: [formPane.v('rx'), formPane.v('ry'), formPane.v('rz')],
                        rotationMode: formPane.v('rotationMode'),
                        autorotate: formPane.v('autorotate'),
                        textureScale: formPane.v('texScale')
                    });
                });
            }

            function createFormPane(){
                formPane = new ht.widget.FormPane();
                formPane.setWidth(230);
                formPane.setHeight(322);
                formPane.setLabelAlign('right');
                formPane.setLabelColor('yellow');

                formPane.addRow([
                    '3d',
                    {
                        id: '3d',
                        checkBox: {
                            value: 'true',
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'face',
                    {
                        id: 'face',
                        comboBox: {
                            value: 'front',
                            values: ['front', 'back', 'left', 'right', 'top', 'bottom', 'center'],
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'position',
                    {
                        id: 'position',
                        slider: {
                            min: 1,
                            max: 55,
                            value: 17,
                            step: 1,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'tx',
                    {
                        id: 'tx',
                        slider: {
                            min: -100,
                            max: 100,
                            value: 0,
                            step: 1,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'ty',
                    {
                        id: 'ty',
                        slider: {
                            min: -100,
                            max: 100,
                            value: 0,
                            step: 1,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'tz',
                    {
                        id: 'tz',
                        slider: {
                            min: -100,
                            max: 100,
                            value: 0,
                            step: 1,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'rx',
                    {
                        id: 'rx',
                        slider: {
                            min: -Math.PI,
                            max: Math.PI,
                            value: 0,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'ry',
                    {
                        id: 'ry',
                        slider: {
                            min: -Math.PI,
                            max: Math.PI,
                            value: 0,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'rz',
                    {
                        id: 'rz',
                        slider: {
                            min: -Math.PI,
                            max: Math.PI,
                            value: 0,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'rm',
                    {
                        id: 'rotationMode',
                        comboBox: {
                            value: 'xzy',
                            values: ['xyz', 'xzy', 'yxz', 'yzx', 'zxy', 'zyx'],
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'autorotate',
                    {
                        id: 'autorotate',
                        comboBox: {
                            value: false,
                            values: [false, 'y', true],
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
                formPane.addRow([
                    'texScale',
                    {
                        id: 'texScale',
                        slider: {
                            min: 1,
                            max: 8,
                            value: 5,
                            onValueChanged: function(){
                                update();
                            }
                        }
                    }
                ], [0.1, 0.25]);
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
