<!DOCTYPE html>
<html>
    <head>
        <title>Cull</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script>
        <script src="../../../../lib/plugin/ht-modeling.js"></script>
        <script>       
            
            ht.Default.setImage('nodeImage', {
                width: 40,
                height: 28,
                comps: [
                    {
                        type: 'rect',
                        rect: [0, 0, 40, 28],                        
                        background: {func: 'attr@color'}
                    },
                    {
                        type: 'border',
                        rect: [0, 0, 40, 28],   
                        color: 'red',
                        visible: {func: function(data, view){
                            return view.isSelected(data);
                        }}
                    },
                    {
                        type: 'text',
                        text: {func: '<EMAIL>'},
                        rect: [0, 0, 40, 28],
                        font: 'bold 12px Arial',
                        align: 'center'
                    }      
                ]
            });            
            
            function init(){   
                dataModel = new ht.DataModel();
                g3d = new ht.graph3d.Graph3dView(dataModel);   
                g2d = new ht.graph.GraphView(dataModel);                   
                mainSplit = new ht.widget.SplitView(g3d, g2d, 'v', 0.5);   
                
                view = mainSplit.getView();  
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    mainSplit.invalidate();
                }, false);                         
              
                g3d.setGridVisible(true); 
                g3d.setEye(0, 300, 400);                                 
                
                wall = new ht.CSGShape();                
                wall.setTall(80);
                wall.setElevation(40);  
                wall.setThickness(30);
                wall.setPoints([
                    {x: -200, y: -100},
                    {x: 200, y: -100},
                    {x: 300, y: 100},
                    {x: -300, y: 100}
                ]); 
                wall.setClosePath(true);                 
                wall.s({
                    'shape.background': null,
                    'shape.border.width': 30,
                    'csg.color': 'yellow'
                }); 
                dataModel.add(wall); 
       
                createDoor('yellow', 0, 0.5);
                createDoor('yellow', 1, 0.5);
                createDoor('yellow', 2, 0.5);
                createDoor('yellow', 3, 0.5);
                
                createNode('#00C149', 0, 40);
                createNode('#00C149', 1, 40);
                createNode('#00C149', 2, 40);
                createNode('#00C149', 3, 40);
                
                createNode('pink', 0, 40, true);
                createNode('pink', 1, 40, true);
                createNode('pink', 2, 40, true);
                createNode('pink', 3, 40, true);
                
                createNode('#A5BAD7', 2, 0.25, true, -20);
                createNode('#A5BAD7', 2, 0.25, false, -20);
                
                createNode('orange', 2, 0.37, true, 0.5, 0.7).setTall(80.001);
                createNode('orange', 2, 0.37, false, 0.5, 0.7).setTall(80.001);
                   
                g2d.translate(420, 150);   
                g2d.setEditable(true);
                g2d.setRotationEditableFunc(function(data){
                    return false;
                });
                g2d.setRectEditableFunc(function(data){
                    return data instanceof ht.Shape;
                });
            }
                
            function createDoor(color, index, offset){
                var door = createNode(color, index, offset);
                door.setWidth(45);
                door.setTall(60);
                door.setElevation(30);
            }    
                
            function createNode(color, index, offset, opposite, gap, thickness){
                var node = new ht.CSGNode();
                node.s({
                    'shape': null,
                    'select.width': 0,
                    'attach.index': index,
                    'attach.offset': offset,
                    'attach.offset.relative': offset < 1,
                    'attach.offset.opposite': opposite,
                    'attach.thickness': thickness || 1.001,
                    'attach.gap': gap,
                    '3d.visible': false
                });
                node.a({
                    'color': color
                });
                node.setImage('nodeImage');
                node.setHost(wall);
                node.setTall(40);
                node.setWidth(35);
                node.setElevation(40);
                dataModel.add(node);
                return node;
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
