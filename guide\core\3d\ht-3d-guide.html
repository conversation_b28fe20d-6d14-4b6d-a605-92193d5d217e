<!doctype html>
<html>
    <head>
        <title>HT for Web 3D Manual</title>
        <meta charset="UTF-8">    
        <meta name="viewport" content="user-scalable=yes, width=1024">
        <style type="text/css"> 
            h1, h2, h3, h4, h5, h6, p, blockquote {
                margin: 0;
                padding: 0;
            }
            body {
                font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif;
                font-size: 13px;
                line-height: 18px;
                color: #737373;
                background-color: white; 
                margin: 10px 13px 10px 13px;
            }
            table {
                margin: 10px 0 15px 0;
                border-collapse: collapse;
            }
            td,th {	
                border: 1px solid #ddd;
                padding: 3px 10px;
            }
            th {
                padding: 5px 10px;	
            }
            a {
                color: #0069d6;
            }
            a:hover {
                color: #0050a3;
                text-decoration: none;
            }
            a img {
                border: none;
            }
            p {
                margin-bottom: 9px;
            }
            h1, h2, h3, h4, h5, h6 {
                color: #404040;
                line-height: 36px;
            }
            h1 {
                margin-bottom: 18px;
                font-size: 30px;
            }
            h2 {
                font-size: 24px;
            }
            h3 {
                font-size: 18px;
            }
            h4 {
                font-size: 16px;
            }
            h5 {
                font-size: 14px;
            }
            h6 {
                font-size: 13px;
            }
            hr {
                margin: 0 0 19px;
                border: 0;
                border-bottom: 1px solid #ccc;
            }
            blockquote {
                padding: 13px 13px 21px 15px;
                margin-bottom: 18px;
                font-family:georgia,serif;
                font-style: italic;
            }
            blockquote:before {
                content:"\201C";
                font-size:40px;
                margin-left:-10px;
                font-family:georgia,serif;
                color:#eee;
            }
            blockquote p {
                font-size: 14px;
                font-weight: 300;
                line-height: 18px;
                margin-bottom: 0;
                font-style: italic;
            }
            code, pre {
                font-family: Monaco, Andale Mono, Courier New, monospace;
            }
            code {
                background-color: #fee9cc;
                color: rgba(0, 0, 0, 0.75);
                padding: 1px 3px;
                font-size: 12px;
                -webkit-border-radius: 3px;
                -moz-border-radius: 3px;
                border-radius: 3px;
            }
            pre {
                display: block;
                padding: 14px;
                margin: 0 0 18px;
                line-height: 16px;
                font-size: 11px;
                border: 1px solid #d9d9d9;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            pre code {
                background-color: #fff;
                color:#737373;
                font-size: 11px;
                padding: 0;
            }
            sup {
                font-size: 0.83em;
                vertical-align: super;
                line-height: 0;
            }
            * {
                -webkit-print-color-adjust: exact;
            }
            @media screen and (min-width: 914px) {
                body {
                    width: 854px;
                    margin:10px auto;
                }
            }
            @media print {
                body,code,pre code,h1,h2,h3,h4,h5,h6 {
                    color: black;
                }
                table, pre {
                    page-break-inside: avoid;
                }
            }                        
            iframe{
                width: 100%;
                border: 1px solid #34495E;
                margin: 0;                
            }            
            .logo{
                vertical-align: middle;
            }

        </style>        
        <script>
            function init() {
                var logoSrc = 'data:image/png;base64,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',
                    logos = document.querySelectorAll('.logo'),
                    i = 0;
                for (; i < logos.length; i++) {
                    logos[i].src = logoSrc;
                }
                
                var iframes = document.querySelectorAll('iframe'),
                    func = function (){};
                for (i=0; i < iframes.length; i++) {
                    var iframe = iframes[i];
                    
                    // a small hack to make it work on android
                    iframe.ontouchstart = func;
                    
                    var div = document.createElement('div');
                    div.innerHTML = '<a href="' + iframe.src + '" target="_blank">Run→</a>';
                    iframe.parentNode.insertBefore(div, iframe);
                }   
            }
        </script>    
    </head>
    <body onload="init();">
        <a href="http://www.hightopo.com"><img class="logo"></a>HT for Web 3D Manual
        <hr style="margin: 1px 0 20px 0">
<p>Index</p>

<ul><li><a href="#ref_overview">Overview</a><ul><li><a href="#ref_engine">Underlying Engine</a></li><li><a href="#ref_tool">Model Designer</a></li><li><a href="#ref_support">Browser Support</a></li></ul></li><li><a href="#ref_basic">3D Basics</a><ul><li><a href="#ref_coordinate">3D Coordinate</a><ul><li><a href="#ref_2drelation">2D Coordinate Relations</a></li><li><a href="#ref_3dcoordinate">3D Coordinate Function</a></li><li><a href="#ref_3drotation">3D Rotation Function</a></li><li><a href="#ref_3dabbreviation">3D Abbreviation Function</a></li></ul></li><li><a href="#ref_projection">3D Projection</a><ul><li><a href="#ref_perspective">Perspective Projection</a></li><li><a href="#ref_orthographic">Orthographic Projection</a></li></ul></li></ul></li><li><a href="#ref_3dmodel">3D Model</a><ul><li><a href="#ref_cube">Hexahedron</a></li><li><a href="#ref_shape3d">Shape 3d</a></li><li><a href="#ref_text">3D Text</a></li><li><a href="#ref_edge">ht.Edge</a></li><li><a href="#ref_shape">ht.Shape</a></li></ul></li><li><a href="#ref_other">Other Parameters</a><ul><li><a href="#ref_whole">The whole</a></li><li><a href="#ref_label">Label</a></li><li><a href="#ref_note">Note</a></li><li><a href="#ref_icons">Icons</a></li><li><a href="#ref_host">Attach</a></li></ul></li><li><a href="#ref_3dview">3D components</a><ul><li><a href="#ref_viewbasic">Component Basis</a></li><li><a href="#ref_light">Light Setting</a></li><li><a href="#ref_gridaxis">Grid Axis</a></li><li><a href="#ref_3dinteraction">3D Interaction</a><ul><li><a href="#ref_interactionmode">Interaction Mode</a><ul><li><a href="#ref_defaultmode">Default Mode</a></li><li><a href="#ref_firstpersonmode">First Person Mode</a></li></ul></li><li><a href="#ref_interactionfunc">Interaction Function</a></li><li><a href="#ref_switch">Interactive Switch</a></li><li><a href="#ref_keyboard">Keyboard Operation</a></li><li><a href="#ref_edit">Data Edit</a></li><li><a href="#ref_interactionlistener">Interactive Monitoring</a></li><li><a href="#ref_collision">Collision Detection</a></li></ul></li><li><a href="#ref_selection">Selected State</a></li><li><a href="#ref_filter">Filter</a><ul><li><a href="#ref_selectable">Select the Filter</a></li><li><a href="#ref_visible">Visible Filter</a></li><li><a href="#ref_movable">Movable Filter</a></li><li><a href="#ref_rotatable">Rotatable Filter</a></li><li><a href="#ref_scalable">Scalable Filter</a></li></ul></li><li><a href="#ref_export">Export</a></li></ul></li></ul>

<hr/>

<div id="ref_overview"></div>

<h2>Overview</h2>

<div id="ref_engine"></div>

<h3>Underlying Engine</h3>

<p><code>HT</code> provides graphic packages <code>ht.graph3d.Graph3dView</code> of 3D technology based on <a href="http://www.khronos.org/webgl/">WebGL</a>. As a graphic interface based on <a href="http://www.khronos.org/opengles/">OpenGL ES 2.0</a>, <code>WebGL</code> is a underlying graphic <code>API</code> interface, which is difficult for secondary development. Like other <code>HT</code> packages, the <code>Graph3dView</code> package of <code>HT</code> drives graphic display with the unified <code>HT</code> <code>DataModel</code>. It lowers the technological threshold of <code>3D</code> graphics development through the encapsulation of <code>WebGL</code> technology. If familiar with the <code>HT</code> data model, an average programmer will need only about <code>1</code> hour to learn before he can start <code>3D</code> graphics development.</p>

<div id="ref_tool"></div>

<h3>Model Designer</h3>

<p>At the same time, <code>HT</code> provides a powerful <code>3D</code> graphic model designer based totally on <code>HTML5</code> technology. Users can set up various visual <code>3D</code> scenes quickly without coding. The <code>HT</code> <code>3D</code> development mode breaks traditions and frees most applications from modeling by professional <code>3D</code> designers proficient in <a href="http://www.autodesk.com/products/autodesk-3ds-max/overview">3ds Max</a> or <a href="http://www.autodesk.com/products/autodesk-maya/overview">Maya</a>, or graphic rendering with integrated engines like <a href="http://unity3d.com/">Unity3d</a>. <code>HT</code> provides a one-stop plan from modeling to rendering, including <code>2D</code> package display and data integration.</p>

<div id="ref_support"></div>

<h3>Browser support</h3>

<p><code>WebGL</code> technology has been supported by most of the latest browsers, <a href="http://caniuse.com/webgl">caniuse.com/webgl</a> website keeps the latest desktop and mobile browsers supportive for <code>WebGL</code>.
Use <a href="http://get.webgl.org/">get.webgl.org</a> to detect whether your browser supports <code>WebGL</code>.</p>

<p>Currently, the PC or mobile versions of <code>Chrome</code>, <code>Firefox</code>, <code>Safari</code> and <code>Opera</code> have already been able to support <code>WebGL</code>, for <code>iOS</code> systems users, please update to <code>iOS8</code> and above, <code>IE</code> browsers need to be <code>IE11</code> and above to support <code>WebGL</code>, regardless of the type of the browser you choose, we recommend using the latest version.</p>

<p>If you have to use old IE browsers like <code>IE6</code>, <code>IE7</code>, <code>IE8</code> and others, or because of <code>HT for Web 3D</code>, and the old machine can not be upgraded to <code>IE11</code>, you can consider the installation
<a href="http://www.chromium.org/developers/how-tos/chrome-frame-getting-started/chrome-frame-faq#TOC-For-Developers">Google Chrome Frame</a> plug-in, in the page embedded in the following <code>Tag</code> code snippet, the page will be used to render.</p>

<p> &lt;meta http-equiv=&quot;X-UA-Compatible&quot; content=&quot;chrome=1&quot;&gt;</p>

<p>Users of <code>Google Chrome Frame</code> also need to note:
<em> <code>Google Chrome Frame</code> does not support opening web pages directly from <code>local file</code>, you have to deploy the page to the <code>Web</code> server release mode.
</em> <code>Google Chrome Frame</code> does not support 64-bit browsers: <a href="http://www.chromium.org/developers/how-tos/chrome-frame-getting-started/chrome-frame-faq#TOC-Does-Google-Chrome-Frame-work-on-64-bit-IE-">Currently, 64-bit versions of IE are not supported. It&#39;s worth pointing out that 32-bit IE is the default on 64-bit Windows 7.</a>
* <code>Google Chrome Frame</code> does not support <code>iframe</code>:  <a href="http://stackoverflow.com/questions/2465322/opening-an-iframe-for-ie-while-using-chrome-frame">At this point ChromeFrame only supports the meta tag detection on top level URLs.</a>. You can use the embedded <code>OBJECT</code> element as a <a href="http://src.chromium.org/viewvc/chrome/trunk/src/chrome_frame/host.html">solution</a> to overcome the problem that <code>Google Chrome Frame</code> does not support <code>iframe</code>.</p>

<pre><code>&lt;OBJECT ID=&quot;ChromeFrame&quot; WIDTH=500 HEIGHT=500 CODEBASE=&quot;http://yourdomain/yourproject/&quot;
        CLASSID=&quot;CLSID:E0A900DF-9611-4446-86BD-4B1D47E7DB2A&quot;&gt;
    &lt;PARAM NAME=&quot;src&quot; VALUE=&quot;http://yourdomain/yourproject/&quot;&gt;
    &lt;embed ID=&quot;ChromeFrameplug-in&quot; WIDTH=500 HEIGHT=500 NAME=&quot;ChromeFrame&quot;
        SRC=&quot;http://yourdomain/yourproject/&quot; TYPE=&quot;application/chromeframe&quot;&gt;
    &lt;/embed&gt;
&lt;/OBJECT&gt;</code></pre>

<p><code>Google Chrome Frame</code> will no longer support or update from January <code>2014</code>, the current <code>Google Chrome Frame</code> is the <code>31</code> version. This version has been able to provide the <code>Canvas</code> function that <code>HT</code> <code>2D</code> and <code>3D</code> required, so <code>HT</code> customers can use <code>Google Chrome Frame</code> to solve the old <code>IE</code> problem.
Other questions can be found on the <a href="http://www.chromium.org/developers/how-tos/chrome-frame-getting-started">Developer Guide</a>
 and <a href="http://www.chromium.org/developers/diagnostics/gcf_troubleshooting">Troubleshooting</a>
of <code>Google Chrome Frame</code>.</p>

<p><iframe src="examples/example_3droom.html" style="height:400px"></iframe></p>

<div id="ref_basic"></div>

<hr/>

<h2>3D Basics</h2>

<div id="ref_coordinate"></div>

<h3>3D Coordinate System</h3>

<p>The 3D coordinate system of <code>HT</code> consists of three axes: <code>x</code>, <code>y</code> and <code>z</code>, the <code>x</code>-axis points to the right, the <code>y</code>-axis to the above, and the <code>z</code>-axis outside the screen in their positive directions. The rotation of <code>HT</code> system follows the <a href="http://en.wikipedia.org/wiki/Right-hand_rule">Right Hand Spiral Rule</a>.</p>

<p><img src="data:image/png;base64,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"></p>

<div id="ref_2drelation"></div>

<h4>2D Coordinate Relations</h4>

<p>The <code>3D</code> coordinate system of <code>Graph3dView</code> is related to but different from the <code>2D</code> coordinate system of <code>GraphView</code>. The <code>x</code>-axis of <code>2D</code> coordinate system corresponds to the <code>x</code>-axis of <code>3D</code> coordinate system. The <code>y</code>-axis of <code>2D</code> coordinate system corresponds to <code>z</code>-axis of <code>3D</code> coordinate system. The <code>xy</code> plane of the <code>2D</code> coordinate system equals to the <code>xz</code> plane of the <code>3D</code> coordinate system.
For example, <code>Node#getPosition()</code> returns <code>{x: 100, y: 200}</code>, it means:</p>

<ul><li>In <code>2D</code> coordinate system, the position on the <code>x</code> axis is <code>100</code>, <code>y</code> axis <code>200</code></li><li>In <code>3D</code> coordinate system, the position on the <code>x</code> axis is <code>100</code>, <code>z</code> axis <code>200</code></li></ul>

<p>For example, <code>Node#getSize()</code> returns <code>{width: 300, height: 400}</code>, it means:</p>

<ul><li>In <code>2D</code> coordinate system, <code>x</code> axis length <code>300</code>, <code>y</code> axis length <code>400</code></li><li>In <code>3D</code> coordinate system, <code>x</code> axis length <code>300</code>, <code>z</code> axis length <code>400</code></li></ul>

<div id="ref_3dcoordinate"></div>

<h4>3D Coordinate Function</h4>

<p>The <code>y</code>-axis of the <code>3D</code> coordinate system is a new axis that is not associated with the <code>2D</code> coordinate system. The <code>getElevation()</code> and <code>setElevation(elevation)</code> functions are added to the <code>ht.Node</code> to control the <code>y</code>-axis position of the <code>3D</code> coordinate system where the <code>Node&#39;s</code> center is located. The <code>getTall()</code> and <code>setTall(tall)</code> functions are added to control the length of the <code>Node</code> on the <code>y</code> axis.
In order to avoid the confusion of <code>2D</code> and <code>3D</code> coordinate systems and for the convenience to set the size of the <code>3D</code> data position, <code>HT</code> adds the following new functions to the <code>ht.Node</code> data:</p>

<ul><li><code>setPosition3d(x, y, z)|setPosition3d([x, y, z])</code> You can set <code>x, y, z</code> three parameters, or an array of <code>[x, y, z]</code></li><li><code>getPosition3d()</code> Returns the <code>[x, y, z]</code> array value, that is <code>[getPosition().x, getElevation(), getPosition().y]</code></li><li><code>setSize3d(x, y, z)|setSize3d([x, y, z])</code> You can set <code>x, y, z</code> three parameters, or insert into <code>[x, y, z]</code> array</li><li><code>getSize3d()</code> Returns the <code>[x, y, z]</code> array value, that is, <code>[getWidth(), getTall(), getHeight()]</code></li></ul>

<div id="ref_3drotation"></div>

<h4>3D Rotation Function</h4>

<p><code>ht.Node</code> in the <code>2D</code> coordinate system is controlled by the <code>getRotation()</code> and <code>setRotation(rotation)</code> functions, which correspond to the negative rotation values along the <code>y</code> axis in the <code>3D</code> coordinate system. At the same time, the <code>3D</code> coordinate system adds new rotation variables two new <code>rotationX</code> and <code>rotationZ</code> along the <code>x</code>-axis and <code>z</code>-axis respectively, as well as the following new functions:</p>

<ul><li><code>setRotationY(y)</code> Sets the curvature along the y axis, which equals to <code>setRotation(-y)</code></li><li><code>getRotationY()</code> Gets the curvature along the y axis, which equals to <code>-getRotation()</code></li><li><code>setRotation3d(x, y, z)|setRotation3d([x, y, z])</code> Can be put in <code>x, y, z</code> three parameters, or an array of <code>[x, y, z]</code></li><li><code>getRotation3d()</code> Returns the <code>[x, y, z]</code> array value, that is, <code>[getRotationX(), -getRotation(), getRotationZ()]</code></li></ul>

<p>There is another very important parameter <code>rotationMode</code> for rotation. This parameter is set by <code>getRotationMode()|setRotationMode(&#39;xzy&#39;)</code>. The order of rotation will affect the final effect. This parameter is used to specify the order of rotation. The default value is <code>xzy</code>, and users can change the rotation sequence mode by setting the following six parameters:</p>

<ul><li><code>xyz</code>: First <code>x</code>-axis rotation, and then <code>y</code>-axis rotation, and finally <code>z</code>-axis rotation.</li><li><code>xzy</code>: First <code>x</code>-axis rotation, and then <code>z</code>-axis rotation, and finally <code>y</code>-axis rotation.</li><li><code>yxz</code>: First <code>y</code>-axis rotation, and then <code>x</code>-axis rotation, and finally <code>z</code>-axis rotation.</li><li><code>yzx</code>: First <code>y</code>-axis rotation, and then <code>z</code>-axis rotation, and finally the <code>x</code>-axis rotation.</li><li><code>zxy</code>: First <code>z</code>-axis rotation, and then <code>x</code>-axis rotation, and finally the <code>y</code>-axis rotation.</li><li><code>zyx</code>: First <code>z</code>-axis rotation, and then <code>y</code>-axis rotation, and finally the <code>x</code>-axis rotation.</li></ul>

<div id="ref_3dabbreviation"></div>

<h4>3D Abbreviated Function</h4>

<p><code>HT</code> has a lot of shortcuts for commonly used functions, such as <code>getDataModel()|dm()</code>, <code>getSelectionModel()|sm()</code>, etc. There are also a lot of convenient shorthand functions for <code>3D</code>:</p>

<ul><li><code>setPosition3d(x, y, z)|setPosition3d([x, y, z])</code> Referred to briefly as <code>p3(x, y, z)|p3([x, y, z])</code></li><li><code>getPosition3d()</code> can be abbreviated as <code>p3()</code></li><li><code>setSize3d(x, y, z)|setSize3d([x, y, z])</code> Referred to briefly as <code>s3(x, y, z)|s3([x, y, z])</code></li><li><code>getSize3d()</code> can be abbreviated as <code>s3()</code></li><li><code>setRotation3d(x, y, z)|setRotation3d([x, y, z])</code> Referred to briefly as <code>r3(x, y, z)|r3([x, y, z])</code></li><li><code>getRotation3d()</code> Referred to briefly as <code>r3()</code></li></ul>

<div id="ref_projection"></div>

<h3>3D Projection</h3>

<p><a href="http://en.wikipedia.org/wiki/3D_projection">3D Projection</a> is an algorithm mapping points in 3D space to the 2D plane, which is the process of projecting <code>3D</code> space objects to <code>2D</code> screen coordinates. Different algorithm will eventually produce different display effects of the screen content. <code>HT</code> supports <a href="http://en.wikipedia.org/wiki/Perspective_projection">Perspective Projection</a> and <a href="http://en.wikipedia.org/wiki/Orthographic_projection">Orthographic Projection</a>, the two most commonly used projection algorithms. 
<code>Graph3dView</code> components use perspective projection by default, and through <code>Graph3dView#setOrtho(true)</code> it can be switched to orthogonal projection.</p>

<p><img src="data:image/png;base64,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"></p>

<div id="ref_perspective"></div>

<h4>Perspective Projection</h4>

<p>A perspective projection is a method of drawing or rendering on a 2D paper or canvas, in order to obtain a visual effect close to the real 3D object, which is also referred to as a perspective view. Perspective makes far objects smaller, close objects larger. Parallel lines will appear to be closer to the actual visual effects like intersecting.</p>

<p><img src="data:image/png;base64,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"></p>

<p>As is shown in the picture above, the perspective projection eventually shows only the frustum part <a href="http://en.wikipedia.org/wiki/Viewing_frustum">View Frustum</a> onto the screen. The <code>Graph3dView</code> provides parameters like <code>eye</code>, <code>center</code>, <code>up</code>, <code>far</code>, <code>near</code>, <code>fovy</code> and <code>aspect</code> to control the specific scope of the  frustum body:</p>

<ul><li><code>getEye()|setEye([x, y, z])</code> Determines the location of the eye (or <code>Camera</code>), the default value is [0, 300, 1000]`</li><li><code>getCenter()|setCenter([x, y, z])</code> Determines the location of the target center point (or <code>Target</code>), the default value is <code>[0, 0, 0]</code></li><li><code>getUp()|setUp([x, y, z])</code> Determines the upper direction of the camera, which seldom changes, the default value is <code>[0, 1, 0]</code></li><li><code>getNear()|setNear(near)</code> Determines the proximal section position, the default value is <code>10</code></li><li><code>getFar()|setFar(far)</code> Determines the remote section position, the default value is <code>10000</code></li><li><code>getFovy()|setFovy(fovy)</code> <a href="http://en.wikipedia.org/wiki/Field_of_view">Fovy</a> determines the vertical radian of the visual angle, the default value is <code>Math.PI/4</code></li><li><code>getAspect()|setAspect(aspect)</code> Determines the aspect ratio of the frustum of the vertebral body. This parameter automatically adjusts according to the aspect ratio of the screen by default.</li></ul>

<p><code>near</code> and <code>far</code> can be adjusted according to the actual scene, but it is recommended to let <code>near</code> and <code>far</code> closer to each other within an acceptable range. The closer the better, which helps to avoid the accuracy problem of <a href="http://en.wikipedia.org/wiki/Z-fighting">Z-fighting</a>.</p>

<p><img src="data:image/png;base64,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"></p>

<div id="ref_orthographic"></div>

<h4>Orthogonal Projection</h4>

<p>Orthogonal projection is also called orthogonal view. In this projection mode, objects look the same in size regardless of the distance. The screen image makes people feel different from what they see. 
Orthogonal projection is useful in modeling, which provides a more &quot;technical&quot; vision of the scene, making it easy to draw and estimate proportions.</p>

<p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAjoAAAE6CAYAAAAfoPhdAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAEwiSURBVHhe7Z15lBzHfd/3WZRESZbIRyuJYycvSvz8RyLJph0lT1kfT/GTEzq29EjLpGRLpCnJWVHWQeoWJdnUZVORLHJJiiREUgIJEgAPkCAFYBcgzsUu9sC1wAKLa7G4gcW5i5MgDuqX+vVM9VR3V0/XzFTPdHV/5716AAbd1VWf6uMzv6rqaiN8QAAEQAAEQAAEQCCnBNpyWi9UCwRAAARAAARAAAQIooOTAARAAARAAARAILcEIDq5bVpUDARAAARAAARAAKKDcwAEQAAEQAAEQCC3BCA6uW1aVAwEQAAEQAAEQACig3MABEAABEAABEAgtwQgOrltWlQMBEAABEAABEAAooNzAARAAARAAARAILcEIDq5bVpUDARAAARAAARAAKKDcwAEQAAEQAAEQCC3BCA6uW1aVAwEQAAEQAAEQACig3MABEAABGIIvHrxMs0b2k8v9u9FygGDY6fO41wvIAGITgEbHVUGARBIJrD3yBma/vIYPThvK1JOGBw4fi654bFF7ghAdHLXpKgQCIBAIwTOvXrJi+JIwXmka5snPPi4R+Dw5Cs0a/m435b7jp11rxIoccMEIDoNI0QGIAACeSGwdd9JemzhDv/BOLD1KM0VXTYzlu7MSxULUY/Lr/2SuO2krP60a7v3d0R0CtH8kUpCdIrZ7qg1CICAQiAcxeEoAEcD+MPjcyA67pwu4SgOR+c27jrhic7iwa306oWL7lQGJbVCAKJjBSMyAQEQcJXAhvETfhRn2oJttHr7MeKIgPxAdNxoWR443jd6xI/icHfjjoOnvMJzpI5F5/6nFtGpMxin40aL2islRMceS+QEAiDgEIHJMxdoTt8e/8HIf+fvwh8pOr8U7oOUTQZ7DgcHji/dcIjOX7jst9eWvRAdhy5N60WF6FhHigxBAASyTmDd2HHi6A3/yuc/+d9xH4hONuWGpZNlhqVGjsXhKA5LT1hIITpZvyLTLR9EJ12+yB0EQCBDBMJRHJYYXRRHLTJEJ5uiM37odGD6/4qRw4Eojio7EJ0MXYQtKApEpwXQcUgQAIHmEgjPwuGZVTxuw+RTEZ1fikgBUqsZnD1/MTD9nweOT5w4V7Vttuydwhgdk5M9p9tAdHLasKgWCIBAiYBuFg7PsjL9QHSyI3csLOHp/5cuv5YooBAd07M9n9tBdPLZrqgVCBSeQCNRHF3X1WsimoPUGgZnRBTnF8pLHDmKc0hEcUzbA6JT7NsBRKfY7Y/ag0AuCfDyDfzuGzlIlQes8vTjej4yomP6UMV2dmVo7Y5jgen//O9aGUN06jnz87MPRCc/bYmagEDhCbDMhGfhsPQ08vFFR7xb5zWkpjE4furVyPR//q6eNoDoNHIFuL8vRMf9NkQNQAAEBIHwIpyNRHF0XVfcFYbUHAZrRNRGnf7P/26E/SgGIxf6HgHRKXTzo/Ig4D4BHlisRnF4/EajURyITnOEJiwvR6ZeoeeUlzj+YnA/TYmXODYiObwvRMf967yRGkB0GqGHfUEABFpKQLcIp7p8g43Cya6ry5fFwx8pFQYXLr5G/Vsqi3DyzKrRPVPWjsV5YQkIG1eDm3lAdNxsN5QaBApNoNoinLbBQHTSFbxDx88RR+HkwHGO4pw+d9Ga5LCcQnRsXxVu5QfRcau9UFoQKDwBNYrD4zgGth4NLMJpGxCLzhNiBhe/rwXJHgMeOL5qS3ARThaSNBhDdGxfFW7lB9Fxq71QWhAoLAFeqoGlQ/7yj1uE0zYgiI49uZESs2siuHzD4uGDxG88TkNyOE+Iju2rwq38IDputRdKCwKFJFDLIpy2AUF07IkOywxLjboIJ0tPWoIj84Xo2L4q3MoPouNWe6G0IFAoAuFFOJsVxVEhe6KzZCddvPQaUgMMwotwLl5/kM68crEpTDdjMHKh7hvhykJ0Ct38qDwIZJMAz5xavb3yLpVaFuG0XSOITmOCd/LsBVq49oAfxZm5bJwOHDvbFMGRcgrRsX1VuJUfRMet9kJpQSD3BBpdhNM2ICk6Fy5dJqTaGGzaMxlYhLNv9Ai9cuFS0zlyOTC93PaV4U5+EB132golBYFcE7C1CKdtSBCd2uSGZfDk2VfppcF9gSjOfhHFaZUoQnRsXxVu5QfRcau9UFoQyCWBcBSH33TM78rJwkeKDk+HRkpmsGl3JYrD0/+Hth312rKV7LhMiOhk4WpqTRkgOq3hjqOCAAgIAmkswmkbLEQnWW64HQ+L5Rue7d3tR3H47/xdKwVHHhuiY/uqcCs/iI5b7YXSgkBuCKS1CKdtQFJ0zl+4TEh6Bhy1URfh5H9nidXILkR0bF8XLuUH0XGptVBWEMgBgXAUZ4Z467DNRThtI4LoxAvexGQwivPSwD7i77IkOVwWiI7tq8Kt/CA6brUXSgsCThPg5Rumvzzmd2+kvXyDDVgl0RmjV8Q4E6QSA37/Te/mw34U51GxCOdGETXJKh8uG8bo2Lga3MwDouNmu6HUIOAUgWYuwmkbjBQdrgPSJdpz5Aw9tWynL6sviijO8dPnM80GomP7qnArP4iOW+2F0oKAcwTURTj5V7ULURwVMkSnJHinRRRnpYjiyOUbZBTHBfmD6Dh327BaYIiOVZzIDARAQBI4fe4izRva3/RFOG23gBSds+cviYUni5l2HjodiOIsWneAJs+86gyPDePourJ9XbiUH0THpdZCWUHAEQKtXITTNqIiiw7LDEuNjOL8fNEYbd9/0hnBkWIK0bF9VbiVH0THrfZCaUEg0wSysAinbUAsOo+Lwcg8ALdIadv+KWKxkZKzUAjPCTEWx0UGG3aewGBk2xeGQ/lBdBxqLBQVBLJMQI3i8CKcm8X6Qnn4FE10WGZYaqTg8MDjsYOnnBQcKWUQnTxcifXXAaJTPzvsCQIgIAjw8g3P9FTeiMvjcrKyfIONBpKic/rcBcp7Gt55XERxdviS0zMyQVOi+8r1enO9ML3cxtXgZh4QHTfbDaUGgZYTyOoinLbBSNE5dfYC5TWxrM4VXXR+FEe8xHFcRHHyUt/1YxAd29eFS/lBdFxqLZQVBDJCILwI58vrDuYqiqNizrvosAQ82l2K4vAyDvwiwMnTr+ZGcljWIDoZuXG0qBgQnRaBx2FBwEUCvHxD3+gR/5c/v+U4y8s32GDsic7iMTopunDylLjd1CjOMz27vLbMUx1lXSA6Nq4Ed/OA6Ljbdig5CDSVgCuLcNqGIkWHx6rkJa0aPUwPi+gNR3H4T/53XuqmqwcPlMcYHdtXhjv5QXTcaSuUFARaQiC8CGcRojiRrisR0eHuHNfTnsNn6GkRuZFjceb07ib+zvV6JZV/7Q6ITktuHhk5KEQnIw2BYoBAFgmEoziuLd9gg6mM6JwQouNy4rE3MorDY3L44e9yfWopO0THxpXgbh4QHXfbDiUHgdQIuLwIp20oUnSOnzpPLiZ+k/GTYhaVjOK8sGovHTh21sm61Mt/zfZj6LqyfWE4lB9Ex6HGQlFBoBkEXF+E0zYjV0Xn6NQrtGzDocoinN3biR/49cqCy/tBdGxfFW7lB9Fxq71QWhBIjUA4ijOnbw8dE1GMon9YdKaLMTrHTp53Jm3bF4ziLFi9n/YfPetM+W2zhugU+yqG6BS7/VF7EPAIbBg/Qbxsg3yXCs9SwadEQIoOR0iynrhLqntNZfmGn4k2Hdl1IvPlTpvr6m1H0XVV4AsaolPgxkfVQSCPi3DablUpOkeE6GQ5bRRCw2Ijx+J4URwhPlkuc7PKNgTRsX1ZOJUfRMep5kJhQcAegfAinBzVwSdKQIrO4clzYl2v7KV9R8/Q/NX7fMGZIVZa3zB+PJNlbRW/oa2I6BT52oboFLn1UfdCEuBxNzz+Rv7yz9sinLYbVYrOxIlzlLW0fuxYIIqzZPggHTx+NnPlbDW3QYiO7cvCqfwgOk41FwoLAvUTKMoinPUT0u8pRefQ8XOUlbRr4nRAVjmKs3n3ZGbKlxVOshwDWxDRsX1duJQfRMel1kJZQaBOAuFFOLvEgFWeZYVPMgEpOgeF6GQh8UP7ETFV3Fu+Yf42Wrz+oDcWJwtly2oZIDrJ53met4Do5Ll1UbfCEwhHcYq2fIONE8ATHbF4Kc9oamUaP3SKnuutdDnOWrGL+J1HjZRp/MBx+sqd/0DvfNfv0DXX/Br9/nv+Oz34yOMN5dlIedLat39LaSHa+59aRKfOnLNxWiAPhwhAdBxqLBQVBGohwMs3zFDeiLtUvDyO163CpzYCLDo/f3mHeA/NmZYlfvGfGsXhfzdanoF1o/Sbv/nv6Y1vfCO1tbX56Vff+lZ67//4Q9q5/1jDx2i0jLb27x+F6NR21udra4hOvtoTtQEBT2ZYauRgY0RxGjsppOjw7KZmpy17p2jW8nG/Lfnv/F2j5RgTEvMf/9NvBQRHlZ03Xnkl3fp3tzV8nEbLaWv/VRCdxi4Cx/eG6DjegCg+CKgEdItwIorT2DkiRYfZNjMtHT7kjcFhYeVoTt/mI9aO/08/7KQ3velNsaLD0sORnWV966wds5nswsdidui6auw6cHlviI7LrYeyg0CZAA8sVqM4/MufByDj0ziBZovORvE+o5lKFIfH5ew4cNKqcPzJ+/93Vclh0WERYiFqpaDYOjZEp/HrwOUcIDoutx7KrifQ1VH9Jt7RlStyWIQz3eaUorNbTOlOM/Fg44VrK8s3PNK1nXo3HU7lmP/lne9OFB2Wndu/dGcqx0+Toy5v5oiITrrXSZZzh+hkuXVQNgsEuqhD3LBz5jYeF90inIjiWDhlQllI0eF316SV1ou1xZ4QC4fKcVVzV+0VM6qmUjveR//27xJF561vfRtNmz4rtTKkxVKX70qIjv0Lw6EcIToONRaKWg8BN0Xn/Pnqq4bz2415RW1+MD4k0pNidhU/kJHsM3hs4XaatmAbzV4xnkpSBYcXVh3aeoQ4upNmmjVnAb3tqquqys41v/Z2Gt62L9VypFlHNe+VIxOI6NRz+8zJPhCdnDRkYapR7paKRmhKQtPeORZCkSA64W6u9k5Sc+jqENNu+WCB7dopchjLDXDttdfSXXfdRVNTU9qcD4iX13m//suDVWUkAH8KJszFwfTigIji7J+inUJybKetq0dodMWgnzZ1L6Vv33gTvenNb9bKDg9E/vnM562Xw3a9TPPrgehYvkO5lR1Ex632QmmFhnS2l+VDpeGJSAdFR9/Ei85YZ7vYR5WWct5KPp7oeO8YqeRd+k53LHvN8453vMM77tVXX60VHik6q7cfs3dQ5NR0AhyB4xlVA+KFdmMHT9WVRrqW0uA3v0kDX/mql/pu+xStuP4DiWn4yWfoc1/8uhfZefNb3uKdb9xdxZGcnz31fF1lqbcOae8H0Wn6qZ2pA0J0MtUcKIwJgVhB0Q7EiROdmAjQWCe1K2N6tFIT2kYt89atWz0xaTSx4KjvNQkLD0TH5EzJ/jYsOj8TLyPkWVX1pt5PfjxRasLi03vLzbR9z1HvmMsHRugH9zzkSc9Dj82ktVv21l2WeuuQ9n4rNqLrKvtXQ3olhOikxxY5p0YgJCmeeMR1J8WITuw+paiO7ALzRCfUnSX6sWK6yYh2795N1113Hb3vfe9rKF0pXtimig7/nfNcvny5RxWik9rJ1dSMWXSmzd9C3X2jtH3/ybrS8Jx51C8iOTKtvvuHtPan07205sedWglaP+OZuo5Vbxlbvd9yiE5Tz+usHQyik7UWQXmMCKgC4kV4YqdVpSc6ac7kkl1XYcGRcFTR+eUviZDcZMCic//cjfTgzEW0TcyyspmGn/sFrfzrD0dEZ+UtH6Otu45aPZbNcqeR1/Lym8Kx1pXR7TV3G0F0ctekBamQH5HhCEy1wcEWuq7CEZ2qESQ7/Fl01AhOOFeIjptiExZSKTo/EaLD08ltpNHRPTTwjW8EBKf3E5XurXUznrZyHBtlbVYevDYY3qNj597kYi4QHRdbDWUWBOTAYV3XkgoofjByafyNZjCyIjbRrqtSfvERJDuNMzw8XDUjiE6+ROeBpxaKNawmG04j/cPUo0RxOHqzYWEP9X3ms5748P+Njh9p+Dg2ytrMPJYOH4To2Lk1OZkLRMfJZkOhPQKxU83NRIe3Kg1srqzcHBaYyqyryjbRKezNbw+ITv5EZ3TPJDWaNu884kvN4D//kDZvPUDr5y32ozur7/1Jw8dotIyt2B+i0/x7VJaOCNHJUmugLLURiJ1SXls21bbWD0a2l3+9OQVF55dijA6Siwxk19X9Ty6kzbsnraSNQ6O0vrvHz8uP5tz4lzQystvKMWyVtVn5LFmPiE6995o87AfRyUMrFrEOVaZ428QB0YFApSlQLDr3icHI989YSJt2nbCe1r1UieYM3fuA9fzTKHMaeS5ZB9GxeV90LS+IjmstVvDyql1Nac56kpizLjpD4oWBr4loDpKbDOaWRec+ITojQnRM04aNu4y2VaM5pvuYlsGl7RZDdAr95IDoFLr5UXlXCciuK4iOm4IjxVSKTqcQnY3jJxLT+oHN1HfHHdQrBhdv2H646vZru1b4Y3MG73kgMW+T47u6zcsQHVdvdVbKDdGxghGZgEBzCfiis01EdF4TD3skJxnwKuXcdcWis0GITrU0+OgT1CPG2ci3HA9Nn111+9XPL/BnWq3fsCsx/6Tju/z/EJ3m3p+ydjSITtZaBOUBAQMCquhcFpKD5CYDKTr3PtFNwzuPa9O6/k3Ue/sdgffiDDwwTawsPhG7j8xr8JEnaO3K9YnbxR07L98vWnsA08sN7it53QSik9eWRb1yTQCi46bYhIVUis49QnTWjx0Ppq0TNCBEZYUSxVnZ0UFrlg5Ftw3vi38HGC2E6OT6fphUOYhOEiH8PwhkkEBAdC6Lhz6Skwx80Xm8m9btOOan1UsGiaXGX4xTyE7/g4/Rui2HAtup++DvFX5hFgvXIKKTwdtY04oE0WkaahwIBOwRgOjkQ+6k6Px4ejetFTPoZFIlh7utVveNBP5f3RZ/r3CLY9G9GqJj7+7jXk4QHffaDCUGAX/18sFtR+nS5deQHGUwd9UebzAyi84aIToyDS4e9LqsVv308cD36jb4e4VXEguITrFvmhCdYrc/au8oARnRgei4LXlSdP5lehetFtKqpqHhPZHvwtvg30FmcTy6Vu/HYGRH73U2ig3RsUEReYBAkwlAdNwWHBmFqyY6LZOYRd+jd7e9h764yEwiai3nfR9uo3d/Y6ipEgfRafINKmOHg+hkrEFQHBAwIeCLztajdPHSa0iOMXj1lfO058UXyROdFzbSj37eRYOiLU3TnDvfU1qM9ve+R3PU/R65JbhIrbpgLf/9ptkJx5hNN4jt3n3nkHFZTMvsb+eJVMrHCLFcMISIjsl9Ja/bQHTy2rKoV64JQHTclbtjGzbS0Gc+7c2o6r7n0TpEZ4i+8HttdMOdpcjLF0TkRS8bJWm54RFTgSrlmyxDpvlV2c4Tsmplt3AMRXYgOrm+HSZWDqKTiAgbgED2CEjRGRA38wuXLiM5wODcyVO0pfPewIv/lnzui57o/PBnC2hgyxGztLAkOHcsHKQ7hJi8+87BmP1m0/VCdK7/qVm+z3pRopvpHtNyNLjdPTeVIlLPNpiPCbf5g/swRid7t7GmlQii0zTUOBAI2CMA0XFL7o6sWUOrPn6LLzkrb/oQ7XruGXqh3HX1/4To9IsHvknyhKQsCOrfo/tWRCc530G6XUjTu4Q0JW+rL2dJlIS8hFO5rJF8hbC9qyxi9R7TdL95EB17Nx8Hc4LoONhoKDIIqKLz6sXLhJRNBmenTtFoKIqz9qtfoqk9e7w2U0Vn1egRSk5CSK4VQvL1wdK23SwL76Hbu3X7zqYPCpH44DSDfL18DLc1Kqc45rSbvQjRj2O3D9XFNN86toPoFPueCdEpdvuj9o4SgOhkU2xU4Tw6vCESxRl/9pmAlErR+cFjC6hv8+Hk1PVdT2w+3yW3neXJzLu+NqjZt/R/H3zYIN+HS1LyLyZlMNrG7Nj/cqOIAF37XXraKE+DesTk84sBdF05equzUmyIjhWMyAQEmktAik7/lqN0/sJlpAwyOCJERy7hsOHb/0iTu/dE2umFvtKsK1PRefprpXE0qpDEy4KZbLBg6fINitcgfV5EkoLdUqpwBSXEK9ONsxLFDaLT3PtGUY8G0Slqy6PeThOoiM4ReuXVS0gZZbDj8em0d9HLse3DotNZFp3eTYepetLJhpQPIR0LwvuXReehpHzF/z9Uiuj8KLEMdvP6UVmIkutucNwqZUdEx+nbXcOFh+g0jBAZgEDzCUB08iF3NYnOAu62El1REXGpdF8FhaEG0YnNu0bB8PLRSZcun5K4cbcbRKf595AiHRGiU6TWRl1zQ0CKzioxU+eciGYgucngeS+is4Hufmw+rdw0UT099DERdfmYiLqEt+unz3G30rXfodmB/5tJHxBi9IGHEvL19inncePM5HLElrOUx7u+1m+Wx4LveOJmVj6TOsRv89LAXkwvz83dr/aKQHRqZ4Y9QKDlBCA6rRebk0eO0rAYe7N7/vy6RVOKzj8L0ekRAlEt/dAbuPsdmqXZbpY3duc99LkFah4V0UnKm/9fn0f1Mqn5lvbXTC+PlKuUZ7X6mJS3lm1ehOi0/J7VygJAdFpJH8cGgToJ+KIjptqePX8JqckM9ix8mXr/5iPeYGN+J86JfYfqagNfdB6dTytGJlqc+umzHBn6q5npl8OLTr2HPju/OXV+sR8RnTpvNbnYDaKTi2ZEJYpGAKLTGrmbOlyK4sjZVPwn/5u/r0c4pej80yPzaPnGQ61P875D7+SozF89lV5Zysd451dXpXeMEEteU+zBeVvp/qcW0akz54p2uyh8fSE6hT8FAMBFAlJ0+kRE58wrF5GawGBP9yI/isOCwxEd/q4R/pkTHRYET0TeQ5+Zl454/eCv2qiZksMCCdFx8S5nr8wQHXsskRMINI0ARKd5cjc5cYSG7wpGcTZ8/3vE3zciObyvFJ3vi4jOsg2HkFJiwC9mRESnabenzB0IopO5JkGBQCCZgC864mVvp89dQEqJwdSxyUAUZ9WtN9OBgSFrvJ/v3e3NuoLopCt5EJ3ke0qet4Do5Ll1UbfcEpCi0ytE59TZC0gpMtgybZo3JmfTPffQ5NFJq6znQHSaEsWC6OT2VmhUMYiOESZsBALZIgDRaZ7cTU2dof39Q1YFR8opRCfdSI7sDoToZOv+1ezSQHSaTRzHAwELBHzREa+9P3nmVSRHGfii89N5tHT4EFJKDPgN1BijY+HG42gWEB1HGw7FLjYBKTorhehMiYc8UmMMDm3e2hKGEJ3myB1Ep9j3S4hOsdsftXeUgCo6k6dfJaT6GExsH6fVX/6iN+D46P7DTecoRed7IqKzZPggUkoMeHYbIjqO3uwsFBuiYwEisgCBZhOQotMjIjonhOgg1c5g66zZ1CPeaixf/rfpgQeazvG58mDk702bR4vXH0RKicHzvRCdZt+jsnQ8iE6WWgNlAQFDAr7oiGUDjp86j1QDg33rR2jw858LvN149LGf0bHjp5rO8bmVpenlEJ10JQ+iY3hjyelmEJ2cNiyqlW8CUnR4faRjJ88jGTA4euwUsdCoyzcM/P1ttHfdSMv4QXTSFRwZJYPo5Pt+mFQ7iE4SIfw/CGSQAESnNrljmWGpUSWHpYflp5WiCNGB6GTw9pK7IkF0ctekqFARCPiis3GCjk69gpTAYON99/uSM/BpjuJszAQzKTrfFWN0Xl53ECklBnMwRqcIt8XYOkJ0Ct38qLyrBKToLBeic0Q85JGqMzh08Dj1/e3NtPnRn9HhYyczw+vZ8hgdiE66kgfRcfVOZ6fcEB07HJELCDSVQEV0DtHhyXNIBgwmjk5ljtOzK3d5g5EhOhCdpt5ACnYwiE7BGhzVzQcBKTr8ivuJE+eQHGXwbE9JdL4z7Re0aN0BpJQY8DR+vEcnH/e+emoB0amHGvYBgRYTUEXn0PFzVPR08PAUjTzyGO3dtscpFhCd5sidK6Iz1tlObW1todRBXS2+37h+eIiO6y2I8heSgBSdpSKic1CITpHT+OoN1P/pT3mDjdf8w7ecYvGMjOg8LCI6a8VDHykVBjzo24WIjic67Z00Vsi7WnqVhuikxxY5g0BqBHzREYsgHjh2tpBp/8QkbRRRHHXK+OCXvkj79h51hgdEpzlyV4/o7N69m5YvX57aNazLGKKTDm6ITjpckSsIpEpAig6vj7T/6JlcpSeffoFu+8zt9M53/Y73J/87XMexoWE/isOi03Pjh2hkxkznOKiis1BEc5DSYVCL6LDg3HrrrV730V133WX3Oh7rpPZw15QSwUkWnTHqbFe7ttRurdL/tXd2VbbpQKcXNyBEx+5pjNxAoCkEVNHZJ0QnD2ls/zG67v98gN72tqsCYxT43/w9///eQydow08fDUZxvvgFGh/Z7iQDFp17n99A33zgRZrbvxcpJQazlo8ndl2pgiPHyVgXncDdoYs6hPS0d1Y6qqqLDotMO1U2L0uPLzMVCVLzbMoNKeMHgehkvIFQPBDQEZCiw6+433vkTC7S9R/6MF1xxRWawZht3vcf/fMP0KryWBwZxdn4xEyn6/70il10z5xh+swPnvUexEjpMrj/qUV06sy5yCV12223ac+76MDg8EDh5H9fffXV2puYTmq0g5GrjNkJ5lEWHYzxifCG6OA5CgIOElBFZ8/hM+R6mrtgWSSSE37I/Id//a9o2V9/2IvmDH3rWzS2cbvz9WbR+fGc9fT33/k5PfBML1JKDH7ybD89+NIIPTC7Rys6ExMTdMcdd9CVV14ZEB7uwuJxOo2k4eHh6B2m3IUV7llK7roqRYEC14YvNuEIj4M3tpSKDNFJCSyyBYE0CUjR4WUDdk+cdj599o6v0Otep4/mqDf1b/7NzbTphfnO11e2mSc6z62j2755H903fS5SCgweeHolPfiL0VK07KVNNHnqbOylGRaetLquujqErGjGz1QXnaSuLohOXMNCdNJ8GiFvEEiJgCo6u4TouJ7+7C+uN+o6eO8f/JHzdVXbCqKTotzN6KKfzF3vdwfy3+8T3+m6rsKXqRSeu+++2/4V3NUhznV1rE3lEFVFx4sCBffTdl1hADK6ruyftcgRBJpPQIoOv013/NAp59Nnbv+yUUTno3/7SefrqrbX7BXj9BPRpfJ8d2/zT6IcH3Hd2HGatmCbJzn8J/87G59oVEYtV7LoKIOX5QwudF0lNi0iOomIsAEIZI+ALzpiSvLOg6ecT3PmLaF/e82v0dd//1r6x//2X7XRHZ59NXPOAufrqrbXbDEbCKJj7/o6PPkKPdNTejkgp3lD++n0uYv2DtBoTl40J/7Nx4ljdAL7i+hOp8gPopPYKhCdRETYAASyR0CKDr93ZUyIjutp0+JeWnDDB/1p43/y734j8EDgWVf/68/+wvl6htsJomPn2rr82i9pYOtRX3AeW7iDtu47aSdz5OI8AYiO802IChSRgCo6Ow6cJFfT1q17aOj7/xx4L849f/wHdM2Vb/RFhyM5f3rdX9Cm8cPO1jOufSA6jV+9HMWR78mRUZxzr15qPGPkkBsCEJ3cNCUqUiQCUnS61xyg7ftPOpmG58yjleXp4jxlfOUtN9PGBUvpsSfn0Cdv+xz953e+2/uT/+1qHZPKzQ9odF3Vd+W+evEy9Y0e8aM4018eox0iuokPCIQJQHRwToCAgwQqorOftu2bciptGd1DA9/4RiCKM3T3D2nL9oNO1cMGd4hOfRcfvySTxUaOxeHFbVl88AEBHQGIDs4LEHCQgBSdrjX7xViEKWfSll1HqfcTH/clZ+UtH6MNi1Y6U37brCE6tV18LDMsNVJwWHZYevABgWoEIDo4P0DAQQK+6KzeT1v2TjqV1j37UuntxiKKM7rtgFNlt8161jJ0XZlefuEozoqRw4jimMIr+HYQnYKfAKi+mwSk6CwQojO6Z9K5tKFnjXNlToPzTIhO4gXIA4t5mriM4nAUjAcg4wMCpgQgOqaksB0IZIiALzriAbB59ySSowwgOtUvKp4izlPFpeTwFHKeSo4PCNRCAKJTCy1sCwIZISBFZ/7QPtq060Tm0rqXFmeuTFnkNHPZTsy60lxTiOJk5EaTk2JAdHLSkKhGsQioojMiRCcraXhwM/WJVaB5DM66rhWZKVdW+ITLAdGJXrcbxk/4UZxsLd9QrHtMnmoL0clTa6IuhSEgRWfe4D7iB0MW0uCjT1DPjX/pz6jq+9JXMlGuLLCJK8NTSxHRkRft5JkLNKdvj99NxX/n7/ABgUYJQHQaJYj9QaAFBLIkOusGKlEcjuRwGnzgpzS8/TBEJ0FCITqliye7i3C24OLGIa0TgOhYR4oMQSB9AlJ0fiEiOsM7j7csDT7yBK1QojgrP9VBa5cNtaw8rWRRz7GLLjrHTp0PRHF4dhWiOOnfP4p2BIhO0Voc9c0FAV90BvbR+rHjTU9r+zdR7+2lsTgyDdw/jdZvnWh6WVpRf1vHLKroYBHOXNyGnKkERMeZpkJBQaBCQIrOSwN7ad2OY01Pg7NerLzduKODVi8ZbHoZWlFv28d8soBjdLAIJ+5kzSYA0Wk2cRwPBCwQ8EWnfy+t3X6sJanva3dS/33TaO3ooZYcv1X1tnncJ5cUZzByOIrDyzfwe3LwAYG0CUB00iaM/EEgBQJSdF4UorNGiA6SmwyKIjq8fMMMEb3CIpwp3AyQZSIBiE4iImwAAtkjoIrO6m1HCclNBnkXHSzCmb17RxFLBNEpYqujzs4TkKIzV0R0hoToWE+bD9Kq+x6mga6V9vNOo7yO5jkjx11X4UU4edVxFh98QKDZBCA6zSaO44GABQK+6KzaS4Ni/R+bqf/lQeoRA4x5NlXPx2+lwU0HreZvs6yu55VH0eHlG1hqsAinhQsdWVghkEnRGetsp7a2NiW1U+eYlfpmNpOuDrW+4u8dXZktKwrWegKpiI4Qmr7OhwNTxld+/nYa6N8K0bEsk1LQ8iY6WISz9fcGlCBKIGOiM0ad7aEHvi88HZTPR398neE6uGTjCEjReWHVHhrYcqThtGrRgB/F8d6LI14C2Pvw9IbztVG2POcxY8lYLhb1xCKcuFdlmUCmREeNagQe8mOd1N6WU9Hx6sZyp0atSvID0cnypdPasvmiI9YD6h89Un/aeIB67w1GcXpEFGdVz4b682ykPAXbd8Zi90VHjeLIRTh5Kjk+IJAVAhkSnS7qKEdv2hP6qaJdW0IU2jup0rsloyRCjnyRKEWKSnmHoigao4gcIy7/ro5yF1tJVJLLFmp6pXzxYqPUR8S1JCevey+0k+nxw9uFj129/lk5fYtbDik6zwvRWSXkoJ7U17eFev5vaSyOlz7yYep98oW68qrn+NjnCD3hsOhgEc7i3n9cq3l2REcb2dDhDD3o1bE8/tO6WheYvmtMfdBHxsvIY/iyo8ufRcekbOE6hfPSjUeqXp+KGJodX1+/SsQsuf6uneb5K68qOn2bD1O9qecLXy4NOv7qndQ3uL3ufOo9ftH3c1V0sAhn/u4pea5RdkQnFBmpBboffdCJiP+dKgHyoa4IhG86crtoV1Kle0nZLxDpiZY6WjZdzXQio3bV6cpJ5AtJlTJEjq+NIHGd5fFM6l9L62DbNAjYEp3e3lFaOeMFCE4DstiIrLkmOuEoDr+wEotwpnGFI0+bBNwUnVB3lD9DSyM6ukiN2jXmi4Dc0BeuapGfinhEupsSy1a9+QLRlIT6CNMpd5spUpR0fLlPnBwZ1d/mKYi86iEgRWdO7x7q3XQYyVEGrogOj7lZLd7AzWNweNr4Ywt3YPmGei5c7NMSAtkRHeUBXXWMTtyDnLuXWi06RmUzaOeIwMSIVXg7k+NDdAwaIPub1CQ6a3ZBhDIqQi6IDhbhzP79ACWsTiA7oiPKGTvryhuAW+5K0j2oI9/pxUDmXzWiYzRWKEE81GiJiViEoitVu+J045Dk/iZskrqujOqPy6rVBCqis5tWbprQJyE4Pd+5m1bceiutHN4Xv13c/vg+dWZZFp3wIpyI4rT6qsfx6yWQKdERU5bKU6113UZl0TGJWiizqmruugoJV/DFhdGxPdFp8DHvAYrpKtLOkioPfq7kXX0wcsV9qvCrOpCay2wwGDmvU/zrvXpauJ8Uned6d1OPEJJIemEJrbj5Y/6Mqp4fduq30+2L75rG6vGMzroKR3GwfEMLL3YcumEC2RKdcnWis35CM5FC40jaOztLU65tdF3FlkGVgSpjdBLLFm2zxPoGxC04syrSzWd0/Kg4hccaJc3MavjMQwYNEVBFZ8XIBPmpfxut+IfvBd5uvOIrX6cV/L26Hf6eCR5ZEx0swtnQZYmdM0ogk6KTUVYtLFYVsWphqXDo1hGQovPsyt20fOOhUnp8jngXzk0VyRERneVPd1X+X26HPzPD5PGXs/PCQCzC2brrGUdOlwBEJ12+lnKH6FgCmZtsAqKzait5URv54j/+U4zNWT40npkHui9jkKxAm2RBdMJRnFnLx4nPL3xAIC8EIDpOtCREx4lmamIhVdEJSI6I4iybs5iWidWjkbLPoNWiw8s3TBdRJbnS+IBYvBTLNzTxQsahmkIAotMUzDgICNgloIrOsiXrvUU4V3z7blo2OA7BcUjyWiU6WITT7vWI3LJNAKKT7fZB6UBASyAgOvxgX7EJguOQ4MhoWytEJ7wIJ78IEFEc3GjyTACik+fWRd1ySyAiOg4+5NG1doiaKTqnz12keUP7/W6qOWJBWCzfkNtbBCqmEIDo4HQAAQcIHFzYRa9dvOCXVIrOMz27aenwISRHGfD4mJ+8NELPd/emehZiEc5U8SLzjBOA6GS8gVC8YhM4d2A/rf9aaYXxXU8+oRGdXbRk+CCSowymv7wjVdEJL8KJKE6x7ydFrT1Ep6gtj3pnnsDe55+jlTd9yJ82vvqzn/ajOpWIDkTHZdFLU3TUKA6Wb8j85Y4CpkgAopMiXGQNAvUQUKM48t04HM3RdV093bOLFq8/iOQogzREB4tw1nPVYZ88E4Do5Ll1UTfnCOiiOKe2b4vUQ0Z0IDpuS55N0cEinM5d7ihwkwhAdJoEGocBgWoEzuwa98fixEVx1P0hOm4LjozC2RId3SKc/K4cfEAABIggOjgLQCADBFh05HgcHouji+JAdPIhN2pXY6Oiw8s39I0e8aeM8ywuXrMKHxAAgQoBiA7OhtQJjHW2UxuvLq+m8HLpqZci+wfYPXuWN7NKHYsTV2o/orNiF7287iCSowymL6p/1hUW4cz+NY0SZoMARCcb7ZDrUnii095JY7KWY53ULqSnvdP/xqz+XR1CljqoK7x13PdmuTq5FUQnH3JXj+joFuFEFMfJyxiFbhIBiE6TQBf5MBHRETC6OkSER5UfE0AQHZ8SRKeYohOO4mARTpMbB7YpOgGITtHPgAbrrxeWLuoQERvZO2UqOpEuLqV7yztOoPurnTggFPd9JXgU6jYLdJmVVoXnyFIgH2+byorxfNxGe9q4O4q7pbh7ysZHis5s0XW1aN0BJEcZ/Nyw6wqLcNq4apBHUQlAdIra8rbq7UVZStLhf0KRl7iuK1UeSpKjdkuVZKlN3ajGiE5ynhWZ8bvRvGOUpCogarouM0OGPLCYBxjzbCoecMwDjxv9QHTyIXcmohNehBNRnEavHuxfNAIQnaK1uPX6loREHW/jRUcUQdEORtZITSRqEpaomkQnGFUKSpgUs7LoBA4crQ+VxxTVGtWRURw5XZz/5OUc+IWAjX4CorNWPPSRnGRQTXTCURws39DoVYP9i0oAolPUlrdY70DERiMFsV1XMhLk7ROKCnH5wt/XIjpGeepEp9KdpYSoAl1xJujUKI6M5PDLAG19IDr5kLs40eHlG3jZhgfnbaVpC7YR/xsfEACB+ghAdOrjhr1UAopURLuL2FdCs668fZXIiZGU8C41zLoyytO+6DQaxRkeHjY6t1TRWSiiOUhuMgiLDhbhNDr9sREI1EQAolMTLmysJyCFodMf3Bv0IBPR0Qz4DYtNzaKTlKd90Vn7hdv9RTh5PE6tUZx3vOMd9L73vY+WL19e9WSTojNj6U6a278XyVEGj3VX3qMTXoRz855J3HBAAAQsEIDoWICILMpRG28Qb7QLShfRKUV+KtuWZj1FByMH3rWjG/jsBYc0A6K9r5PytC86LDaNjMVh0ZGDoasJjxQd7tpAcpvBT14aoUfmb/Lbcd7QfnJp+QZ/DF5kEJuuGxh3SxBoPgGITvOZ5/SI5VlSmnfjJA9GLiEJTxWPvlBQnfKtClXc90l52hMdjsCwmLz/f76P7vhff+r9vZ505ZVXRt4irRMefmkcyw6n2V2DdP9Ti5AcZjBt/mZvTA7PsHLto17fQdeB6LjWlnktL0Qnry3b9HppZis1vQytO+D58+fp7rvvprvuuquhdPXVVwdEh//NeU5NTcVW7vnuXrpv+lwkhxk83TVgHMWZO3cuXX/99a072UNHLkVsO6hDvJMq+BJQiE5mGqngBYHoFPwEsFb9uG4lawfITkY2pofH1UZ2XZkIjsyjZ2iEWHaQ3GXAbZj0YcG59tprPRHm86Rpn/JMysALO5XIrd81XX4HVSWqoxedcIRXFwWqHEvtzpb5dXljAb1tan3nQ9Og4UBZIgDRyVJrOFuWchdQrUs6OFbfC1OTNPLdu7yX/qUlO/wgS4rgOIYNxW2QgCo4UgB+/dd/3Ruw3miqvWjRyK06Bi/4pvSo6ETepB74gcTba7qkfZnRvOCz9gpgjwISgOgUsNFR5doJTCxdQn0f/Yg/o4qFJ40Pd4HhAwKSQHd3N7HUBJc/CS+HUt+/eTzYwMBATbBjJxbIHzmB92iFRYclKTxZoXr3VvB4xfhBVVODYGMjAhAdI0yt2yg6c6hUlroWxWxdNZw9soziqG83Zsnh71v9YSlKmobe6jLi+NUJTExMEMtMtQ+387333hsQnqZ2XcnCxbwhPCw/lXtWSGJ0XWBy/To/alOe1KCua+dHinWTB3CGgUAyAYhOMqPWbqG7udS5JEFrK+Le0cNRHI7o8HdZ+fDDj7u68HGXwB133GHchqrwtEJ0wku7VPwn9J6s8v2pvbM0lsafPRn3Ek+/+ap3i/kL7WJcjrsnfItKDtFpEXjzw0Z/xejePmyeH7ZMIpDlKI4sOz/0ZJcGj+HAxz0CHM2RrxOopQ257WfPnt3cCleZbFBtiZf2iOhoXuIZiBgFu7a0XVcQnea2fQ6OBtFxoBGDYoPwbdpNtnv2LH8sTtaiOLLuHM2R4zYQ1Un7jEgnf47muNGG1V8dUW2JF65fZMHfwItBS93wnrv4kaCxEnDZ1YWuq3ROwALlCtFxorHD60JV+VXkRH2yXUher2r1Zz/tzbDKwlicMC01miMflLVEBLJNvxilU6M5mW/D8rTx4IDoyrRvvehU3pYefvFn+MWggQBN4FgiutMp3noO0SnGRZFiLSE6KcK1mbUcfNylXSDT5pGQFxPIouDoojluRARwToUJqNEctCHODxBIlwBEJ12+9nJXfulEl0awdxjklG0C/IbkuOnGiOpku+1k6Tgid91113lLhPCgYn45pFwuxHT1ejdqilKCQDYIQHSy0Q4GpZAvy4oummmwMzYJEeDFN8/sGneaC79YkB+Q+LhLAG3obtuh5O4QgOg401Z4WZaNpuI3Gq//2pe9wcZrv3C7jSxblgceki1Db+3AaENrKJERCMQSgOi4cnLg3TkNtxRHcXj5Bvnyv/6P35LaUg4NF9YgAzwkDSBlfBO0YcYbCMXLBQGIjiPNGPeGZEeK39JiqlEcKTnb7u+kS2fPtrRcjR4cD8lGCbZ+f7Rh69sAJcg/AYhO/tu40DXURXFOrF+XCyZ4SLrfjGhD99sQNcg+AYhO9tsIJayDwPkjR/yxODKKs+vJJ4jfkZOXDx6S7rck2tD9NkQNsk8AopP9NkIJ6yDAQjPY8UlvPA6//O/U9m115JLtXfCQzHb7mJQObWhCCduAQGMEIDqN8cPeGSYwtWmE8hbFUXHjIZnhk8+waGhDQ1DYDAQaIADRaQBeq3adPH+RHt+0t1WHx3EzQgAPyYw0RAPFQBs2AA+7goAhAYiOIagsbfbIxt10w9xBYuHBp7gE8JB0v+3Rhu63IWqQfQIQney3UaCELDcsOX8+p59YeIr84RlVvPBmUT94SLrf8mhD99sQNcg+AYhO9tsoUEKWG5YcTkWN6vDAYn6rsZxNdXBhl2OtaKe4eEja4djKXNCGraSPYxeFAETHoZZWozlSdooU1eGZVDy4WAoO/8nLOfALAYv4mT17Nk2fPr2IVc9NnZcvX07d3d25qQ8qAgJZJADRyWKrxJRJjeYULarDURyeJi4lh5dy4K4rfEAABEAABECgGgGIjiPnhy6aU4SoDqI4jpygKCYIgAAIZJQARCejDRMuli6aU4SozoZvfQNRHEfOURQTBEAABLJIAKKTxVYJlalaNCfvUR1el6roY3EcOEVRxNQJjFFnexu1dRRz4H3qeHGAXBOA6DjQvNWiOUWI6hwbHHCglVBEEKgQGOtsp7Y2ISZqCkhKreJS6/ZoDRAAAUkAopPxc8EkmpP3qE7GmwjFA4EIAU902jtpzP+fLupg6fFlp1ZxMdy+q0PIVQch7oOTEgQqBCA6GT8bTKI5rkd18rjgZlZOKzWygF6P5rVKVHSISm1Rr4RAdJrXejhS3ghAdDLeos9uO0AzR/f56aaXVvsvDGTBuXftzsD/bz1xOuM1qhTv0tmztO3+Tm8MzsTSJc6Uu5kF1T0wjY8/1kntbe3UWQkrGO+KDRMIeJGTINuuDhGxKUdxkkVHJy7lqE+oy6vda8DK9oFuMcVeveMH9kXb4zwGASYA0XHsPPhE97qA6IwcPeVYDUrF5UHG/R+/xZ9RNdjxSeKp5PgECTQkOrpuDHRtWDrFyuIhu6dC4hNpN08626gkLfwJi05Jcir/H/53eXt1m0ieIlu0r6X2RTZ5IgDRcaw1XRcdNYojX/7H61VdmJp0rCWaU1yITnM413UUXzQ6Q5Iiu6mCEZZg12FIdDTRNy9CkzCmJ7gNRKeudsROuScA0XGsiV0WHZ49pUZx+j76EXRZ1dEFop6y4dk98rkY7cbooA50bVi/2n3+gYHHZdHxvwtFfxqJ6IQGWkF0rDcpMswhAYiOY43qouhwtIajNuoaVTw2B1EcpQvDtAtEOV/VMSHe12FpQtdV6ld3RSiDg4zjuq4qnhLuuqp0TfnjbAymo0N0Um9iHCAHBCA6jjWii6JzpLfHlxyO6PD4HHwUAkldIKFoQWlPHsMRHmxaelj64zwgOumeZn53U3jqeDiiU24xZbByZIyOl1e1GVn6WVcQnXSbGLnngwBEx7F2dFF0GPGWH//Im2HFY3TwiRIw6wKJylHkpXTqu1ogOimeaiHx8Fi3kQzCaMdWBSJuujE64VlT1QYvK/KkRn40XaEpQkDWIOAEAYiOE81UKaSrouMY5qYX17gLRJbMZOo4RCe1dtS9EydpenkpCicHGOtmXYUidOVIX8ljDCM6cjtvmjmml6d2AiBjpwhAdJxqLiKIjmMNZlLcGrtAvCwDD8GYg0B0TOhnYhv97LrwFPNMFBWFAAHnCEB0HGuyrIoOv/APa1LVczLV0QVSPkwpChQc18Hf+T0ZsaKDX/r1tFSa+5QiRMF20X2XZhmQNwjklQBEx7GWzZroqDOqeKAxZlLVdkIZdYGEF4dU5CY8jTwwUUf78jh1dg+Ep7bWSndrvNk4Xb7IvbgEIDqOtX2WRIejOPwuHDltfPVnP03nDux3jCiKCwIgAAIgkGcCEB3HWjcLosMys/5rXw68F2fXk09gCQfHziUUFwRAAASKQACi41grt1p09j7/HK286UOBKA5WH3fsJEJxQQAEQKBABCA6jjV2q0SH33+DKI5jJwuKCwIgAAIggNXLXTsHWiU6zIlf+sfjcXgsDqI4rp05KC8IgAAIFJMAIjqOtXsrRYdnVO2ePQtjcRw7Z1BcEAABECgyAYiOY63fStFxDBWKCwIgAAIgAALounLtHEhbdF67eME1JCgvCIAACIAACMQSQETHsZMjLdFhweEp4vzSPyy86dhJgeKCAAiAAAhAdPJyDqQhOjywmAcYyxf/8Srj+IAACIAACIBAHgggouNYK9oUHRnFkYLDf/IUcrzd2LGTAsUFARAAARBARCcv54At0QlHcfglgPwyQHxAAARAAARAIE8EENFxrDUbFR1EcRxrcBQXBEAABECgIQIQnYbwNX/nRkVnx7SH/LE4iOI0v/1wRBAAARAAgeYSgOg0l3fDR2tUdPilf7ziOMbiNNwUyAAEQAAEQMABAhAdBxpJLWKjosN5YbCxY42O4oIACIAACNRNAKJTN7rW7Mii8/7H5tHvfuab9Ns3fZJu/9a3aXh4uDWFwVFBAARAAARAIOMEIDoZb6Bw8f74y9+l17/1KnrdlW+itrY2et3rrqCrrrqKbrjhBjp//ry3+Yn165r20r8Ll19zjCCKCwIgAAIgUCQCEB2HWru7u5ve8Oa3eIITTq9//evpj/7wD4lf9sfvw2nWS/8+Nn8NvbjjEEF4HDqRUFQQAAEQKBABiI4jjc3RmmuuuUYrOVJ6fvUNb6AvX/u7nujwgGMeeJz258/n9BMnCE/apJE/CIAACIBAPQQgOvVQa8E+HM25+uqrq4oOC8/vvf3tNPLdu5oiOYxBik5dwtPV4dWno0sB6n3XTp1jLYCMQ4IACIAACOSOAETHkSa9++676YorrkgUnWvEeJ1mfsKiU6vwdHVwN1wHlVynizqE+LTDcprZhDgWCIAACOSaAETHkeadO3cuve1tVyWKztve8duRKEucjDTj++QurZLctImwjic97Z2EYI4jJyWKCQIgAAIOEIDoONBIXMRn1myi18cMRJZjdH7l9W+g37r+5kyJjlGEp9yFhS4rR05GFBMEQAAEHCIA0cl4Y02ev0j/2LfFk5ffvvETdMUbr4yN6rzp7f+G/mz2ikyKzg1zB+mRjbuJ6xP+jHW2+3UKjNfJeNugeCAAAiAAAtknANHJcBst3nOEbnppdUBcfuMP3k+vf8uvBmTnV8TUcpacP3no+cxJTjXB8dCPdVJ7eVxOcLxOhhsGRQMBEAABEHCGAEQng02lRnF042i+9PAT9Jcf+hBde+219N73vpfuvfde/2WBza5O3DifRMEpWQ51tqvjcirjdZpdDxwPBEAABEAgnwQgOhlrV10UR8rEpxYN09YTpzNV4rDomAlOqQqlLqvQVHLdlPNM1RiFAQEQAAEQcIkARCcjrZUUxXl8095Mvn1Yik4tgpMR5CgGCIAACIBAAQhAdDLQyK5FcVRkEJwMnEAoAgiAAAiAQCwBiE4LTw5XozgqMt0sqhYixaFBAARAAARAIEAAopPiCcGRmrjFLl2O4qSIDFmDAAiAAAiAgFUCEB2rOCuZ7T/9CnG3zszRfYEj5CGKkxIyZAsCIAACIAAC1glAdKwjLWX4/f5t3jttWHbGp8563yGKkxJsZAsCIAACIAACMQQgOimcGmsnpgIv7vvcko3+2411753J6oyqFNAgSxAAARAAARBoKgGITgq4WWxMFszM4ntxUsCBLEEABEAABECgZQQgOpbRv7jjkJHkIIpjGTyyAwEQAAEQAAENAYiOxdPi7MVL9LH5a6qKDqI4FoEjKxAAARAAARBIIADRsXiK8OrcSV1W4VlYFg+PrEAABEAABEAABEIEIDqWTgk5nTxJdNRZWJYOjWxAAARAAARAAARiCEB0LJ0acjp5kujw//Ng5bgXCVoqDrIBARAAARAAARAQBCA62tOgizra2sTK2moKrbKt7BeeTl5Ndr60bBN9vWfUf7cOzkIQAAEQAAEQAIH0CEB0DNh2dQjhae+ksZht1enkUmR4VhWPx2EJGjl6ChEcA87YBARAAARAAARsE4DoJBHt6hCRnfhoDs+0gsgkQcT/gwAIgAAIgEBrCEB0qnIvd2F1dLWmdXBUEAABEAABEACBhghAdKrg87qs2joImtPQOYadQQAEQAAEQKBlBCA6cejHOqldDEZGMKdl5yYODAIgAAIgAAINE4DoaBGOUWe7iObAcho+wZABCIAACIAACLSSAERHR78czQlOL2+j9s64eVetbEIcGwRAAARAAARAII4ARAfnBgiAAAiAAAiAQG4JQHRy27SoGAiAAAiAAAiAAEQH5wAIgAAIgAAIgEBuCUB0ctu0qBgIgAAIgAAIgABEB+cACIAACIAACIBAbglAdHLbtKgYCIAACIAACIAARAfnAAiAAAiAAAiAQG4JQHRy27SoGAiAAAiAAAiAAEQH5wAIgAAIgAAIgEBuCUB0ctu0qBgIgAAIgAAIgABEB+cACIAACIAACIBAbglAdHLbtKgYCIAACIAACIAARAfnAAiAAAiAAAiAQG4JQHRy27SoGAiAAAiAAAiAAEQH5wAIgAAIgAAIgEBuCUB0ctu0qBgIgAAIgAAIgECGRGeMOtvbqK2tjTq68tQwea1XntoIdckegfJ1095JY1ULZ7pdC2rY1SHuZ+3UGVuBBsru5V26X7Z5N8x686p3Pz3Prg5RnsQ2M2iLSP0M9sEmIBBDoC7RGetsr1xk8mJr66DG/CSvQmBary7qKLNsj78zVj2RvZuM3x6Vv9sVx0p9SjfYZnwaZ5NmKaPXQ7WHW5olyVPepg9g0+1awCY10SldD8H7RL0c6t0vTdHR1a8F7YdD5oZAjaJTeeDoHqim0Rj/wRB4UJoKgWvsTevV6MNcERCN7NQrT1Ha6YmO/rzgEjTKJr1zpiSXIbEZ66T2hsU/vTI7m3OiOGSsZmmV1zu/UpTpBsptJaKTdv0ydppYKU4DbWbl+BnPpCbRUSMGwR/zigAZhC0hOmmcFRUBUaWm0maNRtzSKHMwz3jRSf/Y9RyhVN4UHzj1FCrP+7h2M0+rvGnlK8+dBvK3IjoNHD/Pp3/VutXELD5gYe8HcXJLBHsg1OdT6Ee7gVMkHa0G0Un4Ve33qcobvyysqIDa36qJNgT6mL0xOsGG0MHXdZ9Fe1LiGrRaGUv/p+2eCwBX6qdEHCp95hK9GtGpVi995CdcjvjeIr3okM9enki6dqk8rE24yhM03C6RfTUnqL4+MdEov7JmbKIRRaWu3q/ESnde5JySnIy742oJ+UfrFz6Mx5S/DFwr5TYLlF0jVuHrK5S5zLvCPnwu1NbNqS+rZmxd5LrXyHaoXdSiqw9NXbesbEPtw9Xk2Bwr1HKvnTFf7cFzW973qotwuOxJ5dFxkMx0HJLy8+Klyriaapy9O5rJuaZc96X8ou0eJ0TV6uePQ1Ku47jrKHquhx+F1a/J0v6hcutkonz+VspRPd9aeZRKbZJncNhCVWGJRMzK+Rvf+5K0Ivn/Pb6B5wM/G5m3fEaG7lENls1cdPwbUtyFW3mIl8pUvSsl0PWlDqbTiVBogHLcWBTOs9LAofIELtCw6KgnCf9flS46zcNX141XKUd1DroLJHDjivCIi8zoREc5tn9S6cpT4mHGtbKdPnIUGicUuemFxxFxfWoXHbOymrJXHlLGvx5MxxGUyqBep7pIkF8fbTuFLnqljNEbcvSmFc27cn0mlUt3y9LlF65TfB3V+0eYIZe9cn5HHoYxv1rD25kdu1QzXV30+Wl+cSrwIseM/PDT3/y1osPXfPi6Uc9LQw6m9TPlbHyuqWWNiIBnhN6Pjthnl7Z+9V5HOu4GeWnKyJza29uDY6O8sgavz6rXVM08DMrqC6hhdFmUOXLvNrjvVbvnBp+BSeXg616zje8Y4WdczPbJPuVvYS46iRduOOKje8iW/VQOZo4Zo1O5yDXjQZRfgJFff+oNQm6nbCQbSmvfCQ3t/0LQPYg0x9DWoeq+oaiFtp7SeuMv3uSxUzHtYspVeThEpVI9eeVxyt8Z1Ce+68qEje6hlSB6kQeVnMFicAWV61NXqDfmJhqerZL8q7J0zUUeGIGbr+Ri0HWZ9AAqY9H/GlelJU4Cw99Xl0XTB3BwO9NjK6ITvvYDD1oTxjHHNOhO0IpO1fLIqEr0QREb0UnIz4yzCYdgdCgQjYhca1XORwNupUBHVJjiIiaJV3RcZCbww7YcpQtLaLVoQ2K+8keWwfUpK6G7Tk2ZhUBo7zGJsBrdIOY5Fnv/4ft4kjxVL1MqohOO6ITPg1rG6ES2lcIVvngjNhgX0dE9jDUPi1BI3RcIjawE6hfbVaT7Va8+WEMP87h6xrZnXPQifILou4H8kHQiV01EJ6Fr0uNjUB9j0TE+BxK6vBoJh9YkOvq2ieumqdzPEsLnceeoFwWstHtcN0Fc1DUJiz4/JZJU9YYVlMlKF4PBg9skklHDsf2IRzURMGEcd0yDh48zomPCQf4ICvEMPkwNukliudV3Helvmcl5BbpXuP5evdSHtE7+DPMNRYGqz2BNzrN0f61RBurZp1HHKRmqVlxkICL647GZER3lRNf+io1EfGIeqJ6Il6enx0R0oqE/5eZYx0OuEuUwfOhXu6gdEJ3qUQaIjp1BzwY3bO+iLgt38KSOhO518pAY0bEQgQncYBvKrz7RkffNSli8wa6rlESnqvwVSHTqk2Al4mVyjmkfwPVfR9Fns1lepYhRZcxm6b6qdCWFIqem17rczssvkYdhWWuVlsTjaqjFvL4k2ouQLFzhMTpq1y//PeIASSdegoCZR3QC4yh002nLYy+SREAVHe3g3oTIh2kXS1mIrD30I4Kl6VZTGSVwiD5sTbpnzLqu6qqzKVf5q00dD+XvW+UEr6XrKtKNaMKmetdVVXmOle/qV49uLEhkD90NxXLXVVL3mTYCY1guHQF7XVeROHpAAM26VMLdJel0XVVnXJyuq7rONSXS0xUZhKo5w4wG/XoXrdEPhnqvSSk1XGc+F+U9xLvuxT/kn37+NVxT8txO5GGaZ02iEz1fuzqS5cRKMEfJJDh4P3j8wHggg/FDSWWrQXQqJ1bcOJDgKPX4iI5+5L5pN0NcF03orcqx3Sn6aWzB4FJwhk6gvlUH9epmr9Rer3DXX5B3LYORdc0f1y6GXHWio3wXPTfiZ/iUtlXqE26zQP+42r6mZTVlX89gZCVaEwkZq6HW8C8yfZnqiuj4gha6STHHaoNZtZGmKtdr6DSKHcCrtGVpG93NK9TeVQbZRpjE/ArVD+hNOHa5TlppCz00tEKrY6zWN3FMY6kAmey6iuFszEH3YFKu7SRZ0nfD1H8dJUd0EnofeABy+D4lBs13hCYZRCM6Zs9AE4muRF5j8jSO0JT2DxwzEplK0gb3/r820fHqF/OQiYSWqt04Q3l4+5o/lPwbRGBGUthIK2N0TB6++gGd6nTkztKbi7WRmuCxtG8sDQ0aTYzoxLCOj+BV+NUV0VFv/FW56mdd6dskPLU0eu4E66M7L4LnXGRcS9Wymp9TjXRnRWcjaMQjMCU22rdfr+h4dMJvKg89aGLH6ITEUr7WISlK7OcX2D/6izCpXLpzJmncksq62vRyk2PrRKNsHxFJM8kv8itU6fqIezRkUnRCP1zU+0kSh+TxYAaRg7johMH5Gn/8iK0H3iIfe+7LSHTgmpL3e82PToMylkoi702mPNQf0fqB4bprI3zeRe9V4Z4Y9yTGpMR1iI5Jtq3fRjaoPlJTwwj32KqY/wJuPQ27JYgfNGb3OMgtmwSMHybZLD5K1RIC5fulhW6IlhTf+kHBwzrSKhnmXnS03WxJP1mNWqCAohP5tWIEChvljABEJ2cN2ozqGHetNKMwGTgGeDS1EXIrOnFdbIn9w8b4Cy46VmTRGDY2zBABiE6GGsORopSiwDYi6Y5UOKGY4NHcdsyx6DQXJI4GAiAAAiAAAiCQPQIQney1CUoEAiAAAiAAAiBgiQBExxJIZAMCIAACIAACIJA9AhCd7LUJSgQCIAACIAACIGCJAETHEkhkAwIgAAIgAAIgkD0C/x9IPo01y6tiVwAAAABJRU5ErkJggg=="></p>

<p>Most parameters in orthogonal projection are the same with <a href="#ref_perspective">Perspective Projection</a>, but there is no <code>fovy</code> parameter, which is replaced by the <code>orthoWidth</code> parameter:</p>

<ul><li><code>isOrtho()|setOrtho(ortho)</code> Determines whether it is orthogonal projection, the default value is <code>false</code></li><li><code>getOrthoWidth()|setOrthoWidth(orthoWidth)</code> Determines the width, that is the distance between <code>left</code> and <code>right</code>, the default value is <code>2000</code>.</li></ul>

<hr/>

<div id="ref_3dmodel"></div>

<h2>3D Model</h2>

<p><code>HT</code> provides a variety of basic types of forms for users in modeling. Different from the traditional 3D modeling methods,  <code>HT</code> modeling bases its core on the <code>API</code> interface. It sets through the <code>HT</code> pre-defined meta-type and parameter interface to become able to construct a 3D model. The following sections will describe the predefined 3D model types and setup parameters. Please also refer to the <a href="../../plug-in/modeling/ht-modeling-guide.html">Modeling Manual</a> and the <a href="../../plug-in/obj/ht-obj-guide.html">OBJ Manual</a> for more customized extension models.</p>

<div id="ref_cube"></div>

<h3>Hexahedron</h3>

<p>Hexahedron is a cube composed of six faces, which is the most commonly used basic beta type in <code>HT</code> system. A hexahedron appears by default when building a <code>ht.Node</code> object. The overall parameters of six faces can be controlled through the <code>all.*</code> in the <code>style</code>. For example, if <code>all.color</code> is set to <code>red</code>, then the default color of all six faces will turn red. If you need to specifically set a certain face, you can control through the <code>left.*|right.*|top.*|bottom.*|front.*|back.*</code> face parameter. For example, set <code>left.color</code> to <code>blue</code>, then the left side will turn red, if <code>left.color</code> is not set, then <code>HT</code> will use <code>all.color</code> value.</p>

<ul><li><code>all.light</code>: The default value is <code>true</code>, it means whether the six sides will be influenced by light. When influenced, it looks the lightest from the front, and darker from the side.</li><li><code>all.visible</code>: The default value is <code>true</code>, it means whether the six faces are visible. This parameter does not affect <a href="#ref_part">The Attachment</a></li><li><code>all.color</code>: The default value is <code>#3498DB</code>, the color on the six faces</li><li><code>all.image</code>: The default value is <code>undefined</code>, mapping on all six faces, its priority is higher than <code>all.color</code></li><li><code>all.blend</code>: The default value is <code>undefined</code>, six-side color, priority higher than <code>all.color</code>. If there is a map, then blend it.</li><li><code>all.opacity</code>: The default value is <code>undefined</code>, six-side transparency. Value range is <code>0~1</code>. Generally, you need to set <code>all.transparent</code> to <code>true</code> if there is transparency.</li><li><code>all.reverse.flip</code>: The default value is <code>false</code>, whether the positive side of the six faces is shown on the negative side.</li><li><code>all.reverse.color</code>: The default value is <code>#868686</code>, the opposite side color of the six faces, that is the internal color of the cube</li><li><code>all.reverse.cull</code>: The default value is <code>false</code>, it controls whether the opposite side is visible, that is, whether the inside of the cube is displayed. Generally it is not displayed for a close cube to improve performance.</li><li><code>all.transparent</code>: The default value is <code>false</code>, whether the six faces are transparent. If <code>color|image|opacity</code> attribute appears to be transparent, it needs to be set to be <code>true</code> generally.</li><li><code>all.discard.selectable</code>: The default value is <code>true</code>, means that the part with mapping transparency low enough to be rejected can also be clicked to select. It can be set to <code>false</code>, and then the excluded part is not selectable.</li></ul>

<p>By default, mapping will fill the entire picture into the whole face, but in many cases the image needs to be offset from a certain position. Sometimes the tile needs to be tiled in the way <code>Tile</code>, sometimes the map needs to be flipped, and sometimes the layout of the map needs to be dynamically changed to complete special effects like flow and others. In this case you need to customize <a href="http://en.wikipedia.org/wiki/UV_mapping">UV Mapping</a>, tell <code>HT</code> how to attach the picture to the face according to the need of customization through <code>uv</code> parameters:</p>

<ul><li><code>all.uv.offset</code>: The default value is <code>undefined</code>, which determines the <code>uv</code> offset of the overall map in the format <code>[0.5, 0.5]</code>, including in horizontal and vertical directions respectively</li><li><code>all.uv.scale</code>: The default value is <code>undefined</code>, which determines the <code>uv</code> scaling of the overall map in the format <code>[3, 2]</code>, including horizontal and vertical mapping multiple</li><li><code>all.uv</code>: The default value is <code>undefined</code>, customized <code>uv</code> parameters. Here are some customized <code>uv</code> parameters to meet common requirements for reference <ul><li>Empty defaults: <code>[0,0, 0,1, 1,1, 1,0]</code></li><li>Left rotation <code>90</code> degrees: <code>[1,0,  0,0, 0,1, 1,1]</code></li><li>Right rotation <code>90</code> degrees: <code>[0,1, 1,1, 1,0, 0,0]</code></li><li>Right rotation <code>180</code> degrees: <code>[1,1, 1,0, 0,0, 0,1]</code></li><li>Left rotation <code>180</code> degrees: <code>[0,1, 0,0, 1,0, 1,1]</code></li><li>Up and down: <code>[0,1, 0,0, 1,0, 1,1]</code></li><li>Left and right flip: <code>[1,0, 1,1, 0,1, 0,0]</code></li></ul></li></ul>

<p><img src="data:image/jpeg;base64,/9j/4Q8NRXhpZgAATU0AKgAAAAgABwESAAMAAAABAAEAAAEaAAUAAAABAAAAYgEbAAUAAAABAAAAagEoAAMAAAABAAIAAAExAAIAAAAdAAAAcgEyAAIAAAAUAAAAj4dpAAQAAAABAAAApAAAANAACvyAAAAnEAAK/IAAACcQQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKQAyMDIwOjA5OjA3IDEzOjQzOjE2AAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAADVaADAAQAAAABAAACKgAAAAAAAAAGAQMAAwAAAAEABgAAARoABQAAAAEAAAEeARsABQAAAAEAAAEmASgAAwAAAAEAAgAAAgEABAAAAAEAAAEuAgIABAAAAAEAAA3XAAAAAAAAAEgAAAABAAAASAAAAAH/2P/tAAxBZG9iZV9DTQAB/+4ADkFkb2JlAGSAAAAAAf/bAIQADAgICAkIDAkJDBELCgsRFQ8MDA8VGBMTFRMTGBEMDAwMDAwRDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAENCwsNDg0QDg4QFA4ODhQUDg4ODhQRDAwMDAwREQwMDAwMDBEMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwM/8AAEQgAaACgAwEiAAIRAQMRAf/dAAQACv/EAT8AAAEFAQEBAQEBAAAAAAAAAAMAAQIEBQYHCAkKCwEAAQUBAQEBAQEAAAAAAAAAAQACAwQFBgcICQoLEAABBAEDAgQCBQcGCAUDDDMBAAIRAwQhEjEFQVFhEyJxgTIGFJGhsUIjJBVSwWIzNHKC0UMHJZJT8OHxY3M1FqKygyZEk1RkRcKjdDYX0lXiZfKzhMPTdePzRieUpIW0lcTU5PSltcXV5fVWZnaGlqa2xtbm9jdHV2d3h5ent8fX5/cRAAICAQIEBAMEBQYHBwYFNQEAAhEDITESBEFRYXEiEwUygZEUobFCI8FS0fAzJGLhcoKSQ1MVY3M08SUGFqKygwcmNcLSRJNUoxdkRVU2dGXi8rOEw9N14/NGlKSFtJXE1OT0pbXF1eX1VmZ2hpamtsbW5vYnN0dXZ3eHl6e3x//aAAwDAQACEQMRAD8A9TSWBk/VNmT19/WXZt9fqV+maKztAhnpbmWT/a+itIdN23VWjJuPouc4MLhBDm+nsd7fopKbqSXKaRAMiDEHtrwkpdDFj9JZB0n3AxpqiINlzKN9j527gCQJiQzUpUTsoyEQSarueiXc3xUbLC1ssb6hke0GNJ1Pu/dUcfKpyATU4naYc1zXMcJ4llgY/wByKCD37x80SCDRFIBBFg2PBgx73OgshsTukET+7+8podQADoES6T9zURBKklV6pi5GZ07Ixca84t91ZZXkNkFjj+e303Mf/mvVLA6R1PH6YMW7qdluT6ez7RBcQfUfb6n6Z737vTf6H00lOukqVuDlvDtmY+vc9jxA4DBsfW33/Rt+n/5NN+z8r7O2r7dbva5xNsCSCZDHf1UlN5Qe97SA1m4dzIEa+aFj499V2RZZkOuZc4OrrcIFYA27Ga/nfSRbPoH4j8oSUpr5ncNsHTvI7O0UtzfFN6jQNTEQPwTWX1VNDrHhoc4Nb5ucYa1o/Oc5Gj2QSBuWPq27y0VS0fRduAkbd30fpfT/AEaICSASNpPI8EBuRj5VbX0u9RgeBMEe5rm/vfuqwkQRodCoEEWDYPUP/9D1NVqXXHI3OxjWLGjdbuB4Acxu3+s+3/WxWUklX+LUoY5uLZX9lFch7vR3SHOsLn3N3/y7HOT3sLsXZ9n9UMMNqLo0ZPonef3or3K0kkpixznNlzdpky3ngwq+XjHKpvxw81l5EPAkgj03SOPBWTMGBJ7BBrN+rjVtc8guaXhwGjQdv9XaiCQQRuNQiQEgQdiKLHFwhjvtsNj7H27QXOJ0DBDWt3F7vpOe/wCkiNx2AtPdp0nsJB/gpTd+63/OP/kUxdeOK2njh2v4gImUibJQIRAoDQftXq4d/W/gFNDr3gw5kAiS6QfdxtRE1cpByYdWanNc5tghxZoQJa13/V70ZJJTVyHzVVcabXGs7xWz6QcBt2OA+Key2MtjfStIAgWNnZ7w7dv/AKvps+l++rKSSrpq0EC+1/pWtNr4cXElvsBra6tv5rHMZv8Aaj2fQPxH5QpoVvqEhjWEtOpeHAQQR7Yckpc01u1IMuEEz4xP/UoWZhNya2NG1rqnb69zd7Zgs99ct3+1yK190a1xBIAkcDh3P5yfdb+5+I/8knCUgQQdlphEggjfdr42KcWr05aQbA4bG7RJc3f7JOxv7qtoTja6JriIPI5EPj/O9iIJIEiD3CBkZEk7pjAQiANumvE//9H1L5pfNYd+d9aG/WB2NRhMf0gVy3IIAcX7A7b6n2n/AEvs/oqu13dadcQ/GqZTLwHF3ugB3oO2te76btm9JTf+aXzVSq3qptobdRW2tzXnIe187XA/om1zt3td/VVxJSx0EkwFEW1uAIsaQ7RsEayN2n9lS50PCG2uvcW7QAw+0AARo3iElJPmkSG/ScBOgmBqoWksZubJMjST/wCSanDWvaNwDgfiR+KVaWi9aXDmu0a4ExOhB0PdSQ6h9ImJBgECDEA7URJKk3zVbqmTk4nT8jJxaTk5FTC6qgTL3Dhn6Nr3/wCaxVel9T6hl4NV+RgPx8h+r6XbmwPUfT/ha2f4JjMjY/8A0iSnT+aXzVHA6hmZTmi/AsxWuZu3PcDB0/RkR5q+kpb5qD7qqyBZY1hIJAcQCQC1rj/02IijZo2e4I/EpKU0h07XTBgx2I7KUFRDRA08PFPtHh+JSUx9WqS31GyJkSNIEu/zZUgQQCDIOoIULBG0t9pLmyedNzfFT40HCSn/0vU1XZkMsygG3NLH1yyqNSRte6zf/wAXbWrCiK6wQQ0AtENIA0HgElNZmbQ6m60ZDC1nuDwNGNcP0e799ObXMxqnOvBc0gW27dHmsO+0bW+7093p2I4qrAIDGgO0cIGo81La2I2iJmI7hJRUHNcJaZEkfMHaVFv84/4/wYpAADTQan79ShtsZvc7c3a4jaQQZ9rT/BJVFhmZOLRX+sugGSAAXHTl+2sOdsZu970ap9b62PqcHVuALHDUEHiFSzccZTw5lzaordXuEOMPLHP0nb/gmI+O6vHpZVZeLHN0NjoBcXH92fznJ5EeAUTxXqGOJmckrjUP0ZfyKWrh39b+AU0Kl7CDtcDu9wgg+0ge7RFTGRSFkPtbS807DftPpNsMNLuGBxHu+kUVDtoquj1G7o4501a/t/KYxJSrLNpYWluxxlxc6PbH0mfvfmpnPsF7GDZ6ZB3yffu/we1v9mxRtwsW1jWWVhzWSGgzoHfSAU349L7GWPbL6yC12sgiY/6tySlMe42Pa4t2ggVwdSI927X6XqNsT2fQPxH5QmFFQdvDQHTunz1/8klc9rWwS0OOoDjGgLd3ikqr2crNw8q3JtspbcHPY2ut7CxrWub7/Ud6jg/Zu2++n8zetV7Hu2+7gjd2n4JC6uPpcaHjkc90vWq/eH3j+9PlkMhEUPTsxxxCJkbJMjZv/GY7HMaNztxLm6xHdjf4IyA/JocNLG+yHO14A22f+e/cjNcHNDmmQRII7gppZAKf/9P1NJZlv1j6RV1N/SrLXDMraHvZ6by0At9UfpQz03O2fm7kdvWOmvt9Ft4Nh3ANDXSdg3vj2/uBJTcSVWrqeBc6ltVwcckONMA+4M1sPH5u5WklKTQ3wH4JHUETHmEJjLAdvqueWkSXbRMBv7jfz0lJkxAIgiR5wg35mPjua2+xlbn/AEQ50THhopsf61YfWdodqD4gH+U36LkSCBdGigSiZcNji7dWYAHAj7k6HVJklzjHth0a/nbht/rIiCVJId99GPS+/IsbTTWC6y2xwa1oH5z3u9rWoVXUunXMbZTl0WVvjY9tjSDuJrbtcHfnPY5iSmykg05mHkGMe+q4kbore12n73tKMkpSSSHaHyHNeWNEhzQAZJLdp937qSkiUlQrFoB3OD5MjtAP5ug/NUvd4D7/APYkpeUyG/1Gmd5AcYAaAYmGDt+b9NEExrqe5ST9X//U9KPTenHLOccSk5hbtOSa2+rtjZs9bb6m3Z7FKpuO219VdIrNe15cGBrSXhw9jo9ztrf0iOgU0W1v3PudYIgtI04Y3x/er3f9csSUoeg7dd6XupL2glo3afT2fne//pqQvaaWXbXhr9p27TuG795n8n89M2mxrHMNznbhAceRGm5QfjXupFYyXtfuc71ABMO3ez+xv9iSg2VBv84/4/wYnY0tbBcXGT7jzqZTMINlgGsGD5e1iSmtnYdt9lNtL2ssqdPvZuB/O+lo+v8Asu96PiY7cXGqx2uLhU0MDjyYTZAJ2wHGD+bPjzLQjd08ylwCN6LBCInKQGvmwq4d/W/gFNQq4d/W/gFNMXoM3DozsS3DyATTe0seGuLXQf3XshzFUq6P0jAwxX6cUUAu3Pc5zgJsfO6d3+Gt/wA9aSHa210BhaGx7g4TOrfb/Vc31GJKauL07pGC6p+LRXS549KpzAZII9XYP5O2verbrqm2spc4CywOcxvchm3ef7PqMQvTyzSwOsYbmg7nbdC7/BvH9VSLcn19wc30vb7I1/P9T3/yprd/1pJSRtjHOcxpBcwgPHcSNw/6Kaz6B+I/KEKtmYLSX2MdVJhobB2kuLdf5LdrUW0gVkkwJGp+ISCixN7G6EGQJ45iJj/OQc/NONVW6sNL7nithfu2gkF/v9Nr3/m/RVoMBGomYlM+tj27XtDmgggEdxq0/wBlOiYgixfdZISIIBo9NGph5bsun1XbYFgaNkxLXNDtXhrv+grqCKKaGNZSxtbd4O1ogSXNkoyUiCSYiheiYCQiBI3LqX//1fU0l8rpJKfqhJfK6SSn6nIkETE9xyFD0jAmxxI76a+RXy2kkp+pPS/lu+8f3JnUk8WvbxwR2M/u/nfRXy4kkp+pW1lrp3uIiNpiNPzuPpKa+V0klP1QkvldJJT9UJL5XSSU/VCg9jnEQ8tHdsAg6z+cF8tJJKfqQVnX3nnTQaD936Kf0z++7/o/+RXy0kkp+ohS/dJucW+BDfAN/d/fb6v9f+QigEAAmSBqeJXywkkp/9n/7RggUGhvdG9zaG9wIDMuMAA4QklNBCUAAAAAABAAAAAAAAAAAAAAAAAAAAAAOEJJTQQ6AAAAAADXAAAAEAAAAAEAAAAAAAtwcmludE91dHB1dAAAAAUAAAAAUHN0U2Jvb2wBAAAAAEludGVlbnVtAAAAAEludGUAAAAASW1nIAAAAA9wcmludFNpeHRlZW5CaXRib29sAAAAAAtwcmludGVyTmFtZVRFWFQAAAABAAAAAAAPcHJpbnRQcm9vZlNldHVwT2JqYwAAAAVoIWg3i75/bgAAAAAACnByb29mU2V0dXAAAAABAAAAAEJsdG5lbnVtAAAADGJ1aWx0aW5Qcm9vZgAAAAlwcm9vZkNNWUsAOEJJTQQ7AAAAAAItAAAAEAAAAAEAAAAAABJwcmludE91dHB1dE9wdGlvbnMAAAAXAAAAAENwdG5ib29sAAAAAABDbGJyYm9vbAAAAAAAUmdzTWJvb2wAAAAAAENybkNib29sAAAAAABDbnRDYm9vbAAAAAAATGJsc2Jvb2wAAAAAAE5ndHZib29sAAAAAABFbWxEYm9vbAAAAAAASW50cmJvb2wAAAAAAEJja2dPYmpjAAAAAQAAAAAAAFJHQkMAAAADAAAAAFJkICBkb3ViQG/gAAAAAAAAAAAAR3JuIGRvdWJAb+AAAAAAAAAAAABCbCAgZG91YkBv4AAAAAAAAAAAAEJyZFRVbnRGI1JsdAAAAAAAAAAAAAAAAEJsZCBVbnRGI1JsdAAAAAAAAAAAAAAAAFJzbHRVbnRGI1B4bEBSAAAAAAAAAAAACnZlY3RvckRhdGFib29sAQAAAABQZ1BzZW51bQAAAABQZ1BzAAAAAFBnUEMAAAAATGVmdFVudEYjUmx0AAAAAAAAAAAAAAAAVG9wIFVudEYjUmx0AAAAAAAAAAAAAAAAU2NsIFVudEYjUHJjQFkAAAAAAAAAAAAQY3JvcFdoZW5QcmludGluZ2Jvb2wAAAAADmNyb3BSZWN0Qm90dG9tbG9uZwAAAAAAAAAMY3JvcFJlY3RMZWZ0bG9uZwAAAAAAAAANY3JvcFJlY3RSaWdodGxvbmcAAAAAAAAAC2Nyb3BSZWN0VG9wbG9uZwAAAAAAOEJJTQPtAAAAAAAQAEgAAAABAAIASAAAAAEAAjhCSU0EJgAAAAAADgAAAAAAAAAAAAA/gAAAOEJJTQQNAAAAAAAEAAAAeDhCSU0EGQAAAAAABAAAAB44QklNA/MAAAAAAAkAAAAAAAAAAAEAOEJJTScQAAAAAAAKAAEAAAAAAAAAAjhCSU0D9QAAAAAASAAvZmYAAQBsZmYABgAAAAAAAQAvZmYAAQChmZoABgAAAAAAAQAyAAAAAQBaAAAABgAAAAAAAQA1AAAAAQAtAAAABgAAAAAAAThCSU0D+AAAAAAAcAAA/////////////////////////////wPoAAAAAP////////////////////////////8D6AAAAAD/////////////////////////////A+gAAAAA/////////////////////////////wPoAAA4QklNBAAAAAAAAAIAADhCSU0EAgAAAAAA1AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOEJJTQQwAAAAAABqAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAThCSU0ELQAAAAAABgABAAAAEThCSU0ECAAAAAAAEAAAAAEAAAJAAAACQAAAAAA4QklNBB4AAAAAAAQAAAAAOEJJTQQaAAAAAAM5AAAABgAAAAAAAAAAAAACKgAAA1UAAAACAFUAVgAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAADVQAAAioAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAQAAAAAAAG51bGwAAAACAAAABmJvdW5kc09iamMAAAABAAAAAAAAUmN0MQAAAAQAAAAAVG9wIGxvbmcAAAAAAAAAAExlZnRsb25nAAAAAAAAAABCdG9tbG9uZwAAAioAAAAAUmdodGxvbmcAAANVAAAABnNsaWNlc1ZsTHMAAAABT2JqYwAAAAEAAAAAAAVzbGljZQAAABIAAAAHc2xpY2VJRGxvbmcAAAAAAAAAB2dyb3VwSURsb25nAAAAAAAAAAZvcmlnaW5lbnVtAAAADEVTbGljZU9yaWdpbgAAAA1hdXRvR2VuZXJhdGVkAAAAAFR5cGVlbnVtAAAACkVTbGljZVR5cGUAAAAASW1nIAAAAAZib3VuZHNPYmpjAAAAAQAAAAAAAFJjdDEAAAAEAAAAAFRvcCBsb25nAAAAAAAAAABMZWZ0bG9uZwAAAAAAAAAAQnRvbWxvbmcAAAIqAAAAAFJnaHRsb25nAAADVQAAAAN1cmxURVhUAAAAAQAAAAAAAG51bGxURVhUAAAAAQAAAAAAAE1zZ2VURVhUAAAAAQAAAAAABmFsdFRhZ1RFWFQAAAABAAAAAAAOY2VsbFRleHRJc0hUTUxib29sAQAAAAhjZWxsVGV4dFRFWFQAAAABAAAAAAAJaG9yekFsaWduZW51bQAAAA9FU2xpY2VIb3J6QWxpZ24AAAAHZGVmYXVsdAAAAAl2ZXJ0QWxpZ25lbnVtAAAAD0VTbGljZVZlcnRBbGlnbgAAAAdkZWZhdWx0AAAAC2JnQ29sb3JUeXBlZW51bQAAABFFU2xpY2VCR0NvbG9yVHlwZQAAAABOb25lAAAACXRvcE91dHNldGxvbmcAAAAAAAAACmxlZnRPdXRzZXRsb25nAAAAAAAAAAxib3R0b21PdXRzZXRsb25nAAAAAAAAAAtyaWdodE91dHNldGxvbmcAAAAAADhCSU0EKAAAAAAADAAAAAI/8AAAAAAAADhCSU0EFAAAAAAABAAAAJY4QklNBAwAAAAADfMAAAABAAAAoAAAAGgAAAHgAADDAAAADdcAGAAB/9j/7QAMQWRvYmVfQ00AAf/uAA5BZG9iZQBkgAAAAAH/2wCEAAwICAgJCAwJCQwRCwoLERUPDAwPFRgTExUTExgRDAwMDAwMEQwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwBDQsLDQ4NEA4OEBQODg4UFA4ODg4UEQwMDAwMEREMDAwMDAwRDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDP/AABEIAGgAoAMBIgACEQEDEQH/3QAEAAr/xAE/AAABBQEBAQEBAQAAAAAAAAADAAECBAUGBwgJCgsBAAEFAQEBAQEBAAAAAAAAAAEAAgMEBQYHCAkKCxAAAQQBAwIEAgUHBggFAwwzAQACEQMEIRIxBUFRYRMicYEyBhSRobFCIyQVUsFiMzRygtFDByWSU/Dh8WNzNRaisoMmRJNUZEXCo3Q2F9JV4mXys4TD03Xj80YnlKSFtJXE1OT0pbXF1eX1VmZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3EQACAgECBAQDBAUGBwcGBTUBAAIRAyExEgRBUWFxIhMFMoGRFKGxQiPBUtHwMyRi4XKCkkNTFWNzNPElBhaisoMHJjXC0kSTVKMXZEVVNnRl4vKzhMPTdePzRpSkhbSVxNTk9KW1xdXl9VZmdoaWprbG1ub2JzdHV2d3h5ent8f/2gAMAwEAAhEDEQA/APU0lgZP1TZk9ff1l2bfX6lfpmis7QIZ6W5lk/2vorSHTdt1Voybj6LnODC4QQ5vp7He36KSm6klymkQDIgxB7a8JKXQxY/SWQdJ9wMaaoiDZcyjfY+du4AkCYkM1KVE7KMhEEmq7nol3N8VGywtbLG+oZHtBjSdT7v3VHHyqcgE1OJ2mHNc1zHCeJZYGP8Acigg9+8fNEgg0RSAQRYNjwYMe9zoLIbE7pBE/u/vKaHUAA6BEuk/c1EQSpJVeqYuRmdOyMXGvOLfdWWV5DZBY4/nt9NzH/5r1SwOkdTx+mDFu6nZbk+ns+0QXEH1H2+p+me9+703+h9NJTrpKlbg5bw7ZmPr3PY8QOAwbH1t9/0bfp/+TTfs/K+ztq+3W72ucTbAkgmQx39VJTeUHve0gNZuHcyBGvmhY+PfVdkWWZDrmXODq63CBWANuxmv530kWz6B+I/KElKa+Z3DbB07yOztFLc3xTeo0DUxED8E1l9VTQ6x4aHODW+bnGGtaPznORo9kEgblj6tu8tFUtH0XbgJG3d9H6X0/wBGiAkgEjaTyPBAbkY+VW19LvUYHgTBHua5v737qsJEEaHQqBBFg2D1D//Q9TVal1xyNzsY1ixo3W7geAHMbt/rPt/1sVlJJV/i1KGObi2V/ZRXIe70d0hzrC59zd/8uxzk97C7F2fZ/VDDDai6NGT6J3n96K9ytJJKYsc5zZc3aZMt54MKvl4xyqb8cPNZeRDwJII9N0jjwVkzBgSewQazfq41bXPILml4cBo0Hb/V2ogkEEbjUIkBIEHYiixxcIY77bDY+x9u0FzidAwQ1rdxe76Tnv8ApIjcdgLT3adJ7CQf4KU3fut/zj/5FMXXjitp44dr+ICJlImyUCEQKA0H7V6uHf1v4BTQ694MOZAIkukH3cbURNXKQcmHVmpzXObYIcWaECWtd/1e9GSSU1ch81VXGm1xrO8Vs+kHAbdjgPinstjLY30rSAIFjZ2e8O3b/wCr6bPpfvqykkq6atBAvtf6VrTa+HFxJb7Aa2urb+axzGb/AGo9n0D8R+UKaFb6hIY1hLTqXhwEEEe2HJKXNNbtSDLhBM+MT/1KFmYTcmtjRta6p2+vc3e2YLPfXLd/tcitfdGtcQSAJHA4dz+cn3W/ufiP/JJwlIEEHZaYRIII33a+NinFq9OWkGwOGxu0SXN3+yTsb+6raE42uia4iDyORD4/zvYiCSBIg9wgZGRJO6YwEIgDbprxP//R9S+aXzWHfnfWhv1gdjUYTH9IFctyCAHF+wO2+p9p/wBL7P6Krtd3WnXEPxqmUy8Bxd7oAd6DtrXu+m7ZvSU3/ml81Uqt6qbaG3UVtrc15yHtfO1wP6Jtc7d7Xf1VcSUsdBJMBRFtbgCLGkO0bBGsjdp/ZUudDwhtrr3Fu0AMPtAAEaN4hJST5pEhv0nAToJgaqFpLGbmyTI0k/8Akmpw1r2jcA4H4kfilWlovWlw5rtGuBMToQdD3UkOofSJiQYBAgxAO1ESSpN81W6pk5OJ0/IycWk5ORUwuqoEy9w4Z+ja9/8AmsVXpfU+oZeDVfkYD8fIfq+l25sD1H0/4Wtn+CYzI2P/ANIkp0/ml81RwOoZmU5ovwLMVrmbtz3AwdP0ZEeavpKW+ag+6qsgWWNYSCQHEAkAta4/9NiIo2aNnuCPxKSlNIdO10wYMdiOylBUQ0QNPDxT7R4fiUlMfVqkt9RsiZEjSBLv82VIEEAgyDqCFCwRtLfaS5snnTc3xU+NBwkp/9L1NV2ZDLMoBtzSx9csqjUkbXus3/8AF21qwoiusEENALRDSANB4BJTWZm0OputGQwtZ7g8DRjXD9Hu/fTm1zMapzrwXNIFtu3R5rDvtG1vu9Pd6diOKqwCAxoDtHCBqPNS2tiNoiZiO4SUVBzXCWmRJHzB2lRb/OP+P8GKQAA00Gp+/UobbGb3O3N2uI2kEGfa0/wSVRYZmTi0V/rLoBkgAFx05ftrDnbGbve9GqfW+tj6nB1bgCxw1BB4hUs3HGU8OZc2qK3V7hDjDyxz9J2/4JiPjurx6WVWXixzdDY6AXFx/dn85yeRHgFE8V6hjiZnJK41D9GX8ilq4d/W/gFNCpewg7XA7vcIIPtIHu0RUxkUhZD7W0vNOw37T6TbDDS7hgcR7vpFFQ7aKro9Ru6OOdNWv7fymMSUqyzaWFpbscZcXOj2x9Jn735qZz7Bexg2emQd8n37v8Htb/ZsUbcLFtY1llYc1khoM6B30gFN+PS+xlj2y+sgtdrIImP+rckpTHuNj2uLdoIFcHUiPdu1+l6jbE9n0D8R+UJhRUHbw0B07p89f/JJXPa1sEtDjqA4xoC3d4pKq9nKzcPKtybbKW3Bz2Nrrewsa1rm+/1Heo4P2btvvp/M3rVex7tvu4I3dp+CQurj6XGh45HPdL1qv3h94/vT5ZDIRFD07MccQiZGyTI2b/xmOxzGjc7cS5usR3Y3+CMgPyaHDSxvshzteANtn/nv3IzXBzQ5pkESCO4KaWQCn//T9TSWZb9Y+kVdTf0qy1wzK2h72em8tALfVH6UM9Nztn5u5Hb1jpr7fRbeDYdwDQ10nYN749v7gSU3ElVq6ngXOpbVcHHJDjTAPuDNbDx+buVpJSk0N8B+CR1BEx5hCYywHb6rnlpEl20TAb+4389JSZMQCIIkecIN+Zj47mtvsZW5/wBEOdEx4aKbH+tWH1naHag+IB/lN+i5EggXRooEomXDY4u3VmABwI+5Oh1SZJc4x7YdGv524bf6yIglSSHffRj0vvyLG001gustscGtaB+c97va1qFV1Lp1zG2U5dFlb42PbY0g7ia27XB35z2OYkpspINOZh5BjHvquJG6K3tdp+97SjJKUkkh2h8hzXljRIc0AGSS3afd+6kpIlJUKxaAdzg+TI7QD+boPzVL3eA+/wD2JKXlMhv9RpneQHGAGgGJhg7fm/TRBMa6nuUk/V//1PSj03pxyznHEpOYW7Tkmtvq7Y2bPW2+pt2exSqbjttfVXSKzXteXBga0l4cPY6Pc7a39IjoFNFtb9z7nWCILSNOGN8f3q93/XLElKHoO3Xel7qS9oJaN2n09n53v/6akL2mll214a/adu07hu/eZ/J/PTNpsaxzDc524QHHkRpuUH417qRWMl7X7nO9QATDt3s/sb/YkoNlQb/OP+P8GJ2NLWwXFxk+486mUzCDZYBrBg+XtYkprZ2HbfZTbS9rLKnT72bgfzvpaPr/ALLvej4mO3Fxqsdri4VNDA48mE2QCdsBxg/mz48y0I3dPMpcAjeiwQiJykBr5sKuHf1v4BTUKuHf1v4BTTF6DNw6M7Etw8gE03tLHhri10H917IcxVKuj9IwMMV+nFFALtz3Oc4CbHzund/hrf8APWkh2ttdAYWhse4OEzq32/1XN9RiSmri9O6Rguqfi0V0uePSqcwGSCPV2D+Ttr3q266ptrKXOAssDnMb3IZt3n+z6jEL08s0sDrGG5oO523Qu/wbx/VUi3J9fcHN9L2+yNfz/U9/8qa3f9aSUkbYxznMaQXMIDx3EjcP+ims+gfiPyhCrZmC0l9jHVSYaGwdpLi3X+S3a1FtIFZJMCRqfiEgosTexuhBkCeOYiY/zkHPzTjVVurDS+54rYX7toJBf7/Ta9/5v0VaDARqJmJTPrY9u17Q5oIIBHcatP8AZTomIIsX3WSEiCAaPTRqYeW7Lp9V22BYGjZMS1zQ7V4a7/oK6giimhjWUsbW3eDtaIElzZKMlIgkmIoXomAkIgSNy6l//9X1NJfK6SSn6oSXyukkp+pyJBExPcchQ9IwJscSO+mvkV8tpJKfqT0v5bvvH9yZ1JPFr28cEdjP7v530V8uJJKfqVtZa6d7iIjaYjT87j6SmvldJJT9UJL5XSSU/VCS+V0klP1QoPY5xEPLR3bAIOs/nBfLSSSn6kFZ195500Gg/d+in9M/vu/6P/kV8tJJKfqIUv3SbnFvgQ3wDf3f32+r/X/kIoBAAJkganiV8sJJKf/ZADhCSU0EIQAAAAAAUwAAAAEBAAAADwBBAGQAbwBiAGUAIABQAGgAbwB0AG8AcwBoAG8AcAAAABIAQQBkAG8AYgBlACAAUABoAG8AdABvAHMAaABvAHAAIABDAEMAAAABADhCSU0EBgAAAAAABwAGAQEAAQEA/+Eg8Wh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8APD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS41LWMwMjEgNzkuMTU0OTExLCAyMDEzLzEwLzI5LTExOjQ3OjE2ICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0RXZ0PSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VFdmVudCMiIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczpwaG90b3Nob3A9Imh0dHA6Ly9ucy5hZG9iZS5jb20vcGhvdG9zaG9wLzEuMC8iIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgeG1wOkNyZWF0ZURhdGU9IjIwMjAtMDktMDdUMTA6NTc6MDErMDg6MDAiIHhtcDpNZXRhZGF0YURhdGU9IjIwMjAtMDktMDdUMTM6NDM6MTYrMDg6MDAiIHhtcDpNb2RpZnlEYXRlPSIyMDIwLTA5LTA3VDEzOjQzOjE2KzA4OjAwIiBkYzpmb3JtYXQ9ImltYWdlL2pwZWciIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MGVkM2U5MzUtYThkZS0zNTQyLTk2NjAtZWFmYTk5NmEyODA2IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOmMxOWMxNDU2LTU2MWYtNjI0NS05NTE3LWJiMGZiZmRmMjcwZCIgeG1wTU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOmMxOWMxNDU2LTU2MWYtNjI0NS05NTE3LWJiMGZiZmRmMjcwZCIgcGhvdG9zaG9wOkNvbG9yTW9kZT0iMyIgcGhvdG9zaG9wOklDQ1Byb2ZpbGU9InNSR0IgSUVDNjE5NjYtMi4xIj4gPHhtcE1NOkhpc3Rvcnk+IDxyZGY6U2VxPiA8cmRmOmxpIHN0RXZ0OmFjdGlvbj0iY3JlYXRlZCIgc3RFdnQ6aW5zdGFuY2VJRD0ieG1wLmlpZDpjMTljMTQ1Ni01NjFmLTYyNDUtOTUxNy1iYjBmYmZkZjI3MGQiIHN0RXZ0OndoZW49IjIwMjAtMDktMDdUMTA6NTc6MDErMDg6MDAiIHN0RXZ0OnNvZnR3YXJlQWdlbnQ9IkFkb2JlIFBob3Rvc2hvcCBDQyAoV2luZG93cykiLz4gPHJkZjpsaSBzdEV2dDphY3Rpb249InNhdmVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjIwNzFiYzZhLTY2NGYtMTk0MS04ZDk0LTM0YWRmOGY2ZGZkMiIgc3RFdnQ6d2hlbj0iMjAyMC0wOS0wN1QxMDo1NzozMiswODowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgc3RFdnQ6Y2hhbmdlZD0iLyIvPiA8cmRmOmxpIHN0RXZ0OmFjdGlvbj0ic2F2ZWQiIHN0RXZ0Omluc3RhbmNlSUQ9InhtcC5paWQ6ZTY0Y2M2NmUtZmVkNS01MTQ3LWFmNjUtNTgxOTE0OTAyMGVjIiBzdEV2dDp3aGVuPSIyMDIwLTA5LTA3VDEzOjQzOjE2KzA4OjAwIiBzdEV2dDpzb2Z0d2FyZUFnZW50PSJBZG9iZSBQaG90b3Nob3AgQ0MgKFdpbmRvd3MpIiBzdEV2dDpjaGFuZ2VkPSIvIi8+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJjb252ZXJ0ZWQiIHN0RXZ0OnBhcmFtZXRlcnM9ImZyb20gYXBwbGljYXRpb24vdm5kLmFkb2JlLnBob3Rvc2hvcCB0byBpbWFnZS9qcGVnIi8+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJkZXJpdmVkIiBzdEV2dDpwYXJhbWV0ZXJzPSJjb252ZXJ0ZWQgZnJvbSBhcHBsaWNhdGlvbi92bmQuYWRvYmUucGhvdG9zaG9wIHRvIGltYWdlL2pwZWciLz4gPHJkZjpsaSBzdEV2dDphY3Rpb249InNhdmVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjBlZDNlOTM1LWE4ZGUtMzU0Mi05NjYwLWVhZmE5OTZhMjgwNiIgc3RFdnQ6d2hlbj0iMjAyMC0wOS0wN1QxMzo0MzoxNiswODowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgc3RFdnQ6Y2hhbmdlZD0iLyIvPiA8L3JkZjpTZXE+IDwveG1wTU06SGlzdG9yeT4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6ZTY0Y2M2NmUtZmVkNS01MTQ3LWFmNjUtNTgxOTE0OTAyMGVjIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOmMxOWMxNDU2LTU2MWYtNjI0NS05NTE3LWJiMGZiZmRmMjcwZCIgc3RSZWY6b3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOmMxOWMxNDU2LTU2MWYtNjI0NS05NTE3LWJiMGZiZmRmMjcwZCIvPiA8cGhvdG9zaG9wOlRleHRMYXllcnM+IDxyZGY6QmFnPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IiAgICAgICAgICAgQSAgICAgIEIgICAgICAgQyAgICAgIEQiIHBob3Rvc2hvcDpMYXllclRleHQ9IiAgICAgICAgICAgQSAgICAgIEIgICAgICAgQyAgICAgIEQiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJbIDAsMCwgICAwLDEsICAgMSwxLCAgIDEsMCBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iWyAwLDAsICAgMCwxLCAgIDEsMSwgICAxLDAgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSLpu5jorqTlgLzvvJoiIHBob3Rvc2hvcDpMYXllclRleHQ9Ium7mOiupOWAvO+8miIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkEgWyAwICwgMCBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iQSBbIDAgLCAwIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iQiBbIDAgLCAxIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJCIFsgMCAsIDEgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJDIFsgMSAsIDEgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IkMgWyAxICwgMSBdICIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkQgWyAxICwgMCBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iRCBbIDEgLCAwIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iICAgICAgICAgICBEICAgICAgQSAgICAgIEIgICAgICBDIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSIgICAgICAgICAgIEQgICAgICBBICAgICAgQiAgICAgIEMiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJbIDEsMCwgICAwLDAsICAgMCwxLCAgIDEsMSBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iWyAxLDAsICAgMCwwLCAgIDAsMSwgICAxLDEgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSLlt6bml4vovawgOTAg5bqm77yaIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSLlt6bml4vovawgOTAg5bqm77yaIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iQSBbIDAgLCAwIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJBIFsgMCAsIDAgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJCIFsgMCAsIDEgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IkIgWyAwICwgMSBdICIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkMgWyAxICwgMSBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iQyBbIDEgLCAxIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iRCBbIDEgLCAwIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJEIFsgMSAsIDAgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSIgICAgICAgICAgIEIgICAgICAgQyAgICAgIEQgICAgICBBIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSIgICAgICAgICAgIEIgICAgICAgQyAgICAgIEQgICAgICBBIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iWyAwLDEsICAgMSwxLCAgIDEsMCwgICAwLDAgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IlsgMCwxLCAgIDEsMSwgICAxLDAsICAgMCwwIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0i5Y+z5peL6L2sIDkwIOW6pu+8miIgcGhvdG9zaG9wOkxheWVyVGV4dD0i5Y+z5peL6L2sIDkwIOW6pu+8miIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkEgWyAwICwgMCBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iQSBbIDAgLCAwIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iQiBbIDAgLCAxIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJCIFsgMCAsIDEgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJDIFsgMSAsIDEgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IkMgWyAxICwgMSBdICIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkQgWyAxICwgMCBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iRCBbIDEgLCAwIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iICAgICAgICAgICBDICAgICAgRCAgICAgICBBICAgICAgQiIgcGhvdG9zaG9wOkxheWVyVGV4dD0iICAgICAgICAgICBDICAgICAgRCAgICAgICBBICAgICAgQiIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IlsgMSwxLCAgIDEsMCwgICAwLDAsICAgMCwxIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJbIDEsMSwgICAxLDAsICAgMCwwLCAgIDAsMSBdICIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IuW3piDmiJYg5Y+zIOaXi+i9rCAxODAg5bqm77yaIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSLlt6Yg5oiWIOWPsyDml4vovawgMTgwIOW6pu+8miIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkEgWyAwICwgMCBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iQSBbIDAgLCAwIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iQiBbIDAgLCAxIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJCIFsgMCAsIDEgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJDIFsgMSAsIDEgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IkMgWyAxICwgMSBdICIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkQgWyAxICwgMCBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iRCBbIDEgLCAwIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iICAgICAgICAgICBCICAgICAgQSAgICAgICBEICAgICAgQyIgcGhvdG9zaG9wOkxheWVyVGV4dD0iICAgICAgICAgICBCICAgICAgQSAgICAgICBEICAgICAgQyIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IlsgMCwxLCAgIDAsMCwgICAxLDAsICAgMSwxIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJbIDAsMSwgICAwLDAsICAgMSwwLCAgIDEsMSBdICIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IuS4iuS4i+e/u+i9rO+8miIgcGhvdG9zaG9wOkxheWVyVGV4dD0i5LiK5LiL57+76L2s77yaIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iQSBbIDAgLCAwIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJBIFsgMCAsIDAgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJCIFsgMCAsIDEgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IkIgWyAwICwgMSBdICIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkMgWyAxICwgMSBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iQyBbIDEgLCAxIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iRCBbIDEgLCAwIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJEIFsgMSAsIDAgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSIgICAgICAgICAgIEQgICAgICBDICAgICAgIEIgICAgICBBIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSIgICAgICAgICAgIEQgICAgICBDICAgICAgIEIgICAgICBBIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iWyAxLDAsICAgMSwxLCAgIDAsMSwgICAwLDAgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IlsgMSwwLCAgIDEsMSwgICAwLDEsICAgMCwwIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0i5bem5Y+z57+76L2s77yaIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSLlt6blj7Pnv7vovazvvJoiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJBIFsgMCAsIDAgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IkEgWyAwICwgMCBdICIvPiA8cmRmOmxpIHBob3Rvc2hvcDpMYXllck5hbWU9IkIgWyAwICwgMSBdICIgcGhvdG9zaG9wOkxheWVyVGV4dD0iQiBbIDAgLCAxIF0gIi8+IDxyZGY6bGkgcGhvdG9zaG9wOkxheWVyTmFtZT0iQyBbIDEgLCAxIF0gIiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJDIFsgMSAsIDEgXSAiLz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJEIFsgMSAsIDAgXSAiIHBob3Rvc2hvcDpMYXllclRleHQ9IkQgWyAxICwgMCBdICIvPiA8L3JkZjpCYWc+IDwvcGhvdG9zaG9wOlRleHRMYXllcnM+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw/eHBhY2tldCBlbmQ9InciPz7/4gxYSUNDX1BST0ZJTEUAAQEAAAxITGlubwIQAABtbnRyUkdCIFhZWiAHzgACAAkABgAxAABhY3NwTVNGVAAAAABJRUMgc1JHQgAAAAAAAAAAAAAAAAAA9tYAAQAAAADTLUhQICAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABFjcHJ0AAABUAAAADNkZXNjAAABhAAAAGx3dHB0AAAB8AAAABRia3B0AAACBAAAABRyWFlaAAACGAAAABRnWFlaAAACLAAAABRiWFlaAAACQAAAABRkbW5kAAACVAAAAHBkbWRkAAACxAAAAIh2dWVkAAADTAAAAIZ2aWV3AAAD1AAAACRsdW1pAAAD+AAAABRtZWFzAAAEDAAAACR0ZWNoAAAEMAAAAAxyVFJDAAAEPAAACAxnVFJDAAAEPAAACAxiVFJDAAAEPAAACAx0ZXh0AAAAAENvcHlyaWdodCAoYykgMTk5OCBIZXdsZXR0LVBhY2thcmQgQ29tcGFueQAAZGVzYwAAAAAAAAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAABJzUkdCIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWFlaIAAAAAAAAPNRAAEAAAABFsxYWVogAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAABvogAAOPUAAAOQWFlaIAAAAAAAAGKZAAC3hQAAGNpYWVogAAAAAAAAJKAAAA+EAAC2z2Rlc2MAAAAAAAAAFklFQyBodHRwOi8vd3d3LmllYy5jaAAAAAAAAAAAAAAAFklFQyBodHRwOi8vd3d3LmllYy5jaAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABkZXNjAAAAAAAAAC5JRUMgNjE5NjYtMi4xIERlZmF1bHQgUkdCIGNvbG91ciBzcGFjZSAtIHNSR0IAAAAAAAAAAAAAAC5JRUMgNjE5NjYtMi4xIERlZmF1bHQgUkdCIGNvbG91ciBzcGFjZSAtIHNSR0IAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAsUmVmZXJlbmNlIFZpZXdpbmcgQ29uZGl0aW9uIGluIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENvbmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHZpZXcAAAAAABOk/gAUXy4AEM8UAAPtzAAEEwsAA1yeAAAAAVhZWiAAAAAAAEwJVgBQAAAAVx/nbWVhcwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAo8AAAACc2lnIAAAAABDUlQgY3VydgAAAAAAAAQAAAAABQAKAA8AFAAZAB4AIwAoAC0AMgA3ADsAQABFAEoATwBUAFkAXgBjAGgAbQByAHcAfACBAIYAiwCQAJUAmgCfAKQAqQCuALIAtwC8AMEAxgDLANAA1QDbAOAA5QDrAPAA9gD7AQEBBwENARMBGQEfASUBKwEyATgBPgFFAUwBUgFZAWABZwFuAXUBfAGDAYsBkgGaAaEBqQGxAbkBwQHJAdEB2QHhAekB8gH6AgMCDAIUAh0CJgIvAjgCQQJLAlQCXQJnAnECegKEAo4CmAKiAqwCtgLBAssC1QLgAusC9QMAAwsDFgMhAy0DOANDA08DWgNmA3IDfgOKA5YDogOuA7oDxwPTA+AD7AP5BAYEEwQgBC0EOwRIBFUEYwRxBH4EjASaBKgEtgTEBNME4QTwBP4FDQUcBSsFOgVJBVgFZwV3BYYFlgWmBbUFxQXVBeUF9gYGBhYGJwY3BkgGWQZqBnsGjAadBq8GwAbRBuMG9QcHBxkHKwc9B08HYQd0B4YHmQesB78H0gflB/gICwgfCDIIRghaCG4IggiWCKoIvgjSCOcI+wkQCSUJOglPCWQJeQmPCaQJugnPCeUJ+woRCicKPQpUCmoKgQqYCq4KxQrcCvMLCwsiCzkLUQtpC4ALmAuwC8gL4Qv5DBIMKgxDDFwMdQyODKcMwAzZDPMNDQ0mDUANWg10DY4NqQ3DDd4N+A4TDi4OSQ5kDn8Omw62DtIO7g8JDyUPQQ9eD3oPlg+zD88P7BAJECYQQxBhEH4QmxC5ENcQ9RETETERTxFtEYwRqhHJEegSBxImEkUSZBKEEqMSwxLjEwMTIxNDE2MTgxOkE8UT5RQGFCcUSRRqFIsUrRTOFPAVEhU0FVYVeBWbFb0V4BYDFiYWSRZsFo8WshbWFvoXHRdBF2UXiReuF9IX9xgbGEAYZRiKGK8Y1Rj6GSAZRRlrGZEZtxndGgQaKhpRGncanhrFGuwbFBs7G2MbihuyG9ocAhwqHFIcexyjHMwc9R0eHUcdcB2ZHcMd7B4WHkAeah6UHr4e6R8THz4faR+UH78f6iAVIEEgbCCYIMQg8CEcIUghdSGhIc4h+yInIlUigiKvIt0jCiM4I2YjlCPCI/AkHyRNJHwkqyTaJQklOCVoJZclxyX3JicmVyaHJrcm6CcYJ0kneierJ9woDSg/KHEooijUKQYpOClrKZ0p0CoCKjUqaCqbKs8rAis2K2krnSvRLAUsOSxuLKIs1y0MLUEtdi2rLeEuFi5MLoIuty7uLyQvWi+RL8cv/jA1MGwwpDDbMRIxSjGCMbox8jIqMmMymzLUMw0zRjN/M7gz8TQrNGU0njTYNRM1TTWHNcI1/TY3NnI2rjbpNyQ3YDecN9c4FDhQOIw4yDkFOUI5fzm8Ofk6Njp0OrI67zstO2s7qjvoPCc8ZTykPOM9Ij1hPaE94D4gPmA+oD7gPyE/YT+iP+JAI0BkQKZA50EpQWpBrEHuQjBCckK1QvdDOkN9Q8BEA0RHRIpEzkUSRVVFmkXeRiJGZ0arRvBHNUd7R8BIBUhLSJFI10kdSWNJqUnwSjdKfUrESwxLU0uaS+JMKkxyTLpNAk1KTZNN3E4lTm5Ot08AT0lPk0/dUCdQcVC7UQZRUFGbUeZSMVJ8UsdTE1NfU6pT9lRCVI9U21UoVXVVwlYPVlxWqVb3V0RXklfgWC9YfVjLWRpZaVm4WgdaVlqmWvVbRVuVW+VcNVyGXNZdJ114XcleGl5sXr1fD19hX7NgBWBXYKpg/GFPYaJh9WJJYpxi8GNDY5dj62RAZJRk6WU9ZZJl52Y9ZpJm6Gc9Z5Nn6Wg/aJZo7GlDaZpp8WpIap9q92tPa6dr/2xXbK9tCG1gbbluEm5rbsRvHm94b9FwK3CGcOBxOnGVcfByS3KmcwFzXXO4dBR0cHTMdSh1hXXhdj52m3b4d1Z3s3gReG54zHkqeYl553pGeqV7BHtje8J8IXyBfOF9QX2hfgF+Yn7CfyN/hH/lgEeAqIEKgWuBzYIwgpKC9INXg7qEHYSAhOOFR4Wrhg6GcobXhzuHn4gEiGmIzokziZmJ/opkisqLMIuWi/yMY4zKjTGNmI3/jmaOzo82j56QBpBukNaRP5GokhGSepLjk02TtpQglIqU9JVflcmWNJaflwqXdZfgmEyYuJkkmZCZ/JpomtWbQpuvnByciZz3nWSd0p5Anq6fHZ+Ln/qgaaDYoUehtqImopajBqN2o+akVqTHpTilqaYapoum/adup+CoUqjEqTepqaocqo+rAqt1q+msXKzQrUStuK4trqGvFq+LsACwdbDqsWCx1rJLssKzOLOutCW0nLUTtYq2AbZ5tvC3aLfguFm40blKucK6O7q1uy67p7whvJu9Fb2Pvgq+hL7/v3q/9cBwwOzBZ8Hjwl/C28NYw9TEUcTOxUvFyMZGxsPHQce/yD3IvMk6ybnKOMq3yzbLtsw1zLXNNc21zjbOts83z7jQOdC60TzRvtI/0sHTRNPG1EnUy9VO1dHWVdbY11zX4Nhk2OjZbNnx2nba+9uA3AXcit0Q3ZbeHN6i3ynfr+A24L3hROHM4lPi2+Nj4+vkc+T85YTmDeaW5x/nqegy6LzpRunQ6lvq5etw6/vshu0R7ZzuKO6070DvzPBY8OXxcvH/8ozzGfOn9DT0wvVQ9d72bfb794r4Gfio+Tj5x/pX+uf7d/wH/Jj9Kf26/kv+3P9t////7gAhQWRvYmUAZEAAAAABAwAQAwIDBgAAAAAAAAAAAAAAAP/bAIQAAgICAgICAgICAgMCAgIDBAMCAgMEBQQEBAQEBQYFBQUFBQUGBgcHCAcHBgkJCgoJCQwMDAwMDAwMDAwMDAwMDAEDAwMFBAUJBgYJDQoJCg0PDg4ODg8PDAwMDAwPDwwMDAwMDA8MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwM/8IAEQgCKgNVAwERAAIRAQMRAf/EARMAAQACAgMBAQEAAAAAAAAAAAAGBwMFAgQIAQkKAQEAAgMBAQAAAAAAAAAAAAAAAwQBAgUGBxAAAAUDAAcHBAIBAwUBAQAAAgMEBQYAAQcgMFARMhMIEEASMxU1NjEUNDcWFwlgcCGAkCMYOCIZEQACAgECAwMECwkNBgMEBgsBAgMEBREGACESMRMHQSIUdRAgMFFhMrOUtNQVUHKS0iMzdDU2QHGBkaHBQlKy0zQWCLHRkySVN3BDJWKCU3PhVIV2theAooPD40SEpLUmdxIAAQIEAwMJBgQEBAYDAQAAARECACESAzFBcVBRYRAgMECBkSITBKGxwdEyQvBSciPhYpIzgrIUBWBwgJDxouJDg5P/2gAMAwEBAhEDEQAAAP3wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB1dJeGueGNthNX1UFruyQ9jeMAAADCfTmDGZgAAAAAAAAAAAAAAAAAcTCZjkcAcwAAADEDkczCZT6AAAAAAAAAAAAAACueR6LVQW/mFg9fz8B5HflPQ5Uwv8AIAAAA8RF/GciZFT1cAAAAAAAAAAAAAAAAAChyPEpLUKlJAWsAAAAeUSWkOPRhRB6LNkAAAAAAAAAAAAAAADrZxqsZzAAAAAAxHbNgcDKAAAAAAAAAAAAAAAAAAYjHnHQxn4cTmAAAADEds2BxOZ9AAPGZ56LgL6JsbA75UZZRNAAAAAAAAAV1YjlleTcgAAAAAFazadOTS1qswAAAAA0pEztk7AAAAAAAAAAABXViOW15NwAAAAAAaLKmr1b0FQsgAAfimeDD1KWkerySmyPKp62PSxIAAAAAAAACurEcsrybkAAAAAAi22OpJrNIdwAAAAB+fzP4q3/AD1xQ3v2BrdKSlrHjc9QFMF1ELKKM52D3IUyUUds9GHg892l3gAArqxHLa8m4AAAAAANORKxHYteQAAD8yTxeelj24bs1xPCgC0C2yRAAAAAAAAFdWI5ZXk3IAAAAABFtsdSTWaQ7gAAAAD8+sb/AI8dfyFoRT/r5zvS10enTxAevChSYG/PKxYR1T9HyhykTOWoeAz9Ji6QAAV1YjlteTcAAAAAAGnIlYjsWvIAAAAAAAAAAAAAAAAK6sRyyvJ0LXm91B1+9pdAAAAAi22OpJrNIdwAAAABpSJHbJ4AAAAAAAAAAACurEctrybgAAAAAA05ErEdi15AAAAAAAAAAAAAAAABXViOWV5KL9F8AjXS6VS+k2k1O/7C+f8A06SU7wAAAi22OpJrNIdwAAAAAAAAAAAAAAAAAAK6sRyyvJtCL3ubJ6PS09ir9NxXtZQQnpcqv+txIV0OVK6PR9AeV9ppyJWI7FryAAAAAAV1x/RWL2POgeZz0mZQRYgZcoNEUIemAAACurEcsrya27x/K/ufnfo3yfvI7bpUv3/M+0Pnf1MaazUiV/mSal0dPYq9jTeYc/qRbbHUk1mkO4AAAAAAAAAAAAAAAAAAFdWI5bXk3BCOnyNXPW7ccuTXO2gtZ9JM+u/52fWviF6ea9bR/pPJ2Tx+97e+bfWdORKxHYteQAAAAADyl4P6r6e9v8x78sA/OY/QI24KVM5cYIUeTD3QAAAV1YjlleSM9Xjfn79U+Lz7mdiK3efZ/F9B7R+dfVMW2sF6nFxZxmxtizj5nWzeJ6GNZz1JNZpDuAAAAAAAAPHB6tN0Dx6S49KAHjj519h9jfRfj/1gfl8fpWbYAAFdWI5bXk42akC6vFwbaSKnfjV3nWVxvQbevarPs8HyF7/5leHm/Vx63Sj1mp7b+b/WNORKxHYteQAAAAAAAAAAAAAAAAV1YjlleSM9Xi/n/wDVPjPoXyvtK86vF9e+A+mWByu18I1coa6av9ZktLoRS/zJ3y+zF9sdSTWaQ7gAAAAAAACoixTcApsnhKAfCLUervbVHuyQCiy7zKAACurEctrybg1M9bbQWY7bo59d93WtjqSReGvpfyPaRT/GJdx+H6P8r9a7FfuxKxHYteQAAAACmQdIqo9kEBKmNmehAeezZFyndKAJYRAk5bYABXViOWV5NptrrJq/1nZwT5MbD4cAAdfePtaSRjbHUk1mkO4AAAAAAA0Z2jWGIlQIqakmp2ysyWnSJYRI751DKSQAArqxHLa8m4AAABrJq9R9/wA1L+f04ZP8xsLn+83NP1kSsR2LXkAAAAA88EhIKQw9amiPPJdpvCcFWkTJ2WGVaQo7x9L4AAK6sRyyvJuQAAAAACLbY6kms0h3AAAAAAAFHFwHnc+Hok2J5kI8Sw9IH5/E1JSeozzSdM7JMy5gACurEctrybgAAAAAA05ErEdi15AAAAAAAAAAAAAAAABXViOWV5NyAAAAAARbbHUk1mkO4AAAAAAAhZKCNEmNESg0pHTaEoI2a0xE7NKaokhDydgAFdWI5bXk3AAAAAABpyJWI7FryAAAAAAAAAAAAAAAACurEcsrybkAAAAAAi22OpJrNIdwAAAAAAAAAAAAAAAAAAK6sRy2vJuAAAAAADTkSsR2LXkAAAAAAAAAAAAAAAAHni/Wu6hZ3IAAAAABFtsdSTWaQ7gAAAAAAAAAAAAAAAAAAeeL9a76FncAAAAAAGnIlYjsWvIAAAAAAAAAAAAAAAIgdw6hsiMEgO2AAAcTXbYjWuZEYTsZbTDcAAAAAAAAAAAAAAAAAA0hnMp1jRmxO2AAADFlx03442a7beaDqV7W0nrfWAAAAAAAAAAAAAAB58Ned8uQr872+vbl0AAA4Ghk16tOeRHWOR1y3wAAAAAAAAAAAAAAAAAUQVuXgSs8/lhbY302gAAAgM8Ug8h7jeWqWhp35Vf5cVo9S2e95Pt7xAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADGfTicj4D6cTkfAfTicj4D6cTkfD6Dicj4fQcTkfD6DicjicgcTkcTkDicjicgfDkcgAAADicT6DicjicgcTkcTkDicjicgfD6cTkD4fTicgfD6cTkfAfTicj4D6cTkfAfTicjicDmYjKcT4czEZTiDkYjKcD6cjEZTgfTkYzIcD6cjGZDgcgcDIYzmDgZDGcwcDIYzIfDiZTmAAAAYzGcj6YzIYzmDgZDGcwcDIYzmDgZDGZD4cTmYjKcT4czEZTifDmYjKcQcjEZTgfTkYjKYzsgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGnOuCQGrOZsQAAACLG2OJ2TpG5AAAAAAAAAAAAAAAPLp2ifkvKMLOLPAAAAPMBapGDgb0tcAAAAAAAAAAAAAAAAAFPlUkhL6IYYy0gAAADz4S86BvSKF4gAAAAAAAAAAAAAAGA1ptyLmc6xtAAAADGao2ptTVG8AAAAAAAAAAAAAAAAAABrtsdLXOYAAAAAAGwzjsYyAAB41POuN7mzp7MJEQAnhBidESOZKyvSVG5AAAAAAK6sRyyvJuQAAaixW29ezhMwAK6l06smtn1pQAAAAAAAAAAAAAAAAAAK6sRy2vJuAAAAAADRZUverehaFkAAD8deh53wBiT0fQ73tst88ZndOme5jzKWYXyfmYeyT1+SUAAAAAFdWI5ZXk3IABEr/ADNJZpbCKzvKtzeVrYAim2OpJrNodwAAAAB4KPyfJAf0OFJmsJMeVi1iJno0so3QAAAAAABXViOW15NwAAAAAAaciViOxa8gAAH5KYz4Jzj1ifoYfCElgHWLTNQYScHnMsktMkwAAAAAK6sRyyvJuQADrbx0h6byNh8jty/m9fMACLbY6kms0h3AAAAAHgU/I/0Hz7d8T237rRW6YLXNifloXMag9YnpUlwAAAAAABXViOW15NwAAAAAAaciViOxa8gAAAAAAAAAAAAAAAArqxHLK8m5AAIvd52tnr9ffSdcnt9jGQBFtsdSTWaQ7gAAAACDFNs7hi9zGfTYkVM5sj6bAAAAAAAAFdWI5bXk3AAAAABjzrkxtpyJWI7FryAAAAAAAAAAAAAAAACurEcsrybkAAiV/mV71+HLKHSn3I7uUAEW2x1JNZpDuAAAAAAAAAAAAAAAAAABXViOW15NwfnB9f8Ahk35vV3ta3d3mvXW7wPS/DXSRR63R+G9rXIze5s45fZ1hErEdi15AAAAAAKAJSWsDS1rsdp9CedXhCtSHl9AiBAi7AAACurEcsrybkAAEbuUOzpvta1zMACLbY6kms0h3AAAAAAAAAAAAAAAAAAArqxHLa8m4PzR+yfBt9Vt9nSTR2an6RfH/uuhtU9PZpxu5Q39S9G7lDd1rU153X68M8SsR2LXkAAAAAA80knLvBWPE9NrobFv+h8iKbKxPWQKsNOXWAAAV1YjlleTcgAA1k1fPrv3I5QAIttjqSazSHcAAAAAAAAfnOfoedkFDeV93dfpfF96WAdA8Cn6CmQHkg9TGxAABXViOW15Nux4V+nfIPU3i/oPFjx3775n+hXyf7X9Z6u8VX9zz0oo9HXTQfcZsLk9vTkSsR2LXkAAAAAAAAAAAAAAAAFdWI5ZXk3IABg200dmpI6l4AARbbHUk1mkO4AAAAAAAA84l+mxBXXH9DvLVKU3uWOseXz1SAedy9zYgAArqxHLa8lbdj5RVnZ8pRnsLmLbX2B4D6bdnm/VgAADTkSsR2LXkAAAAAoQmxqzzye3ytCpiBns82J4OLzLwO8ecy8ioDsl4AAFdWI5ZXk3IAId0OX5B9/8zrnr8O0OJ6D3B82+s5ddwBFtsdSTWaQ7gAAAAAADEcjgcjIDCDMDEcziZDiDicDOAAV1YjlteToWvOcWsoq+lH0AAAA05ErEdi15AAAAAKGLkKiK2PTR0jz2doukn55TJyTIsEro7p0SMl2AAFdWI5ZXk3IAPI3vfmswo9Cqu35+R1Lt0eb9ZafE9CAIttjqSazSHcAAAAAAAUIaQ6BhPVZ1T85i7CeF9nk4kB1z0yeeS3CgCXF9gAFdWI5bXk3AAAAAABpyJWI7FryAAAAAAAAAAAAAAAACurEcsrybkAHmH2ngNrFNSXo/KdPeP094v6BcnnvUACLbY6kms0h3AAAAAAAFbG7K4LRNWTgo86hMiySNFfnXLtNKRU35FC0AACurEctrybgAAAAAA05ErEdi15AAAAAAAAAAAAAAAABXViOWV5NyAdfaCgPQfEK57tmDdeQesPC/R7W4fogBFtsdSTWaQ7gAAAAAAAAAAAAAAAAAAVtYimNeXcAAAAAAGnIlYjsWvIAAAAAAAAAAAAAAAAPOvQrXjz7O5AOGdau6nzWf8/3O1i6YAAEW2x1JNZpDuAAAAAAAAAAAAAAAAAABQV6vctGxuAAAAAADTkSsR2LXkAAAAAAAAAAAAAAAHHGfhgy1zDG31jlnAAAAGE+mcz5x2sZygAAAAAAAAAAAAAAAAA6kcukrXvmXftc+LU+jtLFXazVQAABqtsV/rmXGQkRCC0AAAAAAAAAAAAAAACseJ6bVxWuvpJaHb8xV3D9Pvetw+11uGAABhYh02lSUbFiHXJkdwtwAAAAAAAAAAAAAAAAAEXpdOG8zsyC7Q3VmlFqHU7l7n9Xp8YAAAROXTqUrEiJcR8rg9KgAAAAAAAAAAAAAAA4YzzzgfGfmHLOAAAABiMoMRlAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMBzMZzPh8OZjMhxB9OBkOB9PpwMhwPp9OBkOB9PpwOZwOYOBkMZzBwMhjOYOBkMZzPh8MhzAAAAMZwOQOBkMZzBwMhjOYOBkMZzBwOZwOZ8PhzMZzPh8OZjMhxPhzMZzPh8OZjOZxPp9OBkMZ2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAeTjfGlPTRTRYZNQAAACi/Me5sfs+cr3kegl3R5MX53Uun0viwAAAAAAAAAAAAAAAABASLHYLfKDLAJ4AAAAVHwPWzPocaJUutLb3JjFTp2h2fMAAAAAAAAAAAAAAAYTSm+IwcTKd4AAAAx426Uc28lg56SdfSTZTVgAAAAAAAAAAAAAAAABGDQHdO8Rc+k8AAAANLXuYdN9nvrl1262sknucwAAVAQDGZbnF1GY6p2jqmYygHWOyAAAAAACBzxyOCTcgAAEWtea5NpPV9GAINJri31n0EgAAAAAAAAAAAAAAAAAAELm03cO+4AAAAAANQRGxFY1eUAAD8Uzw5f4frfaH1NzvRWqeYywyqz3YefzOelDwsXse0SSgAAAAArqxHLK8m5AB5j9n4Cvetxc+u8t43l7J5Pp7h4/1cAQnfXqya2BBIAAAAAPNR+e20VhaT+w865DSkpIub0hxcZb4AAAAAAAK6sRy2vJuAAAAAADRZVnbgumnOAAB+Lp+dGNvY5+oOdeRQhbhmJsSgjBZR4QLuPURJgAAAAAV1YjlleTcgA8YfRPllSd7zXpbyHuqV9D5b2982+tSCpdAEW2x1JNZpDuAAAAAPz7Py56PnN1rJ+plDv7s87HoU/Gk9VFZH6THqgnAAAAAAABXViOW15NwAAAAAAaciViOxa8gAAAAAAAAAAAAAAAArqxHLK8m5AB549b4iAdXi2dx+95y9b4j198/wDp1j8juACLbY6kms0h3AAAAAHko/PFmfsfooQgnJ0SAkqJeZidkgAAAAAAAK6sRy2vJuAAAAAADTkSsR2LXkAAAAAAAAAAAAAAAAFdWI5ZXk3IAKt7fnvDn0v5JPeX19hDY9x/NfreXXYARbbHUk1mkO4AAAAAAAAAAAAAAAAAAFdWI5bXk3AAAAItd59I+l8lMOf09Bj5hZnM+iyKj7SJWI7FryAAAAAAVeWgAashRZINAaAnwBqYLe2nqAAAV1YjlleTcgAEet0ovd59icnt5ddgAIttjqSazSHcAAAAAAAAAAAAAAAAAACurEctrybgj1ujIal6L3efn12kNS8NZNX8RfSvk095fX4Z1V/N+n/IfXPkPUiViOxa8gAAAAAH5Tn6sAHlY9EkgB52IYevAYNZPHnzr6/7K+j/ABwAACurEcsrybkAA0Fqnv6twAACLbY6kms0h3AAAAAAAAHncscsEFOknJ2AeRj1yAVZwvUWn3fLgAAV1YjlteTjZqQTqcbVT15VR6MVvcyzeL6LcV7VcdfheL/ovyu5PP8Ap4td5+TXf2782+s6ciViOxa8gAAAAAEcJGAdcjhKwa86pugYtd8ONu1vF9AABXViOWV5NyADERi/zN3Wt7GGwAAIttjqSazSHcAAAAAAAAeVyyC4AeeCzicgH5yH6NgHmfxX0r0J675/tJ6oAArqxHLa8m4IT0uR1ZI/uA31W72NJO1pJ+ef1j4pefm/WUH6fx9jcjue3vm31nTkSsR2LXkAAAAArU5H0jxdB0isDAXGCnDvm7JyUkb06ZtyxQACurEcsrybkAEVuUNLbo6uetZHH7+whnAAi22OpJrNIdwAAAAAAAAAAAAAAAAAAK6sRyyvJtCPXKEgp39fLB29Jc2u2UGitU617HBj1ulJKd+5PPeo05ErEdi15AAAAAPPZiI8bMvo3p5OJwWaSwogrc356gPKhVhcBLS8QACurEcsrybkAEZt0Y7e5+2r2dtXs7ytcAAi22OpJrNIdwAAAAAABTxOCtShT3aDzkYz0iDxieiiOlzFAmmIoW4XMAAV1YjlteTcAAAAAAGnIlYjsWvIAAAABFCVnQMhnMh0TvHSO6DpHw7wOid4ipKgACurEcsrybkAGErHu+cx51tjg+lAAEW2x1JNZpDuAAAAAAAKWLcKQIaXwTYqw0ZOCbnl0t4gpfxTxBTsklLtAAK6sRy2vJuAAAAAADTkSsR2LXkAAAAAAAAAAAAAAAAFdWI5ZXk3IAIX0eXg3i0FulaPD9F2dJAAIttjqSazSHcAAAAAAAAAAAAAAAAAACurEctrybgAAAAAA05ErEdi15AAAAAAAAAAAAAAAAB526Fa8OfZ3IAMRlNLZqbqtbAAEW2x1JNZpDuAAAAAAAAAAAAAAAAAAB57v1rsoWdwAAAAAAaciViOxa8gAAAAAAAAAAAAAAAA1u2OnrnMAD4fQAAAMsucbPXIAAAAAAAAAAAAAAAAAAGgk14x7dkAAAAAA4GCTWRR7AAAAAAAAAAAAAAADzaSc+llnnkvnfXSS6AAAYsohLpS9Gx6POuaIip6jAAAAAAAAAAAAAAAAABSpwMpcxShvdsdybQAAAQGeKE0LNzldF+lGHowAAAAAAAAAAAAAAAGsNmYDrHeOQAAAANabI651TZAAAAAAAAAAAAAAAAAA6BFSVnVMx1TeAAAAxZR7Dkdk5GtJWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//aAAgBAgABBQD/AHwMMCWE44skJigsu9AVEjsWYEwP+pXdIYrD4TgI1yI0xQIdx3akhhAmu17Jf9T7r7tr7r7tpC8rXEBsIasNrD2OHytda2+lJYbA2ELytcDiUeZrRobEhWJRpTe8B8rXncOwheVrgcSjzNYK2+zkpTqruykpQf3gPla87h2ELyqLSGDsYWIu+rBxKPM2OHytedw7CF5VAFYQUbKFbctpSF2WR5KeFchMRm6gHEo8zY4fK7HJOFOfX2pnKAkOGDtb2o9bdSzo0dyosnNA7R8tGR2HcOtPeSylvdBeVTeWI09/uoTlequNIXJzGdKUoTE3YlLCYYpEJGYuKCWd6UbYtYWVYugcSjzNjh8rsd991KggolMM0QSlRYzALCwBtdJexCVPdQamTgTluyPmKEgfCTJvwuw7h1prS4KSiTLmF9zF5VMn5tr7qXLLlGJjbmFSU6xaMkuxg1BVijDDAFnrzCfGaMYVbweIdGqAlJxX33BxKPM724OpYXDVh8qiVPKCtK5ypzVEBEJQYSSedYaQ8/mWqP3tZc5OZaABknIHdPKiBClArBR9h3DsIXlUyfm0+JlRp5agtvTOzoJeb2AO5wrmkmCUq7WUnHDOErtuSUDiUeZ3sptILTklWKBqg+V2HqjD70UsEABiswYewo0RQyhJXhP/ABlDX8aRBp3Ufe3UEXKFR3DsIXlUEVw3+7Or7o6hCuK+mMwQ7UDiUeZscPlakhQYQIEpVhsselSqxaosQVh1jBUdw7CF5WuBxKPM2OHytedw7CF5WuBxKPM2OHytedw7CF5WuBxKPM2OHytedw7CMNDcnXA4lHmbHLNDYnXncOzhAuG/be977UvbdQhhDQzAgq55dh3F/wABVFCBsQA7gv8Acir7kVfcir7kVfcir7kVfciqykVHK7jts4IrWvzg1zg1zg1zg1zg1zg1zg1zg0YpBcLygPXDNNvcX2J33a0Ilae5Qik5fB/3RbBve2vva9tTYW/vQvK0+R/4dIkvxjVF+Aexw+VrrUq8Hg02lOmPUuDmauH3kXlaSE0sNXX3KoLnupSosdfQBxKPM2OHytedw6d7Wv3sXlaRY7AEoWkKS1xRIQ6IOJR5mtZ/TgkHmBMH3gPla87h2ELytIggvlAKTjASSkMq9t19AHEo8zY4fK1nhvu7DuHYQvK0ktgml+BGGibFGX0QcSjzNjh8rsIILuW8jEmJbgFuRL0yCQ37Al33XSkmXVpywAOTmE0l3fZ9h3D3FYqAlJZ3AS0jXi8rTKNICFQaUO2iDiUeZscPldhHluJPNLbSOVZYnsoJpSYSO1t10h5FrlHkiuUtsMSRETzEx5AyRUdw9xe0Zy0Le3KEazXi8rTTgAMw0IQj0QcSjzO7SBeoLGQcE4vXh8rsuuF6bdxcr0FwcrXCpMKR3vv7AipWIxUWsBcopM7jJJ3lWTdh3DsIXlaQACHcTepDbSBxKPM7tdjTGHNyKyInXh8qkRYbARuFkYk68pRY9aWQF8fPvNUdw7CF5Wi2N4lpwzETOWWYEwLuyFrAiDcN9AHEo8zY4fKohUIqj1Qjbaw7h2ELytGJFWsTL/NbVNykJ8hMJvISwhV6AOJR5mxw+VrzuHYQvK0Ykqtapf5rUmCJEYzJDKkwbBV6AOJR5mxw+VrzuHYQvK0AB8VyS7E3IeEu/wBZR16yjqQqCz1WgDiUeZscNv8Axa87h2EZcHJ0LX3XC4B3HG3NFpg4lHmbHAo3E687h2Be+6r3tari7N9qtffrLX3UIVxX2KaaEoFnAm6ZQuLICAdh2KcSTTSDwnW1AgCDsV8IOUljuMaJxSjMUiN8d2ZLcsTL4LJPEVXiKrxFV4iq8RVeIqvEVVhFUoGTfZTgluqKPbDC0KpKM8RRYyaSNgiDG/xl19yKvuRV9yKvuRV9yKvuRV9yKvuRUNZe9ti3ta+ha27/ALRi9wNArve91TcoGNU4H/8Aiscb9jtN0eftFBqgdladwNMPcFIyCgLDvttnbrVcgFzKAWEATk4DrbTGkKMsNJYRwEhQDAoy7FmIQCJ099rX7yK1uVqCEojbHpRFaJQPGJQX4B7HCG1y9ecRcsOoQKiSRu7aJuU94F5WiysqZUm/jKKhxZIKyhuEjpaYGwNAsVwiUivcetAAQ72va/eQ+VrrX3UoNEIGo3Wq1t3eReVox48spEFSUKpYoHYbWWE1AuTXTH6AOJR5mtZ1RaVa52Sc/vAfK153DsIXlaKJEJc3o2s8upUG4TGb8J+HYa3QBxKPM2OHytedw7CF5Wixun2JpZgTAujOW4UvcCWtOIVxX0AcSjzNjh8rUom45YIERFuWxpSRYssIArighFR3D3oKooRumLytJMuPTUN/WjsIVxX0QcSjzNjh8rsVIhp+wtKDliSf/nsTkCPMPUJ2dP8Ay4mrS0m931P9nRxwjRUdw95GOwApjzijNMXlaRFiOXpg4lHmd/VvdiFumHyqJKKGFcaJMrdgkG3unueSoLuJIoJCWHw33R/d98sQkrAGsKEsSaPIgXk9t6PsO4e8iDYVhFAEHTF5WlZEHccVYu+kDiUeZ38xgVKykozBlaQfK7Hi25UasKNTmk2ESpJMuSqOAZa6s25JBwiTEK0tYU8q+UpRi8REm/C7DuHYQvK0UFgiNRpweIZSEoCgrlGaIOJR5mxw+V2L1NlJ1BPMCAIxB0Eqw5KI2Qc+wJWMAXGQDWE9h3DsIXlaKSxV7+IsJ4iE4hLDwnG6IOJR5mxw+VrzuHYQvK0vtEx4HNuGEOkDiUeZscPla87h2ELytEpqUHAAzqbiKRKfAYC5YtEHEo8zY4fK153DsIwALE6as4JxukDiUeZscsoNydedw7CuK+7XWvuuMVxX2PYy9g68ZnitsUFg3v4Sq8JVeEqvCVXhKrwlV4SqsEreoATbZ4Q2vfkhrkhrkhrkhrkhrkhrkhrkhoxMCwf9VXvv/wCkz//aAAgBAwABBQD/AHwJIMOGnTGqBkpDjg0YhUF3OJGSP/UsdcCG8YRpzXFpciCUpZYSgvy4lUF/va7h/qfftjftO3HrjL7gk33h2Pfj15Ir3FsK3HrhfQvh1lrb7p366w1ndSXVJ3i/HrwfXYVuPXC+hfDrCx3AKNNTg2VEmtU3IO8X49eD67Ctx04SpEjNQOBK4rVi+hfDse/HrwfXYVuOlZJhBzJIBN9Gva4y6CUrEwm5wKXFagX0L4dj34+xpVDVJq+9K5xi0gsfa5vKdvCkfV6+x8yUkmMsnNXqewH11qaOHHtvdLcdPJKeyeKJUAzvRWqnFpaAJ4asEWq7FhoiykgArim44ZpHrJIjEBp1zaF9C+HY9+PsYr2skSqTlCsBIBHIzQFGIDTBXstDdStVBSkqlRio1jXcpKtF41ER9w7AfXWkv7UiPVE2JN7nbjqQ+33tvpuQWOLVk2KOiKe5i88y5ZaU65xRRRhidtKP8BQChoWFOEFEJRnKQh8NhfQvh720sJwmi9t2rvx0ek5wm437dGzolIggSlKFCZPctcmTcoVScN7tzS0GuRhcPUAsqhikAYcG4l3YD67Ctx1Ifb6jqtGSmNTGOatkZwNpPYYRcgASDyQo0IrpE6cBAEQt66hfQvh72e8qTlik8Sg3VX4+xMjKTWo9AEwZSEosXYcSE4BwFjEp/l7jV5a4isxvatvCwvYXQmgfXYVuOhgCO32SevsiKCGwbaYCgAFQvoXw7Hvx6lSlKUgMhiIV0MfRo7r40tSmxNnNQE0D67Ctx64X0L4dj349eD67Ctx64X0L4dj349eD67Ctx64X0L4dj349eD67CCC/M1wvoXw7HEC/M14Prs6wrX0LW3bVAUMyiyTDLWSnCKsHfcSFQE29t2xBB8Vcq1cq1cq1cq1cq1cq1cq1cq1AJsHZ97b7eC9eC9eC9eC9eC9eC9eC9eC9BJFa8bdkjYWnICEsLonsgbBgb1hZoDlp/mf90Xfbfr9+pEC4b95tx6f3P/n0jBeEJIvEHY9+PXk+LxactcHNC2R+NJGQnvNuPScSDR1Zt59CaN9JEt04dAX0K4dj349eD66dr3t3u3HpGguMCVAqSmNp6gQtEX0L4dbMLyM1chTmpyO8X49eD67Ctx6ShSbzjDlQBnqFxVWvvtoC+hfDrd/er8es8Vt/YD67Ctx6Sy4iTbmLr3UXOJtoi+hfDse/H2KFBtjWAoKs91MNaj2CQBcLdgzbWvZYeXZCqNMMIVFH0stey/sB9e4tyEa5TImgDap19uPTPJUiElJPBfRF9C+HY9+PsU+a1KOSa7KeeJAqElUW/wCaSFHguK97LUym9j06gNjm+5Za1eosUsTqS1AaB9e4xlxTto3Z4SuLdr7cemqGYAogQxF6IvoXw92iTUkOKVJhpjdffjq991Wby/VrNTTahNbTew0hZy+1t3YIFt6IJSI1AZY45UxFHn7jhK+wH12Fbj0jDAl2A5pR30hfQvh7t/JlZSZ5c7uSnX346mS84xYiavvlaptOSiTt5qgUdjv2GqB9dhW49F3cwN5ACl76YaUMobHITUIwDCYHQF9C+HY9+OnuNEuYmSMkto63asH12Fbj0Zude6iDeS7JQnOKeLFHhi5oxodAX0L4dj349eD67Ctx6M2QiFUGt/4XlUILgU/rSqiIriRaAvoXw7Hvx68H12Fbj0FJ9iClrurW3Y2ZcYn9AX2r0FfUXSmp0egL6F8Ox78evB9dhBsLmaAwBGFRBDrGNDWW3J9MX0L4djiK3j14PrsC1r3uENxXsHf2eG9Xte19Za262xSCBnmCaVAVqNpOVGGFiLEoZ1JBCtGNMLUWFa+xYwqToTiQlluTIvLJSFp+WGRrrHBkniu47h1uHW4dbh1uHW4dbh1uHRQR22U0LwoFBD0Se5IF5KUKg4tRS98ApIdxlmX5Vq5Vq5Vq5Vq5Vq5Vq5Vq5VqCRuvsUIrhv2Wvuq973v8A9otoaE5rfYIbNz0jKKRMyO/PNTkeqbTY436gkISliQK2hOUkaEZao8bYmuu2d4r0FUZYnfRp4zRplZqa+0y3A8ugL7hTmOKgwkTkdziHU0CnTta97d5tf/8Aeoe5MS2iZJKS5i0Bi8Nih+IOx73v4teAzxX1Ega1iwqIyQuSNneLcejIJArRq/5c4UXMloLuTsBethqA0xZoCta9irWsHWnHlEAEG4b94vx68sFrC1FhXtQx+LvNuPRlCY09wEjPBUJSguB5HclybVVlSbQF9C+HWzJrPdWOLjdvsO8X49eD67Ctx6Lg4AbnRc8Jjahg7CKfvcI0XcDfoC+hfDrbX3d6vx68H12Fbj0ZEzeoEmlDKEzPprZTa1nvCkALADoC+hfDse/HqXB0ToQmTkFroJalU3XuJ602FuJykigfXvQ0J4CNO3HpK25MrouNN5dwACC2iL6F8Ox78fYjcC1XYatM5oF1vF2KlIUxSdKqflX8HUUOEH2DHGC6+mhpJbSaB9e8lFCNGsTJzydO3HpKLqbG6YvoXw9/QRoSpt078dHnHAE2lBVomQSgq1lQU56Q4IFqVQI0XjD4pPYXpyBxPQmEyVxMCqlDgZaIX3L+wH17yAYixAPMAPTtx6V3EVxJz7mh0hfQvh7+VLESA5cAkB+lfj7GIXiREIDiVRJ9wKEZ5Vj0ScZV7IibHqCAnluLeahOYEPOSLweBREfcOwH12Fbj0XK4wkuCky4Cj3E8aY7nFaIvoXw7Hvx9jajukIoSYoRggBFoLUBCwBUYumuZCwGCaowBAf2A+uwrceiuueG3hNEnAoVBCgTiTkaIvoXw7Hvx68H12Fbj0vvlicbO6gEPSF9C+HY9+PXg+uwrceie9JSBmPyWwTnJJcZRljAaIvoXw7Hvx68H12EG4uZpoSBEE6QvoXw7HEO/j14PrsLw23669Btutse4bb9eEO6+xRb63jreOt463jreOt463jq9x0UId9n3vut471471471471471471471470E4V7/wDVt//aAAgBAQABBQD/AHwWrUTclcHJuaUyx3am4+k76xqykixI4JdSBSnMNucTY6wg3EMYCwCUEA2KIQQBurSBTiGAF65pdFGlnF6kw8gkRhxJV7ml2MoSlOAm17Xt37IseeJalMKfEMFlsZeXCQqFJy86AsToyKMfgGXCdTLoWpcZsQW3Cz1BYkRGJxkZSRkCJAYTWDEexM9x5xkLXkdLDLYRynEDnicp5E2KpHiCHAiCvCAEoMW6nqohK2cs7umCFashjypzMS4M8uJOjaCN9MTL7PsAKxINVY93VLeS/wBcl/rkv9cl/rkv9cl/rkv9cl/rkv8AXJf65L/XJf6ujebnNKlQrRAGAwBhJJt9inEkqCxLEgFShevsvEc+isUN6JL579XPfq579XPfq579XPfq579XPfq579XPfqMu8G3blp6ugjAK5pJR5drWtbR6jusBB08y3/8Ap8y1gvrjbc25EW9SmIEDqVlCFjnETn0UmwojKWicRZ56nsIMLFFck47nR3cm79nNnuGtkTstZ4Nh12WuEQ1j7JI9F0f9w4kpBlLGTos7w5/s0n3/AFqM4KczFklfV0s0/wDJZ+27s8Q/gfQP/wDRk5aIQnzPi5KhlOe45lKMQSJ4IXzxT0ymnv7rj/pffJa9CirOvj8e7i3fs5s9w1pXxrHfwfWf5Ix3BgU5sXpU/T+jWtnUZ1RynNUcYSJhnc0eVZo748gbxnTLGSF3T44zVbB83TPMxL3GX7Jri89Us4lUJYgZyzATMXzNGUpE/rM3ZDvmwt3cXWG4VluUnYWFM+TaVTlkylmu0Z6gZ1JsdY4kOe86M6qNTuUseN9Jz/ZpPv8ArWz8uGfItPrZ6bcvZnyJ/wChHUx4+k3pSzjirNDj0zx92KQdP8YZ28fTNj20svi1pIykHo+bzScS4LJxM5RKKMMGjfcW79nNnuGtK+NY7+D6z/JOEA8BKJxdIjwk5M6/qWyOyGyTLGKo1jGLLM7pFT3jOdMeZFBnTa8hWxB7xsKG9SLImTqeofqccnUtyTmTMsGWcWFz2LZBxPNonIEieyRKz43lFrwTCmRvVo3iTMY4n1LqeawS7HUxkSqNzNzcMWaTn+zSff8AWtn5cM+RbCbv2c2e4OMvZ21S3uKN0Tasr41jv4PrH6Nx2Uo/6dxFSDFmMWtb3hz/AGaT7/rWz8uGfIthN37ObPcFhJ6VY5ZOUwspbkScrzo3maaMR8VlLVMGfUFfGsd/B9juf7NK3+vB5++Gu69/jm5RV31L6uokDWkXW5+/smM/jsJIYsjz6ZFOOe5Q1OGPcwO0yktNn5cM+Ra1zyO1tk+7Z11DrYPICDy1JHZNpkw49icMyFkeSO/ZKXwEYjLL1COpZOm3fs5s9wmZ6JsjuHCIg+vP9eYjqWQXDiSPYCflKCWdj6sUoW1kJTy9siy5W4sV560mucbWPBrrRXxrHfwfY7n+zSff6x1coMRZnZ4eZYnQoz3diWokC+OLFyg8EgIHJJA8ER9kenlwkDpjmSemRWRHfcyDBn7Bps/LhnyLWX/4pbj/ACNI2drWGODZ2ZpxyskGSY+U8EMHZ1Bwd/yDimEZbVzFw7MlIVjnjlUVNcvYs0279nNnuGU/18IIR2isZJdW17bym95wa1mr565KxoG9nXmOjWiSLXGPRNI+CQIk7cohWN2pMlugaFjrJii7FFFfGsd/B+5NEGimccwR2Os8TY+yVYxgOTOqfp4VLkK7tls+aycn2vYVuzPEZWOeR2BxSPDFpOf7NJ9/cmX1NRGV4miHwZgkJ6MhobXmQNTccinLW1enH1mEo43HUGgbvPnJLgKRpCXrp7kyZLgYsZ88ps/LhnyLYTd+zmz3DKf/ABj6sUPcNbIwtaHLIEwx3A0kEZ6ve1qUofSUgEby3p2NgGOKNregaEjAb4ptYQb0V8ax38H7lK8JwyVymPsaSNsvZNcHROcy2HQuL4/YOy9t9kMMYEEWbEBLU3dkt6eMeTeSpyCEpGk5/s0n3+mpjamQFOEaSrlyOPtqNR2OCFM5oVxMyw3Kf75yRV865HPtjhMphJMdfS31I2flwz5Fq8rS+Ux59y3MpQyLcV5Bkbg1QjLmWRr6ynOCsaY3hMyyuw5PnU7ybHct9ied5NKz/wBQ00yJj/GLaeYqbqznO8kQQrNE8cccY8x1MZ8iyewyaUCzFot37ObPcDiCVJX8eYKtH2G1yiSiAdgghFbklVySq5JVckqiUCNOaEAA3K+NY7+D9wkclYoizgeGwTTE5hF52y2nMPFLexpnMPf5BOMqY+xtdAvROqGpdmTF8DeZPKY7C2OHTmJZAbKmM6iUAbY1KI7MmKOTeIy9SOXxsqTaLn+zSff9Q7srU/I1OAYOebGsYQ6LGuMXeEKqHMippRtn5cM+RavN9zksty3CZXIFsSi87jr2yYuytGWuKqXxbGcnwdPkvHaxizi3OM9g7xKpPDX2SPyqnaEOy/Mb42TbMuAU7rJ0k2rL8IdsgRbJ8emWWCsdwecjyFGzhOPUPot37ObPcNaV8ax38H7h1E/rN3Z2p/benZyY43i5yYWNn6p213aXkqoGwscb6krzCCk9RfSusSosJVOXeLNNskO6FHFcWSCLgy/WU1yJqz705u0aG0YQZGeOZSmH7o0XP9mk+/61s/LhnyLYTd+zmz3DWlfGsd/B+4ZBhCLIkWb0xyJveoHGXxKNvQGLYdEWeDMFOLA2OJVsYY+MjbdEGZrlNOsNiD67IMexVEVfGsMAdUgjUcljaXBY2RJiG9AlUrIISvn2i5/s0n3/AFrZ+XDPkWwm79nNnuGtK+NY7+D7Hc/2aT7/AK1s/LhnyLYUfij0ny02e4a0r41jv4PseQRR6UZaJ9/1rZ+XDPkWwJjPobj9Erl0bQpl03hrZJVDaEk1pk0df4u3l2dUHpiuvTFdemK69MV16Yrr0xXXpiuhtyksDUcle0581iSeWp1KVU7wqSQ6Ws0glMMx62L39na3LYj5Jo3GSVz4ytbWrcGtCpUIF916CQEOpiBSvdUfIfq5D9XIfq5D9XIfq5D9XIfq5D9RPq6gJ4nVLSg1ySCQqEaBxLSokYin1jPbrXsK3fs+47kM/AuYn6HschxxLnSctjmvc3GNs6yA42iEWC5Yr/q+PV/V8er+r49X9Xx6v6vj1f1fHq/q+PUZi5gEXDcOoo6ohsZnMFl8a5xOT4cwOkDb8kRt7nuNsigUqZ5sTMLa9GSFVF3LHPT3O4kF6k13q61/wXB5NDb4sTGO+Iv4U+1/Cn2v4U+1/Cn2v4U+1/Cn2v4U+1/Cn2oziuVoHvI8Rk0zWOK881abF30czlBSqVxdQkUtsSQW3If92rnE2vYwsVueRQjCw2CaUK9zSg3sYAVueRVzCw2saUK4jSg3CYWK3PIq5hYbWOKFcRpQbhMLFXPIq5hdg2OKvcRpQbhMLHXPJq5hdg2OJvcRpYLhMLHXPJrmAsGxxN7iNLBcJhY655Ncwvw2OJvQjCwXCYWO/PJrmF+Hnk3oRhYKCYWO9zibX5hfh55NCMLBQTSx3ucTa4RBHbUiGAFWOJvcRpYLhMLHXPJrmAsGxxN7iMLBcJhY655Ncwvw88m9CMLBcJhY73OJtXMB4eeTQjCwXCYWO9zibX5hdw88mhGFgoJpY73OJtexgLh55NCMLBQTShXucUG9jC7h55NCMLBQTShXucUG9jACtzyKEYWGwTShXuaUG9jACtzyKuYWG1jShXEaUG4TCxW55FXMLDbnkUJKQIQSCgA+zTUMgoywExJYhJSBiAQUAP2aahkFGBAmIAIaYkwQCCiw/Zp6EQUMAUpABDTEmCAQUXb7NPQiChAClIAIaYkwRZBRVfZp6uQUIAUhAbmJyTRFkFFV9mnq5BVy7JE4bmJyTRFkFFX+zT3vyCrl2SJ7XMTlG3LIKKvdInvfkFcuyRPa5hBRty05RQrpE972IKsX9mnowgo25ackoV0hArllgKDqTCSzaskIDcxOSaIsgoq/2ae9XIKuXZIntcxOUaIsgoq90ae9+QVy7JE9rmEFG3LIKKvdInvfkFcv7NPRhBRty05RQrpE4r2IKsX9mnowgo2i05JQhJCBCsQUEv7NPRhBRtATEliElIGIJBQQfZp6GQUZYCYksQkpAxBIKAD7NNQyCjAgTEliGlIGIBBQA/ZpqGQUYECUgAhpiTBAIKLt9mnoRBQwfZp/9vn6QsEVaby2K2j71LItHA0jfGZxNaXdpfm/UyOcQqHjPemdM6JX1lXOrg4IGlEskTA3Ie/5ykbS05EY5DMH3E8wu7pclyZ7NSMmD3dTJ1mEXVyfcMamIyBC8ZUjh6pTkfDCl2Soc7LXEeNcgLFqGa7EzswSKS45a4i9Qbp5zdA5RMRBebjkcHiYYlIMNb7wHUujDKY5mRwFys1ROEKI/mN5IIyRjqeJVLdiTv5yVKoE9sDVISLgAIRABkvCgl2UpGRtVRtl579XPfq579XPfq579XPfq579XPfq579QhPIzEzOpRvRBSV8aApUwE7lHGd3ddjGuzeS5nWWqnf01dXpq6vTV1emrq9NXV6aur01dXpq6vTV1emrq9NXV6aur01dTSNTcZKpMoM0+o7q/bunmVg/yhMBlYL64WvN2QniWMDC71/aUCuxmmlkFWybCL47CKwwu2QYEwPkknsGhptr2vaSZZxtEkKCRx51P1rd+zmz3DSs7B9eor66D7IgxeE4lkopDE9juf7NJ9/1qMwZRmKjJaXLtPr+bo07Z+e0oJDDugf8A+jJZB4S5ZwxVEIFNm7GMCTzZnhUVBkjGTiyppDi7pkdmF0R50g8kibFixibchzzpdWrnDp3YwxZOt6OYw2XSxFvkzWxaxu/ZzZ7hoytA5qQAiQHKxkIsbTE1GtJOgEABxrHIABhOs/yKK16PBkMguT8gF9OzvIxZ8ka89qj2KeonGc1huUsl5wxlF49Js3uw0PWHMDHGMdR8tXyiM9WMwOw9iedzGTNMdkbBLmXXuf7NJ9/1rZ+XDPkWn1/4myZP8m26aM+WF0Y4Vy5D85oun6ZxpiaMH5bYYQm6SpKB3gmDJUzxSMdK0Fj8Kw1j5Vi7HsswXFJzJJ7gKE5AkD9FlP8AXizplkr8lw1i6Z4nJiEWSw1h1jd+zmz3DRWpxq0bRGZJHXKKr349SV9dAr41jv4PrP8AIwsE3YQcUuGmWQdNJnM6gcn5vw9HWzB+XumNkxF1XGAN6amfAzPEJpj85K6zWHx31p6jWOpHII/0rH3kwIG7tT7Ete5/s0n3/Wtn5cM+RbCbv2c2e4aLm6ul3dSulCRa4uM1bxJh2MDoFfGsd/B9ZkHGsHyoyX6NOme94/0pdPsVezWtsPM9FZ6dWdofmyhwSEGIWqHRFiWIGhpajELS1NZncHP9mk+/6u5pVjabPy4Z8i2E3fs5s9w0XwShtdPuZ0opyE7tpJX/AALQK+NY7+D7Hc/2aT7/AE6urmF0xgjSyd9myl1xu+4xyinm5VXta9jxpbGhdHZCXHnBWrcW9eyuwX0u5c3sQSG7Z+XDPkWtnU/yknybi3JZs/L7JG+I4ywY6l6uZMHZmKdqcY4vxhlKWSObdk/mzPjiFwp7z27Omm3fs5s9w0nBBJD1LQie0phX10CvjWO/g+x3P9mk+/07+7Qp0s0uk4eLO58aeD4+/wBr2FZkRvyU0VzAzVqdjgvTY5JgvEYNb0s0krkFBLmx2bnlM2flwz5FrZunyTHs4YdgkpYV3Zk2OvkwSxOHyOIzvs6hYo/znCWE8bSCP5E7M2Y9Pypi2EzfL7q56bd+zmz3DSeVDkkam81Yeg0SvjWO/g/cs45XlkYyCiOuoR9mW5ZJUDi1uKR4bex1NMJa2t0yrCenck0J5XZHyJjniSs6JQ2NOk5/s0n3+97WsXE0d8tlwnERYToXiAZahjbHOfBDYIaOIDYUfJa4s5RxQBzd3zHbc8vvhejZXTZ+XDPkWwm79nNnuGipVJkZREwiao766RXxrHfwfuUywQ+Sh+aGpCxNPZ/WUYXPsMjAYcwdixPZWkZ+mx0/i/avwzNGaWM6VchadJz/AGaT7/N3FUc7PUNHMKeYi8x89qjDq+KMVYqtD9S2flwz5Fq80ukkIkGVRzgqE4vljg4YKg04yk2m1mWbqsbYphzjkSBZeyDNspO9mhxA8NNH5HycewdSzzkiNYkazTD2yuoJ/wAmRdJIgSExj6eZBMH+IxhbIUWddFu/ZzZ7hoTmYI4SwpW/IOYnJciVtq3HWU3aILCDyVJGgV8ax38H7gccSnLCYWMshQnUliOKCZ2BUpxnHqU6UPYacSTQxgLAUcUeXQxgLAAYDABOKGZ90m5+i5/s0n39+iyR8GwxVIyG3ta9rWsG2pbPy4Z8i1eZWx7USd9brvDIywyYxZkY8F5CiiSJEyFPFp/DGzIkJPxfmxtBM8ASx9doImlyYyg9PMiu4LYbOcj4RLImxM4rKEEMyJHWu81OnuNIGZAG+PIndVnjRbv2c2e4aHUQ4HGSHpr9mnDKndMktOFmx5Iw4qVHwbQK+NY7+D9w6l0SNyxP1KJE7ThjG8ZYMf8AUQpiTcxdW1LjTSEUEjzMzxaVQ6KZS6kOl1ycXbp+rOUPaysodSKdO7pMIM7TEcl11TxJslWB4cDmwjp8i7fC8iypiZE+f9Fz/ZpPv+tbPy4Z8i2E3fs5s9w0OoePnjrpr9lyE9KU+RUOT5w21gw0xRCdAr41jv4P3DLUMdZ7CHqJt84hYsBxZG3KokxLJZjyGCgscps6fseRt0M6c8euEajGNm6Ky2pDEWKUqFOFI4+lJMGRpmUVJI+1S2Oix+T/ADRoiLExPjpEZK65T0XP9mk+/wCtbPy4Z8i2E3fs5s9w7VR9kqV1dFz4AzHM0ulvjOehv/Wk9rDbO6McN0CvjWO/g+x3Q4m2TCff9a2flwz5FsKPp5h/brZ7h2mFgNLUY+V2PZmkhmQ6ZXxrHfwfY79jj7/KZPv+tbPy4Z8i2AIQQBGYWUHkJCld29yKV+B6vYJT4MPIfq5D9XIfq5D9XIfq5D9XIfq5D9XIfqBd3MNDZ4GYgRFgbkCFCzILnEhN2IvWpWxCXLmU2JPEta2JFdC2O1N7tFHN5aSmd5J9CQ16Ehr0JDXoSGvQkNehIa9CQ16Ehpu/jLsZMMjwCCvLi4RxucWuVxUqZSGQNMVZDsuRQiC9/wAoNT1JWxaavVQCbsK5yk6hxsrNxuxjbzosQ9HY49OydXp2Tq9OydXp2Tq9OydXp2Tq9OydR7bk25GOWLKiGmuEP6fqfZkBq5zxkU0Y+QZkidpcwSott/trYkwYD5QyLoU6NsAf2FzfD25GuaKYIMsaHhpYzHRp/q+PV/V8er+r49X9Xx6v6vj1f1fHq/q+PV/V8epjwoiROs+xlOn9+XRpQwvx8bONnkrjUgn8PmWPJKiwvsAZZZoey9rCsEIQB1QSSQGdlySRG/7Ub7dm+3Zvrfbs327N9b+zfbs32rf2b7dm+1b+zfbs32rf2b7dm+1b+zfbs327d9uzfbt327N9uzfW+2s327d9uzfbt327N9u3fbs327d9uzfbs31vt2b7dm+t9uzfbs31vt2b7dm+t9uzfbs31v7N9uzfahJiRCCUWEP2hFDJLMsBOUAQk5IxBKLAH7QihkljCBOUAQ05QxAJLBb7QihEljCFMSAQ05RggEll2+0IoRJYgBTEhuMgowQCSy6+1Iq5JYgBTEhuMgowQCSy6+1Iq5JdwWTEhuMgoy5ZJZd7pSL1yS/BZKRa5hJZlyySy73SkXvyS/BZKRa5hJZlwEFl3ulIvexJdgWSkWowksygEFF3umJFcBYS7akwoBtWTEhuMgoy5ZJZdfakXq5RdwWSk2uYQWZcBJZd7pSL35JfgslItcwksy5ZJZd7pSL35JfgslItRhJZtwEFF3umJFexJdgfakUYSWZQCCi7iTEivYksIPtSKGSWZQCCixCTEiEEksIPtSKGSWZYCcosQkxIxBKLAH7QihklmWAnKAISckYgklgD9oRQiSxhCnJAIacoYgEll2+0IoRJYw/aEf749Vsjf2FrJkL0+RmYSh4LyxZeicTMYWcV+JMarljnjnUy6YPaGbiMOHOYU8OK6WzN4BdoTODtfHWxMlypzh0WjmSX9SXKpzLGHElIsuu6vIUNlLhIHjUzrJNonJ1rovKmTTMHtdJ5g8LWVoJkz9aH9/NTp1FP8WYpOK4AXGl9PbXJ0KKeGtsEFnbfU1depq69TV16mrr1NXXqauvU1depq69TV1defewwgMdECstcnTIkiNI5tDe8A2JMIk1zdiZsYxyNxpNDLK8evRaa76iimNUEqhiBDGXP19ir19ir19ir19ir19ir19ir19ir19ir19ipSGELAn/x019bWiLicC4212al0Vb1DDpZCz3iPFLr/wC5nTNvhPUlhLIshUqUyJPa9r2EvQgJte17EL0KlOQeSpJ7VaxI3ptcgWKx5EbPcNN+lSRkMYZSkfB6C59KjUQxrJxSmK7HXuzgTOSrWu/+ENeENeENeENeENeENeENeENeENeENeENeENeENNdtyuGzxNKnrT/AMllg/24zRlybov0cxVTCerLqCSoHTJkPs0OMzQtsmkydsn80KbUcmdmjA3T9aSNxub7tx+W8ZHrepV86X3V8csPzVmn8oV9MEZyI4ymMq5WrBrG79nNnuGhlDKEuisu/vPINJs+ThMakmqWZK4Q3KjnbQUtyFyi2Mm5Ajh2s6qM1vmBsci/yW5LtWKOv6e5ByNmjq8wJg1GHrJ6ULhyd1tdM+L2SP8AVl00SdyR9YmLl7my9WGF3dfD+u3pllsRxJnbFebGXuDn+zSff9ajJApMx7EmNslOn/kkQr1eWxMr+JN0FtS9v6iXnEc0iGV1Ma6g3DKSTAvUcpT4oxJN4+kYenCRel9OOLHTHjbkyA5LnJr3gh1aHvHkCSYnx049L02mTH024elOFXmMt0nbgaxu/ZzZ7hoZiaHZ3yIcwvqaunRlQGIcgKlDZk2KPYJHHNAr41jv4PrP8lBhhPT86ZFwHEoxh1iRRrqo6jyXHLuTsVP2a8guGRH3IeeOrEtxzjEssQpsga2fQxzkIcjMj9JJXhPpXkkklUtxsxMsZg+vc/2aT7/rWz8uGfIthN37ObPcNCTSpFBMpSSfRpzrp7OLUNGT/wBi4jTGpMd6BXxrHfwfWdZuI5dmjE6Po+6n0doB0sdTRmcpR09Y5k7u2dIGLmWp70/Y/wAmTNo6a8XR6etXRxCI+ZAumWLQeSx/B0EZkOHMLw/BkQi0UjUHj+vc/wBmk+/61s/LhnyLYTd+zmz3DQyrAbzdlWIlbcqx/kx2x7USh7/lKTkEEpiNAr41jv4Psdz/AGaT7/qJRMo9D0qnqNSBNjGcIo/HOLktc1cFc1a1G2flwz5FrUeasTr5R2vb4zRppguYMa5MU9kolMdhTHDcp45yCo7Sn1mPedNu/ZzZ7hovcUjkkCkxFjxGaQnISk6JXxrHfwfY7n+zSff6Y5M3P9qWv66zkTIAhU9j06pmNpaWSVZllP8A62yGjOnGQEl4mPtMbMzMlZEjZ+XDPkWtPXr0Srt6njW9Ee2SaKva3s6qjCycKz57ikxz52KlRCJKzuT22Omm3fs5s9w0XQckC6aZXxrHfwfuTzkzK6qd4xyI25PinZlDJrrEHKCLMkqknam6jJcCQ9r/AJMJY57puf7NJ9/clzwkURdvSyOFY9NkLaQF3Tsj2zLCSJuzuZ69TY8i5+YbH3x1GJY/Q5wQZhyWvTPeaMhrSMECuXPqbPy4Z8i1qOHRFude1WkSr00YgkHhIex0aWp8QMsbjsbI7DyCFRJyBCoR2ta1tJu/ZzZ7hoAve5g5Qpue2LzHEjSK+NY7+D9yUiy3Ccv4Sx67Y7iHZlmLztDP4NJ5LKUnagwVPBzDtV4mlkoaWM9zUs2k5/s0n3+sdGWNhrfHHdsk6FxOTP7I4tQX1hbFqAwEfaAPrq2pnhtlcXc4g84vjlnWISYj7aSYM/YNNn5cM+RavIeRwQFRkrJKLHLfjPJDfktox3nGNZNmVOTgkaW7E2YGbLxUgzVHWHJXY35qjrtlfJGUS4GsxxP2fJsSrIuaAQiQuOX4W3Yox7le01fGDIN3mfaLd+zmz3DQk41pDbJHdxumSrciOSxpX2c2zRK+NY7+D7Hc/wBmlb/XghNteLsR0dZPAdRrOhPXmpAn3sE21+yQRljlCNBiAxhGq6fUa5VC8OoYa+02flwz5Fq8y/sLOQHBlk2IpGFslTEUpYeoiLyBJLI3UDeQ4xPyy3OThOY1MmmVr6kDa4m9R8hniFTkTATucxE1LpArxZnMxkkCDpxx2adPs8NX/wBKaLd+zmz3DQfRPRVXKclEfJdJQQljbWezMuiV8ax38H7hnd8kUexuvRytLEOniRyyT48ecmZPTMnZjmRZPOzZ1HyHKUSjnZkKV5bfnRQ6S2X476eJS+zfCNZZf5WsnMHyhJmnD0IkuUolO31fKWrNWi5/s0n3/Wtn5cM+RauVwaJTgmjmpsUOK9AhdUJRRRBVOTW2PCWkLa3NgexubW5nQ2bG2zl2J2xtSLKZoRE4++aLd+zmz3DQL80UhlrItg0zRHKtIr41jv4P3DqBQOjhjJtWhcm5phc3xvDz+nuUmrMfHzJXHavDF7JkGQwOW5Kwy3HzM+f1OsEy97e4AllCI6GQub4uhtZMx7I5I/sOKZS2psfYdnbTIZDdwcs46Ln+zSff9a2flwz5FsJu/ZzZ7hoLp1FmdeoyPFS0zhJ4jZciVFrkeiV8ax38H2O5/s0n3/Wtn5cM+RbCj7vLzsuNnuGgC97mVHW5U0M2kV8ax38H2PIJa7psrk+/61s/LhnyLYRTSgJdLEO6Vbzn+uc/1zn+uc/1zX61+c/1zn+uc/1zn+uc/wBc5/rnP9c5/pE3G2amVpTsTVsdRHG5TIjlIUT364kr1xJXriSvXEleuJK9cSV64kr1xJXriSvXEleuJK9cSV64kpnHc01lYPSHHYGYAFDyG9lto3dYxnpeoSy1qkyePIFSPpXOUv5Md9TyfXqeT69TyfXqeT69TyfXqeT69TyfR7pk+xGOX3Ki+pdYwU5TB5ubIEyGMk6ye9lP0fQJ3tLiDYmaZW/R9POVElh2NMlvcsYpv4wePEbxL3dXA5E+KsZ/zV9r+avtfzV9r+avtfzV9r+avtfzV9r+avtRnKkrXvfUdP8AJEMcnh6ebZfjs5lzjkZ5Jc3FpbZfLF3TTsBwZWd2OohKmS07NDU/NwAALBqhMrOJ3pIkSoEriys7uPYjo1Nb2gcccxJZEljW2OJpDC1pn4hobUd43H0EVY9QSQQnC9QuHSVwXRZtXylPGo4ke2Jha44idoWwusZ/6Rv/2gAIAQICBj8A/wCeBc8gAYkyHfFVxwaN5KCchM7zDWvcAXFACQFO4bzpyOLXtIZJyEeEjGrd2wHsILSFBEwRvB/4mZZaBQSr1mEbMNRQZuQ7vCVi0299bHsBO+l4Fcl+oCrgs4cQ2oPbbAKjwFj3OJOeYIRfEJpIw+2AQjRPJXLhoinUQXvbQ0WrbEUY261dKVPiFOBxUCUW1/KP+KF2wu0xqenAOEKM9kO1HUGocNhjU9OIOvTWzeeGG5NEUtYcHu1yGMG29FCEEYOBwcOB6y7UdQZpsManpxB16UjfFlwqrDQ27kCGy8HYvsgOsr5bWta1cUaM+3rLtR1BmmwxqeRRIQjukEHXZDtR1BmmwxqeQEYQHXCjRkMT8hCC03tC++PC2h28fLCDbuY5HIjeOhEHXZDtRym23ABvfSF9vJ5pQNyUgEpigxKQXhppEycuZ+2JDEnD8aQG37rqiFRrYD23HI4KJDODda4kgjFM+VmnTM9IQVcMcgSpa08XBjk06qNTyMtgkBxAOmfshjvTK0AkOp7EXhjH1vhoBc5TgRJOO7WPMzYfYZfLlDXAlcAMzrkN5nFBts4fco3qq+6C1shI94BT2w264gMIUncDhLMnIBeKRbfbCVA4lVQov/iXIIOuyHajlcTmnuEBW+JwaQcyTNx/S0eHCbiuUrJaATS4CS/ccBvhrvG5xVVRAmIDZkJxTSLZYEqYCdVIJ7UWBeUISiL4u7dDbYxcUgW2BAILuA90MG5og6j38rNOmu+oDwx73+Y1hZ4v2z+22qoIoaPt+474a4gtJAKHEcDp1Qankt6/DkAXKGuXEQW5uIHtX4QGkgKUU4DjDmBwchxGBhjismMQNkvhGfymYLnsJeRLFrNwdSRUE/KqSxSUIwAuIaAoVPCJzl2nCLbai5tIKnMqRV7PD/LFktYHfV9QXPdh74JgQdeuH1VcrD226ZoWGV1y4Sc4f/z4y6N2o5CKWknMhSNMvZBdddTbFKnsEmjfphiYDbbWu8ACk1BonIDBc1nPcYtFhQlrgoxSo55dkCl5c8/Un1SxrlNrQQBPH2MCJS1PaSvt5La8fcYDnglSgAhTaPePlDWOYWjBVBSCDm4crNNhjU8lvX4HkBtMLhSJjephgvuAIE9eG+KsGjAfE8Tyt815aGhAQFMsBl3rBAxdi+4SfY3/AOUG5aKgAAEjEBtJludPsMVOKn4DAAZARYO+v/NyCDr1w+nAVhBBBmtSlyniphrAqNAAWZlx6N2o5QXlUCDkoLWuGVQVN6a90UyDdwCD2Y9vKHtkQVHZArCpiFQtd+O+MHf1fwhUdL+b+EBpPhbuzMJiDyM02GNTyKChj63d5j63d5hSV6BoJk0IOEyfeTyCDrsh2o6Ku24tPCEIaew/AiKXuRu4SH8e2FVDADcByM02GNT04g67IdqOoM02GNT04g67IdqOoM02GNT04g67IdqOoM02GJBT+D04g67IMgo/A6gzTZ6EJzFO1QpRZdsCogKUC5ncIFsuFRCgKFIGaYpEzhHmB7S3eoTdjhjsVRGA7owHdGA7owHdGA7owHdGA7owHdCAS2epCx9Aj6BH0CPoEfQI+gR9Aj6BAFKw1rEa1gLgSF/cwYRMIWTM5eIR6Z1wUkmY3EsdKSjGWMElqg3RcDpSaLYZT+ZVByRHKuIi7aa2c2zkHSBPYVpnxyj1Vx4oFxSGlFH7bWTQkK5zVkcxnA0H/dGXLrP4TrQ1PQeav3Up2Kv43c8COB2Q7UdQah006BrfVXAy2JlcHp9pOW/jgJwryKGEhjWilrW5INN/Whqee5rmtJOBdgD3iR35aLDrb7LBwpzGGevfH9q3/T/GFDGs/SoB7CT7E5og67IdqOoM06CfWxqeeHEKhVDgeEMu3bagq0lTU0iYXBQk01Qw11shTkCoSSGfibupdOXOEHXprtz1jayCKW5mWA7c4LmMDG5NGXWXajqDNNhjU883bpKLSAEVUU44ADvhzmh6NRZt0VEw45LAk9oJDalaQCcFCAkdsJzRB12Q7UdKqS5WabDGp57rbiiEO7MHJvKTTgYebJM2zAn4Fmi/dIKDg1TPI2mfeya5XGgnE70/9k5wg67IdqOVvhGAyG6A601qlyTaDJIcL9ttTSigIq6YGK2TtnvHA/A8tRBpVCfhqkftPXg7wnvUt9ohlxihSQQSqFqYGUiv8TAraQu+L4P8h9pHuJ5WadSfef8ASxpJ7BFVxtFxpLXNVaXD5gg9vUBqeg8THE7w5PYhgC2ylMSpcT7h3DnCDrsh2o5W6D3QBxh3EiHWz9w/8e3kZ5bS0geKaqd8MBKNNxy55NyhjblwBqeEkKv6UFQb+arPASi1bCE1PEjI4YHCC54IeHioEYFCKh+uS8RxEXQ0hTTLDPeZRS8IeRmnUrdhihjnAvcE8LW+IIDiS8NyIxWHuLnXLd1oLnOpBD2yEmhq1NzT7Z49QGp6BouOpaTM4pBDCrQZHeN/OEHXq7bfpnI6203XYeJrSgZ/j8XHww24wq1wBGhmOoO1HL51qbqQmc5NMuE4m9/tiTrntgXb8nhqnX5mUt/KGkmlfwUhrGBr0zB8ROEwZhQihMRjFu276hUSNyoi8ZYQbRFS/mJIQhCE3doi4WO+ot8J+oISuo498+VmmwxqeejQSeEKbbgNDzxB16vcvX2NuueQlTQaWgIGtVeJO8mBZDla0lODSSQ3/DgOHUHajkqzMVO+lZpiOMLbeD2/DGKrjw0cTHl25Wx/7H5dEzTYY1POFsSGJO4Q1pCVbgrjvJ/GkB7SoIUGC5gDbmR38D84IMiOaIOuyHajkTEQmA6Vmmwxqec9+ZcncP4xa/SffFpAsviYmwEalYLm4OAd3jmiDrsh2o6gzTYY1POfZOJ8Q9x+EWv0n3xaDx9vxMeJi9phBgGtHNEHXZDtR1BmmwxqeaBvgFkiM84DfUgVZEiofEiP7rY/ut74qtuDggmOaIOuyDqOoM02GCB/5z5qx4gVio9AIOuyCMxLqDNNhThOoKdjF7igaCSeAxg+pX9sNLsJoAplio3b4DnrMKgBLkAUyG4Y92JEBwmDOPKaSs8ihoNLgDgS0yP8DBIWRInvHQzGxW2LbQ4OPiUlgpbNKgHEK6mSTCiLQ9SBUHsDlwJa8AmeTkUcDBJYXK23QfyOa9znlftlSVlUBSFMofbZ9QAP9Sp7vdDiWFjDbY1wdJbja/MJ34hXzD95RYtKJUDCMD3xge+MD3xge+MD3xge+MD3xge+JCfDZRtB1IcQpzpVSBrhoY9TYYa6w+kSBV7SoOAm8krL6uEMusNLg1zUdNA9FwOLS0cDMKFWGW2hWNahOahA2XEVLoN8AlwLWm4RKf7rqyv6Zgb+CTe4tRXuIVMPbGA7owHdGA7owHdGA7owHdGA7owHdCAbGn/2kA5i+VaID0Snx/VVNf22lj5bzAClAxUWSrugK8kkXK2nBpa8C3LBnhJAl4x4iqLDS1yMLwHOBRGzXxZTFK4jIgzi28uK+awArNzDeDAv5qrZmuKrjtS3bCJIvXENcQxqdquJ/KwxbtBKXMe47/CWAf5ysBUoc64xEmDbKVKudJywLdxUFiVFzWhcBW4NXiiqmeEOeSKmPLVSRAdTgu7GeO0BcTxAJ2e7kDWhAMoAeFAIcNWlQeKGc89qPDmg1hHcQiJpMwy6CQWNc1MiH0kqs5FgRDBuBoDjnrj3oFOaBcINt3iBJJWcyavYfp/KgTCPKZ4Wrkm9TiuJxOJxVegTrQPE9CuAhcRzQIIy2QTmo6g07+hLL1lt23cIBWTmrJWuyg2C4OGLTnTlVx9+I6yNTzhcuAqpzjA95hG1Dt+cUqrRn84pzPNCQVy6algJO4Ywo6y7UdQauY6GfWhqec0vcGq4opSJPB7Ytsa7wost6xba4KC2feYfb/KfZl7OaIOvTWr1w0sbUp1CQX+jctt80mKHZieRy3dZdqOoM02GNTzmttkVW3mRzUCPEB3iLYP5fiYtfp+Ji4RvA7gBzRB12Q7UdQZpsManneL6HY8NxgOaVBzENLiWluY3boFtk3AI0fE+/jlBJmTzRB12Q7UdFTaau85DUx4roB4BfiIqajwN2Pd8lhAIBEl5GadbNkOFYCluYBz6Aann/tPLfd3YQhudwA9whXFSecIOuyHajlFSESwyJCodx/A5A+4+kFUkpKY6CC624OAxSRHYZ9y8rbbcXFIa1CmAAxJzJj+27vHyhPLdPiPlAuAKHFDlP+MKeRmnWi50gApi3/uD7Lmi5cNTyWJ5V1GskHV+Gm2Zt/Nv6Aannv8AMJqQUphxXoBB12Az01KtclTlkwvUWwn8xaRwlv6B2o5DU+k5SJHePlBcQHNKKDMOAAx/Eoa8lzS5gLSitdwKTBGGfYItNaQCjkUoviMgslhtsNFbcQElvK/cUSoAykTwYR9zV7VI+EVJLfFtePuMUXQoGGRGkJ4u/wDhDbgBOczL3QSd45WadaIIUGKCAW7kl3dANTzwXXWBQqKSe1AYQODuIX4gc8QddgXrj7jrdy44uDBQQKD+0pRxkGtJpdDXXG0vIFQkUKTChRjz3ajleNPcIpK1ANCZK2VQPFsiExAMWVcGgtdMqi1GUlhvgk37mkuYmkw3jhpDA1fC0Ce9ST2KZQLK+AFU4w17cWle6BcYdRuO4wW8B7oYd7RB1Hv5WabDGp51DgDUC0LkSPCe9IYVV5UFpGeCcAWmbskO6K2OqQon5gVVRJEGDhmkoczcecIOuyHajlNxEUBdQAD7RyG2HGk5Zd0FCQvMqtOQ+/UQPPsseRnMQGttNAEgFMhBtFgCkTXdys02GNTznNuGlRJyKhXMYoQolOPOF5tSrJrkXuzzgnzQATgGulovzhz2hAcNAE5wg67IdqOoM02GNTzxQ5rJYl2aTDwZ44OYoTKPPFIBAUA54KEkhkZbzu54g67IdqOoM02GNTzg9jQQdzm+0Ko7YALU7R848i6PAv5m+F2/HvHxgtOIKSwlzhB12Q7UdQZpsMH8L0Be0ID70n7eeIOuyCV493UGabDTLp1gk57ILcj1ADdsbxFIxPdGJ7oxPdGJ7oxPdGJ7oxPdGJ7okZ8NnoSkfWI+sR9Yj6xH1iPrEfWI+sQDUn/Vv//aAAgBAwIGPwD/AJ4Bltpc44AAknQCcUWmF7pyaC4ymZDcJmHOtsc5rAriASGje4jAcTyMa+24F4BaC0ioHAtl4lySCy40tc0oQQhB3EGYP/E1z1Dy7zA1LYbJyuk5wcQ5opYonPxBM0vu9N9Fy3cLRhSX2yfLmn0uJZuKSWGB76XWn3XFqFbgu22saAgLZFpDqi0UuUKVEW7ziHAuKtzRtOPBykDQwLdp/mudfu3FQhBdopYagDV4TUitEkcZxfIKis+//mmdOnlskadQK7DOnUB0qCPUs9JYddb6dW11Brbt4Y2ba4pm4yWWYhvqrIIa5QWuCOY9pRzHDJzTLjiOsjTqB12GdOoDpQ4YgrHrbTyzyS97/SgFXBzySfO4VUyxx4QWesp/1Fy7cu3aSCwveVFKZUpLf1kadQOuwzpyG04uc4Y0hU4EqJ8AsC7ZcrT3g7iMj0o2QNOoHXYZ05H27snhxVdce3GL1u1NzqccG45Zn5ZwpvO7CnsCR4neY3c74HH36QLtsyzGYO49ENkDTlbdfi4u7qiAO5OTyQpcACUBIauCnALxnnAtueKyUAz7uZ+4VccGjH/x+BBf6ew2gFFc5F9kOtutsVpIMziIFlzGgEEqFy5Tr01z14cAGH6fuc0Foe4cGF7V16qdOR967ba8saSFAM8vbFxvqGMVwCLqV+Ef27fs+cPcWsYgMwZrkk56Zx5RweDLiJr7+UuaQExJyGmZ3BRFYu3FwP2IUBRESS8Ya583TBO9CQvaiw600E3ASAExIkZzQDEkpKYWLrLrg6ktRAgFQVBw1nyjZA05WNBmF/zGCjvCwuDhkAPC0YfW4q7GTQAk49QHkgVNJQ0/YMSElDm/tsYERFUrgaijXLwVMCYuteVpeQP0kBwHYqQbFLlDalTw6Lvh912DQT/Dth124Vc4/jsGUNbxPvi47e4w39LvdynXprHoyw3LbLflOuNuftkXgt51FJqRzjOqdI3Q+21wcGkgOGDgDiOBx6odOS7+n4iJwXJnDmpgYa4YMBJ7k+MOeAXEAlBieAhry0tJCocRrD2tpncuVVT+44DfrIQGW7gDASuDn4qW1A0FfzIqHBYquuLWNLyUKKKnSOZHDOLr6A1xeQg+0ICG9i+L+ZYvh73Nm0+Ap9uCpVlwmsAbuQdcHovLX/U23XS5QrbgQ2G41IWtKy/+3v6MacgJe4AZNKA65+2GtstquOLiAv8AMZuJyGAXg0QTdc5grLiAKS4lCpOKZIEwRUi8LjQ4Nc0ocFoGIwPbrDq2BttfCs2zkPLCoHOLaiEwi45VrdVpIBPZyXU4f5hBZbIAaFJOW7VYQXm/0n5w57bjXEKUQhe1YBGTSfh8eU67DOnJd0+I5CL9xrXVEocUQQ93pmEtLpFJJvO7tinF7vqPwHAfx5XeRbDi9yuBKCciZruwAgE4Nwt2gANFci/+sC1fCEkuIB+klxeAo/LKfCKGCXtJOJJzJzMepG7y/wDLyjrg9YSl1paQQEAoADUGCAASh11yAvJJQIFJUoMhw6MacpFtqKVP4+HJ5gc5js6SlSYLiCmWcVzc7e4lx7Fw7E5XW3TDgQdDBoKLgUUOav44iMW/0/xghW4fl/jBcGsqcmIOAyxzz7ILkpe0o4e4jgeQ67DOnIjgoO+P7bf6R8o/tt7h8oQBB0DnNCFxU8SgHuA5RsgadFRdaHN4woLxwBHxBipjVdvMz2ZDsEFjbbntXwuaFUZLuO9Yc69J9wgpuAwXjMryHXYZ06gNkDTqB12GdOoDZA06gddhnTqA2QNOoHXaY2wddny2yaQSgUoFQDEngN8EsaTSFKBUG87hxg3gxxYChchpBOALsFO6JYmBZdbcLh+2k1YL9KLhPTY2cZxnGcZxnGcZwq7QxMYmMTGJjExiYxMYmFWHXLoLnXHBha0p+0JvDlBBbcUBAh8J3x61lp1bWtCEfc1txvimhwnhAbWjhYdaNtDN5vG4H4UU0kFVqqaiYGPT3rjgknFJlkyAo/MED5ZEZx6Czbd5htUtc4Kh/ddclUAUa1yKQMDkkO1Pv/7oydZQou5ZjUYjt60dOg8lPtqXtROyXfz12SNOoFeguXP9rsG96l3hbStVpR/dDQDUh8O5pIcZAwRaD/Ouhpvve8vfcuIri44ScThLrR057XMc4Bq1BqAkHMSM27s55pDLtr1Fwj9Qm04zpUGWkkI3f3ro/wAf8ILTcc/9SEjtAHtXnDZA06gdegUdbOnPLQUJBC7uMPsWbyEAOAQUubgUWqkgyWeSiaw5l0OQZuAaVU+GXhdvDm7588dN6b03+y3TaY9hN1/2NNRQuM8G/aJmSQ23evOv3B9VxwQuOilBuC9ZGnUDrsM6c8WbIFVNRLlQBUAQYknRAIaxzrYc5U8Lspoq45pmh3QUNtxALqUcC5oxRykA7pawvOGyBp0qLPlOuwzpz23WhVBb24sXcF8K73CLYvgSdIkIt1PCtJ+hCZgBSAIF65/9dxAkybbyGzAzCr/hB542QNOV3iOJzO+HMvudSGrJxE1ENPprr6XBUJqRNZHtEUP8NwdzhvHxH4HJQ0itCQD796Kix+9bT+Znjb2hA72HWLlu4hppc1wCK16ooJKEIdZSEE23ByYpHpiN1wewH3gcp16lb9Pb+q44NHacdBiYDLT/ADLT2h7HolTT8nBw7OoHToCWXGhu4sVO2oZ8IJu3K1wFIaB7yV4nnjZA05X/AKj74LuEMO4GGXW/aQezPvEuR/mvDgXK2SUt3cYe5oqcLbUGH3OWeSpFx1uyS+VTQUT9RcaXOl4acsTOL11ygUsMwVEnYjFeEBrHAsNs0EFVFQNH+CafylMjFmsEAB5UKcgDITCL3T3pVbKjDkOvUrvqriOuNYRbYavE5/hcVbgGsLvuaZimcW2BjbN2w8hrG1kOtv8AEZuLkLXjAuwdLPqB06BzrTanASCosNLxS4gKMUOYXnjq7rvrGq268WGY+F7wpuf/AJ+Hh4pw+1cCOYS0jiCh6gNOX/T3SKC854tQuE+ISJMt+z5xNlv2fODZ9MVYXo3TPsE57uUuaBUifILuWH3LhcxZUkeEDGTgochVCuBwi7eatDqQCiLSCqLlNF1gXQaP0gAqpK1b5zkYtB7T4A/xD6SoCHgd7e6XKddhnTn1OIA3mUUtusJ/UPn0A6va9P6Z77LbYK0Pc2tzipc5E4ACaAQfUOaGucBUmBcAAXcKkUjf1AachsKQxgEsiSFU79wi2xiB803SBM4pu2y06S78DFNq2XHgI827O4R2NHz49nRHXYZ05xuumcAN5/GMOcDVTvKNC4AcezWCx4RzShG4wGPJdaOI3cW/LA+2A5pUEKDvB5w2QNOQPqLHgIomo3EZ8M4NyoveQikIg4DjvXpTrsM6c63byDV7ST8ovfqHui8pI8XwESuOBykE7Ya18ywub/SecNkDTqB12GdOcz1DQoHhPDMfH2Rd/UPcIvFh+73AR4Xp2D5QXHEvce/nDZA06gddhnTmvuGYa0nuCwTduOR32gkNA3IN2/HOHv8ATPcRUhFRBwHEA47+yP7Lu6P7Lu6KLrS11RkeznDZA6gddkFrgoIQ6GD5NxtGVS1AbpSKdkCywrmTvJxP4y6EbIXLqB12CghAFgkDkEsYQ9Kg2M22wK5xAA3klAO+B6IgeaXhgnKpxAE8EKhDunDmWy1GkAuLgGqSjUJRajh3yAJBY4IQUI3EYwL7wKfCoUFzQ8VMLmqoD2hWr8QrQ4g1NDghUIcOhlsV/qrry11tqMRoe6t/hUMc5gIa2pfEENJ4RfPo3EsNu65iYgOtlwbLBzVpPEGGjzAxHXfNCzuMfba22E+/xVtAnSXVlBMW71wftucRxIbTVLd4kG+e6GNbdFy4L11zC0rTadR5TR+WYcW25FmbWkpF/fW7HWMRGIjERiIxEYiMRGIieGyhfLai0GkZVkENJwPhJqlmBHo/U3Gi35TrQeQpbTbeEcB4nSthrSJ/SuaRcsXQXsL2uVhRTaLkSoLS4OIVARJyFKYu33ki456howR1ReSeBpAGandDmtYQ+4LQcVFP7DKBSEXxSJXBEmsrTWODqbbQSFxGImBhGcZxnGcZxnGcZwpOxlBTmKf+0Y5lynz74cbSrX+39NEk/dcLjCpGASC5ApuoqBUpVFxE90FLbWtBs+U8BDcD7ZN3xYvFQBKn9t3hCKkXGvYtxttzmNIVXoC3w/d4SXBpBBSYIlF62GNpNi4S1Ajbg9Objg0faWXQUpREpw2pevEOqmLaYFzWm46qW4NY1Pve2L19y1tuW2CckeLjnEhJ/wBsATGJhxC+Yy3ZuFy+Ei8AS2lJU1NmpmHbwhbcWlrHvKYny2OegKFFpRUKCcW7YBFu7ba8BZtLmVIqTAdhKbUz2eCuEGyD4CVRBiM1RR2HkNxxJcSqw42yhc0tMgfC4I4TElElCFFCz2pbLHkeUamfyuUFdVAx3DKLtgtBFxzHLMFrmVAIiCYuOBBB4JAsueSwIg0VBvIappBk1SiKYbeaaHNa1oplJrQztVo8S/UpXGP9Rc/cehHiXdSMCPpEmj6QAAidATkOtHToRbpL3kKgKIOJ45BILKSx4CoZqN4OfHmrskDqBHQi76L1dz0t+w1zm0iu3dQVUXbf3qiDFFVDDPXMYbblpuNMm+aAC7y5lWT1b9JmD1k6c42rRAaAMgcRH1N/pEK6kjcR8ouPSlxSWOAGBgX0IYwGeRJCIN+882cDpq7zwxgIFTpBThPjCOCEdZGnUDw6GUZDQIO4e3f1k6c54ttLka1UCpKPExw7Iuvc0F1QExgEWLr2SLXqO4Qy6AlQ9uft5w6b1Xo/TsrvXKKGqk2vqKEpNIZa/wB3tlnqbQDalDvNYJNcU+8AAOP3Y7+sjTqB12GdOc990Gm5baFE0QnKDS49xi6R+f4CL36vgItA7ie8kjnDZA06gddhnTnCj+4zDjvHbkfmYLHghwxBhwY0Oa7Iynv+cG7ck1xVzklo33cMYDWhAJDnDZA06Kq85FwGJOg/Ajw2SRxcnwMUvW27jh/V80g3bjipMgpRoyAEPt3SXeWQhOKEGROaJ3ch16231BYRbcS0OSRIxC9AdOf+8wO9/fjCi0DqSfYTAa0AAZDnjZA05TSoM5EIoBRRvC/x5DbtW6y1KioaAuA4nPhKcBlxpYTgs2nhUJd6Hldddg0Ew9ygETJODQsgPhH91vcfnBPmtw3H5wbRvAFoUeFVbnnlLvjy7c1KknFx/GAy5Dr1oMaFc4gAbyZCLv8AtFu+xxtWgGWwH1efZqfcKltCvqugo4/bulzzpz7flBtCmtcUyp6EbAu+srRzaixiTe23Sbrgf5A4HNZ7ugGnIKLdbczUAR2HHvgMUsc0umJOarnIRwcPZLHB7Ghrw24Q4KQ5pkFCyLUCpLtKxec4EtqapAWnwCZSacQDxh10ucGPwLlNSoQExYA4mkkBRFxrk8DkGiA/GKVnuzi6nDuqEeZZchIQ5gjiIqBZ/T/GHWy5oBUFAh71lAA/KfdynXrQc0kETBEiDvEeY1xD1VQSq71x6A6c8htm4UKKgAOlRCjjhCljmcHJ8CegGwPT2bVpl21ZYGG4aw4i4P30arQhLnJUz4Q9tl1dsONLkIVq+EoQCJcOeNOVh1/zGK2pQS4k5kO8VJCYtfNrl+kkR6ilpcQ5qgIqUCcyFzhy3AC/7XNDXrrIu3CUhmYuF6K95IT8sg3tQT4wb9PjIpXh7odbfg4EHtg2rgwwORG8fiWENdxPvi43c4++G/pd7uU67DOnOrYSCwhxTNoPiHa1YuNIpZ4SHg/ail3EhwQNGKjIxQ9lCtqX8pCIFChyn6mmaLDX7x3HMdh542QNOUWlVC5NC4kdwKcguFoLxJUmm5YCgFMOHMovNqHtGhxg/wCn9Rctg5SPwgvdecXEqSgmYF5twuIBCIM+U67DOnOa600PQ+JqpU0giRMlBQzkZx5BsOoRJuYqd+WUAeSXEDEuaF4lPgIaxxUhV3KSSU4TlzxsgadQOuwzpzz5jHXAuAbkpQscJGSK16FV8UH0xrJDnUlwnTijlKqJjDIb588bIGnUDrsM6c4suOLXDe13sKIeyCWuqO5D8o/1Nk/uUp9LhU3FD4ZHcdFlAeFAIBnIz3jnjZA06gddhp0DbbipCz4KU7gg6AbIw6gddhr1BNkL1AnfsaUYCMBGAjARgIwEYCMBE8NoYGMDGBjAxgYwMYGMDCJ/1b//2gAIAQEBBj8A/wDHCa7kLcNClXHVYt2JFiiQE6as7kAcz5Tw13K5CtjKasiNbtypDEGkYIil3IGrMQANeZ4o1chk6lGzk5e4xtexNHFJYlPYkSuwLt8C6n2MjNUzNG1Dh3ePLSw2InWq8Y6nWcqxEZUcyG00HFe9QtQ3aVuNZatyu6yRSxsNVdHUlWBHMEH3KSvHPG88OhmhVgXTXs6lB1H8PArmVBOy9awdQ6yoOnUF7dNfLwyhgWXQsoPMa9mo4eSR1jjjUtJIxAVVA1JJPYBxEXnjQWGCwdTAdbHmAup5k/B9xWd2CIgLO7HQADmSSePS2sxLV0B9JLqI9CdAerXTmeXCh3VS7dKAnTU6E6D3zoPYl/KL+ROk3MeYdA2je9yIPPycLLDIssTjVJEIZSPfBHI+5RJLMkTTN0Qq7BS7e8oPaf3uIxLKkRmYJEHYKWY9irr2n4OFhMiiV1LJESOoqumpA7dBqPYaw88aQJr1zswCDQ9J1YnTt5cAg6g8wR+78Rt2lBWOItW/SdxWLgMkBhqjrhgaJJYpH7ybobkdAEPV2jXadXcqN9q4XMYmtkbYKuJ46d+ONbh7svoJY0EpB5rrowBHGUmq4sZGruChhKVbINJEqY6TGZCxbmkkVmWTRllVkMYYl1AbpGh4zWGrwz1ZIaUZiyrLpCZbQlULGddS0XQGb75fh0lyGVxi4CnQ2xhsG1XvInEs2KNtp7CiFmURaTKIywDka6qvLXaySI0brjYAyOCrA9A5EHQj3Lcn+X/DjJxeJtvxFxWV274pRY8VIaGGhjxHpzyZaRV7yGSCCxC1dC5k1K9I16hl5Z46xykew8WuPkcJ6QsTZPI9+IifOCkhOvTl8XXyceKdmjipaGP3Fcxt9bbmRxbstWYWJBJKzFiG0XTXRRoo0AA48Ld04vFW9z+H77lpZzdeIipNde/g/Qbogk9BVXeyi2pKtgIisxVQwVtNOPBfE7noQ0MzjN6be+xcba7k2KMUmaU160XSXCvDTcRMI2OigrqV+4uxo22/a3hsvG7pgu+JO0aSJNLkMTHTthFas5HpMcdxq8skABMiqdAdNDga2L2pBtLZU2+NlGXbF7FriIIq8u98Y1rv8fLHGIklLPIwdB1BizDmePAfc2PxVi/c2xvSVsndiaRkqY2bBZeOSSRA3QqtYaBS5XXUqNeMrtWB5Hy2Fx9HJZFe7bukhyMlqKuO8+KXY1JCVHNR0k6Bl18caVjDvi9v5ffkuQxK3Op47VKTb+Ghln65ixkRpoplZmJ1YNrxtNKKxJRSKytNIAoiEQtTBBGF83p6dNNOWnuW2sXBibGSqvHmYLE1bBHcDwS2KRjrvHB1xrDIZNOiZmAjbnqO0f6aId04+vSyNfInv8fbmW36PkU27d0RJ5eckkco0V/jEjUc+LWXt7Wt3M4u/wChmts72EJ9HpbVh21DRs1hcPUEDXDYQ1gwZ3l77oKdTruvBqtiWrjbMuCzTAy1+t5KsUsogmjZH0VLAXrQgq4YA6rxv3bmZxEGLxmOxW71rYq9GqwxQG5fko6JLy06TG0f/ukc9OMT+hwfJr9wZaKWoXuwosk1QOplRG5KzJr1AHTkSOMnDVs068FGZIUEteSV26oY5SSyzxjtfTs4/WGP+Zy/WuP1hj/mcv1rj9YY/wCZy/WuP1hj/mcv1rj9YY/5nL9a4/WGP+Zy/WuP1hj/AJnL9a4/WGP+Zy/WuP1hj/mcv1rj9YY/5nL9a4/WGP8Amcv1rj9YY/5nL9a4Fg28YZ1XoWf0GTrCk69Ib0nXTXycCW13ZnSexDI0SlEPcTvECFZmI1C69p4WSNg6OAyOp1BB5ggjt14jMsSSmFg8RdQxVh2MuvYfh+4zQ2IknhfTrikUMp0Oo1B1HaOIqT2oUuzo0kFRnUSuifGZUJ1IGvMgcS0aNGCx3NeKeWWaw0P515FAAWGTXTuz5eCrYqgysNGU3ZCCD5D/AMrwsUOHx8USDRI0uOqge8AKug4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4jMuGx0phYPEXuOxVh2MutXkfh4upZrJWnpWO4dI5TKp1ijlBDFIz2SadnDqrBjGemQA6lToDofeOhB4aKeJJon5PE6hlPl5g8jwABoByAHtsTtS3sOzul8piIcsL0V9aioJp7MHd9LV5dSDX1118vwcf9nrn/AFqP6lxiPD+t4cW9vy5ZLLDLPkktJH6NVms6NGK0fxu56R53l4yGGsZnNG5i7WTp3ZYdsbhnqrJhrS0skwtxY567R1Z3WOWVZDGhZdW84a3fDqfLJQ3ZWdY6uOtaRemuasdxlqMT+UZIpVZl5NpqQCFJG4Ytu5QWre08pPh9x4+aOStZp267FSs0EypIquB1xsR0up6lJHG394YCSSbCbmoQZLEyyoYnavZQSRlkbmpKkcjxn85lN/Ymm+AfJo2357lWDK3DirM1SY0qU00cs4klrusRUaSaebxbrbJ39tzeNjHost+DB5WpkHgRyQrSrWlkKAkEAnt/ce5PUtL5R+NxfpsX0SD3bNWcbXnsZCWzkK9QQIzsjSW5gZD0g6BF1bU8tRxWqX688UmKbuas8qMqzVm86JkZgAwUary8gHuqZHc2ex23cfJKteO9k7UNSFpXBZYxJMyKWIUkDXXkeP8AultD/rdD++4rY7GeIu2MjkLsiw06NXL0pppZHIVUjjSUszEnQADU/una3qi//aTjIer6fytr3bc1h1Zkgud46opZiFp1yQqjmTy5Acbqgy1C3DFmJmtflIpAtadAOmJiRousWgGv9Ue4bS8v/wDqNLl//X5PhNwDdsjb0NoxHbXd+aNG0Nbo7QAnn99rofi8bS15HucjqP8A7Lu8bkxx3hh7Va5k99ZbLXILGLn9GsZbO4+SMTwxbgCtLH19MYlSOTWNT3DMn5Lx1wUG7NwxSYWzjrmwdxWbMVo27WPx647KWBHPEySGjYmSI8tNH6G5Ejjxgn3/ALuo7l8SsG25RkssaNGll8pi9vympS9KXHwQxjzpkhh6wOpm0TU68RbFwsFfDeL3h5Vl2Pfp3mCpjL1YrFXtSADV1SnLFaUaef8AF1568eCmx4E3Bk9p5zwZxVjP7bw0W4Z4LEt2WVLElqLb9K45Mo1DGYAN75OvHitQ3Lks674nK0IcVBnBlRZqpPj45W6Y85WrWgGdiw64+k+TUcYrDZTcV3dmQx8PdWtxZFYUtW21J65VrpHGDodPNUchz1PP9xbk9S0vlH43F+mxfRIPdsz9/lvpE/G2P0GP+f3XBOHKdG9MeSwJGg9ByHbpxi7FrISo2RRbNirFFNO9GixIW3aMeoRWI5Dt058eFOMu2O+kg3ZhpElilZopontRNHKh15q6kEfy8XMbtjP7S21hN+ZfFbT2jl2s36eegtZRo45ZlsKywRmELNL1j4sa66ajiGtX3V4KTzSsscMS38o7uzHRVA7wliT/AAni9vCpBhpnw7QNljl58hBVSKVxEzR/ZtDJWXcyOoVFgOuvMjTjYdbaWP29T9F3rWxObrUM3uvDyOcphcssEV4ZDbWPda5Kd6siJL0yxxaJqepZ6u9dx4feFzBZW/iau5cVkZck860rMkLxW5pMZi1aauV7lnWMiQr3hbVtONveEmP3tsvbOX8RZ55kzNP7QpXsRg6Ld9NdlstbAQyFVrL0gFmdujTpJGPqWd4eGuWoCRWyNPEC7JeauuneGItckHVp5WUj3+NjDBbmwmx6O5tz1cbZ3rmL01GPHWK8U2UiMxSN45K86UXglRz53eKPKSMrubI5WPbuM3WaOD2FhI9l7q3fhMnDVgnupk8RcxdinXke+jyy6L1uYIoy2nQ3HghLhN7bduYXJSV8/cyGNxGbw8Nn7WyA23h62QpTZCSYwT2bxlCNJzNcyEEBV4y9N97berY/Y23Ext6imF3RJjJsrkbXVP3lauWLzV0pL3cjeaI5j0dXUx4+3Nsx1MpmLuIa3gobff0ali20BaBZe8jaaGN5NA2qF1X+iSNOPGnHeIl3E57c+yNyrQxlPCQGrj40kw1G+lWBpQZnUSWCO8lJY9vmjRRsLH3/ABEpb6m3Rh8zlPFfw/gw8VKTY0mOiUpEssfTNyskVitkuzE9akDjwm8esru2rb2X4mbqx2MyfhMmMqpBjMPn7hpULEGRVPS5LMPVE8nW/duWYBV0Gs24toJBJn5c3g8VTWxUa8umUydek5Wstir3rhZiUXvUBbQFhxvvflndk1PJ+HFXKYUbNn2otfF3BEle4s92Ft0u8E4Pmh445CqE/H16Re3X4h4rOZvK0cgsDY3FbYkpZCSGaWGGMxYuDIZVmVDIWZ+++KGJUdPP221vVF/+0nGQ9X0/lbXu24PWC/RK/HiJ63i+QX3Db2f8Ptv1sviaG3auPtTy3qtVlsRW70rL0zyoSAkyHUDy/Bx3n+SKPeadPX9r43XT3tfSONubv3ntmpjdu0I7y3baZGnYdTNQswR9McMzsdXkUHlxm3ym8NxZG9lxfli9Js95Sgt3LGVsJYWmfM/InJjpRSq/kIG06k148K4cJmMpiM14WXpb1Xc8EiNdyn2gS+YiyDSq4lTIyHvJuWocKylekcV9wVls1cVNuC1u/c+2A3eQ5vcEs4nq3MhPJ1TSx1HLtFX6u6DEN06r5y+K2Iy2RweXvY77N3fh6bxihnY4VK0pL0To572r1Hu5Iyr6eYzFPN42rBld6VdxQ7O2zQ2thK2b2rg8rDBUor8eNL9ewVeRyzM2uvPp10A43PYx25IrGG3WiNf2vRwmMwtJLMcccKWIkxsMPQ3doVYDkxYsfO0PGH2ltek2N2/ga4q4qi001gxRKSQve2HkkbTXtZif3FuT1LS+UfjcX6bF9Eg92zP3+W+kT8bY/QY/5/dcJE/ZLvOimnv60MhqP4uKH+X2miyOWx8NDf5tQhopI647uP0HXsYRFgSeWvHhDLghYiwFTcOAx2DS6Om0IILEKgTe+xOvPhMx4s+GmS3j4Y7Rqy0PDradPEruKvkMhZige9mMjWQSpGEVxXqpKOrVZn0XzSd5ZHO/6YMkmQk3vkcxsm+uwY5ZqeOLQvRFeQQFoO6ZCURSOg8xpxewlLb+5s5Pn7VGKODbEVJsjWMU6XFnIyFqpCiq0AUsXJUsD0tz43Jtvavh5vlvEzcb4Ld22ty7hymJyD4kbZnl0aazTiNNXnR3iihEzOWfzgE1JtYPDbKzOzdj7P8AQ8VtYbjpWKGWuzCqk+UmtRWdGZxclkV5ANHkEh59pTdu5NjZ7/UJe3jtHPzZdoaONmho/wDrGO+za8EGVuV6sEdWqpiAWUyOzPJ06M3R4R38P4FZfwko0du7vjyVy5j8NViuPP8AZXcr14a5cXVOh9BMU+N5nV5/T4b1KG0Nzbpp4Rtwbjvpt3E2ck7TphbeGo1QYEbpeWfK9XMhQiMzlRoeNjU8/svf2A2Dgd3YWKjuzbePsXpMZV2xtG3trIRehwVrE8AfIuQj9wySozaEdIL+HuW2fsfbGX29TzW1Nu4fc+79u5A7nNWDPV6rR2sfJWxxjoIDIZE8zrgLadAbq4xOBxtHN7Ox/iBldvYi74v+HOb3Dj6GOiq1K+P6Z9sUp5Y68jrXStWctJBGrL3rAx+fWqCWWwK0SRCedzJK4RQvVI55sx01JPaeP9SlWW4dvN4p5aabaOdrSq80Ec23qONW0AupR4p4XIBGvIHy8eBmLzHh1tfw5xngxWsVs9vPCZFbNnPxPjpaBgrQx1opI4rLyd/N6QxIbs1YdTeFvghuLB4yrsnwv3Rj8lb8SIMmsj5bE4GybmNghxwj72KaV1iSXrboQIxUtqumwsC9a8aGR3rh8puHL06tiyMdituzjNXLLmvHKVJWmsaDQks/IHQ8eIdzbWzd6+IOzd2XknpeKWWwMNa/Thsp3dppsJbxiZHLGJUUxNAI+vkr6aamrk/DullfEjP4T0TFPFu2K1te7fnhaBLk87XqMRVlR2kJSHoZgUU+97ba3qi//aTjIer6fytr3bcHrBfolfjxE9bxfIL9w9yepaXyj8bi/TYvokHD1JHlsTRHSYQJ1BD7zEkDX4BwtulKJYmOh5aFWHarA8wR7pmfv8t9In42x+gx/wA/uqY7c+Ax248fHMtiOhlKsNyFZUBVZBHMrqGAYgHTXmeP+1m0P+h0P7jitksZ4c7Xx2RpSLNTv1cPShmhkQhleORIQysCNQQdR+6dreqL/wDaTjIer6fytr3bcHrBfolfjxE9bxfIL9w9yepaXyj8bi/TYvokHFqvaBWzHK/ehu0kknq+EHXXi7j8LDHZyV1kEk82rRVygOuijTqcgjy6Dy69nBmn3TkVYt1dME7QID8CRdCj+LhPTLzZ+jqO+qXj1OR5eibTrU/v6j4OIMxiZS0TnosV30EkEoALRyAa6Eag/CNCOXuOZ+/y30ifjbH6DH/P9yNreqL/APaTjI6dv2fT01/+ba4HUU08ugOv+3inmLXQs16a26p06BYlsypCvm+URqoJ9/j40f8AEf8AfwcHHI9jIxRpLbSGvLJHXWTXu++lXzELaHQE66c9NOfFbGWMtUTJW5BFXoA9UpY8xqikkD4SNOB1FNPLoDr/ALfZR8tYaS5OparjIAHnkA8umoCrry1Yge9rxYvbW2bj48ZBMYVt5K8Yw7jTVU6UHURrz0Gg4vYu5t7GC3jp3rWhHLK6CSM9LdLa8xr5eK+Dt4mpUgmhmlaaFpC4Ma9QA6iRz9jcHrBfolfjxE9bxfIL7ththS1ZZLGVhDy5VT+QrTyrM9avJy+PMteQqNfJ8PtPEWq3hrkM9tLwqqY+9vTdNHI0klghyEHpAaKjZaFpe7TqLBZNTpyBJA4hswkmKxGskRZShKuNRqrAEcj2Ea+znd6bntNTwW3qrWr0qKXkYahUjiQc3kkdlRFHxmIHl4xkWc8E8rszbeWhlnhz93LY2easFQvElyjBI0sTygaAKXCnk5U+zuPcsldrce3sXcyclRWCNKtSF5igYg6FujTXTjZ2U8RPCrLbB2tvx6EO3t5JkKWWxyT5MJ6HFeNdkmq98XVVZoinUQCw9w3J6lpfKPxuL9Ni+iQcZjN2KUFufF05ZqvfIG/KBT3Y589CxHGbpb4jp35rEMU2I+0SApcM/f8AQzEDqOqkjtI/h4/UGD/CT8bjKWJ6eLxHdV3aG/UlCTpIB5ndhW1Y66ebodeDh3citnaziSE8gJ4FMiNp7/SGH8Ps2LVSavX7jzrNuyGdYolBLsI0ILtoNFXUany8Jkody55ZFfu59HFJoplVWZDEkaqenqAOvUPhPFK1fkWa5rLDYnVQgkaCV4uvpHIdXRqQOXF7B0oLNrN1pXgq0inSth4z0yFZAWCpGfjM+nLmA3Ljc2Py9yO4+PlqmHuohGkffwCRo0/pMoPYWOvsZn7/AC30ifjbH6DH/P8Acja3qi//AGk4yHq+n8ra9jF10dS8HfiSMEdS62JSNR5NfJxcWLIH0LF27cOQxyKvcw14tYIEc9PUZ5pAZderRUAGnnc98pcsyVKhvU7FmWKdq3UvoMSFXkRlPSQOfPjI1VXD4nF10hNdIOsSyd9yikksOqRyd4AeSliDyLE8blrXrPpRx2YmhqP0KhWu8UU0aHp7ejvCuvaQOfE22hSuCzDTF1r5i0qlSwXoEmvxufvf7OMpmrI6osbWecx66Fyo81AffZtAOLmYyk5sXb0hklc9g8gVR5FUcgPIOKNTr6e7s2G0++k14zlnXXv708mv3zE8Uv0S1/Y9jcHrBfolfjxE9bxfIL7rr73G6d4xZOHDZfL5cbhxW17WMLXkfEP04yE2vSVMZaOIcu7OnW3vnjH3pqktCa5WinmozqUlheRAzRup7GUnQ+z41St4V773dn89SwMvhZl8FZephUydCge7kvGzbhx7rDZCFvSIZdAGAU/FOEg3FZhu7ghx9ZM7crr0Qy3FiUWJI1/oq0mpA8g9nPYHaohl3LWtYzM4OjZfu4LdnD5CvkEqyty6RN3HdgkgAkE8hxjsPa8K98bSyUkTtnJs1i/RsfQlji6zH6cziOyGbzUav1g/0ujnp7G/8bj60ly/kNt5WtRqRKWklmlpypGiKO0sxAA42B4I0/DDdO0akUe2I9/by3LWhx1alVwM9O3MtJDO81iaWSoI0Aj6QCWZhp7huT1LS+UfjcX6bF9Eg43R+if/ALxeOl1DL7xGo4ls+jq3TYaPXT3gD/PxeqiJVatJ0jkNRqoPL+PinbRCYcRXnszv5B1IYVGvvkycXbsdSW/JUgkmSlANZZSikhEHlLaaDijkZqM+MluQrK9CyNJYi39Fx7/GcpwNTC2sxllyNm8veiNVsyDqSM+aWXQadRAXTXn2cVKeMzNOvh6k5NwaJdvo3X1yQPZjcwOXJJMnT1aHmvVz4jnyt21SxlGxfnuRVJnh75FtTjumMejsCeQVSCToPg43NdOMr42/JlJIDWhIcV4BFDKtcN2aqX1k05F9feHG847uXt49g9Eyx4uT0dWDVyF1kIaTUAdoZefk4jiDM4iUIHdizHpGmrMeZJ8p4zP3+W+kT8bY/QY/5/3H43v4rUE3dU8N8tj9u7P2PkZHfH46pNi4bj5AVA/dtPcew+krqWVUCoRz4xu29v0/s/C4eEV8bREkkoiiUkhA8rO5A15an2c3V35taluaHEeFeBnxQtqxNeWTOZlWkiZWVlbQDmDqPJx4y7AXOXdy7X8Nd5HEbPyuRsyXbMNabH1LsuOktSs0kvocs7RqXYsBopPmj2lnepzbRjYeXqbegxKpKY5sfIHjy85YKY+pJZ101P8A5PwjQMpDKw1VhzBB9n/UhuDD+HbZnM7exu2po/FCrkEp39qVUxzS2btSETQTWHjjQyd0jr1hekk69DYXLULhyNHKUK1ulkGAUzxTxLIkpUAAF1YHQD2+1vVF/wDtJxkPV9P5W1xBM+VyNOKFSrU6c/cRyHXXqcovXr5OTDinVweOTI7iuemvUx4ITvDHYlUTWJCRog0A6mPM+aOfFizmMjdxEcmWnuzY+GBak1qV2V2knkIMhQtqoVQo6QBqV043RFlKkd6GnepTw15fOj7wU0AZ0+K+gPIMCAefbxda3h4sThIHYYiOcqaoedulGpIH6UmmZGZwF16dOQJJbMTmfvny943G83p6B3UcSoOZ10EeuvsbiWDXVUgeQDyotiMt/IOLNDFzw046MSy3r9gMyRhyQihV5szEHyjs4WCLdtPoUlh/ysnlOv8A8Ti9kqufpZOxErzmj3LwtJoOoqrlmAJ8mo04gmjUlIKFiWUkdikKg1/hYexuD1gv0Svx4iet4vkF+4e5PUtL5R+NxfpsX0SDjdGv/wBUHyi+xbh3DuCnirpyEsiVrDHvDF0JowUA6gnXTTjNWNpYuxco3LZNadkMaJEAqB5WbzU1010J1/h4NVZFtZW6RJlb6jQOy69KJrz6E1OmvaST5dBxqToPh4yAwGHhyM+YuNYv05rHdRM9gBZZWLLJy0Uaqq8+fLiGxNJpXokGvtnb0MUcZ0bUKzzlSwPYQOgcQ4jPxiCxYmluT1oZfOrySWmtxKsiHm0TFeY5ajly4WlQQRQKzyMWcu7ySMXkkkdiWZmYkkk6k8eIK6+ZCcUhbyBjWZiNff0IP8PGgYE/AeMz9/lvpE/G2P0GP+f9xje/pGc2tu6SmMff3BtnLW8RPcqqCI4rfosiLMI9T0F1JXyHkNMdgqE1yzUxkIhhs5C1NdtSaakvNZsM8srsSSWZiSfZj3rfzO6sHnRiYMHal25n8hhUs0K889mOGcUZYjIA9iTtPl4qbY2fh4cJhKRd4qkRZi0kjFpJZZZC0ksjsdWd2LMeZJ9kgHTXyjyfx8WNnRVnlwduG1DchmdpJJRdZ3nZ5D5xZ2kY68UMZXeSSvjq8Vau8zmSQpEoRetzzY6DmTzPs57cu4JdxS/5qhrV90bep53I0MTkoqkXcwpcp1J4UmATlo+oPMHkSOIataFK9atGsVeCNQqIiDpVVUcgABoB7fa3qi//AGk4yHq+n8ra9idMXTSqLMjSzsCzMzMxY6sxJ0BY6DXQa8vY+0Yb17E3ZEWO3PQm7r0hI9egSqysrdPUdDpr5NdOEulJLt+MFUv3JXsSqDyPQZCQmo7egD2bmOuR97UvwvXsx++kilWH8R4s/Z1k1jOrJWuNGJK92t1ar1K3LVfKAdVPl0PP/GY35l//ABODAbOOcTfkyi0uZ6uWg/KdvPi1chrR+n5IIsi2AWaOFCSE5EaFidW/eHDy913FmuwSzCDqASNQVPLkeNwesF+iV+PET1vF8gvunhHt/a1uhj5N/wC6pMNlL96o9wRVocVevkxRrPB57NVVdS2gBPI8eHOy9kSUqW6vE7PSYqpnMlA1qtj6lKjYyN2z6OskPeyCGsVjQuoLMCToND4mY7xDenPuDwl3Bbw+XzOLrtXgyFSKlXyNa4tZpZjE717Kh4w7AODpyIA8Dt172sbfm2R/qCeWLGbboUpoLmAazj5cpiR6Y9hxb72CEpMTFHo5BTzfN9je+/pavp3+UsNbyUNHUqJ5YYi0URYAlQ79Kk6cgdeNv+HXitk8Dnm31tS3uLD3cLQlxxx+Qxs9eO7jmSWzY76IJaVo5SVfzWDLz5eFu3q+LwdPw43hnnw1jJPNNYy1yQYi7kD0w9CRVo45KwXUtIz+ToHb7GM2HnsXg8VsXNYDPZLACnNNbyVg4q1j4Y7FqR0ijhEi2zpEgYjTznPYNzbu8OsZhbl3b+NvZLK3s3NMsdWtUrvL1Q14UJnlZgAqs6KO1iRyNCzLoZbFeKWQgaDqdAToP3z7G0b+0cbhDty9uLBYvdOYyU0z3ETK5erQ7mlVjQIWKTMTJJJoo7EY9mX3Jg6EOU3FLZx+I2zQs9fcSZLL3YcfVM3QQxjSScO4BBKggHUjjdPhD4kZjEbmyuP21jN27f3JiaL4zvqd2xYpWYJ6rz2ADFYraoyvzRh1c+N77Ey92jfweN21htx4JoKjVrEH2neytV68zmeVZehaCEMFTmTy9tuT1LS+UfjcX6bF9Eg4eCxCk8Eg0khkUMjD3ip1B4/UeP8Am0X4vAIwlAEdhFaL8XgRwxJDGvxY0UKo/gHs6MoYe8Rrx+aT8Ecfmk/BHH5pPwRx+aT8EcXZ4a6JNkZVnuydpkkWNYlY669iIoH73Gqoqn3wAOMz9/lvpE/G2P0GP+f9w2c/uTJw4jD1JIIrF+fXoWS1MleBPNBJMksiIoA5kgcRZ1r0UGHlqpdXIzt3MS13QOJHMnT0DpOp6tNPLxFuPZ2cq7jwNiaevWy9J+8ryvWlaGXu5BycK6EdS6g9oJHC7Dj3HQm3kaUuRfbcUyyW46sLRq8ssa6mNdZVA69OrXlrofZzu1sHuOhl9wbYjgk3Di6cyzSUhYaRYlnKaqjMYm80nqGnMcxxj03vumpgJsqs8mPrSiSSaWKsoaeURQo793ECC7kdK6jqI4pZPGW4b+OyMEdrH367rJDPBMoeOSN1JDK6kEEHQj2Ku394b1xuBzFuKKwKdl21ignmFeKawyqywRvKehXlKqW5A8XtybqzFbBYLGqrXMlafojTrYIijtLM7EKqqCWJAAJPE2Y2dnK+dx9W1JRuSQ9SvXtQ6GSCeKRUkikUMCVdQdCDpofYgy28M5BhKVuzHSpNKHeSxZl1KQwQxK8krkAnpRSdAT2A8UNz7WzNXO4DKI0lDK1JBJDIEYo41HYUZSrA81YEEAg8ZyrtbcVHcE22rS0s4aEyzpWsMgkETyJqnUFPMAnTsOh4g2bJloo9zWq726uIYOJJIYwC7oSvSwUMNdDy19ttb1Rf/tJxkPV9P5W17i9DMUIcjUfmYZl10P8AWU9qn4QdeO8hnydNCde4inRlA94GSN2/l4Wzj8b395fiZC23fSr5NU1AVT8KgHiWGKjNcg6iYLEK9YZSeXVp2H39eLEt1e7s3nVjBqD0IgIUEjlqdSTxuD1gv0Svx4iet4vkF90/0+5o0L9vGYLfU9jMWKNOxdNaGXA5SuksqVo5XVDLKi9RGgJGvHh1vDYr46XdvhnnZctRxOXmkrUr9a5Rs465VexFDO0LtFYJSTu2AYc156ixiNx4mrkbvjlmc9uLxQyOOFqbHYWlXxtTHUsfVuNHCGlYLCA0qKZNJ2VNE83w5rbxiwma2j/pqoX7+zzg2uXcvuWeli56GKjkotWUVmjryHqSKSZnl6Qmg429d3PUgobjuY2rPnqFYOIYLkkStPFH3hL9KOSo6ufLnxvTYFq21CLduItY0X1XrMEk8ZWOXp1HV0PoxXUa6acHxd3fg9vZbemx9qvtTYe1tuyXsnDfyWZu1EtZS27VK80EX5GMmNA3dx967yADjwezdGzTjr+H+5ps1m1naRHlgkxF+gFrqqSAv3tpTo7KOkHztdAd6PnMIcJjcVuGxi9qCSKaKe5RqxQq1yQS6arLYMojKDpaNVYE9WvsbJ8QobFRcLtva+ewl6q7yC09jKWcbNC0aCMoUVabhiXBBK6A89N74TIYyttTdG/sRnMdhsbfSzVWrBZexBjmurJG8yO0HdvKBHqrEgLy427s+DChtsVtsT38/ud45u7F6OevXqU68vmxlnXv5HB1YKq8h1c+MZhMNYqVrdLdG3M3LJdeRIzXxGWq351UxxyHraOBgg00LaalRzHiJsChXi21S27DtvLbE3bchsdFncNO4cpoxGivWiNavHIYwzAu/PqUDjcvi14mV8Hi9y5bAY7auG2/gLM96vVx1Cexbllkt2K9VnexYsswURgIiqOpzrp4jZOtTvLjamx9tYaTIz07Neu96plc9LPDDLNGiTdCWI2JjLLow5+23J6lpfKPxuL9Ni+iQe7Zn7/LfSJ+NsfoMf8AP+4Y/wD757E//F2I4uYbOY2tmMRkI+6v4y5Ek9eeMkEpJG4Ksp05gjTjMT5O/RwOLi8Q94Uq81qWKrXWSfdF2vWgVnKqGd2WNFHaxCgakceH13FYenjbmd2Ru+3m7daCOKW5Y9NwSiWw6gNIwUAAsSQOXFmfD5SplYadqejbmpzxzrFaqyGKxA7RswWSKRSjqeasCCAfY8WaOBxFLB0X2NtW1LVpQR142mmyOdeWVljCgu7HVmPMnmeM/ufcO58PLtTP+ENCbZ+elt13xlnH18nfkywgn1McnJ4HfoY6p0k6gDp8LdrW70cG4K+2IMpFty1NH9pQ4e1YmGPmmr6hwhjUIH6ekspAJ0Psf678bva5BFmc1iqjYnH2JYksXcdPtqKrjI6KONXJu97GvTrpKezqOp/04Utw5mpJPsbf2zovFOJrEbfZliTDWTVOSQEGANclrOveheZR+znx/qEy1TMUa2D3DujbuFxV1rMaVshn4cHGbcFMs/TLMIwgZY+ZKMdDoT7H+nTJZ63DQwC1d41at20wirR5iepSNUNI7BBI9dLKx68/jgc2043zVrZCnFjd7eI+9L2xcdJPGDkqEN0Lcnows5aSETF2JQFQGHYCOP8AUrg9v4qpg8LjNw7ahx2JoQpXrQRja2MPTHFGFVRqddAOPBf1fuv5Cj7ba3qi/wD2k4yHq+n8ra923B6wX6JX48RPW8XyC/cPcnqWl8o/G4v02L6JB7tmfv8ALfSJ+NsfoMf8/wC4bW1r+UyGEhnu43IQZbFmuLdezib9fI1nj9LgswnSasmoeJgV1GnFGnZyFjL2KleKGxlrawrYtPGgVp5lrRQwh5COphHGianzVUaAYejZx0dahhc9X3JXoVESGGXIVZmsxyTIq6N+XImPlLgMTxBk5KVeTJVYpIK2QaNTPHFKVaSNJCOpVcopYA6HQa9nFfbmDWUUYJ7dt5Z2Ek0ti9Zlt2ZpHAXqaSaZ2J08vsZgGBad3OUGx17MVVSO4YCsioBN0k/kzIxTXUKSTpzPG1dp5HZ2IzuC2VXq1tsUctSr3lprTiWKF4u/R+l1VB5w0PG4t4VkkOb3NTx9C/K7AolXGd+a8MKgDpUNZkY8zqW/e9jE57N7Vw+ZzmBYtg8zeo17FukSdSa08qM8R1/qEcbyrz4yLMVt/wCUky+6auTjjtRWpnggrBHjdOkxpFWjRVIOgX3+NkGhhK2Go+Hl2fIbWwmNhiqUILM9SakX9GiRU1SKxIF0A0La9vsS4bdWAxu5cPMwebFZWrDcrOy9haGdXQka8tRxhN1V6QrX9t4a1gcFUhCx1KlO7NXmnEMCqFVmNWMajsUaeU8XrlajXr3Mm6SZK3FEqSWHjQRI0zqAXKooUFidAAOzjC7+t7jy0sm3qVulh9saUVxsJvLGtifVagttI4iX41goNOSjn7ba3qi//aTjIer6fytr3bcHrBfolfjxE9bxfIL9w9yepaXyj8bi/TYvokHu2Z+/y30ifjbH6DH/AD/cja3qi/8A2k4yHq+n8ra923B6wX6JX48RPW8XyC/cPKCfIXXxeOQXlleeQ99BKT6NCzE6sFbq5Hl5h43F+mxfRIPdsz9/lvpE/G2P0GP+f7kYoQ5C6mLyKG8ZVnkHcwxEekwqwbVQzdI0HLzxxkPV9P5W17tuD1gv0Svx4iet4vkF+4NXI703DU25RuyvDVtXGKo7xQyWJACAfiRRO7HsCqSeQ427csZeD0bdtmGptmaImYXpp4JLUawd0G69YInk1HLoUsToNeKW1buep1tz5QRrWxhbWU94JWgSQqCIzL3chiVyC/S/R1dLaX8gMzZxsU5E9zpNcRL3caoXJlhcgBUGup04O9MFvyxmdq62gmcorWsQSehzyVpzGY6rFwksTrqoIOmo1HPijlMdui7cx+SrxWqNuMVCksMyh43U+jdjKQRx+0GQ/BqfV+P2gyH4NT6vx+0GQ/BqfV+P2gyH4NT6vx+0GQ/BqfV+P2gyH4NT6vx+0GQ/BqfV+GkfcOQCICzHpqcgOZ//AJbgW8RvCzkIDpq8Jpt0k+Rh6Pqp+AjXhNiv4hTvu1hGZcLCkE0kHfDqiFhoqjJAZBzQSMpYfF14yGBr7rvS5bFRQzZGmsdbWFJwTEWb0Xp1YAkDXXi5NtDcdPeGIqXrVG9lKssdmB7XV3s8fexARPoZefRqPJ2g8Y+bcOYxu1cZZtQ43ERTuldZrM7dMVatENC7sexEBOmp00B4wWHyF5KuR3NNPWwNZw3/ADM1eB7UsaMB09Qhid+knUqrEa9J0+4kFjcm4Mbt+Cy5jrT5K3DUSRwNSqNM6AkDnoOGzmTzFLHYVEjkfMWrEcNUJMyrGxmdgmjllCnXmSNO3jHw3r1WpcyUrVcVFPKkctiXpMrRQhiC7dMZYqup0XXycS3qN6Cv31eKCWKau035p5GBBWaPTXvD5OMzDjN67eyU23Je43DFVVZmoShO87u0EuEwt0edo+h058V8ji9w4nJY+4gkqXqtZpoZUPYySJbKsPhB4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4L181jp0VmRnjqOwDKdGUkWjzB5EcRGznMZXE8qwwGWo6dcj8lRdbQ1ZvIBzPFdLefxdZ7kogqLLVZDLK2pCIGtjqY6dg58T4y7mqU2cyztdixylYZmjjjjiZkgaR3KqEGrdnPi1YhrwVXtN3t2dEVDIyjTrkYAdRAHaeHy8OZozYmIssmUSxE1dSjdDAyhugENyPPt5cBlIZWGqsOYIP7v8PfsGlLfi27mr9rM1q2Xlwdlq9zCZHGjub0KvJGRJbQt0gkp1DQ9h/wBKmNzdebO3tkbgpUN2ZDb2Ms2a0T/5QzWK9J9HpVyYaxtTxp1mNI0DAt3a9mRmqx41dr53c+2N1Ws41iSO9Wbboh7ykIBERIJ2rR9B7wAK83VzCiTdFC7gpMfjcPcipY3ITv1DJxyVILEs0cZQdMaPMYdST1Mj9g013bXzNaaaT7c3nmIquKrWMjM1XM57JZGosVapFJNI5htR6oiEg6jnpx4c4TOR3sZYxuAxAtVUeSpYinhopE8co81lKkkMp7COfZx/jsx/1Cb/AH8f47Mf9Qm/38f47Mf9Qm/38f47Mf8AUJv9/H+OzH/UJv8Afx/jsx/1Cb/fx/jsx/1Cb/fxIqXsv1lSE6shNprpy159nEOTv5e1byUWhCVJHqwr8BKEO4/fIB8o43vXgwmKzu1N97qn3L/mb7RetfqLbrwRTQWajVXExiMAWFkl0MfSjBO7BfxMhmoXoUu18LZpX5KdhacyJXeJxFbMYgd0YaMiuXXkSoBB48Ub8uMFtchuLI5/AYmgQ0k1X0KskMKIq+a7NAVCge9x9mQ42tT3JdfD3HoSzB0rSQXatmzGs5RerpWN1DdI6veGvHgFWq47IXPQ94ZLKZC5XpWZqlSpDtbN0zJatxxtBB1T3YY0Erq0jN5gbpbp+4nhjn8bsO74h0dvW8wcvgqJoB2W7jZa0PUMjYrQ9LSOAxLchz04wGP3OlfE/ZviLtrO2cXXk72rhcbY39QySUlkGo7qhWcISPMUIek9AB48Ltw1MJXu5PbG42lsZgxxekVMfNj7scwSV9HCPK0PUqnziFJHm8sztWvUt1psfiat586Yx6KGvyWYY4o2JBeSP0Yu4HxQyanzhxibm6duptRNo+HGB2dmbjz1pRlL+HezJYvRtWlk1g0kLo8wSQmV+pBz129FRyU2MN2OaWtk6wHeKjXZJFZOsEaOvLs7Dx/3DzX4MH4nH/cPNfgwficf9w81+DB+Jx/3DzX4MH4nH/cPNfgwficf9w81+DB+Jx/3DzX4MH4nH/cPNfgwficXsrPuqfERS2pZNKrCSeypckNMCO6BYc9CG/e4xuOxs0FKlgqUuQq5W5H3vXlmPd1ZI1jeMrJXCs/UR0+eNBy5eF17LVTiblu5J6ZSkKkwWZ8bYUwllLKSHbpGhIPv8WJzjeurLuyvuKDcfXD0x04sOlBqvT198H7xWAAXpKsT1dq8bxwtCg5nXvMfDHZIiiu6RxyOFbU+Y/W0RJHaG8nM+LWZyVNsHW3IbVvH4qYxGSFfsuvRHeCB5EDzSwltFY9o184kcUgeREEeo/8AdH/i2QZUBHaCw4LK6lR2sCNBx+eT8IcAs6qG7CSBrwFWRGY9gBBPBDSopHaCwHBZXVlHaQQQOPzyfhDgMzqobsJIAPACyoxPYAwJ46WkRSO0EgHglXVgO0gg6cfnk/CHAZnUKexiRoeAFlQk9gDDjpaRVI8hIB4JV1YDtIIOnH55PwhwGLqFPYxI0P8ADwAJUJPYAw40aRVPvEgHg9Dq2nboQdOPzyfhDgOXUKexiRp/HwAJUJPYAw40aRVPvEgcHodW07dCDx+eT8IcdZdQh7G1Gn8fAAlQk9gDDjR5FU+8SBwQjq2nboQePzyfhDjr616P62o0/j40EqEnsHUONHkVT7xIHGiOrEdoBB4075OXb5w46+tej+tqNP4+NBMhJ/8AaHGjyKpPYCQONEkVj7wIPBBlQEdo6hx19a9H9bUafx8fnk/CHA63VdezUgcaJIrH3gQeCDKgI7QWHGqMGHvg6+5DrdU17NSBwAJUJPYAw40eRVPvEgcHodW07dCDx+eT8IcdfWvR/W1Gn8fGglQk9g6hxo8iqfeJA4ISRWI7QCDx+eT8IcdfWvR/W1Gn8fGgmQk/+0ONHkVSfISBxokisR2gEHjQyoCO0dQ46+tej+tqNP4+PzyfhDjR3VSewEgcaJIrH3gQeCDKgI7QWHHWHUoO1tRp/Hx+eT8IcDrdV17NSBxokisfeBB4IMqAjtBYcFw6lR2tqNP4+PzyfhDgdbquvZqQNeNFkVj7wIJ4IMqAjtBYcFg6lR2sCNB/Dx+eT8IcAs6qG7CSBrxosisfeBBPBDSoCO0FhwWV1KjtYEaDj88n4Q4BZ1UHsJIGvACyIxPYAQTwQ0qKR2gsBwWV1YDtIIIHH55PwhwGZ1UN2EkAHgBZUYnsAYE8aNIikdoJAPBKurAdpBB04/PJ+EOAzOoU9jEjQ8fnk/CHBZk1ZjqTqeGjVdFb4w4/N/ynhVddQnxRwHRNGHYdTwWZNWPadTwyKuiv8Ya8fm/5TwqMuqp8UcB1TRh2HU8F3TVj2nU8MqLoH+MOPzf8p4VGXVU+KOAypoy9h1PBd01Y9p1PDBF6Q3xuPzf8p4WNl1RfijgMqaMOw6ngu6dTHy6nhuhenq+NzPH5v+U8CMrqi9g4DCPQg6g6njqdOpuzXU8N0L09XbzPH5v+U8CIr5g7F4DCPQg6g6njqdOo9mup4JReknkeCe77e3meO66fM/q8AiPQg6jmeAzr1Ecu08Exr0k8jwSY+Z5nmeO66fM/q8AiPmOY5ngF16iBoOOpE6SRprqeCTHzJ1PM8GIL5h/o68fm/wCU8AyL1dPIcdSJ0tpprqeCxj1JOpOp46UHSuuunuQ7xerp7OAwj0IOoOp46nTqbTTXU8Exr0k8jxr3f8p4EXT+THYvAIj0I5jmeOp06iBprwTGvSSNDwSY+Z7eZ47rp/J/1eARHzHMczwGkXqI5DnwWjXpJGhPBJj1JOp5ngxdP5M/0dePzfZ8J4BkXqI5DjqROk6aa6ngsY9STqTqeDEF8w9q8fm/5TwDIvV09nHUidLdmup4LMmpY6k6ngxBdEbtXj83/KeF616un4vHWiaMPLqeCzJqzHUnU8NGq6I3xhx+b/lPCh16gnxeA6Jow7DqeCzJqzcydTwyKuit8Ycfm/5TwquuoT4o4DomjDsOp4LMmrHtOp4ZFXRX+MOPzf8AKeFRl1VPijgOqaMvYdTwXdNWPadTwyougb4w4/N/ynhUZdVX4o14/N/yn/w+t57dGcx+28FQ7v07NZSzFTqQ97IsUfeTzsiL1O6qNTzYgDmeE3adzYkbVkiSaPcxuwfZ7RyMERxa6+6KsxCg9WhPLjGtuHcuKwK5qwlTDtkbkFUW7EpASKAzOveOxYAKupOo9jKQY/LU702Dm9GzUcE6SGpN3ay91OFJ7tu7dW6W0OhB7DxXy2DylTM4q2GNTJ0Z47FeUIxRuiWJmVtGUg6HtBHuWPi3dvDCbWky8vc4qPL5CtRa1LqB0QCxIhkbUjkup4o4OzlqdfNZSKafGYeSeNLViKv0988MJYO6x9Q6ioIXUa9vGUwdLLU7eZwkdeXM4qGZHsVEt956O08SktGJe5fp6gNek6dnFrJZS9XxuOpRmW5ftSpDBDGvMvJI5CqB5STxQyeRzdDH47Ky16+MvWrEUMVia2QK8cTyMod5SQEUc2PZ9wPDTGbq8S7vhls+9trd9/JZWrlxh1a7Rnwa1GaRz0Ssi2JuiN1YN1HzTx4L5HdFzIU87uHOYqDN2kWXFWrlZpJu7lmihMbQmzEkckkY6QCxUqB5vHhNYrbjyNfFZK7kcff2zE0SUZ+jFXrInlAjEruGjTpDSdA01CdXncbuXb0kGR3Vg8NNeq4WN0ecTPDM1PvItSyiaSJlUsNG0bTsPG8aCbyyO9dp3Nr7WuPnnvySSxZzJ1732xXgtQOjV2EcdWbuoujuWl8wICoHhFnMxdlyWXzOysBeyuRnYvLYs2MdBLNLIx7Wd2LE++fct7199eI9zCbrwm6mxuyvDdsn9l1pMStaF6k0dHWM5D0rWSQyHvADrGvT3Z48SEnu2poMdFh4aFJ7EprwrLXeWQpAW7tWZuZYL1Hs1048Tqu5t25Dc7bd3pkagz2ZkhEi1oadOTQiCOCCJFLMemNFUczprqTFuPa28L+Fjq5PC24rmFmgCZGrav1oe6ecxyP3LpN1awujNoB1FCyt4Fmlft1I8tvDIYvK1YZ5UgtVH2vm7vdzwKwjk6Z6ULqWUspXzSAza/cSzjtqVMhczlfP7XylaHEyY+LILFitwY/IWJKjZVkpd9HDXd4xOe7ZgAwIOhjwWaxs+Pno7sr5QVJ2r2LyU7G64sh3t96ANYz93IZJu4/JKdQnmrxnY9u4CHOpvLYOX2bUybWYIfse3kpI3ivSrO6MYR0h2MPXKGjTSM66rPt5cbb0rY2LIzZgppU1nmkijgVyfOk/JMxA+KNOrTqXXxOsV8NVweDzuZqZDEJUSGKKRExdSGxL3UPxSZ4316gCT53MHU1JCjolnMbgsQdalS0U2auyROAwB0dGDA+UHX3Lce/YNgyeIWI3ZgMLhMfZoWaEV7DtQmvvZWWPJ2asfo0vpKOTAzSFgQY2AUjaryKyx2NnZuvDL0noaX07Gy931aadRRGYDXsB97jxV3TWwVbFbe3dgtsR1b1YQRi5kqdnNy5B5I4j19YFyEs7qOvq5FiG03BVt7XdkzFbIQUsBm68XVJLVmlSnM0UpZF63iSeItoV1Vj0sOW2cPbhZMnHd2jTamg71u+gydBpVHd9QPSsTsSOWgJ10+4ET2K0U7wHqgeRFYoeR1UkHTsHZxUr5aCSeOhchv02inmryR2a7dUciyQPG40Pk10I5HUcIzIrNGSY2I1KkjQkHycjxn7GKwtP0meaBMnflsvFJO0deMx6gQyckR9ANffPl4t0WxVSCC6ksc7VslYrS/lgQ7JLDXSRHOpIdGDA8wQefGI27hNv47H4XA0q+OxFCO9MUgq1Y1hhiUtWJIRFAGp14/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4/VdH57J9V4jlfD45pYeoRSm45Zerk3SfRdRrpz04ye4K2EqxZXMxQQ5KYZS0Y5VrAiImAwGIMoJHUFDacidOLtbIY+Ja+S9Lp5SkG60kAd68oLBULBwp56A6cR1FgjFWJVSKv0joVU06QF7NBoNONu5rI1Xs5Dalie3gJDNMscFizWkqSTdyriN37iaSNWdWKq7hdOptfuNVw0ljpyV2GSetW6HPVHEQHbqA6Rpr2E68WasWTnowV6deVUhSBup5ZJ1YkyxyHsjHZx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vx+0F7/AIdT6vxla9m29w07gihmkWNW6DBDJoe7VF7XPk4tRQTJLJSkENtFOpjcosgVvh6XB/h9wxO1rexp90SZTEw5UXIsgtQRrLPZg6Chrza6ej666+Xs5ceZ4RXGH9FvtlNG07ek+haH+DjFbAreHljb02USw6ZKTJLZVPR601k/kxWi11EOnxvLxtfBZW+a2W3pcnobaprDNKbM9atLcmXWNGVAkMLuWcqvLTXUgexJuIbiibEw59drTTLFOZEzL31xq0mgEfeiQ2XVdCmmhD69353Ek0zrFFCpeWRjoqqo1JJ8gA4Hiuc0U2C2JGcXOvVtITQZO8WYVmiFg9SkFV7vqbUaA6jhXGoDAEBgVPP3wdCP4eMdtjO73wGF3Ll+j7J29fyVWtetd6xSPuK0siySdTAqOlTqeXGPg3fvPBbUny7lMVDmMjWovaYEArAtiRDIQWA0XXt4BB1B5gjhMln964qlTe7Vx5mWws/RPcnStCJFh6yi944DOwCoObEAE8Gri89jslZCFzXq2oZn6RoC3SjE6DUc/dtyepaXyj8bi/TYvokHt2wZruregC9Hb1HQw70xMgHbqvmkn4fYm/8AmH/YPa5jKhgtlbV+HHg+WxLbmWPl5ek+cfgB4rx2JTLkMM3odtmOrMqjWJzrz5py18pB+5G1vVF/+0nGQ9X0/lbXu25pY4WsSRXOtIEIDOVp1yFBOg1PZz43X9sUJBBfmZsw/WhWvc/OINAx5FGK+br/AEfe9w2JS3jnU2/t1NnVbFueRSy2mjvZMrUJHNe8Pl/g4r76u73xnpFKzJU29sOrUFaKpB3hQU0UDr74IBIWbzdPLrxtLlp+RyPL/wCy7vG4cptbwXi3xsjwbwVqrut6eQgqI25MmI7cwLWpl7xqlKMAhDqGmKnTTnht1r4CHY+2dybfXLYndEmZhaQ1cjWVkXoq2u+iZ4JiSeXTz5g8eHuN3HHi8Laevta5VkyVe9urFbmyu68ZkJls5jG279FRYrQ03cNG5Xql6ululSN32to+Hfh5nc3uPZGJarS2RgIsFmsbdzNoR34xLYyJSUUIF71SzxCSTpGmik8bZxuL8HZNuYxd+zYnc+Wt4i1lZRjMRmck1aOOtty5dsKkEGOWjbLQKWeTWJ2XWQb1j2w23kx+JyUdHLU8Lh89hpq+RSPqkitRZ3pkLCNk0CKNP6XH+qu1ufwdj3/D4gtb3FtbxZSfH6YmlUx8QggstZlS1C1Boi0CxI3eMele3j/UG3injquZyeO2ZsjBTNk4UsNTx9/bq3ryRq+vSslyWV2KkdTL26rx4N2sjPJZtvtXHo00vN2SOMRx6k9uiKBr5ePDG1X8QYz9tbNyWSy+Dlxm1Y3oyXblCxSVo8hhrPpHeQO7CWZZJV0KrKiu6vufdcW84dwZOvmc9jrmEip7fIpR2cm9mm7XsZQgsBmrKh7jvRFGH6VhjCIiVqW79xQbrz0c1l7Obr0Vx0ckck8jwIKyyShe6iZYyeo9RXq8vuu5PUtL5R+NxfpsX0SD21G3RyOQrVqLOclRxzok08TgDqQsjlmj01Cj42pHbpxjcvjN5ZmyhBAsm2pD1pdO8VCsQ0PUqnn5V0PHPd25U+8yBX/YnE1d81kcygcCN8k8Ukkeg5hXjjjZgdR8bU+1zAdFcB8qQGGuh9In58+NtMqKrPRjLsAAT29vuuBfGySJcm3rjYIEiOjSPJUvKia+8W048QU2jnjmcr4fPTWxh1Bj9O79C0yV5GOnXERouugbjwvxuUs36ditu3Fw38bbVoponW5GGjljcajjPZSqENnG461arhxqvXDCzr1AEajUc+fHhtPl/FHZMe/t24bDvlNr1s1RSyuYv14TLTjptYaZZBO5jWI6uD5vM8bz31Z2DsbJ7U2lDYvAR7myaZCenE3mHuDg+6WVl0JXvioPLrPbxiclY234aTbTtyxSXs3iN2ZO8wpdYE0tZTgYopWVAxVTKqkjQsvaFrXspsGtDjHnO6B3NxkpwwxZItM88N+dggepAwMcErPHOD0Lp1cWcDltr4fJtUw25MhZx2DtyxXEn23YpV5U7zKLUqmOcWy8cne9IClWKsGUbu8SMx4YZO2uDTJXhlY7+34KleOQLdxtWSs2U9L7xKNmvJKDF1sSelSSqmnV35se5tPN47EVJMxlrN7Cz1rlwRqtp4IcbkbskSderASqugIGuvFHcW18xUz+Byas+Py9CVZ68yo7RsUkQkHRlKn4QR+4NreqL/8AaTjIer6fytr3bcHrBfolfjxE9bxfIL7htjJbL2Hm9142ttinWsXMZSmtRpOl3IOUZo1IDBZFOnwjjvP/AMmd29f9f7Js66+/8Tt425md1eHm4tsYeGHIJYzF6hNXiiMuOtxIeuVOkEu6ga+Uji7tDY3jRa2/tK/FL6ZTu4Khk79q1cj/APULlvISskk09qZnlZio0LdKgKqjirsHGf6gHXA4zbjbbw8L7Yo95BBHRNKs/fLMJC0I6XBJ5lefaeMPZm8YLNWjtz0Wxtd8LjEx02MnwdBMZgERHmtJNFXry2u/Eh/LGTp6VXkMH4Y+Im8MJv7wy21iKuOxGLpYe7hL7y0hGleSzZjy1hZECKwZAgDEg8tNDPt+mn+Vt0nO5TNUfEDahOMy0LWL92fHq1hdWnWrVtCv3c/XGVDDo0bjC7KvZGpmbuJeybWcq1pKz33mnklNu0JZ7DPZm6uuZy+jSFiABoOJM5urN7nzGJntVL1rw/nzFg7cmnooiwNJjgegqGjV2j17tnHWyEk62tzXr+4Nv5XL4tcHud9vZWfGpmMYjsy1b6QnSRR1uvUOl+livVpoBltl7Fs1dpWBgZsNtS13LSV8d/yxr1mWJGQlYhpoAeWnl7ONm7fyXiPHs/bPhbAuP8OL2yKEmO3LBTiriqosZm1atc5Y1HfJHAqORqeYBB2vNuvA7g2HBDNLT7rCS0M5LflkRms3rq3porLyDrMsjQh3Yg6jQg1tv08plsxXqy2JUv5u7LkbjekTPOVexOS7KhfpQE8lAHk913J6lpfKPxuL9Ni+iQe2t1Y7ElOSzDJEluEgSRF1Kh0J/pLrqPh4zO38DuhqtiKODI08c9aJa1utIeiYoXE3dSLICpYAjmhZTrrxkKOXhtyRVkjeG7cqrWkWRmdXgZoiYZukKGEkXIg6Ea9s3/zD/sHtcz9/lvpE/G2P0GP+f3XauRWLv/s7f+HtNB2dawV7sjL/AAheN45RdzX9+UclHVu7Zp4izYx0yWbK95YpTGIqGELkjvG7Brx4X2DH6MLW7sU61WmewYwbcWid9J58hHvnjeu0894m7aw+5q2Lt17GBt5KvFbSWaqWjRoWcMC4dSBpz1HHhBBl99+H+N3PiNobfjyi2bWPju179ehAJRIWIdZUlU9WvMMPf48YpYnEkcu2LLxup1DKwUgg/COJtyeHu4snsTbmcFo708Ocaw+xr808LIlqrCx/9PnRyrs9bp6+kAqD53GJqUc5t+9ubLej0Lv2zlMZXNoej5qS0mWs4+y2WsRu0cZspMG7zULY6OqIjBY25eqb/p5/w58QsKcfsQ06meyFM5zHK9qWxYsJCguekOYUL92kSkozs7Djxxp5TZe7ZsBgPEGvI1OzLhdw5ChPQTAWjrh4AiWJRWhVZWr2vOgJhWN2143pv2nDtabaMltsNsrJ4jb1PCZaeKox9OkvQ1XkeFZJlXu4JNJOlQ7opZRxhctg9vXtqYq5E5p7eyWObE2qyrI6FZKTqpi1KlgNOYIby/uDa3qi/wD2k4yHq+n8ra923B6wX6JX48RPW8XyC/cPcnqWl8o/G4v02L6JB7aHBYSGt6X6Ib1y5d6zFHCXMaKiR6F3ZgfKAAPhHGPp2bmEit5MyR0HNW0Udo17xo+873QMVBYAnnofe4l6J8JkLVarJeOFSCzDJYghIDiOdpXVW1IA1UjUjXlz4aQBlEhDhWGjDqVToR5D7XM/f5b6RPxtj9Bj/n91g25v/ARbjwta5HkIKMss0IWzEjxpIGgkjbULKw01058anwqpE6aam7kddPnXGK3HgPDanjs1hLUN3FXlt3naGxA4kjkCyWGUlWAPMHhpp8dVmlfm8skKMx8nMkanj9U0/wDgR/i8XMLnMXTzOGyMRgyGJvQR2K08R7Y5YZVZHU+8Rp7FfFybNwb4ymWNTHNjqxgiLo8bFIjH0r1JI6nQcwzDynh8jg9rYjDZB4Fqveo0a9eYwIFCxGSJFYoAigLroNB7w4yUuLxdTGy5m21/LyVYI4WtW2jSJrE5RQZJCkSKXbVulVGugHGRlxmMqY6XL2mvZaWrDHC1q0yJG085QAySFI1Us2p0UDXQD9w7W9UX/wC0nGQ9X0/lbXuiwGRBM6s6QkjqKqQGYL2kAsNT8Psbg9YL9Er8eInreL5BfuHuT1LS+UfjcX6bF9Eg9tRy9Wusxs15MbKXJVFldhJVLsqt0qXBQtpyLDjCxbipwd5SvKaNqYCJJMn0EQrMYS35EB2KugHU4VDp/ShzeTQSSYPNNHXmhBkkmxV2RIj1onlTrBPL/wAsN5eJvv8A+Ye1zP3+W+kT8bY/QY/5/uRtb1Rf/tJxkPV9P5W17GSVclaCrbmAAmfs6z8PF6huO/kZKUFBp4RBclgbvRIq82U6kaE8uMZLtDc+T+z8hA0oo3LBsrG8T9LIRJqrqwII1Go4fHZCNKW4akfXJCmojsRjkZIgdSCP6Sk/COXZwQew9vD0a8lb7Uau89apK3MqpCh2Ued0BmAJA4Az+3xEQuhyeLLXa4Y66s0PSkyqO34p/f4zGKyXo9v0GKpcoZKCFq/e17iuVWSJmYq6GM6nXQgjkDrxM2Mt17wrv0T9y4foY8xroeWo5j3/ACcbAeIlFMeXhmUdjRvBG5U/+9Gp/g4BEYBHMHjcHrBfolfjxE9bxfIL7tT8OfDTbm1stP8A5XO5chc3LkbtBQgumn3UJqVLerE6HzgB8PG6MVmtvvtDfGw8mMTvPaz2EuJBNJClmvPXsoqCaCxDIrxsURu0MoI9nM7hvnSphqc1uca6FhEhYIuvlYjQfCeJLuVxgwmex1yfH57DB+v0axCQwXq8oaNkYH4fZ3x4gU8fFlbW08XLkIMdM7RxzNFp5jOoJAOvaBxuzw53vgsBV3BtbFY/MSZnaeUnyuLeLIy2IlrStYqVJIJ09H6ughupW6hp5fY3JvnPCZ8VtmjJdtQV1DzzFeUcMKkgF5XKooJA6iNSOMJf3jsjaO2tqZaOSXI42vl7lrN4wGJ3gSRTTStM5fpSQI6hOZDPpp7huT1LS+UfjcX6bF9Eg9u70c1ShpMVaOpZoGYoy6HUOsya8xqNRy4tSZjOLlu/6PR4IqqVo4QoPVpozuxbXn1N+9pxN/8AMP8AsHtcz9/lvpE/G2P0GP8An+5G1vVF/wDtJxkPV9P5W17GU/S5/lG4tWOsL3lUx66/+0D/ADcYx+vr9HikXt/rMDxiMzXcpJj7UcjacupNdJEPwMpIPwHgMDqGGoPwHjMNm8tFk4rNx5cRHHCIjXrH4sTEfGI9/wDl4zM9eslm9Bgaa1Ynbu+pWs2Syh+ltNSB5Pe4z9zE7Zs2bqzdOXx1eYwtC4ALCx383cSysecfdDTo7W5gcbpzNhJqlY4/GTzRzROJokCzkq8QBYMvPUacV6uOu1beItYWf7CuV5g5kgSyjrUcAnnW6pOjy9DadqtxtOa7XljpVkyDJbiVp+rqgUMDHEGcdP72nw8emYy0lyt1tGZU1Gjp8ZSCAQRxuD1gv0Svx4iet4vkF92ob72l4Y2/EHC29kf5eszVcrjMcKts5M2gZResRSFOjQkxo3wAnlx4hb+396BX3z4qZWtkcthcVLJPSxlTH1I6NCkk8ioZnjij6pZQiBnY9K9IBPsbf2vjjLTw+QyaWd0ZqJoOqtWpKZ4kWOUku0k6x6aIwGh6hpxmbb5K5ubA7sx8U+VzF01I5oMlSIijBjgSHqWWBtNVTkUHUeY9nxM2htah9qbi3Bg7FPEY/vYoO+mfTpTvJ3jjXX32YDjMbvpeGNPwJ2Jc2+MbJ4c071S02RyotCRclYhxzSU4TFCpjQxuzuHPWfNCj2N4bEp3kxuSzNWOTC5CUdUUOQpTx3KTyDRtUFiBC3I8teR4w2G3p4KTbWQJKu493JncXaxivFEeh6UEMz3JBNKAFWWKPoU6sxI6W9vuT1LS+UfjcX6bF9Eg9ves4iiuTycMRanQZxGJXHYpY8hxSmyFUUr0sEb3KauJBFKygugccm6Ty19tmfv8t9In42x+gx/z/uPxlrYfe29MRmNqV9vS7Cx2LoQ2Ntwy3KfVYfOWbNOavXrdZDyPJNGVQMynlxUsNLDM08McjTVm64XLKD1Rt5VOuoPvezQxWz7y1bu3cfNuzcER6f8AmadRwkdI9X/1j8p2c/M5cY/LUJRPRydaK1TmGmjRTIHQ8te0H2clNExSWKrM8bjtDKhII/ePG1v9R0HjBuLd2Qh27i9y7x2LuVcdPir9WdYnuV6rwUoLNWQK7NE/ev5wVXDKTxFMmvRMiunUCp0YajUHmPZ8TsrL4m7i8P8AZ+xd1ZDZm1Nv7TetUnktYZljuZG9ZsV7DTGSclY4tBGqKOpWLE8YvG28nYzVrH1IK1nM3BGLFuSKNUaxMIUjjDyEdTdCqup5ADl7fa3qi/8A2k4yHq+n8ra4JJ0A7SeP8s51u5xUuUnew5fu1krFXsRDvNRoHHSDodeenbwFTD7fAUaDzoidP3y2vD99iNviMKeti8Q0HlOvUNOJsBtOVruHtZRa+Km1LgxEjrIbtZE87Rv6o14VRyCgAD4B7EtuCvE9/uTFHI/mlgNWVGcAkL1cZTJ5OfIYY3NY0xVmHWpDGWEvSk8KskvS7P0t1a9LaEa8bizdRXbFXVpw0LjoyLOYEfvHjDAEqC4Xq00JB04q5uO2+IeFVNgUY0imkljcyRyiYdjAnQ6q2o5cuNsxZGjJI2KhyHfZiFP+VmSVI1jfXU925IIKH+AkexuD1gv0Svx4iet4vkF+4e5PUtL5R+NxfpsX0SD2zT27EVWBPjTTOqIP32Ygcej1ty4uefXTuktwlifeADc+NRzB9tmfv8t9In42x+gx/wA/7j8SbuN8T7u1MF4r0qWO3fiqONrS2zWqVGpsla7Oz9yZY3YFu6JGvm6Ea8YvCYyEVsbhqkFHH1wSRHBXjWKNNT7yqB7O4dwbmxeP3Zdzc0JrfadKCdaVavEI468IlD6DXqZiNOonn2cVtuw3pL9KhLP9mtKoV4q8krSRwEgnqEQbpB5cgOXs2qhboFqF4i4GunWpXXT4NeNm+Hm8fFK9ujwz2ZBRgi2RVxlbGxZJMYUarHkrCvPNNGjRqxjRkDkedqOXtN17m8J/FUbCqb6urlN07YyWEizlBsj3SQyXKgNmnJBJKsa94C7qx59PZxi6WUyj5zJU6kEGRzUkUcD3J441WSw0UIWNDIwLFUAUa6Aae32t6ov/ANpOMh6vp/K2uJMcZWSpUSMrCpIDs69RZtO3t0HCUqLRx5V42WDvfzcioC/Q50OnZyPD1sxgpqMiH40kP5NvhWQAqw+EHhK2Iwk1+Zz5ohh1UfCzadKj4SdODms0Y59wTRmOGKM9UdSNvjBW7GdhyLDsHIctSfcNwesF+iV+PET1vF8gvungltzb+6chtOvvPeUuNz1/FpUazJUgw+QvCJTcr2UUNLWTqITXTXQjt4zFjw/zeN27nKkMlmbMZKm18Q1oYnkkMEAkjVpiVATvCUHMlW7OPDvfW47D5TK3dh4jO5630oj2LEmMitWH6UCoC7ljoAANeQ04/wBOe/N2b+m3Dif9QcphzuyZaVKKjimymLny+LOLkrwpYUQLCsMnfyy9YYselh7HiDvyhXjt5Da2DuX8dXm1MT2Y4z3IkAIJXrI6gCDpxtLw83r4h2/EOl4gbOv5hpcjTo1pqGZw81RbK1WoQV19GmjtnSOQOyFBpIdTr/qM3vs3xAsbWxv+ntxFgtpQ0qM9PKy4zGQZfJnJvZrvOyzLKYIxDLH0BevUk8YvLRRtDHlKkFuOJiCyrPGsgUkctQG09ix491N+Wl2tj/E5NsnwxWlR+zpNujPrt1i0zwel+lu7+kCQTdIOidHSON4bn8ONxY7bF3bOHyGVyeSt0jesmKpWeVI6aO6wo7uoBeVXCjsQnmMdPM3XLNVhklc+VmQEnl759jZ2a2fuTHYHbg3TtvG7kqtR9JyN1cnmqlJ4YppH7qCLupm6iI2cnQKydvGTj2pYx9XcbwMuHtZWKWanHMeQeaKF43dR29KupPvjjdQ3zuEboz+3987n2/JmUqxUklhxWSmqRdFeLVY16Y+SlmIHazHnx4i7Wu7qyWd28dqYHcmKxV9anRjrGRyOYrTw1mr14HMXRSj0ErSMDr5/P225PUtL5R+NxfpsX0SD2s+Xsp387MIcdTB0M07AlQT5FABLH3h7+nGQsxzC99ngNKZ5e5qV+81KRRKAwBIHkGunNjxbx1+Bqt6hK0Fys+mqSIdCOWoPwEdo4gp5CeXIbblYLYqOS711PLvICTy07SvYfgPPiGzXkWaCwiyQSqdVZHGqsD7xB19rmfv8t9In42x+gx/z/uFprEqQQpp1yyMFUanQak6DtPCyo6vE6h0kUgqVI1BBHLTTgS1p47ERJAkiYOuo7RqCRxHC0qLNKGaKIsAzBNOoqO06ajX2ZKyTxtYiAaWuGBdQdNCV11HaOFezPHXR2CK8rBAWPMAEkc+Xsx99KkXeuI4utgvU7dirr2k+QcPJI6xxxqWkkYgKqgakknsA4WWCVJon5pKjBlPk5Ecj7DySOsccalpJGICqoGpJJ7AOEkjdZI5FDRyKQVZSNQQR2g8SRJKjSw9JliDAsvVzXqHaNdOWvHovpEXpPT1ejdY7zp9/p1109ttb1Rf/ALScZD1fT+VtcJYMrVLka9HfoAwZe0BlOmumvI68PZ75rltlKLM4ChFPaFUa6a+/rxoRqD2g8aAAAdgHuW4PWC/RK/HiJ63i+QX3TwMz2KwV/O0tpbzmyGfTHxiWWCrLhclTWUoWXUd7YQHT39eMxiFlEDZSjYprOR1BDPG0fURqNdOrXTXjwS8MKEsWS2XtnbTYbxHzpjhQW4sbioqFSukMkjSx+lTOZdUDdKxMjOOperaEdzeNPe2B8DKGSl8HNrVscKF+5cahNSxyZW7PbeBzWglaFCkcSsSHkI00425Bu27Hkd0xYyqu5L0MaRRS3xEvpLxxx+aqmTq0A7BxurYuZeSLGbsxdnGW54dO8iWxGUEkeuo6kJDLry1HF7e9veWI3n4oYLbH+T/DZ8fixjalSO9brG7lr0dy7Os05WFJHRWROmMqiMz8eJFDb+/qWE8P/Gj0R/ErD2MY9jJLJDWjpW3xltLMUUJu1okSTvYZOggsnboN2puSKKhiIc29XYGHiEJ9HwlWvBBCzvCWLNNKkkoDnqVGVSFII9ibbEu9qEngtLvY77G0fsxxlvSzd+1Ps43/AEoxGp6cO+/Md50/k+vTzuN47L35fiwu69+Uc5Rd1ihmjxlbISzpRhZazhJWgrNGHIfzmDHq425QrmOHw9xO2rP2xb0hMl3LvNXjqRKCxlRYYY5nYgAMXUanQgcY/BRZJcU1LcOCzhsvEZgy4bJ18gYukMmhlEHQDry110OmnG7JMmsdTYlXHYuvtOuO5aS1dJsS5C07KWlVQGhiVW6RqrMAdQTuqjLk1yh3Ju/cG6EkWIxdyubvy3VgILP1GISdJbl1aa6Ds48QNyyYO/j8Cdm7fwFPKW4hHFat0MlmrE/cecSyLHbiIbQa6/B7bcnqWl8o/G4v02L6JB7XB4vrPo9SgbKx+TrnlZSf4ohxuv1lD8gvG71lmkhBvID3YX/4Mfv8MsOeuVrbqfR2eKN4+vTzesDQ6a9uh4p1brddjDWreNc66j/l5WUAHygDkPg9rmfv8t9In42x+gx/z/uGfHZGpDkMfkN27IrX6FmNZYZ4Zd2YlJI5I3BV1dSQykaEcjxU2nh6yYTbOXz21Np5GrjuinFVwd7LU6NivCsYVY42rsYelAAFbloBx4lbN2Liam3Nn39j7bz97buMhWvRr5Z7uTpGVIIwI4nmr14+vQAv0hjqRrxs/O17eSuZHdeyN1T5F712ezFEsF3CLFDVhkcx14kDMemNRqSS2p9i5PAneTwwSSQxkE9TqpKjQczqfe4/0beK2EqRTeJfiJumCLfu7ouhL+Yhz2JydzKJfmUdVhIpolZVfUR9ACBeXG4Nq+JGGqbl27tfwzoW9u4DJxiatHNmMlegvX4o31VZglWKNZR5yaeaQdePCu7lL0uUs/YiQRZOZXSSzWrSPBVmYSEtrJDGjecdTr7H+nvfLWsnYzNjxHoYmGtNfsPQrVjhcxLIIKPX3CPI8aF5OjrOgHV08uPBzaOVbvNs738TMRid2Y5m6Yr1JKeQvLUm15NHLYqwqyn448z+lx/qE2RtWvDitm4XM4K/iduU0WOljreTxMc12GtEgCxK7KkpjUAAvqAA3seJ75SxkEi25tXPZmpVpXZ6cU9qpi7TQC0sDp38aPo/duShZVLKdONqx9bR95g6K94h0ZdayDVT5CPJx/qR21i7OQuUsbuTb/RcytyfIXZnm23jppZJ7Vl3kkZ5HZiSeWug0AAHhJuCvh6MGeyWH3LVyObjrxrcsQQQ0zDFLOFEjpGWYqrEganTt9ttb1Rf/tJxkPV9P5W17tuD1gv0Svx4iet4vkF+4e5PUtL5R+NxfpsX0SD2uF3PCheCFDj7zAfE1YyQsfgJLDX39Pf43WfJ9pQ/R143fNjbS9AyHQJAFYdUcaKwBOvYwI4X0PMrF09mteFv9qniSxK3VNZylyaZ9NNXkZWY6D3yT7XM/f5b6RPxtj9Bj/n/AHDa27g8hUxmXGVweWx9y9HJLWEmGy9PKBJUiZHKyei9HIg8+LG0fELHUc7UzmPjrbnowiWOrLL0q0jQav3sYWVeqNurrQhSG6hrxFjsDfyVI3ty4bcG78tk7t3MZLMR4ORZqtKe9dsvMIlaNAFLMir1KE89jxh97WKrPuLBY67icZcEjhUq5CSvLYQxg9DFmrRnUjUacu08Nhp8tJnr9vJ5PL5XMyoY2sWcpdmuyHoMkvSqd73ajqOiqPYsbh2pTt4jOwR5RtrmxkMhdxuFtZcP6TZx+LmtejV2dnJbuVQkEqCoJ42Ngc0MravbI26u2YdyY7KX8Peu0XjjS1DbloWIXljsvGJHjdmXqJ+HjKbix1kQ4yTb2H2xtra0EIhqYnH4hrL9EIDEEytYGvmjQIg56a+xti1mqrWZ9n5mPPYB1lePub8VeeqshCMA4EVmQdLajnrpqBx4hwb0sTbnj37uKtuCNg81GbFtj61Wvj46M8Ewliet6N3iyxsrF3Y6AHTjZq7du3cbjtu7pn3fuGK1ZtZG7nclJQnpRSX8hbsPPJ3ZmWTzy+vdoo6QvsZ/audga1hNzY61icxVV2iaSrdhaCdA6FWUsjkaqQR5ONmbnjyssWL2Nt+/hcPtoKxjMt9qg9LklMnnNHDV7tQU/pserycbt3HjKrQZbe9qrd3HYMruJpqdSKjCyozFU6YYUXRQAdNTzJ42pu+bIYyPa20sfkq9PHJFN9oS2MkkKSNJKX7roXuRoAuvPmfbbW9UX/7ScZD1fT+Vte7bg9YL9Er8eInreL5BfuHuT1LS+UfjcX6bF9Eg9pZtMOpa0TysvvhFLafycWYsnO1irbUrLR10h6TzChRy5cufbrz4s3NlZCwaBkMdrHVrTVJiwUHVgGRH5HTXXX4OCv8AlW/qDzPRrz/f158fsrf/AOH/APTwtHL0ZcfbF2d/R5h0t0t06HT3jp7XM/f5b6RPxtj9Bj/n+5G2CZUATE31c9Q0B6k5HjIer6fytr3bcHrBfolfjxE9bxfIL9w8pTtZWZ69RO8v2jHGDNRQ9VaMkJpzLgHTn8b3uNxfpsX0SD2jxSKHjkUq6nsII0I4YUr0PohP5Pvg3eKvveaCG09/lxHShYyaEvLM3Iu7draeT3vcMz9/lvpE/G2P0GP+f7kYzIrBrhb6nJZLl5olqlQ6H/5jFCff1b3uMh6vp/K2vdtwesF+iV+PET1vF8gv3BLOwRR2sx0A/h465HWNOQ6mIA59nM8Pb7uOO5bRIHm5B3SIuyJ8OnWx/h4v2KWQrRR35UlaKaq8rKyxJFyZZ4+RCa9nDkZfHkREiQ+hv5pA1Ov/ADXLlwrplqDow1VlpSEEfARa4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4/WlH5lJ9a4lgjzeNeaDTvoVqOWTXs6gLWo1+HiWFMzjnmg6e/iWm5ZOoar1AWtRqOzXiajPYjvCWSyLbxgopaeR3dNAzFdOvT42vFXHUoxVo01WGrEXZuka6KvU5JOpOnM8LA0qCd1LJCWHWVHaQvaQPuLcyV6Za9LHwSWbk7diRRKXdj8AUE8Sb1jmkbBQ46XKSy92wlWCCNpJQYz5wdQpBXt1GnFO3kY7aSXoXsQ46KvJNaEcMYlmZoowxXulPna+XzRqxAMGUjklkW1Xj7m1WszwrJCdXjP5J0BHnkjX3+JcFUt5RrielCKZrGQSvM9GVYbaQzNIEdoZGCuAeR9/Q6WpaxykXodqWnYjnuXI3WWE6NyM3Z7x4/OXvn9v++4/OXvn9v8AvuPzl75/b/vuPzl75/b/AL7j85e+f2/77j85e+f2/wC+4/OXvn9v++4/OXvn9v8AvuLMOOy09uenI8VuumSt95G6MVYOhmDDmPKOL2H3AdyouHxlLMbgzFNMrdo42jfmuQwWLstd5DDGWozFnZelFXqcqOfG18YZMren3fJKmHlp27c0RSGLvnlkkWfRYwhB6vLrxkvDzHyXW3HVpfbGRWaC20Hd/kI/NuTqY5HCyx6pG7FQR1BdRrk9xZ20aeJxEDWL1hY5JmCL5EiiV5HZiQFVFLMSAASeN1+IEsOVjxGx2truzHvRljyFE0Ar2e8qyBX/ACcTCbUagxkOuoI+4GN2ricYl+tmbXeZ1p7MtKuKdQd73bWYYZ2RpJe7AAQ9Shx2akbTj3jVSLJ1szh6efSyo7mWatkIoXmHeAAxzFO8U6c1YcWpWw02VaxTwabRvJGWShcq5Gee7KZeYgPdmJyx6esL0DqPmnNYbHuy5WjSjl70rpGj2hKsI6uwkGIkjyDT3+MjNZwcuDxMm2sRQzVe9EsQs5Wsbn2hM6tr3vmyIHmJKyeRmA142dHtq7Tx9haNUmWxEZYjAIiOlVUjQ69Oh97j9o8L8yk/H4/aPC/MpPx+P2jwvzKT8fj9o8L8yk/H4/aPC/MpPx+P2jwvzKT8fj9o8L8yk/H4mB3DhnBjYFFpSanl2Dz+3iBslk0o4Yaf+nZIG1MV95FDBo+XkLjT+qeJd4Q7XstEdxZMXcwMS9KNcTa29ViWxJmevpuL6XXWJKvQelmMuo6Br/q7rbcEEW88hnYatKxEyRWuttkYMUOuTkwVZnkKEnQEuR/S48S96natzYWxaeGxdy7j7NM05nu4ejMMra9E0DM3QsUXe6HvjH1Izr0s2zL9fC2Mjmdv712blcbCvX11YotyYuW9M0St0ExVUlLMwPSvXppqePCmYx1vtgVs8iy6J6T6N6NGSAfj9HXp8Gvw/cWXAx3jj61+eBcpOgBkaokiyTRR9QZdZFXoJYEaE8uPEvbdC5JnDn6mXlwEM3dpYE2RqsZInZRHGS9pncHRQOvQ9mvGEzuKmjxWTq0LtJq2QjMgjhyiwmQssT6GSFoUIGpVtCvUNeoYHCVIkmwmOxjV7N+VtJu9riCKsioO0OveMx8mgA7eVO1NkYZ8XhrGbsYiBI2Wdmzdv0uQTMWK6Qksq9I84EE9Omh3JRy9W3jIctmbNmNBKIp+67xGjcPC7dIYpr266dvH+OzH/UJv9/H+OzH/AFCb/fx/jsx/1Cb/AH8f47Mf9Qm/38f47Mf9Qm/38f47Mf8AUJv9/H+OzH/UJv8Afx/jsx/1Cb/fxNlclmbUrLZklpV6kjwsqliV7yxr3jHTtK9PPy8b0t7S3Lh8FjvEDZ+J2hmLeRqWL1ynDQsZd5bFVBNFHJI8WVITvWIV0DMHGq8eDdTB4i3kMBtOC5ibFmNomNSA0Ur15Z+8kRmX8mASgY6+TjF7vFlBXoYG/h3p9J62e5ap2BIG7NFFYgj4eN27cyLVNv2bORDbXtdBtoIqFiGxTmtRCResSTQ9TIrKe7IXk2vH+oKhVpDc2+fF2pmrLYfEBY4heyeJgw1aGF7ksYKRRV4jI7FddHYIDon3B6JEWROR6WAI5dnI+yVYBlYaMp5gg8KiKERRoqqNAB8AHucsyRIk0/T38qqAz9I0XqI5nQdmvsrO0SGdFKpMVHWFPaA3aAf/AAp7fY7fZ7fY7fa9vsdvs9vsdvs9vsdvs9vsdvs9vsdvs9vsdvs9vsdvs9vunb7Pb7Hb7Pb7Hb7Pb7Hb7Pb7Hb7Pb7Hb7Pb7Hb7Pb7Hb7Pb7Hb7Xt9jt4LMupJ1J1PBRV0Vu0a8fE/lPCqw1C/F58BlXRh2HU8FmXUntOp4ZFXRW7RqePifynhVYahewa8BlXQjsOp4LMupPadTwyqNA3bz4+J/KeFRl1VewangMq6Edh1PBZl1J8up4YIugbt5nj4n8p4CFfNXsGvAZV0I7DqeOp11Pv6nhgi6dXbzPHxP5TwIyvmDsGvAYLoRzHM8dTrqezXU8HoXTq7eZPHxP5TwIyPMHYNeAQnMcxzPHU66n39TwSi6a8jzPGvR2/CeO76fM97XgEJzHMczwC66kcu08EouhPbzPBJTme3meO76fM97XgEJzHZzPALrqR2czxqi6H988ElOZ5nmeO76fMPk1416Oz4TwOtdens5njqRdD++eCSnM8zzPHSg0GuunuQDjXTs58AhOY7OZ46nXU9mup4JRdNeR5njXo/lPHdlfMHk14BCcx2czx1OupHLtPBKLoSNDzPBJTme3meO76fM97XgEJzHZzPALrqRy7TwSi6E8u08ElOZ5nmeO76fM97XjXo7PhPALrqR2c+OpF0PZ2ngkpzPM8zwYwvmHya8fE/lPA61107OZ46kXQ/vngsV1J5nmeDGF8w9o14+J/KeB1rr09nM8dSLoff1PBZl1J5k6ngoF0Vu0a8fE/lPADrqF7OZ4DKujDy6ngsy6k9p1PBRV0Vu0anj4n8p4VWGoX4vPgMq6MOw6ngsy6k9p1PDIq6K3aNePifynhVZdVXsGvAZV0Ydh1PBZl1J7TqeGCroG7efHxP5TwqMuqr2DXj4n8p/8cfDeDBbsh2eM1nslVyOVt519uVAkW38pZhNjIxwWegRzwxyKpjKyOqxtorlh/pQs28nmK0u9crRfdHeM+MuXOnZ2ZyfRcjquvT1Wq0cjxBuglek6rqCcfBua9X3BjN07VpbX2XDakgr3MBklT7YtS006ktBR6Qe8dD3RgUK8fU3Vl8XQyiLkccFgviu0bzU5Z4hJEXRg6huhg6h1II0OhB43DTzO4Mvm7dDcW+sYuftXJUyMkON3LlatYmzXMLIyRQoo7vpCgBVCqAONgZLIWZLl/IbbxVm9blYtJLNLTieR3Y9pZiST7lWt49bp2psyarW3ZJB3fojNkh+XNnqdXHokUkEy9Kn4za8RVzYmFaHCNMlVZXWIyNY6C7RghWPTyBIOnk4rLLmLd27Zgz43ZhpZWkhoyVMlFHj17rULXbunZV0Ud6vnnq6erjDz08oa2GvZmvTzmXrTd13VTrdJv+YUgxayII2dSCup0KnmNs5CbIXDbj3RjK9PIGd+9s4+XcMdKFpmBHeielICevUN1dXxtCPuImXw1SrdylzObfwdKG6zpXV87maWJ72QxgtpH6X16Dt0048UMPujB0aG7fDCNLFoY2zJZx92taptcqTRPJHFLGWCMkkbLqpXUMykHhN+47D4vKZmLFQ5TI1J7E1apDC8PfSsnTHNJIyjQKmq9R7XQc/Yw+CbEUU2nnt0ZrZeOtrLK2QGSwuOmyEliRSixiF/RZ4gg1YFUfqIcqniXiMhWrwtsXdC4SpPXL6WK82HxmWikdXJ0dRke7bQ6Hp6hproPcduYhJKYpuI7O5/SG0mSrZsJTgMI6hzDs8rk9iRN5TxtzBwvElC/istkL3UhaRnpTUIYlRuoBR/zbE8jroOzilFL6OMJk8rnMNDQEDLPC+HdlWdpjIQwlML6jpHJk07D1Qz44Q+nX8ljsZWlsgtFE1+3FV71lDKW6BJ1BQw6joNefGXyMs9eTL4HL2cdNbFcrDZjrXe4DiPvD0lo9NdG0Da6DTl9wI+/gjm7pg8XeKG6WHYw1B0Pw8YOTNU2tS7byaZjBzJNNA9e7HFLXEqvA8bHWKeRGUkqysQwIPCyFFMiKVWTQdQViCQD7x0Gv73G4WxO3Z2t5G9Haz92sK6+k2/RK8KySNJMjMy14oowSOSqAOQ4vYa9trJnH5FHjtxQTwVmZZDq+kkFuN1LEkkqwPGPxGM2xeqY3FVoqePqq9UrFBAgjjQFrJJCqoHM8fs/kPwqn1jj9n8h+FU+scfs/kPwqn1jj9n8h+FU+scfs/kPwqn1jj9n8h+FU+scfs/kPwqn1jj9n8h+FU+scfs/kPwqn1jiRTtu8Vl5yqTT0blpz/5jnyHEGZbbOR+0q8DVorAlrgd056ipQWgrcxrzB08nHpUcL1y8kkckUoUOHhdomDdDMORU9h4ioVa6Q04U7uOso80L72h4px5GA2I6FyC/Wj63RRYquJIXYIy9XQ4DANqNQDpqB9xZNv5eS1BUe5j8hFZozGvYis4u7BkKkscq81KT10b+DTjdG38CbcVjdy2Hzm4MlasZTIWrM9cVhPZs25XllKRqqqpcBVUKvSOK2w91XYswj4dMRl71GB6CWEWMRFo4mnstFqo7O9bT3+Nu3rW6PsiDC+kzzYUTiFLrWIjBEZx1r1xxAuwUqQX6W5FBxPvCvfhGVmtT5BIHyjyU4b1qAVrFuGq0xijllhUIzKo5dWmheQt4i5bJbzxGXsb93Q2fjSsi00p148Zj8VWqkPasmV0hx6M8uqB3ZiI0Gi8frqh85i/G4/XVD5zF+Nx+uqHzmL8bj9dUPnMX43H66ofOYvxuP11Q+cxfjcfrqh85i/G4/XVD5zF+Nx+uqHzmL8bjNrat4ywu464q5sPZQ+kQKjRiNvP5KFdhoNO0ntPGBzse569eTBUrtBaoswPHPBdNdmEjP1PqrVY2BVge3XUHizn8VVpS352dZ79cq/5SQL3pHSSqu4VesgAtovVroOLWGtRNkqV21YuWVtt3jNLYstaJDADp6JG/J6adGi9Omg4XbtBmw2O71JHWqqFm0l75wxkV9TI+pdvjEknXU6+3qYPxA3nBtzK3qqXqtOWvamZ68jyRq+sEMigFoXHM68uFH/5qUwW+KPQcjz097/leKm1dl78rZzcF4SNUxsdW5EziGN5n0aaCNeSRseZ8nE1u5YiqVayGSxZmcRxxoo1LO7EAADtJ4BB1B5gjizYe5AlekWW5OZFCQlBqwkbXRSAeevZwCDqDzBHElutdgsVYmkSWzHIrxq0JKyBmBIBQqQ3vEc+IbNaZLFewiywTxMHR0caqysNQQQdQR7Sa5ftQ0qddeqxbsOscaL77OxAA/fPu+4KL2pnpQ4mnJDULsYkdncMypr0gnTmQONxfpsX0SD3BK3ctctsodoUYKFU66FmOumunZpxJXETVLca9fcOQwZewlWGmunl5e1zOak0LU7GQNdD/Tma5KsS/wALEa/BxSuWJe9yNQmpkmPaZY9NHP36kN+/r9yMDho7HTjbuOtz2a3Qh6pImUI3UR1DTXsB04yGo1/9Pp/K2uPij+Lj4o/i4+KP4uPij+Lj4o/i4+KP4uPij+Lj4o/i4+KP4uPij+Lj4o/i4+KP4uPij+LjP6cv/UF+iV+N0YyMp04uzrjGX/zawAjZ/h/KKW195gPcNpNoNf8AKFIdWnPT0/J8Sb7zW18ZuHZGUmigvQzWhBkalYy9wL1SYHRCJDoUPPl73G3NuXbMGRaGtetY29A6zFqk+KumIyso0VyO0Dij4Kt4v+K26KO5Vs5/xW2thKkGdTFYMOHr1IqeMws1hTLYeJVMzOqRad4H7xOrbO208cPH2hmMk8tnD4ndOETD07yY5RPPD3l3bdVXUIPOVXB0PLTXjfO1Yt1bnsWN17ou2fEuLdeSwuC23uHF0tyybdqfZ1zGY2xZr3bUtOtWmLwoJT3oVT1RsNxZm1R33t+pg8Bu6/BnMVvmxuMw3dtXXxBqvSyGFjqs9myp7kN1kJ+UKLyI3RtTafi7ufN7mXL/AOXp8bSt4Grj/S8+8KTWm9LxUdgw2chk5OsLY7xQvV3igMeLmAv3txZXBY7F1oMa+azm18pDRFUiKKCCLAVa0q94hPnSlhpHoNCefi4PGO3vjG4ilt7FH/TjPtz7bSobpqSNdkrnE6xtf9N0UCb/AMvTXzOY8PsP4pZDI3cHhPB7D7htYilet45b+czNqxVkyVh6UsBZ44qgMS9iM7OvPTTE1tx5mfcWU2zl89txs9ak76xbhw2WtUa8s0n9NzFCoLf0iOo8zxfyeUt7k3tsHdniJv3C39v0s7vTIRz0KGRvw1a8+JwwsJFXi7pVQQx9OgUPoNeNx7qz13csG2tu7is47D4jcG4d5LLHXOHp9CJiM4Y0mhEth2V7MfUH1KckjPGcO68NRwz1sxcg2+tG21sWsUjD0W1N1RRd1LINeqMdQX+sdeXum5PUtL5R+NxfpsX0SD2trEYizXjoxQQSIkkCuwLpq3nHn28f42p81ThXtNj7kZPOCSDo19/Qxsp4kv8AcGhdtKrNSZusHoUKe7fQajlroRrxHkBEyVKiSBpmBAZnXpCrr29up9rlYchUiuRRT5SaOOZQ6rIlix0sAfKNeXGEs1KcVefIVIpL00ahWlca6M5Haefl91x289v42jlLtzP1sTJXyEckkQinr2ZmYCKaBurWAAHq059nvN0bI2/MFYI7xUbrqHKlwhIyHxukFtPe58bG2db2vt+HH7rzNHHWLdWvaEiRWpkjLozXnUEBtRqp/e43hDu3xFwMu89m00tXPDWDKUUz07yxRzwQRVJ5oz1yxyo69RAKkNrpz4Bb/Ub4cqxHNf8AMePOh9789xvO9kfFvbGc3DstbUdvYeLy9GXMz3qrmJqMdZplIl70dDB9AvMtoAeMJg8L47bEyG4dw2a1HE4Ctn8fPbnu23WKGrHHDO/eSPI4RQhOp7NeKWOqYbddlchEz0blfGJZFmVYr83o1StBPJbt2CuNsaQ1oZZNUPm8jpNRtZXN7SjgwmQ3C+Y3Zt7L7expoYiWtBkpFv5SpWrs1OW3FHOgctE56JArAgbh3afFXbGCXb0mZaXA5LN42HIzVMTeloxWkgNjULdKI9dSQzrJHy84cUMn4fb52/uHIy4qllM3tfG5jH5HI4gXow6wZCGjYn7mRG1jbU6dSsATp+4dreqL/wDaTjIer6fytr3bc1eQsI57nduUYq2jU64OjDQg8+0cbvsUq8kMuFuinQPeyELDJCrMrAsQ3Pnz9w2kalCzZQbRpAyxRM6gi/kuWqg8+eunDUjRyhx7Tiy2NMcxrmcDTvO7001042m02MuQRPHki88sUgUf+mXdOp2HLmdBxhd3eCmDwEIyGF3Sd657dNm1OJ7+ZyWJtIHeKRrcknTUKxkgxxxx9Gq+YD4abq3VidnZPE7OqbjZX29YtQFLN3HiKulj089RSWRQoMSN08y3Ljb+Hkx21NtGocULe4EuNkYHt4vLXd1TXbVQR1T0Wso8EYij6yqmRmY8jxkaGZ8OdubD3tuK9n7We8dNr2sfZzCJlcrZyccUPpmMaWZAHjgAmOgVQ3SOlV48Sq+Q35nzmsvuexe27f3G2PzuNuQBKMkdm9h+6SozSyV2RiqQzKmoRow3PeuS3htnEYfxB3Rnp5M9mMI0Qx9+nAf/AE/0CvFHGataKKQxrC47zqDu7MX6jew+C8Uq+zdjZ7HfZufxsGEWzllWTvFsPRyLWkSFpo3CAvA/dkdS8zy2tuTwX3jV8Nspt7akOx7NW/jDmaNrC1G66QMJs1mE9VyzJIXPV1FXBHGN2Ztxp87Jg69mYWrsipPkchZkktWJppNNFaexKzHlouunYONlYvIeEm2MTv8A29efNZ/xX3BeqXqdyfK2pcpkqBxEEFtrtdZbssKid4CrL1xuBozbhx+U8OMBFPu+xJa3D4lbeyccVSVK/eGlWiwnoVT0REDlQkZk5ks8jnnxnBufckO5Hu5m5bwbQUVoijjJWBq0XCySd80KggynQvrzUe67k9S0vlH43F+mxfRIPa5SPE4yzk5K1Ko9hKsbSGNWTQMwXsBPLg+k4a7X07e8hZf9o43NkrdGOXIJcjqxzTIGaOIRK/Suo5asxJ9/l73G6LdCU1J6WVWWq8fLoZI4yCB2dvGGzaII/tCskksS9iSDzZFHwBwQPa5n7/LfSJ+NsfoMf8/uuJkiCd4u8sf0tJqVXWneHUwHMga66Dt48D8DsrOZHJnZW7Lec8X8vex0kEtya5ReuzmOTzmUMwjSMdi8eGONxNDIYrA5LcmGzWCxeVrPVtVq1y5G6x92/MxknVD73Ljw+8BPD6phLWZwF7G+JnixlcvWlsUaeNwtpZcJj7hrsjmTIX4VdY+sEw15CR0Hnuw3cR4WUMXsbe1/aeZSDF5JrFmPGPD389dms9KGRZD0Bg2h7dePDnZextu4LZWxPDlN07k23nd97et3a249y7bmp4q5b+zILuJsmCi+VZK0rydDyrJIFcLG48Mti75yHhbuHE+JVXcS17W3Np5TCXKVrEUFtQSd9Pn8gGVncBgqK2g81gefFkf5t3Lby25YJa2R2rtzd1zP369zIQ5yfJSYbCUdwSZo2Yni6k9MktBJH67aIJupDuKOafwVzOW8L/Ftbm/N/NXx+Bx+4ps9g4bNhJkk6XFJJKySzI5gaVe7jZjHJx41+CGy/FjEXdoY3euO2vhMNs7ctEx5TbctLa+GvY6hktyV0xq1pKktl4FW3XlkultFdC4bxLoZjP8AjHYi8Ou625lMR4gS7GsYdclMEnkShb2dTTrs040VJV9IKxiXpZO806Nv4Lbu6MlvPC4+B0obnzGVkzl22rSu5ebIyu7zkMxUEsdAAvYP3Btb1Rf/ALScZD1fT+Vte7bg9YL9Er8eInreL5BfuHuT1LS+UfjcX6bF9Eg9rlbmYhnGM3LhKsS24F62ieB3APQCCwJBB05jlxJ6JcsSdWunVA6/7eN0yxklGyq6EjT/AMhON5esT8lHxtqOYMHkhlmUN/UmmkkT+AqwI9rmfv8ALfSJ+NsfoMf8/uuN2hszHrksnDuSpkLNZpYIf+WirW4nYPYlhXUNKvY2vwcVvSNibX3HNSVUp5DOQ4ezZRU+IGcXh3hT+iW58bA8UfE3rzU2AyuLbIZWa1jT3OOo2EkEMccNxm6UVToFUn4DxlM+j7m2dms/dbI7lyezNz5va82VtGtWppJkGw92obPdQ1I1iEmoj1coAZJC2Q+x91eK2J+1rsuSyvofibvKD0m5Pp31mbu8uveSydI6nbVjpzPGzd7bu+17t7Y+GymExGPrZKenXkhy0tKWaWw9Yx2XkU0UC/lgujN1Kx6Suz/EbAVsxjM9syPJQ0IWy1y9VnTKQCvMJ478tk+aq6qY2Q6/G6hy4xdvb3iR4j4XLYVu8xmVjzcNjuXjr369Zlp26k9L/lhkpmiHo/SrEagrqpwu5bO9d3b8k25t/IbfwWJ3TZx9unXTM2KlzL2+mvQrSS2L9mlHNM0sjqG17pI10UeLWHv46PdO3/GXctjcu7NuZmCvYo95ZxtDGPVjg7oKYe6x6No4Y9RbnpoBb2BsaTILss5O5kMJt3ITLahxUV5u8lpVHKCU1xIXdVmeRgXYdXT0qMZtTZ2Bo7Z21hYzDicFjYErVa0bMzlYoowFUFmJ5DtP7g2t6ov/ANpOMh6vp/K2vdtwesF+iV+PET1vF8gv3D3J6lpfKPxuL9Ni+iQe1jaj0rnMSWkxxYhRKrAdcLMeQ6tAQT2EeQE8T0b9aSncrMUnrSqVdGHkIPGTio0YMnTyhWSWrOzR9EyL0rIrKDyI0BGnk4uZrIo0OOuW2tZvKBSkfM84YNe1tAFHb0jmfhhrV41hgrosUESjRVRBoqge8APa5n7/AC30ifjbH6DH/P8Acja3qi//AGk4yHq+n8ra9xWznLwgaUH0amg655tO3oQc/wCE6AeU8aU9qzTQA8pJrSxuR96sbgH+HhKd9JcBclPTELLK0DsexRMNAD98APh4ls2pnLMxMcXUQsa68lUDs0HFutakaf0F0EMznVuhwfNJPbppxuD1gv0Svx4iet4vkF92bZdXf+GfdS37OKXCPYWOZ71NzFPVjD9IeVHBBRSW+D2l/PbhytTCYXFxGfI5W9MkFeCMdrSSOQqjnpzPGQp7F3bU3DbxcUc9+rCssckcMxIjlKTIhKOQelgND5D7N3cu68vXwWBxxhW7lLTdMUZsTJBEGIB5vJIqj4SOMjS2VvPFbjv4jQ5XHU7CtZrq3JWlgOkiqTyBK6a+0tbehyUEmbpV0tW8WrgzRwuQFdl7QCSPcNyepaXyj8bi/TYvokHtlGcw1bIsg0SaRNJVHvCRdHA+AHjvo9uRSsDqFnlmmT8CR2X+McR160MdavCOmKCJQiKPeVVAAHtsz9/lvpE/G2P0GP8An+5G1vVF/wDtJxkPV9P5W17EgqrNXlQyFILKd28kccjRGWPmQyFlI1B5eXTl7E+LwuFbLzUVjbJzPOlaKHvgWRAzBi7EDUgDQAjU89OIKWUx9nD2bLBK7S9MkEjtroizRFlBOh0D9JPkHs5HMXCfRsbXksSgdrBBqFHwseQ+HjKWRbhilRRNduWOow1omYiKGNF5nsIA5dhJOvb+1OO+bS/j8SStujHlYlLkCtLz6Rrp8fi3hprhp2sdGs9XrXvDJXJ6XGuo+ISv8B+Dj0at1OXbrnnfTqdvfOn8g43B6wX6JX48RPW8XyC+7S2cjvTBZfb+P/1JZa1ivBmGFa24Mlkf81yQwSwXDYlaQQPMLJjEEQKp0tJ0nn7Pghl93qh8LsR4gVZ/ECScFqUIanZjxs98fEFaO68RdpPMVugtxJWw24MVl8hFXWeWvStwWJlruxVZGWN2YIWBAPYT7OXmmkWKKLceznllchVVV3TiiSSeQAHHgTX8PMjQzu+NsZPI3N65TDyx2DQ202PsQzQZCeBmVFnsvCIo5DqXHUoGhPs2blqUQ1qkTzWZm5BI41LMx+AAa8be8XchtPI46vuDOzy7h3FNNS9HODzXc1aSMkdg2OmuIqzDqjAB6/f5+33J6lpfKPxuL9Ni+iQe2wa4iGlJiHlk/wAwSWGYTLHoOjuQvLXXXXX4PcMz9/lvpE/G2P0GP+f9x+IG0vDzw+23uWt4exYt7r5bcFnF27j5OsbKx10TGW4QQFK6ySqOziLctDH28LZguW8Vn9v3wotY7J4+Zq9ypN0FlLRyIQCp0I0Pl09naWzNmbZXePiLvx7X+XsNYs+hUa9SgsbXL+QtCOZooYRKg0VGeRmCKNdSL6eJeB2/hclBMq0JNuZGzkKtiErqWJtVakkbKeWhU69uvk9pVGR2HhjszJ+Jt/wzxtunnJpM4bNTITUFvHGPj0ieHWLrkCWSUQljyXn7OC2cca1mlf8AR4sznBIAlGxkDMmPhZNDqZ3hYdo01X3/AHDa3qi//aTjIer6fytriBaOC+1ajqTYnSzHFKja8gI5AAw0569Y4rUks2MZk6j2zXyEIaKxUlmmmIZG5agqdDodGHLjI42vDRy8GPzM9TK00maGzVYMqmVO81SSNwC4HmnmfjNrxuu3bgsy1JLlOOxPWhab0cehoRJKqasE5aagHTy6Dnxkcu9+2mGyqkV7FwSMsgZw0cRic9VdFkLGJ2QBwSF7AWz9ewiKcTkTVhZNfOjMMUqltSef5Qg8GsJozZVO8av1DrCE6dXT26a8teNxdxrqFrmTT+oLEfV/Jw+S2/cWrYmj7qzHIgkilTXUB0Omuh5gggjhLKSYwK7Mo0qN/ROn/wATi/iJr1OnHL117E1Wt0TdJ81grs7dJI8oGvFSOM9KPSsoyjyqEBA/jA9jcHrBfolfjxE9bxfIL7tZz2P2tiKGcuPJLbzNejXityvMxaRnnRA7F2JLEnme32k9K9Wiu07SGOzUnRZIpEbkVdGBDA+UEcWU2bs3BbSS63VcTC46tQEre/IK8cfUf3/ZnxebxlTMYy10+k469CliCToYOvXFIGVullDDUciAeJK23cDjsBWmYPNXxtWGqjsNdCywqoJ5+X2Zq1mFLFewjRz15VDo6MNGVlbUEEciDwcfYpQT0GQRtRkjVoSi6aKYyCug0Gg04AA0A5AD2+5PUtL5R+NxfpsX0SD2swJ1CldB73LizDW2pm7S1ZWiNjuYYY5Ch0LR99MjMp8h00Pk4aaXG28XIjlGrXFQPyAOo7t3Ujn26+3zP3+W+kT8bY/QY/5/3H4sZ3bXg7b31hd9RYE4bNx5vE46rHLjqLQSrYSzY9JVQ7cykDHQHRW4yFbclupd3bu3cGW3ZvCXH9foS5LNWWszRVe8CuYogVRSwBbTq0Guns+HvjD4eYKLeGQ2lSyeB3TsprMVOxexOVavKZKNiwywrPXmrK3TIyiRSV6lOmt65uLw+yXh6sUqx46jlrdCzasJ06vIyY+eykYDcgGk6j26D2m4LGO8HoNn7vm8WMnurG/6gvtXHJOuCtZ1rjRJBTnktzGxRZoTBYRUKuQ+nZ7TeeZye4Mht3cO6b8uRr7cjNGWCN6D9OJWWYJKwKpDGSY5QBqeznxirGapfZuYmqQtlaHWkghsFB3qB42dWAbXQg9nt9reqL/9pOMh6vp/K2vYw0g7H9IP/wDcS8NdgaucXPYtzT2eoiZ4LWs3o0kfRoxjsEuj9XJGZdPf3wa1CXIzwXaPfUoWjWUQtSjIkUSMgYEggDXmQfe4vM+djS1lOhRhL9aKpfEg00UyERvMFB0UaNoP6R4z8954WkyuVmtwCEsQsHRHFEGLAed0xgnyanlxLuVaYGamrCnJd6m1MIIbp6denyDnprxfxV1eurkYJK84Hb0yKVJHvEa6j4eLOHycRDRMTVsgaJPET5siH3iO0eQ8jz4o21UPrbsxsRz0Kydh/gPG4K2mncZCePT71yOKX6Ja/sexuD1gv0Svx4iet4vkF902XQi2zkt15Xfea+w8NjMZJUicTLVnuvJI92evGqJFWcnztT2AE8YRzhchuncG6spFhdpbUxXci3fuyo8vSr2JIYo0jjieSSSRwqqCe3QHLXYMRf25mNtZezgd17Yygi9Kx+SqBHkhdoHlikUpIjo8bsrKwIPk43ltHb+IzdT/ACbUo25stlqMmPiupfksRo9SGwEmaMGs2jsihv6Oo5ni/lb8jRUcZWlt3ZUjeVlihQu5Ecas7EKDyUEnsAJ43hYwuDzOCh2hmzhLEOcqtRtSyCtBaEvoz/lIlZZ10EgVtOZUdnG1PC77IzV/NbnvegSZeKlLHiqcpoT5BEkuyhY5JHigJEcRcgc26fL7DeE2PxGabJQ0Mldsbhs0pauMZ8XNWgsV6004Q2GRrS9TRgoOzqJ5DbGAxm1cpvree85LY25tPEPVhllioRrLbsTT3ZoIYoog6Asz6lnVQCTxQ3bhK9ujBZms1LuKyEYiuUrtKd61qrYRWZQ8UsbKeliDpqpIIPsR7Twmxc94i7kgwsm5c5jMEacZx+IjlMPpEr3bFdWeR0cRRIWdyjaAacJ4y+lWLezbOJq5bHNXhZrVpb3drUrxQHQmeaWVIlQ6eewU6cbi2jm9nZjw+3ptmtUyF3bWZenM8uPyBlWtbgno2LMMis0Lo4Dao46WHG7vD65tfJYDJbWo0srBftyU5a+RoZCe3Wgs1/RrEzqC9KQFZlRxy1Xn7bcnqWl8o/G4v02L6JB7We/RnmhkxNqvesRw6azV4WVp4m17Q0fV/DpxmYngFbExLUtUtwQSHVK7I0jzoAD3kkcsahYh8YMvkJ4kx2Qxy4gWKptJei1EdaaIJ3SxyAusgdubxvodOoAkAE0cgFEZtwpJJEDr0OR56a/+y2o9tmfv8t9In42x+gx/z/cja3qi/wD2k4yOh0P2fT0P/wC1tcDqlDDyjp0/n4rYb0lZUpzWjBIATrDLYkliU66c1Rwp+Ecfnh+B/wDTxBlJqlaTJ1k7uHIGEd8qak9PXrroCdQOzXnxE06wzvXfvIHkiDFHHYyknkfhHA1lBHlHTpr/AC+z6DncfHegXUws2okjYjQtG66Mp/ePFkbU31m9v17bh5qsfdSoSBoDoyga/DprxZvXd23bNy5I01qy8EfVJI51Zm0YDUniHO183YuyQxSRejyRIikSL066gk8vY3B6wX6JX48RPW8XyC+6f6aP/v8A2/8A8N5jjwR8SIsNkc5gvD7dF191VsRUnv3IKeXw93GC2lWskk0qQy2EMgjRm6eYHI8bryeXwuVxMnj9vrIZDYOMtU5K1kYzD4WpXa9egnEclZJvQ2ZFderz49QC+g8dt2ZalZpbbTZW15FzUkLrWk9COTksrHKQEZolILAHUajXt4wG6KFe1Uobjx9bJ0a12PubKQ2olljE0WrdD9LDVdeR5ex/qp3xu3GZKng62/JMpVKVZDLdr/ZOOhQ00YL3xeUGNek6F/N114/0626eNtWYMZvizayssUTSLVhbb2ViEk7IGVF7yRU6idOoga6kcbuoYlLJOy8wcHlbUsYSGS4leGzIsDdRLiNZ1VjoNG1XyH2PDLLRULMmKp7G3XWt5JYnNeKae7hmijeUDpVnEblVJ1IU6dh48KvG+hgc9lvDypS3jszLZLHYu1kLNWybtVYbnodGOxO9aaXGSxrIqf0kJ0DcDbG4MLksNunxRzW8/Eeng7EIWTG4m5mu8gXIatrFK63ItI9CwJZWClG9jdO9sltPcG4MDvXYmMx+Bt7fxVzLMcthLl+U4+ZKUMxhawl1DG8nTGeltWGnGA8GKW37uc8SfBzB7F3bunblWJJo5e4yyX3xkTrJpLZ7ujNoighiF0bzxxu/xbxuIzmI2jX2Lh9oYyfOY23iZL9yPIXcjaeKreihm6IFmjj6ygBYsFLAa8b8/wD+Z7S//wAzuP225PUtL5R+NxfpsX0SD2te1hqiZMVrJOTxDSLE1iu8LJpG7+Z1K5VtGIBGo1B04O3JdmZFsa1X0QGW9SEyxgaKQyyHRk5dJ8mg4igO1bNyeGJUNqe7TQyuo0636GIXqPM9K/wcUcfanWxahDvZlQEJ3ksjSsE159KltBrz09tmfv8ALfSJ+NsfoMf8/wC4bV7aucl23nLef2xia+cggrWZa8WW3Bj8fYaOK5FPCzdzYcDrjYAnXtHD4/AZirkd41sdHWo7gz0IME9tEVDatwURXU9RBdkhEa68l6B2WL2982m4tx0d0bmxNvLxVkpxypjMzbpxd3XjJEahIgFBZiB2sx1Y7j8eV3zJR2ZtnxIXayeGRo0jSnwUGei29Ymls909v0p5WadWEoVQAnQw7fY8T9ob/wA9isjjsXt/B5fbeJw1Nq1alHkLuUh6XlmeSaeVo6sfW5Krr8SNRrrj9ybI3DisBgMflsFW3CstJrWTt/aWbpY8w15HcQQR93ZZmco7nTpXp16h7Hj5nNhb4k2njvAKpAMHtyLH07VfPZKDFpmbseRktRtKI3jmSughkjKnV+rXTjEZrYNvF4DcG58dj79G7ma8t2tUiuJHLKxghlgaV0jc9ALqpbTq5ajjw03bue8cnuDcGEgu5a+USPvZpNSzdEaqi/vAAex4YeFG0dzTbKn31Fm8rnt10oK1m9XoYSKv+QqpbjmhV55rcYLtG2iK+nnEHjxX3Hviwm48/wCCt7dePyOWjijrHKxbeWSxBYeGELHHJNB0dYQBerXQAceCmP374hNvar46YTJzX8PPQp1I8RmKNGLKKmOanAjGDue9iIndielW6uokcbBow7stybU3Zis0Luz3rUfRo58bHWeKxHYFcW+pjMeoGYp2aKPbbW9UX/7ScZD1fT+Vte7bg9YL9Er8eInreL5BfdMZBu3A1s7Fhri5DFLZBPo9pUaMSxlSCGCuw194n2KWYnx1WfLYyGevjcpJCjWa8NoxmxHFKQXRZTDGXCkBuhddekcXcZk6UGSxuSgkq5HHWo1mgngmUpJFLG4KujqSrKw0I5HiOGGNYYYVCRRIAqqqjQKoHIADkAPYNDL46rlaLSwztTuQpPEZa8qzwv0SBl6o5UV1OmqsAw5gexYTG0K2PS5ZmuW0rRJEJbNhi80zhAOp5GJZmPMnmfZrYzEUK2LxtJBHTx9SJIIIkHYsccYVVHwAcNmRj6wy71hSbK90npJrK5kEJm06+gOSwXXTU69vs38hVx9atfyrRtlL0USJNZaFBHEZpFAZyiDpXqJ0HIcvYz+5cNgq2Pz26Ch3BlYw3fWu7aR0EhJPJWlcgDkOo+23J6lpfKPxuL9Ni+iQe1n/AH1/sjiyuQxmQzkfpLh6lal1L3Bkbu5Kc8OqnSPp6o59G110b35NrSfaMs8Vy1Hi7V2FlcwKDMsc/We8V4x1JqwGvSPKw9vmfv8ALfSJ+NsfoMf8/wC4bKYfEXc7co7k2lkmxeOhaxakr47cmNuWTFEvNykMLvoPIOKGRWtZprfrRWVp3ImgsRCVA4jmibmjrroynmDy4pbb2dbgzGSzniBbyuWy0kKJHRxGZzU+UvkxSSnqeOvI0KEE6uVbp01Ayu1F3vRXwazW+U35d276BJ9sLYF5Mq+MjuekGEVXvxibq7nvApMYPY3Bu77ghpZ65kclLFjYFUCrQNyYY+FmVmDutURl215sT7G/fFGlOMrYzu1MZiKG2kj6HM+HlyFhT3zOFPfG4EA0HTprrz5YLae8cvVxu9Ln+Wspue/BX6q6XsXkaWUswxRLIPNZ6zRKerlqG59nG5o70EVTYVDE4yLbzdKme3k5ZLUl+UsCWWOOPuEUEDVus+97HikNn73x+29qeONGrT8RKNrHSWrtd4aox89jFypPFGj2KaiNhKjBWHeDUkjjdlDMU4cXtfEZODF+G+MjVe8XD0qFaLvpXDMWMtgS9IPMIF9/jwN8MduWYMzjtvTCjv3dDwLGoxdSlZkHdQPKzK89ruEBBbpXr+A+xsHfGyM9QwO8vD+fIJTGWqy3Mfdx+WhSK7UnjhmgkUkwxSI6tqrINQQTxY8N81cjz2z9/wCL3dlfGXdKwpA2QzW4p4o46tKFpZHgijhmn0B6tESJert12HmPEXfWP3VX8KMFZwWyIcZj5aElhraRV5b+SaWzYDzejwIgWMBQWdvLpx4bpVwOVbH7ZxWekyu4GqOMcrX4qqwRrZ+IzkxNqo5j221vVF/+0nGQ9X0/lbXu24PWC/RK/HiJ63i+QX7h7k9S0vlH43F+mxfRIPa2aOTyE1G0raGOanaVT08tUk7rocfCpPEs9a811416lrxwThmA7dCY9NdOz3+BunB2g+aWv3U5FOz03qvJ+5ZxCdDyBRh2HQHVeXFS7EkkcVyFJ445UMcirIoYB0PNSNeYPZ7bM/f5b6RPxtj9Bj/n+5G1vVF/+0nGQ9X0/lbXu24PWC/RK/HiJ63i+QX7h5PHWYqiyqggys6xOF9CrEvHIgLnQydajU6/G7ONxfpsX0SD2swJ1CkaD3uQ9injblhbUtMPGkyAgd0JGMS+cSfNj6VP73t8z9/lvpE/G2P0GP8An+5GKox4JJbVRGoUR3xCzRXCrLMT0HpCgcxz7Dz5cZD1fT+Vte7bg9YL9Er8eInreL5BfuHbzMcAXIXYIq1ifymOEsyj/wDW5+/oPe4yc1WtTsQXpkmQy2JInXphjiIKrBIO1Ne3j9X4/wCeS/VeP1fj/nkv1Xj9X4/55L9V4/V+P+eS/VeCRjsfqe0+mS/VeP1fj/nkv1Xj9X4/55L9V4/V+P8Ankv1Xj9X4/55L9V4/V+P+eS/VeP1fj/nkv1Xj9X4/wCeS/VeP1fj/nkv1XiejeKq9t7bT9wxYKLMsj6KzKuugftK8UMRUeSStj4hDC8xBcqvYWKhRr+8PuRjtzSR65HG1ZqsDaciJSNGPwqC4H3x4tzTV7TxT0aqRywVpp1LJLYLAmJGAIDDt9/j/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrj/D5D5hb/ALrjM2O5mijs3g8PfxPCzKK0CE9Miq2nUpHZxuK/6X6R9v21td13fR3XTGE6depurs110H3B8PTvVc4fCaLGZeTIHEjIGoc+JafoH2qMb+UEIg9I7vvPyRk5N53d8eAFXE+kDAtkZmx1edrCu0EGAuy1e+WwRMWTpRtJfODDVvO42pnoJ8rPVy2xNzwZOKS1ZlxsElbI7c9EWKuWNeGR1edupVDyedqWCALuXD0slN10JZcNmJ6byV56tiStHMRFOoUq6x2EYOh81j29SkDctLIm96XQwm63jmvzTvcDV7V+SvK80zGZnHSjh2YseTa8YaXAUq+SvvDX7+K3MYl7sw6s/UAST1afx8fsxh/nz/i8fsxh/nz/AIvH7MYf58/4vH7MYf58/wCLx+zGH+fP+Lx+zGH+fP8Ai8fsxh/nz/i8TE7axCARsS63n1HLtHm9o4gXJYxL2GOn/qWS1qyhffRgpaTl5Sh1/rDjcqbXG5j4wHxFxTbYagcqKC4MR4f7RORK/wDIehej+kaibtflH+V04zRkLOKeycU1VCx6Y2nyORErKuugLiJATpqekceKyCzlblW/cxt2Gxk7dm4Oueu7yJXaw7iOJCdFjj0RPIo56+HOYxNq1f8AD7Jbprvvi7jxbHeYT0K8FdvRgsphN0VusgdJTUt+TJ48IWzy36+Yo7uwEVU32nW8lKXNpBXSx3xE3W1N0SQS+dzIfnr9xdg4XBZtdpPv3dUO38hvV4YZ/sqB6Vu33kaWUkg72aSskEZlRkDScwToDiq2N35k8pnJd27Xwc+9rUGLfINXy+6aGOtK0cVKOl1pXsvCCK3LQMR16tx4JJiM7FT25ujdsmC3HhBUR5bSNhcrfRvSnYlFR6aeaiAk9r6ebwY+te8ChimvMA6gHT3uXHjLiN2bkGbubX3xJh8Rka1OGkteo+CxF1I4oR3o8yW3IQZGcknmSOXGHz2RFrdWbKSJYaNIIrFopbeDrKRJDECFGp6VUcjoOP8At5mvwoPx+P8At5mvwoPx+P8At5mvwoPx+P8At5mvwoPx+P8At5mvwoPx+P8At5mvwoPx+P8At5mvwoPx+P8At5mvwoPx+L2Kn2rPl4orUsetVRHPWUOQFmJPdEqOWpK/v8YS7srK38TjcRt6/uLc0MUGJsVFr0r+OjmlyC3Uay8UdeeXzKMgmZtOkNyHHh1t2plpqm3sltTdWay2MjigK3LGPt4GtU72SWJ5UWNcjM2kbpq3T1dQHTxiTa3A32Rmd0bt2vPsVq9QR1a+AWQ1cgkqI1kSSdwrN1ylClhNI1IXi9V29mYcNlZPyVfLPXF1a7Bh1nuDJGGYLqB1NoG0JVgOk+Hu/DuCevuzI7U2vl8pmI4arGzYuw03tdcUkDxBZjK2vQika+YV+4ONsZXE08lYw1kXcPPagjmepZCsgngZ1JjkCsR1LodCRrz9ib0avFX9Ilaex3SKnXK/xnbpA1Y6cyefFvD5zGVMzib6d1exd6GOxWnTXXpkilVkYajsI4SONFjjjULHGoAVVA0AAHYB7mm4GxNNs9HVNGPNmCM21ql+8MAn6e8EZfzunXTXnp7FajRrRUqNKJIKdOBFjiiijUKkcaKAqqqgAADQDjHyZbE08o+JtJexT24I5zWtRa9E8JkVu7kXU6MuhHv/AHFs4rNY2rl8XdQx3MbdhSxXlQ9qyRSBlYfARxU2RRxUO2tuY25j8hicfg44qEdOxjL8OTqvBHEgjTpswK5HToeeoOp4x0+Qx1a9Ph7PpuJmsQpK9Wz3UkHfwM4Jjk7qaROpdD0sy66MeMluaKtpmsvSp469cLMS1WhJYlrxhSelQr2pW5DmW5+TjKvjqUGMsZuc28tbqRRxS2LRhjr+kSsq/lJBFFGgZtT0oq9igcY/AYxpmo45GWF7D95K3W7SMztoNSWYns9xKV4UgRmZ2SNQoLMdWYgacyeZPGIy+4tp4bP5Xb7M+ByeSoV7VikzlSzVpZkZ4ixRSShGug97jb+8JHsRZjbdHJY2kYpOmKSrlWqyWYpkIPUDJShdSNCCvboSDe3NV2/ja25MnClbJbgiqQpesQxHWOOayqiR1XyBmIHE1DEwGvXsXr2SnDO0jPayNqW5ZkZnJJ65pmOnYByGgAHFbZ6VvsfblNKkNXF4sJUiir0ihhrRoi9KRKI1UKoAAGg0H/6I/wD/2Q=="></p>

<p><iframe src="examples/example_cube.html" style="height:300px"></iframe></p>

<div id="ref_shape3d"></div>

<h3>Shape 3D</h3>

<p>The display of 2D graphics of <code>HT</code> in <code>GraphView</code> is determined by the <code>shape</code> attribute of <code>style</code>. Similarly, <code>HT</code> provides the <code>shape3d</code> attribute for <code>3D</code>, which pre-defines a variety of <code>3D</code> objects. The default value for <code>shape3d</code> is <code>undefined</code>, and the beta is shown as a hexahedral graphic as described in the previous section. When <code>shape3d</code> is set a certain value, it is displayed as specified by <code>shape3d</code>. The remaining specific parameters are set by <code>shape3d.*</code>:</p>

<ul><li><code>shape3d</code>: The default value is <code>undefined</code>. When empty, it is displayed as a six-sided cube, the other optional values are:<ul><li><code>box</code>: Cube, different from the default hexahedron. This cube type requires six face color and texture to be the same, and higher performance than the default hexahedral.</li><li><code>sphere</code>: Sphere, can be divided into multiple pieces by <code>shape3d.side</code>. Hemispheres can be formed when combined with <code>shape3d.side.from</code> and <code>shape3d.side.to</code>.</li><li><code>cone</code>: Cone, a shape of a triangular pyramid, a quadrangular pyramid, or a shape can be formed by <code>shape3d.side</code></li><li><code>torus</code>: Rings that can be divided into multiple pieces by <code>shape3d.side</code>, semi-rings can be formed when combined with <code>shape3d.side.from</code> and <code>shape3d.side.to</code>.</li><li><code>cylinder</code>: Cylinder, you can control the top and bottom parameters through <code>shape3d.top.*</code> and <code>shape3d.bottom.*</code>.</li><li><code>star</code>: Star-shape object, you can control the top and bottom parameters through <code>shape3d.top.*</code> and <code>shape3d.bottom.*</code>.</li><li><code>rect</code>: Rectangular object, you can control the top and bottom parameters through <code>shape3d.top.*</code> and <code>shape3d.bottom.*</code>.</li><li><code>roundRect</code>: Round rectangle, you can control the top and bottom parameters through <code>shape3d.top.*</code> and <code>shape3d.bottom.*</code>.</li><li><code>triangle</code>: A triangular object, you can control the top and bottom parameters through <code>shape3d.top.*</code> and <code>shape3d.bottom.*</code>.</li><li><code>rightTriangle</code>: Right angle triangular, you can control the top and bottom parameters through <code>shape3d.top.*</code> and <code>shape3d.bottom.*</code>.</li><li><code>parallelogram</code>: Parallel quadrilateral object, you can control the top and bottom parameters through <code>shape3d.top.*</code> and <code>shape3d.bottom.*</code>.</li><li><code>trapezoid</code>: Trapezoidal object, you can control the top and bottom parameters through <code>shape3d.top.*</code> and <code>shape3d.bottom.*</code>.</li></ul></li><li><code>shape3d.color</code>: The default value is <code>#3498DB</code>, overall color of the <code>3d</code> graphics </li><li><code>shape3d.top.color</code>: The default value is <code>undefined</code>, top-surface-color of the <code>3d</code> graphics </li><li><code>shape3d.bottom.color</code>: The default value is <code>undefined</code>, bottom-surface-color of the <code>3d</code> graphics </li><li><code>shape3d.from.color</code>: The default value is <code>undefined</code>, starting-surface-color of the <code>3d</code> graphics </li><li><code>shape3d.to.color</code>: The default value is <code>undefined</code>, ending-surface-color of the <code>3d</code> graphics </li><li><code>shape3d.image</code>:The default value is <code>undefined</code>, overall texture of the <code>3d</code> graphics </li><li><code>shape3d.top.image</code>: The default value is <code>undefined</code>, top-surface-texture of the <code>3d</code> graphics </li><li><code>shape3d.bottom.image</code>: The default value is <code>undefined</code>, bottom-surface-texture of the <code>3d</code> graphics  </li><li><code>shape3d.from.image</code>: The default value is <code>undefined</code>, starting-surface-texture of the <code>3d</code> graphics  </li><li><code>shape3d.to.image</code>: The default value is <code>undefined</code>, ending-surface-texture of the <code>3d</code> graphics</li><li><code>shape3d.light</code>:The default value is <code>true</code>, whether the graphics are influenced by the light</li><li><code>shape3d.side</code>: The default value is <code>0</code>, number of the sides of the <code>3d</code> graphics. The graphic is displayed as a smooth surface when set <code>0</code>.</li><li><code>shape3d.side.from</code>: The default value is <code>undefined</code>, determines the position of the start side of the <code>3d</code> graphics.</li><li><code>shape3d.side.to</code>: The default value is <code>undefined</code>, determines the position of the end side of the <code>3d</code> graphics.</li><li><code>shape3d.visible</code>: The default value is <code>true</code>, whether the graphics is visible. This parameter does not affect <code>label</code>, <code>note</code>, <code>icons</code> and other elements.</li><li><code>shape3d.from.visible</code>: The default value is <code>true</code>, whether the starting surface of the graphics is visible.</li><li><code>shape3d.to.visible</code>: The default value is <code>true</code>, whether the ending surface of the graphics is visible.</li><li><code>shape3d.top.visible</code>: The default value is <code>true</code>, whether the top surface of the graphics is visible.</li><li><code>shape3d.bottom.visible</code>: The default value is <code>true</code>, whether the bottom surface of the graphics is visible.</li><li><code>shape3d.torus.radius</code>: The default value is <code>0.17</code>, determines the radius of the 3d circular tube</li><li><code>shape3d.resolution</code>: The default value is <code>24</code>, determines the accuracy of the<code>3d</code> graphics, similar to <code>side</code> but different in directions in terms of the sections they divide. The larger the value is, the better distributed the graphics are, but the more it influences performance.</li><li><code>shape3d.blend</code>: The default value is <code>undefined</code>, determines dyeing of the <code>3d</code>graphics</li><li><code>shape3d.opacity</code>: The default value is <code>undefined</code>, determines the transparency of the <code>3d</code>graphics, value ranges between <code>0~1</code></li><li><code>shape3d.reverse.flip</code>: The default value is <code>false</code>, determines whether the front side is shown on the opposite side</li><li><code>shape3d.reverse.color</code>: <code>#868686</code>, determines the color of the opposite side of the <code>3d</code> graphics</li><li><code>shape3d.reverse.cull</code>: The default value is <code>false</code>, determines whether the opposite side is shown. Hiding the opposite side can improve performance.</li><li><code>shape3d.transparent</code>: The default value is <code>false</code>, determines whether the <code>3d</code> graphics is transparent.</li><li><code>shape3d.uv.offset</code>: The default value is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics overall map in the format <code>[0.5, 0.5]</code></li><li><code>shape3d.uv.scale</code>: The default value is <code>undefined</code>, which determines the <code>uv</code> scaling of the <code>3d</code> graphics overall map in the format <code>[3, 2]</code></li><li><code>shape3d.top.uv.offset</code>:The default value is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics top-surface map in the format <code>[0.5, 0.5]</code></li><li><code>shape3d.top.uv.scale</code>:The default value is <code>undefined</code>, which determines the <code>uv</code> scaling of the <code>3d</code> graphics top-surface map in the format <code>[3, 2]</code></li><li><code>shape3d.bottom.uv.offset</code>:The default value is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics bottom-surface map in the format <code>[0.5, 0.5]</code></li><li><code>shape3d.bottom.uv.scale</code>:The default value is <code>undefined</code>, which determines the <code>uv</code> scaling of the <code>3d</code> graphics bottom-surface map in the format <code>[3, 2]</code></li><li><code>shape3d.from.uv.offset</code>:The default value is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics starting-surface map in the format <code>[0.5, 0.5]</code></li><li><code>shape3d.from.uv.scale</code>:The default value is <code>undefined</code>, which determines the <code>uv</code> scaling of the <code>3d</code> graphics starting-surface map in the format <code>[3, 2]</code></li><li><code>shape3d.to.uv.offset</code>:The default value is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics ending-surface map in the format <code>[0.5, 0.5]</code></li><li><code>shape3d.to.uv.scale</code>:The default value is <code>undefined</code>, which determines the <code>uv</code> scaling of the <code>3d</code> graphics ending-surface map in the format <code>[3, 2]</code></li><li><code>shape3d.discard.selectable</code>: The default value is <code>true</code>, means that the part with overall texture transparency low enough to be rejected can also be clicked to select. It can be set to <code>false</code>, then the excluded part is not selectable.</li><li><code>shape3d.top. discard.selectable</code>: The default value is <code>true</code>, means that the part with top texture transparency low enough to be rejected can also be clicked to select. It can be set to <code>false</code>, then the excluded part is not selectable.</li><li><code>shape3d.bottom. discard.selectable</code>: The default value is <code>true</code>, means that the part with bottom texture transparency low enough to be rejected can also be clicked to select. It can be set to <code>false</code>, then the excluded part is not selectable.</li><li><code>shape3d.from. discard.selectable</code>: The default value is <code>true</code>, means that the part with starting-surface-texture transparency low enough to be rejected can also be clicked to select. It can be set to <code>false</code>, then the excluded part is not selectable.</li><li><code>shape3d.to. discard.selectable</code>: The default value is <code>true</code>, means that the part with ending-surface-texture transparency low enough to be rejected can also be clicked to select. It can be set to <code>false</code>, then the excluded part is not selectable.</li></ul>

<p><iframe src="examples/example_shape3d.html" style="height:300px"></iframe></p>

<div id="ref_text"></div>

<h3>3d text</h3>

<p><code>HT</code> can render <code>3D</code> text by loading the corresponding <code>typeface</code> font and specifying the <code>shape3d</code> type of the <code>node</code> as <code>text</code>.
Rendering <code>3D</code> fonts needs a <code>json</code> format typeface font first, for more details please use the website <a href="http://gero3.github.io/facetype.js">facetype.js</a> to generate, download the generated font (<code>json</code> format), and load fonts into memory through <code>ht.Default.loadFontFace</code>.</p>

<ul><li><code>ht.Default.loadFontFace(url, callback)</code><ul><li><code>url</code> The address of the <code>typeface</code> font in <code>json</code> format</li><li><code>callback</code> The optional parameter. If without, it is synchronized loading; if within, it is asynchronously loaded fonts, parameters of which are successfully loaded font names</li></ul></li></ul>

<p>Specific control parameters for texts include: </p>

<ul><li><code>shape3d.text</code>: The text needed to be displayed. If it needs to set, the text is used first. If not, the <code>name</code> of the <code>node</code> is used first, then the <code>label</code></li><li><code>shape3d.text.amount</code>: The default value is <code>0.5</code>, which determines the relative thickness of the text</li><li><code>shape3d.text.size</code>: The default value is <code>1</code>, which determines the magnification factor of the text. Of course it can also be done through the zoom in and zoom out of <code>node</code> itself (also proposed).</li><li><code>shape3d.text.fill</code>: The default value is <code>true</code>, which determines whether the graph is solid</li><li><code>shape3d.text.curveSegments</code>: the default value is <code>4</code>, which determines the sampling frequency of the curve of the graphics, the greater the finer (pay attention to the balance of performance)</li><li><code>shape3d.text.spacing</code>: The default value is <code>1</code>, which determines the size of the space between words</li><li><code>shape3d.text.font</code>: Font name, note that it is the <code>familyName</code> in the <code>json</code> font file. The first of the loaded font will be used when empty.</li><li><code>shape3d.text.style</code>: The default value is <code>normal</code>, indicating that the font is <code>normal</code> (standard), <code>italic</code> (italic), <code>oblique</code> (tilted)</li><li><code>shape3d.text.weight</code>: The default value is <code>normal</code>, indicating that the font is <code>normal</code> (standard), <code>bold</code> (bold)</li></ul>

<p><iframe src="examples/example_3dtext.html" style="height:300px"></iframe></p>

<div id="ref_edge"></div>

<h3>ht.Edge</h3>

<p><code>ht.Edge</code> in the <code>GraphView</code> topology components, as a connection linking nodes, is an important beta type. The display function of 2D connection still exists in the <code>Graph3dView</code> package. It can also realize the 3D connection effect. At the same time, it also added some connection parameters for <code>3D</code>. Connection in the 3D space is displayed in a non-stereo way by default. By setting <code>style</code> to <code>cylinder</code>, it can be presented as a tubular with 3D effect. The commonly used parameters to build the connection are the followings:</p>

<ul><li><code>edge.color</code>: Connection color</li><li><code>edge.width</code>: The width of the connection</li><li><code>edge.gradient.color</code>: Gradual color of the connection, currently available only in the <code>3D</code> connection on the non-stereo mode, the <code>target</code> end will be displayed in this gradual color</li><li><code>edge.source.t3</code>: Connection <code>source</code> end offset, <code>[tx, ty, tz]</code> format, empty by default</li><li><code>edge.target.t3</code>: Connection <code>target</code> end offset, <code>[tx, ty, tz]</code> format, empty by default</li></ul>

<p>When the <code>edge.type</code> is the type of <code>points</code>, the inflection point parameter set to <code>edge.points</code> is compatible with the <code>JSON</code> format of the <code>2D</code> <code>{x: 100, y: 100}</code>. The new parameter <code>e</code> is introduced on the <code>3D</code> to represent the elevation of <code>elevation</code>, so the <code>3D</code> supports the inflection point parameter in the format of <code>{x: 10, y: 20, e: 30}</code>, which represents the position of the <code>x</code> axis <code>10</code>, the <code>y</code> axis <code>30</code>, and the <code>z</code> axis <code>20</code>.</p>

<p>By setting <code>style</code> to <code>cylinder</code>, it can be presented as a tubular with 3D effect. Then all the parameters to control the connection are in<code>shape3d.*</code>. <code>repeat.uv.length</code> parameter is added with the default parameter being empty. If the length value is set, the texture will automatically adjusts the map multiple in the direction of the connection based on the length of the connection.</p>

<p>Refer to <a href="../../core/shape/ht-shape-guide.html#ref_polyline">Shape Manual</a></p>

<div id="ref_shape"></div>

<h3>ht.Shape</h3>

<p>Refer to <a href="../../core/shape/ht-shape-guide.html">Shape Manual</a>.</p>

<div id="ref_other"></div>

<h2>Other Parameters</h2>

<div id="ref_whole"></div>

<h3>Whole</h3>

<ul><li><code>brightness</code>: The default is <code>undefined</code>, as for the <code>3D</code> brightness of whole graph, 1 means unchanged, values greater than 1 mean brighter and values less than 1 mean darker.</li><li><code>opacity</code>: The default is <code>undefined</code>, values vary from 0 to 1 (<code>0~1</code>)in the <code>3D</code> transparency of whole graph.</li><li><code>transparent.mask</code>: The default is <code>false</code>, this attribute makes graphic data not display on screen but you can still click on and select the data.</li></ul>

<div id="ref_label"></div>

<h3>Label</h3>

<p>Text attributes of <code>label.*</code> in 2D still apply in 3D which also support the second internal <code>label2.*</code>, meanwhile it added some attribute parameters in accordance with the layout of 3D space and the features of displaying as following: </p>

<ul><li><code>label.face</code>: The default is <code>front</code>, as for the orientation of labels in <code>3D</code>, the value list contains <code>left|right|top|bottom|front|back|center</code>.</li><li><code>label.t3</code>: The default is <code>undefined</code>, in the matter of labels’ offset in <code>3D</code>, the format is <code>[x,y,z]</code>.</li><li><code>label.r3</code>: The default is <code>undefined</code>, as for the rotation of labels in <code>3D</code>, the format is <code>[rotationX,rotationY,rotationZ]</code>.</li><li><code>label.rotationMode</code>: The default is <code>xzy</code>, labels in <code>3D</code> rotate along three axis with sequences changes, the value list contains <code>xyz|xzy|yxz|yzx|zxy|zyx</code>.</li><li><code>label.light</code>: The default is <code>false</code>, it means whether labels in <code>3D</code> are influenced by light. </li><li><code>label.blend</code>: The default is <code>undefined</code>, it means the coloring of in labels in <code>3D</code>.</li><li><code>label.opacity</code>: The default is <code>undefined</code>, as for the transparency of labels in <code>3D</code>, values vary from 0 to 1(<code>0~1</code>).</li><li><code>label.reverse.flip</code> : The default is <code>false</code>, it means whether the back side of labels in <code>3D</code> show the content of the front side. </li><li><code>label.reverse.color</code> : The default is <code>#868686</code>, it means the color of back side of labels in <code>3D</code>.</li><li><code>label.reverse.cull</code>: The default is <code>false</code>, it means whether labels in <code>3D</code> are showed in the back side, and hiding the back can improve performance. </li><li><code>label.transparent</code>: The default is <code>false</code>, it means whether labels in <code>3D</code> are transparent. </li><li><code>label.autorotate</code>: The default is <code>false</code>, it means whether labels in <code>3D</code> move towards eyes automatically, and the value can be set <code>true</code> or <code>x</code>、<code>y</code>、<code>z</code>, in which <code>y</code> represents the rotation is limited along Y-axis.</li><li><code>label.texture.scale</code>: The default is <code>2</code>, this value means the multiple that memory actually has created texture, and it is suitable for setting too large or it will affect performance. </li></ul>

<p>Refer to <a href="../../core/position/ht-position-guide.html#ref_3d">Position Guide</a> </p>

<div id="ref_note"></div>

<h3>Notes</h3>

<p>The attribute of annotation of <code>note.*</code> in <code>2D</code> also apply in <code>3D</code> which also support the second internal <code>note.*</code> annotation, meanwhile it added some attribute parameters in accordance with the layout of 3D space and the features of displaying as following: </p>

<ul><li><code>note.face</code>: The default is <code>front</code>, as for the orientation of annotation in <code>3D</code>, the value list contains <code>left|right|top|bottom|front|back|center</code>.</li><li><code>note.t3</code>: The default is <code>undefined</code>, in the matter of annotations’ offset in <code>3D</code>, the format is <code>[x,y,z]</code>.</li><li><code>note.r3</code>: The default is <code>undefined</code>, as for the rotation of annotations in <code>3D</code>, the format is <code>[rotationX,rotationY,rotationZ]</code>.</li><li><code>note.rotationMode</code>: The default is <code>xzy</code>, annotations in <code>3D</code> rotate along three axis with sequences changes, the value list contains <code>xyz|xzy|yxz|yzx|zxy|zyx</code>.</li><li><code>note.light</code>: The default is <code>false</code>, it means whether annotations in <code>3D</code> are influenced by light.</li><li><code>note.blend</code>: The default is <code>undefined</code>, it means the coloring of in annotations in <code>3D</code>.</li><li><code>note.opacity</code>: The default is <code>undefined</code>, as for the transparency of annotations in <code>3D</code>, values vary from 0 to 1(<code>0~1</code>).</li><li><code>note.reverse.flip</code>: The default is <code>false</code>, it means whether the back side of annotations in <code>3D</code> show the content of the front side. </li><li><code>note.reverse.color</code> : The default is <code>#868686</code>, it means the color of back side of annotations in <code>3D</code>.</li><li><code>note.reverse.cull</code>: The default is <code>false</code>, it means whether annotations in <code>3D</code> show in the back side, and hiding the back can improve performance. </li><li><code>note.transparent</code>: The default is <code>false</code>, it means whether annotations in <code>3D</code> are transparent.</li><li><code>note.autorotate</code>: The default is <code>false</code>, it means whether annotations in <code>3D</code> move towards eyes automatically, and the value can be set <code>true</code> or <code>x</code>、<code>y</code>、<code>z</code>, in which <code>y</code> represents the rotation is limited along Y-axis.</li><li><code>note.texture.scale</code>: The default is <code>2</code>, this value means the multiple that memory actually has created texture and it is suitable for setting too large or it will affect performance. </li></ul>

<p>See examples in <a href="../../core/position/ht-position-guide.html#ref_3d">Position Guide</a> </p>

<div id="ref_icons"></div>

<h3>Icons</h3>

<p><code>icons</code> is a very useful attribute of <code>HT</code> data extension, and it can be used to add as many icon accessories for data as you want. 
The sample codes of <code>icons</code> content structure are as following: </p>

<pre><code>data.setStyle(&#39;icons&#39;, {
   whateverName1: {
       position: 17,
       direction: &#39;north&#39;, // east, west, south, north
       gap: 1,
       names: [&#39;icon1&#39;, &#39;icon2&#39;, &#39;icon3&#39;]
   },
   whateverName2: {
       position: 20,
       width: 16,
       height: 16,
       name: [&#39;icon5&#39;]
   },
   whateverName3: ...
]);</code></pre>

<p><code>icons</code> can be divided into many groups, and <code>whateverName*</code> can be understood as  the name of a group. This name is not displayed by <code>HT</code> and not used to present interface results. Users can name and manage it according to their demands. 
Setting the icons objects directly will remove the side effects caused by the icons that have been set. For this reason, <code>HT</code> provides the function of <code>Data#addStyleIcon(name, json)</code> and <code>Data#removeStyleIcon(name)</code> so as to control and manage additions and deletions of icons, therefore the above code can be implemented by the code shown below: </p>

<pre><code>data.addStyleIcon(&#39;whateverName1&#39;, {
       position: 17,
       direction: &#39;north&#39;, // east, west, south, north
       gap: 1,
       names: [&#39;icon1&#39;, &#39;icon2&#39;, &#39;icon3&#39;]
});
data.addStyleIcon(&#39;whateverName2&#39;, {
       position: 20,
       width: 16,
       height: 16,
       name: [&#39;icon5&#39;]
});</code></pre>

<p>From the sample code above, you can find that every group of icons defines <code>icon5</code> for single icon as well as puts many icons in order, such as <code>[&#39;icon1&#39;, &#39;icon2&#39;, &#39;icon3&#39;]</code> of <code>whateverName1</code>. Other parameters of <code>json</code> are related to how to set and display these icons.
The other parameters of <code>json</code> are associated with how to place and display these icons.</p>

<ul><li><code>names</code>: It contains many arrays of strings; for every strings, there is a image or vetor (you can register by <code>ht.Default.setImage</code>).</li><li><code>visible</code>: It means whether this group of images show. </li><li><code>for3d</code>: It means this group of images can be only used for displaying in the components of <code>Graph3dView</code> rather than the componets of <code>GraphView</code>. </li><li><code>direction</code>: Its value is one of the <code>west</code>、<code>east</code>、<code>north</code>、<code>south</code>, and it sets the arranging direction of <code>icons</code>; its default is <code>east</code>.</li><li><code>keepOrien</code>: When you rotate <code>Edge</code>, <code>icons</code> will adjust the directions automatically in order to maintain the best reading effects (such as characters); this attribute is <code>true</code> which means automatical adjustment of directions is forbidden. </li><li><code>gap</code>: It specify the distance between icons in the same group. </li><li><code>width</code>: It specify the width of every <code>icon</code>, its default width is the one of image first registered.</li><li><code>height</code>: It specify the height of every <code>icon</code>, its default height is the one of image first registered.</li><li><code>face</code>: The default is <code>front</code>, as for the orientation of icons in <code>3D</code>, the value list contains <code>left|right|top|bottom|front|back|center</code>.</li><li><code>t3</code>: The default is <code>undefined</code>, in the matter of icons’ offset in <code>3D</code>, the format is <code>[x,y,z]</code>.</li><li><code>r3</code>: The default is <code>undefined</code>, as for the rotation of icons in <code>3D</code>, the format is <code>[rotationX,rotationY,rotationZ]</code>.</li><li><code>rotationMode</code>: The default is <code>xzy</code>, icons in <code>3D</code> rotate along three axis with sequences changes, the value list contains <code>xyz|xzy|yxz|yzx|zxy|zyx</code>.</li><li><code>light</code>: If <code>shape3d</code> is null, the default will be <code>false</code> or it will be <code>true</code>, it means whether icons in <code>3D</code> are influenced by light.</li><li><code>blend</code>: The default is <code>undefined</code>, it means the coloring of in characters in <code>3D</code>.</li><li><code>opacity</code>: The default is <code>undefined</code>, as for the transparency of characters in <code>3D</code>, values vary from 0 to 1(<code>0~1</code>).</li><li><code>reverseFlip</code> : The default is <code>false</code>, it means whether the back side of characters in <code>3D</code> show the content of the front side.</li><li><code>reverseColor</code>: The default is <code>#868686</code>, it means the color of back side of characters in <code>3D</code>.</li><li><code>reverseCull</code>: The default is <code>false</code>, it means whether characters in <code>3D</code> show in the back side, and hiding the back can improve performance. </li><li><code>transparent</code>: The default is <code>false</code>, it means whether characters in <code>3D</code> are transparent.</li><li><code>autorotate</code>: The default is <code>false</code>, it means whether characters in <code>3D</code> move towards eyes automatically.</li><li><code>discardSelectable</code>: The default is <code>true</code> which means the transparency of texture is so low that the excluded part can be clicked on and selected, otherwise you can set the value of <code>false</code>.</li><li><code>textureScale</code> The default is <code>2</code>, this value means the multiple that memory actually has created texture , and it is suitable for setting too large or it will affect performance. 
See examples in <a href="../../core/position/ht-position-guide.html#ref_3d">Position Guide</a> </li><li><code>shape3d</code>: This attribute specify to display the effects of icon for <code>3d</code> model, if you set this attribute , the attributes of <code>names</code>, <code>width</code> and <code>height</code> will be ignored.
See examples in <a href="../../core/position/ht-position-guide.html#ref_3d">Position Guide</a> </li><li><p><code>position</code>: It specify the position of <code>icons</code>, the corresponding positions of enumeration values it supported are as following: </p><pre><code>    34                            35
   1 | 2     38     3     39     4 | 5
 ----6-------40-----7-----41-------8----
   9 | 10    42     11    43    12 | 13
     |                             |
     |              44             |
  14 15 16   45  46 17 47  48   18 19 20
     |              49             |
     |                             |
  21 | 22    50     23     51   24 | 25
 ----26------52-----27-----53------28---
  29 | 30    54     31     55   32 | 33
    36                            37</code></pre></li></ul>

<div id="ref_host"></div>

<h3>Attach</h3>

<p>The function of attaching is very convenient for designing hierarchically related mode, for example, device panels attach rack and device ports attach device panels. So it will shape the mode of frame-panel-port attaching, and when users drag the whole frames, all graphical elements of this hierarchy will move accordingly.
In the context of <code>3D</code>, the concept of host extends further. When rack shift anywhere in 3D space and rotate by arbitrary angle, all related host graphical elements would pan correctly. They will do some rotation to corresponding location. As a result, all parts of images of the whole device will relatively uniform in location of scenes.</p>

<ul><li><code>Node#getHost()</code> and <code>Node#setHost(node)</code> Used for catching and setting the data objects to attach. </li><li><code>Node#getAttaches()</code> Returns all attached objects of this graphical element and return the objects of <code>ht.List</code> link lists. If there is no object to attach, the result of returning will be null. If there is no object to attach, the result of returning will be null. </li><li><code>Node#isHostOn(node)</code> Used for judging whether the graphical elements can attach specific objects. </li><li><code>Node#isLoopedHostOn(node)</code> Used for judging whether the graphical elements can attach in a ring with specific graphical elements, for example, <code>A</code> host <code>B</code>, <code>B</code> host <code>C</code> and <code>C</code> host <code>A</code> back, so this is the ring-like attaching among <code>A</code>, <code>B</code> and <code>C</code>. </li></ul>

<p><iframe src="examples/example_host.html" style="height:400px"></iframe></p>

<div id="ref_3dview"></div>

<h2>3D components</h2>

<div id="ref_viewbasic"></div>

<h3>Component Basis</h3>

<p>Displaying 3D view of <code>HT</code> component are <code>ht.graph3d.Graph3dView</code>, <code>ht.graph3d</code> is a package related to <code>3D</code> components, <code>Graph3dView</code> is a kind of components for presenting <code>3D</code> view. 
By analogy with <code>ht.graph.GraphView</code> as <code>2D</code> view components, both <code>GraphView</code> and  <code>Graph3dView</code> can share the same data model <code>DataModel</code>. The design of API in <code>2D</code> and <code>3D</code>, <code>HT</code> kept consistency in many places. </p>

<p>The <code>DOM</code> structure, the interface of <code>Graph3dView</code>, consists of the bottom element <code>div</code> and renders layer element <code>canvas</code>. The <code>div</code> can be gotten by <code>getView()</code> while the <code>canvas</code> can be gotten by <code>getCanvas()</code>. The interactive events of <code>HT</code> default are added on the bottom element <code>div</code>, so users can make custom interaction extension through adding ways of events listener on elements returned by <code>getView()</code>. </p>

<p>Color parameters are flexible in <code>HTML</code>. They can be the hexadecimal format of <code>#F0F1F2</code>, string names such as <code>red</code> and <code>black</code>, the format of <code>rgb(255,128,32)</code> or the format <code>rgba(255,128,32,0.5)</code> that contains transparency. However, in matter of the API interfaces of <code>WebGL</code>, it is generally required that values of four rgba parameters vary from 0 to 1 (<code>0~1</code>). So default values of color parameters for <code>Graph3dView</code> components are formatted in numbers array of [r,g,b,a], but considering compatibility of <code>Data</code> and <code>2D</code> on <code>DataModel</code>, color parameters will be transformed automatically inside <code>HT</code>. Therefore, no matter <code>data.s(&#39;all.color&#39;,&#39;red&#39;)</code> or <code>data.s(&#39;all.color&#39;,[1,0,0,1])</code>, they all have the same effects.</p>

<div id="ref_light"></div>

<h3>Light Setting</h3>

<p>Lighting, frog and other effects refer to <a href="../lighting/ht-lighting-guide.html">Lighting Guide</a></p>

<div id="ref_gridaxis"></div>

<h3>Grid Axis</h3>

<p>In order to provide 3D space coordinate reference, <code>Graph3dView</code> has prepared real <code>xz</code> surface grid, three direction axes of <code>x</code>,<code>y</code> and <code>z</code>, and the displaying function of central point position. 
All these parameters close the status of not displaying, and you can open the switch and change the displaying parameters of as required. </p>

<ul><li><code>getGridSize()|setGridSize(40)</code> Specifies the numbers of rows and columns of grids. </li><li><code>getGridGap()|setGridGap(20)</code> Specifies the spacing of grid lines.</li><li><code>getGridColor()|setGridColor([0.4, 0.75, 0.85, 1.0])</code> Specifies the color of grid lines.</li><li><code>isGridVisible()|setGridVisible(true)</code> Specifies whether grids will be displayed. </li><li><code>isOriginAxisVisible()|setOriginAxisVisible(true)</code> Specifies whether coordinate origin <code>[0,0,0]</code> grid will be displayed. </li><li><code>isCenterAxisVisible()|setCenterAxisVisible(true)</code> Specifies whether current central point axis will be displayed. </li><li><code>getAxisXColor()|setAxisXColor([1.0, 0.0, 0.0, 1.0])</code> Specifies the color of <code>x</code> axis. </li><li><code>getAxisYColor()|setAxisYColor([0.0, 1.0, 0.0, 1.0])</code> Specifies the color of <code>y</code> axis. </li><li><code>getAxisZColor()|setAxisZColor([0.0, 0.0, 1.0, 1.0])</code> Specifies the color of <code>z</code> axis. </li></ul>

<div id="ref_3dinteraction"></div>

<h3>3D Interaction</h3>

<div id="ref_interactionmode"></div>

<h4>Interaction Basis</h4>

<div id="ref_defaultmode"></div>

<h5>Default Mode</h5>

<p>There is a big difference of interaction between <code>3D</code> and <code>2D</code>. It is set default that <code>Graph3dView</code> provides the rotation along central point of <code>Graph3dView#getCenter()</code>.
When you operate <code>Drag</code> in this mode, it will change the location of eye observation points for <code>Graph3dView#getEye()</code>, mouse wheel and zooming effects of touch screen  <code>pinch</code>. Actually, it will change the position of <code>eye</code> as well, which make the <code>eye</code> close to the central position of <code>center</code> or further away from it. Eventually, the effects of approaching and moving away from something and zooming effects will be achieved. </p>

<ul><li>Mouse operation: You can click the left mouse button to <code>drag</code> for rotating along the center, you can click the button of <code>shift</code> to pan the viewpoint, the mouse wheel can approach and move away from the center. When you click the right mouse button to <code>drag</code>, the location changes of top and bottom will influence forward and backward moving, and the horizontal position changes will influence horizontal translation.</li><li>Touch interaction: You can use a single finger to drag for rotating along the center, you can use two fingers to zoom in the way of <code>pinch</code>, you can use three fingers to drag for translation in the way of <code>pan</code>. </li></ul>

<div id="ref_firstpersonmode"></div>

<h5>First Person Mode</h5>

<p><code>Graph3dView</code> also provide the interactive roaming model of first person; this mode will change the position of <code>eye</code> and <code>center</code> at the same time. 
You can switch to First person mode by <code>Graph3dView#setFirstPersonMode(true)</code>, and this mode will produce the results that passengers are walking and cars are travelling:</p>

<ul><li>Mouse operation:  Click the left mouse button without loose, and the operation of moving forward will be done; Click the right mouse button without loose, and the operation of moving backward will be done. You can change the forward and backward direction by moving mouse in the whole process; click the button of <code>shift</code>, and you can pan` the viewpoint, and that means changing the positions both vertically and horizontally. The mouse wheel can adjust the vertical positions.</li><li>Touch screen operation: Click by single finger without loose, and the operation of moving forward will be done; Click by two fingers without loose, and the operation of moving backward will be done. You can change the forward and backward direction by moving fingers in the whole process; drag by three fingers, and you can <code>pan</code> the viewpoint, and that means changing the positions both vertically and horizontally.</li></ul>

<p>The parameters of <code>Graph3dView#setMouseRoamable(true|false)</code> can be set in the mode of first person. The default value is <code>true</code>. When you set the <code>false</code>, the mouse will lose the function of moving forward and backward; in this condition, the left mouse button can drag and edit graphic elements and the right mouse button can change the view directions; the above operation will combine with roam operation by using <code>w|s|a|d</code> buttons on keyboard.</p>

<div id="ref_interactionfunc"></div>

<h4>Interaction Function</h4>

<p>Interaction functions of <code>HT</code> generally contain parameter <code>anim</code> that means whether to start animations. This parameter can be simply <code>true|false</code> type of <code>boolean</code>, and can be the <code>json</code> object as well. 
When the parameter is the <code>json</code> object, it means starting the animation. Meanwhile the attributes of the <code>json</code> object will be applied to control parameters related to animation. You can refer to the sample code snippets as following:</p>

<pre><code>g3d.walk(distance, {
    frames: 50,
    interval: 30,
    easing: function (t) {return t; },
    finishFunc: function () {
        forwardIndex += 1;
        if (points.length - 2 &gt; forwardIndex) {
            g3d.setCenter([point2.x, 1600, point2.y]);
            setTimeout(function () {
                g3d.rotate(Math.PI / 2, 0, {
                    frames: 30,
                    interval: 30,
                    easing: function (t) {return t;},
                    finishFunc:function () {forward();}
                });
            }, 60);
        } else {
            var lastPoint = points[points.length  - 1];
            g3d.setCenter([lastPoint.x, 1400, lastPoint.y]);
            setTimeout(function () {
                g3d.rotate(-Math.PI / 2, 0, {
                    frames: 30,
                    interval: 30,
                    finishFunc: function () {
                        window.isAnimationRunning = false;
                    }
                });

            }, 60);
        }
    }
});</code></pre>

<p>Although the nature of <code>3D</code> interaction is to change the position parameters of <code>eye</code> and <code>center</code>, direct operation of 3D coordinate points is too raw and obscure. 
<code>Graph3dView</code> gives clearer and more intuitive way for function operating below: </p>

<ul><li><p><code>setZoom(increment, anim)</code>: It is the operation of zooming; the default operation mode means the changes of  distance between <code>eye</code> and <code>center</code>, and <code>increment</code> means the proportion of stepping.
Calling <code>zoomIn(anim)</code> and <code>zoomOut(anim)</code> equates to call <code>setZoom(1.3, anim)</code> and <code>setZoom(1/1.3, anim)</code>.
Under the condition that <code>Graph3dView#isOrtho()</code> is <code>true</code> orthographic projection, the zooming means changing the <code>Graph3dView#setOrthoWidth(width)</code> width of visual scope for <code>Graph3dView#setOrthoWidth(width)</code>.</p></li><li><p><code>pan(dx, dy, anim, firstPersonMode)</code>: It means the pan of up, down, left and right direction. The nature is that <code>eye</code> and <code>center</code> will make the same amount of four directions’ offset; <code>dx</code> means the parameter of horizontal offset, <code>dy</code> means the parameter of vertical offset while the <code>dx</code> and <code>dy</code> present the moving pixels on screen. <code>Graph3dView</code> 
<code>Graph3dView</code> can convert into proper amount of 3D logical coordinates offset automatically. 
When parameters of <code>firstPersonMode</code> are null, the current value of <code>Graph3dView#isFirstPersonMode()</code> will be adopted by default. 
When first person mode calls the operation of <code>pan</code>, this function will consider limiting the boundary of <code>Graph3dView#getBoundaries()</code>.</p></li><li><p><code>rotate(leftRight, upDown, anim, firstPersonMode)</code>: It means that four directions rotate at some angle; <code>leftRight</code> means horizontal rotation radian while <code>upDown</code> means vertical rotation radian. 
When the parameter of <code>firstPersonMode</code> is null, the current value of <code>Graph3dView#isFirstPersonMode()</code> will be adopted by default. This parameter will influence reference standards of rotating movement. When it is not set in the first person mode, rotation takes <code>center</code> as the center of rotation, that is, rotating around a central object, When the first person rotates, the center of the <code>eye</code> rotates, that is, the eye turns toward the direction.</p></li><li><p><code>walk(step, anim, firstPersonMode)</code>: This function changes the position of <code>eye</code> and <code>center</code> at the same time, that is, <code>eye</code> and<code> center</code> move the same offset at the same time in the vector direction established at two points.
<code>step</code> is the vector length value for the offset. When the number of <code>firstPersonMode</code> is null, the current value of <code>Graph3dView#isFirstPersonMode()</code> will be adopted by default, if the <code>walk</code> operation is called for the first-person mode, the function will consider limiting the boundary of <code>Graph3dView#getBoundaries() </code>.</p></li><li><p><code>reset()</code>:  It is the reset function, calling the function to set the <code>eye</code>, <code>center</code> and <code>up</code> three variables to correspond the original default value of <code>graph3dViewCenter</code>, <code>graph3dViewEye</code> and <code>graph3dViewUp</code>. </p></li></ul>

<div id="ref_switch"></div>

<h4>Interactive switch</h4>

<p>By default, the following parameters are in open status; you can use the mouse or keyboard to perform the following interoperability, according to the requirements of the switch settings:</p>

<ul><li><code>isRotatable()|setRotatable(true)</code> Controls whether to rotate. </li><li><code>isZoomable()|setZoomable(true)</code> Controls whether to zoom. </li><li><code>isPannable()|setPannable(true)</code> Whether the control can be translated</li><li><code>isWalkable()|setWalkable(true)</code> Whether the control can advance or retreat</li><li><code>isResettable()|setResettable(true)</code> Controls whether the space key can be reset</li><li><code>isRectSelectable()|setRectSelectable(true)</code> Whether the control can be selected</li></ul>

<div id="ref_keyboard"></div>

<h4>Keyboard operation</h4>

<p><code>Graph3dView</code> Preset a lot of keyboard operation</p>

<ul><li><code>w</code>: Go ahead, Press the <code>shift</code> key at the same time to move up</li><li><code>s</code>: Backward, Press the <code>shift</code> key at the same time to Backward </li><li><code>a</code>: Left shift</li><li><code>d</code>: Right shift</li><li>Left arrow key: The first person mode rotates left</li><li>Right Arrow: The first person mode rotates right</li><li>Up arrow keys: The first person mode rotates up</li><li>Down arrow: The first person mode rotates down</li><li><code>ctrl</code> or <code>command</code> key: Frame selection</li><li><code>space</code> space key: Call <code>reset()</code> reset</li><li><code>shift</code> key: When the <code>shift</code> keyboard is pressed, the default operation becomes the translation effect of <code>pan</code></li></ul>

<p><code>Graph3dView</code> by default, the moving data move along the <code>xz</code> plane and change the moving mode when the following keys are pressed:</p>

<ul><li>Hold down the <code>shift</code> key, or press <code>x</code>, <code>y</code> and <code>z</code> at the same time, move the <code>xyz</code> 3D space</li><li>When you press the <code>x</code> key only, you move along the x-axis.</li><li>When you press the <code>y</code> key only, you move along the y-axis.</li><li>When you press the <code>z</code> key only, you move along the z-axis.</li></ul>

<p>If you want to change the default implementation in move mode by keyboard, you can apply the function of <code>getMoveMode(event, data)</code>. The default implementation of the function is as following:
If the <code>style</code> attribute <code>3d.move.mode</code> specified in the final element specifies the value, the keyboard state is no longer taken into account and the setting value is used:</p>

<pre><code>getMoveMode: function (event, data){
    var movemode = data.s(&#39;3d.move.mode&#39;);
    if(movemode){
        return movemode;
    }
    var map = ht.Default.getCurrentKeyCodeMap(),
        x = &#39;88&#39;,
        y = &#39;89&#39;,
        z = &#39;90&#39;;
    if(event.shiftKey || (map[x] &amp;&amp; map[y] &amp;&amp; map[z])) return &#39;xyz&#39;;
    if(map[x] &amp;&amp; map[y]) return &#39;xy&#39;;
    if(map[x] &amp;&amp; map[z]) return &#39;xz&#39;;
    if(map[y] &amp;&amp; map[z]) return &#39;yz&#39;;
    if(map[x]) return &#39;x&#39;;
    if(map[y]) return &#39;y&#39;;
    if(map[z]) return &#39;z&#39;;
    return &#39;xz&#39;;
},</code></pre>

<p>See it in <a href="# ref_host">Adsorption chapter example</a>, and set the code of the move mode to move along the <code>xyz</code> in 3D space:</p>

<pre><code>g3d.getMoveMode = function (event){
    return &#39;xyz&#39;;
};</code></pre>

<div id="ref_interactionlistener"></div>

<div id="ref_edit"></div>

<h4>Data Edit</h4>

<p>By default, the data can be dragged along the <code>xz</code> plane in a 3D scene, or <a href="#ref_keyboard">Combine keyboard</a> achieves any movements to any directions in space, but after all this method is not direct and easy to use.
At the same time the data have rotation angle of three axes, the size of the three axis size and other parameters need to control, that is, need to modify the editing function of <code>p3</code>, <code>s3</code> and <code>r3</code> parameters.
For this reason, <code>HT</code> provides an intuitive solution that, when `Graph3dView#setEditable (true) is in edit state, the last selected data will present the following:
The center of data extends control bar of three axes, and the control bar of each axis has been divided into three parts.  </p>

<ul><li>The first part: It is the first part that is closer to the center, and it is used to change the position of the axis direction.</li><li>The second part: It is marked in yellow marked used to change the size of the axis direction of the data; press the <code>shift</code> button, and you will change the size of the three axes by same amount and at same time.</li><li>The third part: It is the farthest part from the center point, and it is used to rotate along the axis; when you double-click the part, the rotation angle will be reset <code>0</code>.</li></ul>

<p>See <a href="#ref_filter">filter section</a> to know whether the data are allowed to move, rotate and scale.</p>

<p><img src="data:image/png;base64,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"></p>

<h4>Interactive monitoring</h4>

<p>You can monitor the process of interaction by <code>Graph3dView#addinteractorListener</code>: </p>

<pre><code>g3d.addinteractorListener(function (e){
    console.log(e.kind);
});</code></pre>

<p>The parameters type of callback events <code>e.kind</code> are as following:</p>

<ul><li><code>beginRotate</code>: It means starting rotation.</li><li><code>betweenRotate</code>: It means between rotation.</li><li><code>endRotate</code>: It means ending rotation.</li><li><code>beginWalk</code>: It means starting walk.</li><li><code>betweenWalk</code>: It means between walk.</li><li><code>endWalk</code>: It means ending walk.</li><li><code>beginZoom</code>: It means starting Zoom.</li><li><code>betweenZoom</code>: It means between Zoom.</li><li><code>endZoom</code>: It means ending Zoom.</li><li><code>beginPan</code>: It means starting pan.</li><li><code>betweenPan</code>: It means between translation.</li><li><code>endPan</code>: It means ending translation.</li><li><code>beginPinch</code>: It means starting double finger pinch.</li><li><code>betweenPinch</code>: It means between double finger pinch.</li><li><code>endPinch</code>: It means ending double finger pinch.</li><li><code>toggleNote</code>: It means double clicking on <code>note</code>.</li><li><code>toggleNote2</code>: It means double clicking on <code>note2</code>.</li><li><code>clickData</code>: It means clicking data.</li><li><code>clickBackground</code>: It means clicking background.</li><li><code>doubleClickData</code>: It means double clicking data.</li><li><code>doubleClickBackground</code>: It means double clicking background.</li><li><code>beginEditRotation</code>: It means the edition of data’s rotation angle begins.</li><li><code>betweenEditRotation</code>: It means the edition of data’s rotation angle is on. </li><li><code>endEditRotation</code>: It means the edition of data’s rotation angle ends.  </li><li><code>beginEditSize</code>: It means starting to edit the size of data.</li><li><code>betweenEditSize</code>: It means the edition of data’s size is on.</li><li><code>endEditSize</code>: It means the edition of data’s size ends.</li><li><code>beginMove</code>: It means starting to move data.</li><li><code>betweenMove</code>: It means the process of moving data is on.</li><li><code>endMove</code>: It means the process of moving data ends.</li><li><code>beginRectSelect</code>: It means starting to select data in frame.</li><li><code>betweenRectSelect</code>: It means the process of selecting data in frame is on.</li><li><code>endRectSelect</code>: It means the process of selecting data in frame ends.</li></ul>

<p>When you click or double-click on the data, in addition to the return event such as <code>e.kind</code>, the <code>e.part</code> parameter also provides information about which part of the data should be clicked.</p>

<ul><li><code>edit_tx</code>: If it is in the condition of editing, it means the position of [<code>x</code>] is changing.</li><li><code>body</code>: central part of picture</li><li><code>label</code>: text label of data</li><li><code>label2</code>: second text label of data</li><li><code>note</code>: bubble note of data</li><li><code>label2</code>: second bubble note of data</li><li><code>key</code> on <code>icons</code>: It represents a group of icons.</li><li><code>edit_tx</code>: In the edit state, mark the changing part of the <code>x</code> axis position</li><li><code>edit_ty</code>: In the edit state, mark the changing part of the <code>y</code> axis position</li><li><code>edit_tz</code>: In the edit state, mark the changing part of the <code>z</code> axis position</li><li><code>edit_rx</code>: In the edit state, mark the changing part of the rotation of the <code>x</code> axis</li><li><code>edit_ry</code>: In the edit state, mark the changing part of the rotation of the <code>y</code> axis</li><li><code>edit_rz</code>: In the edit state, mark the changing part of rotation of the <code>z</code> axis</li><li><code>edit_sx</code>: In the edit state, mark the size changes in the <code>x</code> axis </li><li><code>edit_sy</code>: In the edit state, mark the size changes in the <code>y</code> axis </li><li><code>edit_sz</code>: In the edit state, mark the size changes in the <code>z</code> axis</li></ul>

<p><iframe src="examples/example_interaction.html" style="height:400px"></iframe></p>

<div id="ref_collision"></div>

<h4>Collision Detection</h4>

<p><code>HT</code> not only provide <a href="#ref_firstpersonmode">first person roaming interactive mode</a>, but also supports the detection the collision with any blocks like wall in the roaming process. This collision detection can limit the allowed scope of first person roaming. Roaming generally operates along the <code>xz</code> plane, so <code>HT</code> provides the multi-lines to define the <code>xz</code> plane in order to describe the roaming boundary that can not go beyond.
The boundary of collision can be specified by <code>Graph3dView#setBoundaries(boundaries)</code>, the format of <code>boundaries</code> are following:</p>

<pre><code>g3d.setBoundaries([
    [
        p0.x, p0.y,
        p1.x, p1.y,
        p2.x, p2.y,
        p3.x, p3.y
    ],
    [
        p4.x, p4.y,
        p5.x, p5.y,
        p6.x, p6.y
    ]
]);</code></pre>

<p>The above code sets two polylines <code>p0-p1-p2-p3</code> and <code>p4-p5-p6</code>, array describe all endpoints in each polyline, and the first and second element means the <code>x, z</code> coordinates of start point. The following are the <code>x, z</code> coordinates of the second, third and other endpoints; you can understand that the <code>MoveTo</code> goes to the first endpoint and then <code>LineTo</code> the shape of other endpoints.
The following sample code apply the function of <code>ht.Default.toBoundaries(data.getPoints(), data.getSegments())</code>. This function can convert discontinuous curves into differential straight lines segments. The code uses <code>GraphView#addTopPainter</code>, the position orientation information of <code>eye</code> and <code>center</code> in <code>3D</code> are real-time rendered in <code>2D</code> so as to intuitively understand the position and orientation of the current first person.</p>

<p><iframe src="examples/example_collision.html" style="height:500px"></iframe></p>

<div id="ref_selection"></div>

<h3>Selected State</h3>

<p>The data in <code>Graph3dView</code> selected are displayed in a darker status, and the dark coefficient is determined by the attributes of <code>brightness</code>, <code>select.brightness</code> and the <code>style</code>. The default attribute value of <code>select.brightness</code> default value is <code>0.7</code>, the final return value is more than <code>1</code> with brighter result, less than <code>1</code> with darker result, equal to <code>1</code> without any changes or null result.
<code>Graph3dView#getBrightness</code> function controls the final brightness of data, so you can also override the brightness of data that is selected by this function in user-defined manners, the following is the default logic: </p>

<pre><code>getBrightness: function (data){
    var brightness = data.s(&#39;brightness&#39;),
        selectBrightness = this.isSelected(data) ? data.s(&#39;select.brightness&#39;) : null;

    if(brightness == null){
        return selectBrightness;
    }
    if(selectBrightness == null){
        return brightness;
    }
    return brightness * selectBrightness;
},</code></pre>

<p><code>Graph3dView#getWireframe</code> function is used to define the result of 3D wire frame for data; the default implementation code is as follows.
Through the code, we can know that the result of displaying the selected wire frame by controlling the related <code>wf.*</code>（<code>wf</code> is the abbreviation of <code>wireframe</code>）.</p>

<pre><code>getWireframe: function (data){
    var visible = data.s(&#39;wf.visible&#39;);
    if(visible === true || (visible === &#39;selected&#39; &amp;&amp; this.isSelected(data))){
        return {
            color: data.s(&#39;wf.color&#39;),
            width: data.s(&#39;wf.width&#39;),
            short: data.s(&#39;wf.short&#39;),
            mat: data.s(&#39;wf.mat&#39;)
        };
    }
},</code></pre>

<ul><li><code>wf.visible</code>: The default is <code>false</code> which represents not displaying; it can be set in <code>selected</code> value, which means displaying when selected; the <code>true</code> value represents displaying the wire frame all the time.</li><li><code>wf.color</code>: It means the color of wireframe.</li><li><code>wf.short</code>: The default value is <code>false</code> which represents displaying the closed 3D wire frame, and when set the <code>true</code> value, an unclosed short wire frame will be displayed.</li><li><code>wf.width</code>: It means the width of the wireframe, and the default value is <code>1</code>. Some systems just show the effect of <code>1</code>, the biggest value that different systems can display has restrictions. </li><li><code>wf.mat</code>: The default value is null, The transformation matrix can be constructed by <code>ht.Default.createMatrix</code>. Refer to examples in <a href="../../plug-in/form/examples/example_unboxing.html">Unboxing instance</a></li></ul>

<div id="ref_filter"></div>

<h3>Filter</h3>

<div id="ref_selectable"></div>

<h4>Select the filter</h4>

<p>By default, all data can be selected and users cancel the function of being selected of some data by setting the selection of filters, 
The final control that decides whether to be selected is in the <code>filterFunc</code> filter on <code>SelectionModel</code> model by reloading the <code>isSelectable</code> function of the <code>GraphView</code>, or calling the packaged function control of <code>GraphView.setSelectableFunc(func)</code>. The sample code is as follows:</p>

<pre><code>graph3dView.setSelectableFunc(function (data){
    return data.a(&#39;selectable&#39;);
});</code></pre>

<div id="ref_visible"></div>

<h4>Visible filter</h4>

<p>In default situation, data are visible, and users can set the visible filters to hide part of data, the sample code is as follows:</p>

<pre><code>graph3dView.setVisibleFunc(function (data){
    return data.s(&#39;all.transparent&#39;) === true;
});</code></pre>

<p>The example code logic is to only display the data which <code>all.transparent</code> is <code>true</code>. The <code>Graph3dView#isVisible</code> function ultimately determines whether the metafile is visible, so you can also override the function:</p>

<pre><code>graph3dView.isVisible = function (data){
    return data.s(&#39;all.transparent&#39;) === true;
};</code></pre>

<div id="ref_movable"></div>

<h4>Movable filter</h4>

<p>By default, all data can move, users can fix part of data by setting movable filter, and the sample code is as follows: </p>

<pre><code>graph3dView.setMovableFunc(function (data){
    return movableItem.selected;
});</code></pre>

<p>The sample code logic is when the <code>selected</code> of <code>movableItem&#39;s</code> <code>selected</code> is <code>true</code>, the data can move.
<code>Graph3dView#isMovable</code> function ultimately determines whether the data can moved, so you can also override the function:</p>

<pre><code>graph3dView.isMovable = function (data){
    return movableItem.selected;
};</code></pre>

<div id="ref_rotatable"></div>

<h4>Rotatable filter</h4>

<p>When <code>Graph3dView#setEditable(true)</code> is set to be editable, the selected data can rotate by default. The following code can be used to prohibit part of data to rotate:</p>

<pre><code>graph3dView.setRotationEditableFunc(function (data){
    return data instanceof ht.Shape;
});</code></pre>

<p>The logic of the above code is that only data of the <code>ht.Shape</code> type are allowed to rotate. <code>Graph3dView#isRotationEditable</code> function ultimately determines whether the data can be rotated, so you can also override the function: </p>

<pre><code>graph3dView.isRotationEditable: function (data){
    return data instanceof ht.Shape;
},</code></pre>

<div id="ref_scalable"></div>

<h4>Scalable filter</h4>

<p>When <code>Graph3dView#setEditable(true)</code> is set to be editable, the selected data can rotate by default. The following code can be used to prohibit part of data to rotate:</p>

<pre><code>graph3dView.setSizeEditableFunc(function (data){
    return data instanceof ht.Shape;
});</code></pre>

<p>The logic of the above code is that only data of the <code>ht.Shape</code> type are allowed to change the size. <code>Graph3dView#isSizeEditable</code> function ultimately determines whether the entity can be resized, so you can also override the function:</p>

<pre><code>graph3dView.isSizeEditable: function (data){
    return data instanceof ht.Shape;
},</code></pre>

<p>In addition to setting the filter on the view components, the internal filtering mechanism for <code>GraphView</code> and <code>Graph3dView</code> also refers to the following <code>style</code> attributes, users can directly change the following <code>style</code> to achieve the result of controlling single data:</p>

<ul><li><code>2d.visible</code>: The default value is <code>true</code>, which controls whether the data are visible on <code>GraphView</code>.</li><li><code>2d.selectable</code>: The default value is <code>true</code>, which controls whether the data can be selected on <code>GraphView</code>.</li><li><code>2d.movable</code>: The default value is <code>true</code>, which controls whether the data can move on <code>GraphView</code>.</li><li><code>2d.editable</code>: The default value is <code>true</code>, which controls whether the data can be edited on <code>GraphView</code></li><li><code>2d.move.mode</code>: The default value is null, which controls the movement scope for data, it can be set as the following parameters:<ul><li><code>xy</code>: It can move on the <code>xy</code> plane.</li><li><code>x</code>: It can rotate only along the <code>x</code> axis.</li><li><code>y</code>: It can rotate only along the <code>y</code> axis.</li><li>Any other non-null values represent immovable.</li></ul></li><li><code>3d.visible</code>: The default value is <code>true</code>, which controls whether the data are visible on <code>Graph3dView</code>.</li><li><code>3d.selectable</code>: The default value is <code>true</code>, which controls whether the data can be selected on on <code>Graph3dView</code>.</li><li><code>3d.movable</code>: The default value is <code>true</code>, which controls whether the data can move on <code>Graph3dView</code>.</li><li><code>3d.editable</code>: The default value is <code>true</code>, which controls whether the data can be edited on <code>Graph3dView</code>.</li><li><code>3d.move.mode</code>: The default value is null, and you can refer to <a href="# ref_keyboard">Keyboard Operation</a>. In order to control movement scope of data, it can be set as the following parameters:<ul><li><code>xyz</code>: It means movements only in 3D space.</li><li><code>xy</code>: It means movements only on <code>xy</code> plane.</li></ul></li><li><code>xz</code>: It means movements only on <code>xz</code> plane.    </li><li><code>yz</code>: It means movements only on <code>yz</code> plane.<ul><li><code>x</code>: It means movements only along <code>x</code> axis.</li><li><code>y</code>: It means movements only along <code>y</code> axis.</li></ul></li><li><code>z</code>: It means movements only along <code>z</code> axis.<ul><li>Any other non-null values represent immovable.</li></ul></li></ul>

<div id="ref_export"></div>

<h3>Debug information</h3>

<ul><li><code>showDebugTip</code>: It shows the Draw Calls, the vertices, the number of faces and lines of current scene.</li><li><code>hideDebugTip</code>: It means turn off the displaying of debug information.</li></ul>

<h3>Export</h3>

<ul><li><code>toCanvas(background)</code>: It means exporting the contents of the current view into a <code>Canvas</code> component, and <code>background</code> as the background color</li><li><code>toDataURL(background)</code> : It means exporting the contents of the current view of the <a href="http://en.wikipedia.org/wiki/Data_URI_scheme">base64</a> as string image content, and <code>background</code> as the background color.</li></ul>

<p>Export some snippets of examples:</p>

<pre><code>{
    label: &#39;Export Image&#39;,
    action: function (){
        var w = window.open();
        w.document.open();
        w.document.write(&quot;&lt;img src=&#39;&quot; + g3d.toDataURL(g3d.getView().style.background) + &quot;&#39;/&gt;&quot;);
        w.document.close();
    }
}</code></pre>    <hr id="contact" style="margin: 20px 0 1px 0">
    <a href="http://www.hightopo.com"><img class="logo"></a>Welcome to contact us <a href="mailto:<EMAIL>"><EMAIL></a>
</body>
</html>
