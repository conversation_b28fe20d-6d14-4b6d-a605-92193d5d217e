<!doctype html>
<html>
    <head>
        <title>HT for Web DataModel Manual</title>
        <meta charset="UTF-8">    
        <meta name="viewport" content="user-scalable=yes, width=1024">
        <style type="text/css"> 
            h1, h2, h3, h4, h5, h6, p, blockquote {
                margin: 0;
                padding: 0;
            }
            body {
                font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif;
                font-size: 13px;
                line-height: 18px;
                color: #737373;
                background-color: white; 
                margin: 10px 13px 10px 13px;
            }
            table {
                margin: 10px 0 15px 0;
                border-collapse: collapse;
            }
            td,th {	
                border: 1px solid #ddd;
                padding: 3px 10px;
            }
            th {
                padding: 5px 10px;	
            }
            a {
                color: #0069d6;
            }
            a:hover {
                color: #0050a3;
                text-decoration: none;
            }
            a img {
                border: none;
            }
            p {
                margin-bottom: 9px;
            }
            h1, h2, h3, h4, h5, h6 {
                color: #404040;
                line-height: 36px;
            }
            h1 {
                margin-bottom: 18px;
                font-size: 30px;
            }
            h2 {
                font-size: 24px;
            }
            h3 {
                font-size: 18px;
            }
            h4 {
                font-size: 16px;
            }
            h5 {
                font-size: 14px;
            }
            h6 {
                font-size: 13px;
            }
            hr {
                margin: 0 0 19px;
                border: 0;
                border-bottom: 1px solid #ccc;
            }
            blockquote {
                padding: 13px 13px 21px 15px;
                margin-bottom: 18px;
                font-family:georgia,serif;
                font-style: italic;
            }
            blockquote:before {
                content:"\201C";
                font-size:40px;
                margin-left:-10px;
                font-family:georgia,serif;
                color:#eee;
            }
            blockquote p {
                font-size: 14px;
                font-weight: 300;
                line-height: 18px;
                margin-bottom: 0;
                font-style: italic;
            }
            code, pre {
                font-family: Monaco, Andale Mono, Courier New, monospace;
            }
            code {
                background-color: #fee9cc;
                color: rgba(0, 0, 0, 0.75);
                padding: 1px 3px;
                font-size: 12px;
                -webkit-border-radius: 3px;
                -moz-border-radius: 3px;
                border-radius: 3px;
            }
            pre {
                display: block;
                padding: 14px;
                margin: 0 0 18px;
                line-height: 16px;
                font-size: 11px;
                border: 1px solid #d9d9d9;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            pre code {
                background-color: #fff;
                color:#737373;
                font-size: 11px;
                padding: 0;
            }
            sup {
                font-size: 0.83em;
                vertical-align: super;
                line-height: 0;
            }
            * {
                -webkit-print-color-adjust: exact;
            }
            @media screen and (min-width: 914px) {
                body {
                    width: 854px;
                    margin:10px auto;
                }
            }
            @media print {
                body,code,pre code,h1,h2,h3,h4,h5,h6 {
                    color: black;
                }
                table, pre {
                    page-break-inside: avoid;
                }
            }                        
            iframe{
                width: 100%;
                border: 1px solid #34495E;
                margin: 0;                
            }            
            .logo{
                vertical-align: middle;
            }

        </style>        
        <script>
            function init() {
                var logoSrc = 'data:image/png;base64,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',
                    logos = document.querySelectorAll('.logo'),
                    i = 0;
                for (; i < logos.length; i++) {
                    logos[i].src = logoSrc;
                }
                
                var iframes = document.querySelectorAll('iframe'),
                    func = function (){};
                for (i=0; i < iframes.length; i++) {
                    var iframe = iframes[i];
                    
                    // a small hack to make it work on android
                    iframe.ontouchstart = func;
                    
                    var div = document.createElement('div');
                    div.innerHTML = '<a href="' + iframe.src + '" target="_blank">Run→</a>';
                    iframe.parentNode.insertBefore(div, iframe);
                }   
            }
        </script>    
    </head>
    <body onload="init();">
        <a href="http://www.hightopo.com"><img class="logo"></a>HT for Web Data Model Manual
        <hr style="margin: 1px 0 20px 0">
<p>Index</p>

<ul><li><a href="#ref_designpattern">Design Patterns</a><ul><li><a href="#ref_mvc">MVC</a></li><li><a href="#ref_mvp">MVP</a></li><li><a href="#ref_mvvm">MVVM</a></li><li><a href="#ref_ovm">OVM</a></li></ul></li><li><a href="#ref_data">Data Type</a></li><li><a href="#ref_datamodel">Data Container</a></li><li><a href="#ref_selectionmodel">Selection Model</a></li></ul>

<hr/>

<div id="ref_designpattern"></div>

<h2>Design Patterns</h2>

<p>Proper use of design patterns can greatly improve the maintainability and scalability of the framework, a good design interface can greatly reduce the amount of user code and improve readability, in favor of the team work of large-scale project development, even do not need to access backend data to complete the unit testing of the front-end component, facilitates data binding and data synchronization for components and models, this will help the developer to further provide WYSIWYG visual development tools, such as <a href="http://www.adobe.com/products/flash-builder.html">Adobe Flash Builder</a> and <a href="http://en.wikipedia.org/wiki/VisualStudio">Microsoft Visual Studio</a>.</p>

<p><img src="data:image/png;base64,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"></p>

<div id="ref_mvc"></div>

<h3>MVC</h3>

<p>In the early <code>GUI</code> design field, <a href="http://en.wikipedia.org/wiki/Swing_(Java)">Java/Swing</a>, <a href="http://en.wikipedia.org/wiki/JFace">SWT/JFace</a>, 
<a href="http://developer.apple.com/library/ios/#documentation/general/conceptual/devpedia-cocoacore/MVC.html">Cocoa</a> and <a href="http://en.wikipedia.org/wiki/Qt_(framework)">Qt</a> are model of the design pattern based on <a href="http://en.wikipedia.org/wiki/Model%E2%80%93view%E2%80%93controller">Model View Controller(MVC)</a>.</p>

<div id="ref_mvp"></div>

<h3>MVP</h3>

<p>After <code>MVC</code> slowly develop derived <a href="http://en.wikipedia.org/wiki/Model%E2%80%93view%E2%80%93presenter">Model View Presenter(MVP)</a> design patterns are gradually being adopted by the new <code>GUI</code> framework, such as based on <code>Flash</code> <a href="http://en.wikipedia.org/wiki/Apache_Flex">Apache/Flex</a> enterprise application component, and the <a href="http://www.jgoodies.com/">JGoodies</a> framework encapsulated on the <code>Swing</code> basis. <code>Martin Fowler</code> <a href="http://martinfowler.com/eaaDev/uiArchs.html">GUI Architectures</a> analyzes and compares the two design patterns. </p>

<div id="ref_mvvm"></div>

<h3>MVVM</h3>

<p>In recent years, on the basis of <code>MVP</code> to add the <code>ViewModel</code> layer derive from the <a href="http://en.wikipedia.org/wiki/MVVM">Model View ViewModel(MVVM)</a> design pattern, but also by Microsoft new generation of <code>GUI</code> development framework <a href="http://en.wikipedia.org/wiki/Windows_Presentation_Foundation">Silverlight/WPF</a> is adopted, including a framework for simplifying <code>HTML</code> and <code>JS</code> data bindings, such as <a href="http://knockoutjs.com/">Knockout</a>, also based on the <code>MVVM</code> design pattern.</p>

<div id="ref_ovm"></div>

<h3>OVM</h3>

<p><code>MVC/MVP/MVVM</code> and other design patterns to facilitate the understanding and classification of different frameworks, any <code>GUI</code> framework in the specific implementation of their own evolution and characteristics, <code>HT</code> overall framework is more like the <code>MVP</code> and <code>MVVM</code> design patterns, but we prefer to call the <code>HT</code> design model <code>Object View Mapping(OVM)</code>, analogous to <a href="http://en.wikipedia.org/wiki/Object-relational_mapping">Object Relational Mapping(ORM)</a>, which masks the heterogeneity of various view component by facing encapsulation of object mode, employs a unified <a href="#ref_datamodel">DataModel</a> data model and <a href="#ref_selectionmodel">SelectionModel</a> selection model that can drive <code>ListView</code>, <code>TreeView</code>, <code>TableView</code>, <code>TreeTableView</code>, <code>GraphView</code> and <code>Graph3dView</code> all <code>HT</code> view components.</p>

<blockquote><p>The <code>HT</code> Design architecture, users only need to master a unified data-driven interface, not due to the increasement of view component to bring additional learning costs, which is <code>HT</code> easy to master the basic.</p></blockquote>

<div id="ref_data"></div>

<h2>数据类型</h2>

<p>The <code>Data</code> type runs through the <code>HT</code> framework and is the most basic data type.</p>

<ul><li><code>getId()</code> and <code>setId(id)</code> Gets and sets the unique number, assigned by the system automatically, the settings need attention <a href="#ref_datamodel">DataModel Description</a>, use <code>DataModel#getDataById(id)</code> to find.</li><li><code>getTag()</code> and <code>setTag(tag)</code> Gets and sets the tag, use <code>DataModel#getDataByTag(tag)</code> to find.</li><li><code>getName()</code> and <code>setName(name)</code> Gets and sets the name of the data object.</li><li><code>getIcon()</code> and <code>setIcon(icon)</code> Gets and sets the icons, as node icons on components such as <code>TreeView</code> and <code>ListView</code>.</li><li><code>getDisplayName()</code> and <code>setDisplayName(displayName)</code> Gets and sets the display name, as <code>Column</code> and <code>Property</code> header and attribute names display.</li><li><code>getToolTip()</code> and <code>setToolTip(tooltip)</code> Gets and sets the text tip information for the node or data on the components.</li><li><code>getParent()</code> and <code>setParent(parent)</code> Gets and sets the father node, as the information of the tree hierarchical structure, automatically call <code>addChild</code> or <code>removeChild</code> inside.</li><li><code>addChild(child, index)</code> Appends child node, <code>index</code> append the index for children,  if it&#39;s null then appended as the last child, automatically call <code>setParent</code> inside.</li><li><code>removeChild(child)</code> Deletes the specific child node, automatically call <code>setParent</code> inside.</li><li><code>clearChildren()</code> Deletes all of child node, automatically call <code>setParent</code> inside.</li><li><code>onChildAdded(child, index)</code> The callback function while append children, it can be overloaded for subsequent processing.</li><li><code>onChildRemove(child, index)</code> The callback function while delete children, it can be overloaded for subsequent processing.</li><li><code>onParentChanged(oldParent, parent)</code> The callback function while changing the father node, it can be overloaded for subsequent processing.</li><li><code>size()</code> Returns the total number of children.</li><li><code>hasChildren()</code> Judges if there are children, if yes, return <code>true</code>, otherwise return <code>false</code>. </li><li><code>isEmpty()</code> Judges if there are children, if yes, return <code>false</code>, otherwise return <code>true</code>.</li><li><code>getChildren()</code> Gets all of the children node, this function returns the internal <code>ht.List</code> type array object reference. </li><li><code>toChildren(matchFunc, scope)</code> According to <code>matchFunc</code> function logic to build new <code>ht.List</code> type array object of all matching datas. </li><li><code>eachChild(func, scope)</code> Traverses all children, and can specify the function <code>scope</code> field.</li><li><code>getChildAt(index)</code> Returns specified index location child.</li><li><code>isParentOf(data)</code> Judges this data whether or not the father of the specified <code>data</code>.</li><li><code>isDescendantOf(data)</code> Judges this data whether or not the child of the specified <code>data</code>.</li><li><code>isRelatedTo(data)</code> Judges this data and the specified <code>data</code> data whether have parent-child relationship or descendent relationship.</li><li><code>layer</code> Attribute is operate by <code>getLayer()</code> and <code>setLayer(layer)</code>, corresponding to the position of data in the <code>GraphView</code> component, with the default value null. </li><li><code>isAdjustChildrenToTop()</code> and <code>setAdjustChildrenToTop(true)</code> The default value is <code>false</code>, <code>ht.Node</code> type with the default value <code>true</code>, <code>GraphView</code> click data will call <code>sendToTop</code> by default, this attribute decides whether operating <code>sendToTop</code> to children. </li><li><code>firePropertyChange(property, oldValue, newValue)</code> Distributes attribute change events and can use the shorthand method of <code>fp</code>.</li><li><code>onPropertyChanged(event)</code> Attributes changed callback function and can be overridden subsequent processing.</li><li><code>invalidate()</code> In this function users trigger on attribute change events to alarm update interface by force, internally implemented as <code>this.fp(&#39;*&#39;,  false,  true)</code> .</li><li><code>getStyleMap()</code> Returns data internal style mapping information, while use <code>getStyle(name)</code> if <code>styleMap</code> value is null, automatically return <code>ht.Style</code> defined information.</li><li><code>getStyle(&#39;name&#39;)</code> and <code>setStyle(&#39;name&#39;,  value)</code> Gets and sets the entities style and can use the shorthand method of <code>s(name/name, value/json)</code>.</li><li><code>onStyleChanged(name, oldValue, newValue)</code> While the attribute of <code>style</code> changing, it will callback this function and can be overridden subsequent processing. </li><li><code>getAttrObject()</code> and <code>setAttrObject(obj)</code> Gets and sets the <code>attr</code> attribute object, it&#39;s default as null and used on storage users&#39; business information.  </li><li><code>getAttr(name)</code> and <code>setAttr(name, value)</code> Gets and sets the <code>attr</code> attribute and can use the shorthand method of <code>a(name/name, value/json)</code>.</li><li><code>onAttrChanged(name,  oldValue,  newValue)</code> It will called on changing <code>attr</code> and can be overridden subsequent processing.</li><li><code>toLabel()</code> Returns value is default as the data text label of the components such as <code>TreeView</code> and <code>GraphView</code>, default return <code>displayName||name</code> information. </li><li><code>addStyleIcon(name, icons)</code> and <code>removeStyleIcon(name)</code> Adds and deletes <code>style</code> and <code>icons</code> attribute, you can reference <a href="#ref_styleicon">Icon Chapter</a>. </li><li><code>getSerializableProperties()</code> Returns attribute name <code>map</code> which is need to be serialized, you can referece <a href="../serialization/ht-serialization-guide.html">Serialization Tutorial</a>.</li><li><code>getSerializableStyles()</code> Returns <code>style</code> attributes name <code>map</code> which is need to be serialized, you can reference <a href="../serialization/ht-serialization-guide.html">Serialization Tutorial</a>.</li><li><code>getSerializableAttrs()</code> Returns <code>attr</code> attribute name <code>map</code> which is need to be serialized, you can reference <a href="../serialization/ht-serialization-guide.html">Serialization Tutorial</a>.</li></ul>

<p><iframe src="examples/example_data.html" style="height:160px"></iframe></p>

<pre><code>ht.Default.setImage(&#39;edit&#39;,  &#39;res/edit.png&#39;);
ht.Default.setImage(&#39;mail&#39;,  &#39;res/mail.png&#39;);
ht.Default.setImage(&#39;readmail&#39;,  &#39;res/readmail.png&#39;);
ht.Default.setImage(&#39;search&#39;,  &#39;res/search.png&#39;);
ht.Default.setImage(&#39;settings&#39;,  &#39;res/settings.png&#39;);            

function init(){                                
    dataModel = new ht.DataModel();                    
    treeView = new ht.widget.TreeView(dataModel);
    view = treeView.getView();            

    view.className = &#39;main&#39;;
    document.body.appendChild(view);    
    window.addEventListener(&#39;resize&#39;,  function (e) {
        treeView.invalidate();
    },  false);                         

    var inbox = addData(&#39;Inbox&#39;,  &#39;mail&#39;);
    addData(&#39;Read Mail&#39;,  &#39;readmail&#39;,  inbox);                             
    addData(&#39;Drafts&#39;,  &#39;edit&#39;);
    var search = addData(&#39;Search Folders&#39;,  &#39;search&#39;);
    addData(&#39;Categorized Mail&#39;,  &#39;search&#39;,  search);                             
    addData(&#39;Large mail&#39;,  &#39;search&#39;,  search);                             
    addData(&#39;UnRead Mail&#39;,  &#39;search&#39;,  search);                             
    addData(&#39;Settings&#39;,  &#39;settings&#39;);

    treeView.expandAll();
    treeView.getSelectionModel().setSelection(search);
}

function addData(name,  icon,  parent){
    var data = new ht.Data();
    data.setName(name);
    data.setIcon(icon);
    data.setParent(parent); // or parent.addChild(data);
    dataModel.add(data);                
    return data;
}</code></pre>

<div id="ref_datamodel"></div>

<h2>Data Container</h2>

<p>Data container <code>ht.DataModel</code>(following abbrivation <code>DataModel</code>) as the model of load <code>Data</code> type, managing the additions and deletions of <code>Data</code> and the distribution of change events, all components of <code>HT</code> framework to display in the users interface by binding <code>DataModel</code>, as the same time, components would also listening the changing events of <code>DataModel</code>, real-time synchronization between data and user interface, mastering the <code>DataModel</code> operation has mastered the model-driven approach of all the components.   </p>

<p>The <code>Data</code> type object is automatically assigned an <code>id</code> attribute inside the construct and can use <code>data.getId()</code> and <code>data.setId(id)</code> gets and sets the, the <code>data</code> object is not allowed to modify the <code>id</code> value after it is added to <code>DataModel</code> and can be quickly found <code>Data</code> objects through <code>data.getDataById(id)</code>.</p>

<p>Generally recommended <code>id</code> attribute is automatically assigned by <code>HT</code>, the only sign of user&#39;s business meaning can exist in <code>tag</code> attribute, through <code>Data#setTag(tag)</code> function allows any dynamic change <code>tag</code> value, through <code>DataModel#getDataByTag(tag)</code> can find the corresponding <code>data</code> object and support through <code>DataModel#removeDataByTag(tag)</code> delete <code>Data</code> object.</p>

<p><code>id</code> and <code>tag</code> are all target the only assigned <code>Data</code> object, if search for the not uniqueness attribute can used <a href="../../plug-in/quickfinder/ht-quickfinder-guide.html">ht.QuickFinder plug-in</a>.</p>

<p>The use of <code>DataModel</code> requires more attention: <code>Data</code>, which is generally require a parent-child relationship, should be added to the container. Often encountered <code>parent</code> in the container, but <code>children</code> didn&#39;t, resulting in components can&#39;t see the problems of <code>children</code>, adding <code>parent</code> wouldn&#39;t automatically load all descendants, this must be noted.</p>

<p>The <code>Data</code> type has <code>getDataModel()</code> function, <code>data.getDataModel()</code> can obtain the current container information while <code>Data</code> attended in the container, a <code>Data</code> object to be added to multiple <code>DataModel</code> containers at the same time is not allowed.</p>

<ul><li><p><code>add(data, index)</code> Appends <code>Data</code> objects, <code>index</code> generally need not be specified, it works only when the <code>data&#39;s</code> <code>parent</code> is null, specifying the index position of inserting <code>roots</code> array. </p></li><li><p><code>remove(data)</code> Deletes <code>Data</code> object, this operate has the following side-effect:</p><ul><li>Children and grandchildren be delete from <code>DataModel</code></li><li>The relationship be disconnected <code>data.setParent(null)</code></li><li><code>Edge</code> type disconnected data relationship through <code>edge.setSource(null)</code> and <code>data.setTarget(null)</code></li><li>The <code>Node</code> type will remove its associated connection from <code>DataModel</code> </li><li>The <code>Node</code> type will disconnected from the host by <code>data.setHost(null)</code></li></ul></li><li><code>clear()</code> Deletes all the <code>Data</code> objects in the container, and the operation is emptied one at a time without the <code>remove</code> process and does not affect the <code>data</code> parent-child relationship.</li><li><code>onAdded(data)</code> Data adds a callback function that can be overloaded for subsequent processing.</li><li><code>onRemoved(data)</code> Data deletes a callback function that can be overloaded for subsequent processing.</li><li><code>contains(data)</code> Determines whether the container contains the <code>data</code> object.</li><li><code>size()</code> Returns the total number of <code>data</code> objects in the current container.</li><li><code>isEmpty()</code> Determines whether the container is null.</li><li><code>getRoots()</code> Returns all <code>Data</code> objects that their <code>parent</code> are null.</li><li><code>getDataById(id)</code> Returns the <code>Data</code> object for the specified <code>id</code>.</li><li><code>removeDataById(id)</code> Deletes the <code>Data</code> object for the specified <code>id</code>.</li><li><code>getDataByTag(tag)</code> Returns the <code>Data</code> object for the specified <code>tag</code>.</li><li><code>removeDataByTag(tag)</code> Deletes the <code>Data</code> object for the specified <code>tag</code>.</li><li><code>each(func, scope)</code> Traverses all the <code>Data</code> objects.</li><li><code>eachByDepthFirst(func,  data,  scope)</code> Takes <code>data</code> as the starting depth to traverse <code>Data</code> objects.</li><li><code>eachByBreadthFirst(func,  data,  scope)</code> Takes <code>data</code> as the starting breadth to traverse <code>Data</code> objects.</li><li><code>getDatas()</code> Returns all added to the container&#39;s <code>Data</code> <code>ht.List</code> array.</li><li><code>toDatas(matchFunc,  scope)</code> Returns the filtered new <code>ht.List</code> object array, the first parameter is null equivalent to copying all object arrays.</li><li><code>getSelectionModel()</code> Gets the selection model of the container and referred to briefly as <code>sm()</code>.</li><li><code>addDataModelChangeListener(function (e){},  scope)</code> Increase the data model add or delete event listener and referred to briefly as <code>mm(func, scope)</code>.<ul><li><code>e.kind === &#39;add&#39;</code> Represents the added <code>Data</code> object, <code>e.data</code> is appended object.</li><li><code>e.kind === &#39;remove&#39;</code> Represents the delete <code>Data</code> object, <code>e.data</code> is deleted object.</li><li><code>e.kind === &#39;clear&#39;</code> Means the container is cleared.</li></ul></li><li><code>removeDataModelChangeListener(func,  scope)</code> Deletes data model add or delete changed events listener and referred to briefly as <code>umm(func, scope)</code>.</li><li><code>addDataPropertyChangeListener(function (e){},  scope)</code> Adds the event listener for data attribute changes in the model and referred to briefly as <code>md(func, scope)</code>.<ul><li><code>e.data</code> Represents an object with a property change.</li><li><code>e.property</code> Represents the name of the change property.</li><li><code>e.newValue</code> Represents the new value of the property.</li><li><code>e.oldValue</code> Represents the old value of the property.</li><li><code>Data</code> Object calls the <code>firePropertyChange(property,  oldValue,  newValue)</code> trigger property change event within the set property value function: <ul><li><code>get/set</code> Type property, such as <code>setAge(98)</code> trigger event <code>e.property</code> for <code>age</code>.</li><li><code>style</code> Type property name preceded by <code>s:</code> prefix to differentiate, such as <code>setStyle(&#39;age&#39;, 98)</code> trigger event <code>e.property</code> for <code>s:age</code>.</li><li><code>attr</code> Type property name preceded by <code>a:</code> prefix to differentiate, such as <code>setAttr(&#39;age&#39;, 98)</code> trigger event <code>e.property</code> for <code>a:age</code>. </li></ul></li></ul></li><li><code>removeDataPropertyChangeListener(func,  scope)</code> Deletes <code>Data</code> property change event listener in the model and can be abbreviated to <code>umd(func, scope)</code>.</li><li><code>onDataPropertyChanged(data,  e)</code> It will be called on changing data, can be overloaded for subsequent processing.</li><li><code>getSiblings(data)</code> Gets the sibling array with <code>data</code>, if data parent is null, then return <code>dataModel.getRoots()</code>.</li><li><code>moveTo(data,  newIndex)</code> Should specified index if remove data to the same sibling array.</li><li><code>moveUp(data)</code> Removes data to the same sibling&#39;s previous position.</li><li><code>moveDown(data)</code> Removes data to the same sibling&#39;s next position.</li><li><code>moveToTop(data)</code> Removes data to the top of the same sibling.</li><li><code>moveToBottom(data)</code> Removes data to the bottom of the same sibling.</li><li><code>moveSelectionUp(sm)</code> Removes the current selection data to the previous position of the same sibling array, if <code>sm</code> is null, use <code>DataModel&#39;s</code> binding selection model.</li><li><code>moveSelectionDown(sm)</code> Removes the current selection data to the same sibling array&#39;s next position, if <code>sm</code> is null, use <code>DataModel&#39;s</code> binding selection model.</li><li><code>moveSelectionToTop(sm)</code> Removes the current selection data to the top of the same sibling, if <code>sm</code> is null, use <code>DataModel&#39;s</code> binding selection model.</li><li><code>moveSelectionToBottom(sm)</code> Removes the current selection data to the bottom of the same sibling, if <code>sm</code> is null, use <code>DataModel&#39;s</code> binding selection model.</li><li><code>serialize(space)</code> Serializes the data model into a <code>JSON</code> format string, <code>space</code> to intent space.</li><li><code>toJSON</code> Serializes the data model into a <code>JSON</code> format object.</li><li><code>deserialize(json,  rootParent,  setId)</code> Deserializes data to data model<ul><li><code>json</code> Data object that resolves to generate the corresponding <code>Data</code> object and added into data container.</li><li><code>rootParent</code> Parent node object, if not null, the deserialized object if don&#39;t have father, set <code>rootParent</code> to be his father.</li><li><code>setId</code> Specifies whether to set the <code>id</code> value on <code>JSON</code> information when deserializing.</li></ul></li></ul>

<blockquote><p>Through the following <code>firePrppertyChange</code> code snippet can be known, when <code>oldValue</code> equals to <code>newValue</code>, property change event won&#39;t be distributed, property change events through <code>handleDataPropertyChange</code> send to <code>DataModel</code> continue to do processing, the subsequent processing include continuing to distribute the event to the property change listener which through <code>addDataPropertyChangeListener</code> add to <code>DataModel</code>.</p></blockquote>

<pre><code>firePropertyChange: function (property,  oldValue,  newValue) {
    if (oldValue === newValue) {
        return false;
    }
    var e = {
        property: property, 
        oldValue: oldValue, 
        newValue: newValue, 
        data: this
    };
    if (this._dataModel) {
        this._dataModel.handleDataPropertyChange(e);
    }
    this.onPropertyChanged(e);
    return true;
} </code></pre>

<div id="ref_selectionmodel"></div>

<h2>Selection Model</h2>

<p><code>ht.SelectionModel</code> manages the <code>Data</code> object in the <code>DataModel</code> model, each <code>DataModel</code> object has a <code>SelectionModel</code>, if you control this <code>SelectionModel</code> then you can control all binding components&#39; object selection state in this <code>DataModel</code>, that means share the same <code>DataModel</code> components default has a selected linkage feature. </p>

<p>If you want some components to be selected without linkage with other components, you can invoke <code>view.setSelectionModelShared(false)</code> so that the <code>view</code> will create a exclusive <code>SelectionModel</code> instance.</p>

<p>In summary there are two ways to get <code>SelectionModel</code>:</p>

<ul><li><code>dataModel.getSelectionModel()</code> Gets selected model for the component share in the data container.</li><li><code>view.getSelectionModel()</code> Gets selected model for the use of the current component, if <code>selectionModelShared</code> is <code>false</code>, return a <code>view</code> specific selection model.</li></ul>

<p>The common functions of <code>SelectionModel</code> are as follows:</p>

<ul><li><code>getSelectionMode()</code> and <code>setSelectionMode(selectionMode)</code> Gets and sets the selection model<ul><li><code>none</code>: Can not be selected.</li><li><code>single</code>: Optional only.</li><li><code>multiple</code>: Default value, allow multiple selections.</li></ul></li><li><code>getFilterFunc()</code> and <code>setFilterFunc(func)</code> Sets filters to customize selectable object rules, refers to <a href="../beginners/ht-beginners-guide.html#ref_filter">The Filter Chapter</a>.</li><li><code>appendSelection(datas)</code> Appends selected object, parameter can be a single object, also can be <code>ht.List</code> or <code>Array</code> array and referred to briefly as <code>as</code>.</li><li><code>setSelection(datas)</code> Sets selected object, parameter can be a single object, also can be <code>ht.List</code> or <code>Array</code> array and referred to briefly as <code>ss</code>.</li><li><code>removeSelection(datas)</code> Cancels selected object, parameter can be a single object, also can be <code>ht.List</code> or <code>Array</code> array and referred to briefly as <code>rs</code>.</li><li><code>clearSelection()</code> Cancels all the selected object, referred to briefly as <code>cs</code>.</li><li><code>selectAll()</code> Selected all object of <code>DataModel</code>, referred to briefly as <code>sa</code>.</li><li><code>size()</code> Returns the number of selected object.</li><li><code>isEmpty()</code> Determines if no object is currently selected.</li><li><code>contains(data)</code> Determines if <code>data</code> object is selected, referred to briefly as <code>co</code>.</li><li><code>getFirstData()</code> Returns the first selected object,  return null if there is no selected object, referred to briefly as <code>fd</code>.</li><li><code>getLastData()</code> Returns the last selected object, return null if there is no selected object, referred to briefly as <code>ld</code>.</li><li><code>each(function (data){},  scope)</code> Traverses all the selected object.</li><li><code>getSelection()</code> Gets all the selected object array, you can&#39;t add or delete to the return array!</li><li><code>toSelection(matchFunc,  scope)</code> Returns filtered selected object, if <code>matchFunc</code> is null, it represents copy all selected object to new array.</li><li><code>addSelectionChangeListener(function (e){},  scope)</code> Adds listener, listen selected change event, referred to briefly as <code>ms</code>:<ul><li><code>e.datas</code> Includes all objects which selection state are changed, if the object is selected currently then not being selected before, otherwise not selected currently but being selected before.</li><li><code>e.kind === &#39;set&#39;</code> Represents this event caused by <code>setSelection(datas)</code>.</li><li><code>e.kind === &#39;remove&#39;</code> Represents this event caused by <code>removeSelection(datas)</code>.</li><li><code>e.kind === &#39;append&#39;</code> Represents this event caused by <code>appendSelection(datas)</code>.</li><li><code>e.kind === &#39;clear&#39;</code> Represents this event caused by <code>clearSelection(datas)</code>.</li></ul></li><li><code>removeSelectionChangeListener(function (e){},  scope)</code> Deletes selected change event listener and referred to briefly as <code>ums</code>:</li></ul>

<p><iframe src="examples/example_datamodel.html" style="height:130px"></iframe></p>

<pre><code>index = 0;
dataModel = new ht.DataModel();  
selectionModel = dataModel.getSelectionModel();                                           

// monitor data property change event
dataModel.addDataPropertyChangeListener(function (e){                    
    document.getElementById(&#39;property&#39;).innerText = e.data + &#39;\&#39;s &#39; + e.property + &#39; changed&#39;;
});

// monitor data model change event
dataModel.addDataModelChangeListener(function (e){
    var output;
    if(e.kind === &#39;add&#39;){
        output = e.data + &#39; added,  &#39;;
    }
    else if(e.kind === &#39;remove&#39;){
        output = e.data + &#39; removed,  &#39;;
    }
    else if(e.kind === &#39;clear&#39;){
        output = &#39;data model cleared,  &#39;;
    }
    output += &#39;size:&#39; + dataModel.size();
    document.getElementById(&#39;model&#39;).innerText = output;
});

// monitor selection model change event
selectionModel.addSelectionChangeListener(function (e){
    var output = &#39;&#39;;
        size = selectionModel.size();                    
    if(size === 0){
        output = &#39;nothing selected&#39;;
    }
    else if(size === 1){
        output = selectionModel.getLastData() + &#39; selected&#39;;
    }
    else{
        output = size + &#39; datas selected&#39;;
    }
    document.getElementById(&#39;selection&#39;).innerText = output;
});

graphPane.getGraphView().setEditable(true);

addData();
addData();
selectionModel.setSelection(addData());

function addData(){
    var node = new ht.Node();                             
    node.setPosition(50 + index % 12 * 50,  50);
    node.setName(&#39;node&#39; + index++);    
    dataModel.add(node);                                               
    return node;
}
function removeData(){
    while(selectionModel.size() &gt; 0){
        dataModel.remove(selectionModel.getLastData());
    }
} </code></pre>    <hr id="contact" style="margin: 20px 0 1px 0">
    <a href="http://www.hightopo.com"><img class="logo"></a>Welcome to contact us <a href="mailto:<EMAIL>"><EMAIL></a>
</body>
</html>
