!function(N){"use strict";var I=ht.AlarmSeverity=function(N,Z,Y,f,i){this.value=N,this.name=Z,this.nickName=Y,this.color=f,this.displayName=i},Z=(ht.Default.def("ht.AlarmSeverity",N,{toString:function(){return this.displayName||this.name}}),I.severities=new ht.List,I._vm={},I._nm={},I._cp=function(N,Z){var Y;return N&&Z?0<(Y=N.value-Z.value)?1:Y<0?-1:0:N&&!Z?1:!N&&Z?-1:0},I.each=function(N,Z){I.severities.each(N,Z)},I.getSortFunction=function(){return I._cp},I.setSortFunction=function(N){I._cp=N,I.severities.sort(N)},I.add=function(N,Z,Y,f,i){Y=new I(N,Z,Y,f,i);return I._vm[N]=Y,I._nm[Z]=Y,I.severities.add(Y),I.severities.sort(I._cp),Y},I.remove=function(N){var Z=I._nm[N];return Z&&(delete I._nm[N],delete I._vm[Z.value],I.severities.remove(Z)),Z},I.CRITICAL=I.add(500,"Critical","C","#FF0000"),I.MAJOR=I.add(400,"Major","M","#FFA000"),I.MINOR=I.add(300,"Minor","m","#FFFF00"),I.WARNING=I.add(200,"Warning","W","#00FFFF"),I.INDETERMINATE=I.add(100,"Indeterminate","N","#C800FF"),I.CLEARED=I.add(0,"Cleared","R","#00FF00"),I.isClearedAlarmSeverity=function(N){return!!N&&0===N.value},I.getByName=function(N){return I._nm[N]},I.getByValue=function(N){return I._vm[N]},I.clear=function(){I.severities.empty(),I._vm={},I._nm={}},I.compare=function(N,Z){return I._cp(N,Z)},ht.AlarmState=function(N){this._d=N,this._nm={},this._am={},this._ps=null,this._haa=null,this._hna=null,this._hoa=null,this._hta=null,this._hls=!1,this._aac=0,this._nac=0}),Y=(ht.Default.def("ht.AlarmState",N,{_ep:!0,_f:function(){this._c1(),this._c2(),this._c3(),this._c4(),this._c5(),this._c6(),this._c7(),this._d.fp("alarmState",null,this)},getHighestAcknowledgedAlarmSeverity:function(){return this._haa},getHighestNewAlarmSeverity:function(){return this._hna},getHighestOverallAlarmSeverity:function(){return this._hoa},getHighestNativeAlarmSeverity:function(){return this._hta},hasLessSevereNewAlarms:function(){return this._hls},_c1:function(){var N,Z=null;for(N in this._am)N=I.getByName(N),I.isClearedAlarmSeverity(N)||0!==this.getAcknowledgedAlarmCount(N)&&(Z=Z&&0<I.compare(Z,N)?Z:N);this._haa=Z},_c2:function(){var N,Z=null;for(N in this._nm)N=I.getByName(N),I.isClearedAlarmSeverity(N)||0!==this.getNewAlarmCount(N)&&(Z=Z&&0<I.compare(Z,N)?Z:N);this._hna=Z},_c3:function(){if(this._hna){for(var N in this._nm)if(N=I.getByName(N),!I.isClearedAlarmSeverity(N)&&0!==this.getNewAlarmCount(N)&&0<I.compare(this._hna,N))return void(this._hls=!0);this._hls=!1}else this._hls=!1},_c4:function(){var N=this._haa,Z=this._hna,Y=this._ps;this._hoa=N,0<I.compare(Z,this._hoa)&&(this._hoa=Z),0<I.compare(Y,this._hoa)&&(this._hoa=Y)},_c5:function(){var N=this._haa,Z=this._hna;this._hta=N,0<I.compare(Z,this._hta)&&(this._hta=Z)},increaseAcknowledgedAlarm:function(N,Z){var Y;0!==Z&&(Y=this._am[N.name]||0,this._am[N.name]=Y+=Z=Z||1,this._f())},increaseNewAlarm:function(N,Z){var Y;0!==Z&&(Y=this._nm[N.name]||0,this._nm[N.name]=Y+=Z=Z||1,this._f())},decreaseAcknowledgedAlarm:function(N,Z){if(0!==Z){var Y=this._am[N.name]||0;if((Y-=Z=Z||1)<0)throw"Alarm count can not be negative";this._am[N.name]=Y,this._f()}},decreaseNewAlarm:function(N,Z){if(0!==Z){var Y=this._nm[N.name]||0;if((Y-=Z=Z||1)<0)throw"Alarm count can not be negative";this._nm[N.name]=Y,this._f()}},acknowledgeAlarm:function(N){this.decreaseNewAlarm(N,1),this.increaseAcknowledgedAlarm(N,1)},acknowledgeAllAlarms:function(N){if(N){var Z=this.getNewAlarmCount(N);this.decreaseNewAlarm(N,Z),this.increaseAcknowledgedAlarm(N,Z)}else for(var Y in this._nm)this.acknowledgeAllAlarms(I.getByName(Y))},_c6:function(){for(var N in this._aac=0,this._am)N=I.getByName(N),this._aac+=this.getAcknowledgedAlarmCount(N)},getAcknowledgedAlarmCount:function(N){return N?this._am[N.name]||0:this._aac},getAlarmCount:function(N){return this.getAcknowledgedAlarmCount(N)+this.getNewAlarmCount(N)},_c7:function(){for(var N in this._nac=0,this._nm)this._nac+=this.getNewAlarmCount(I.getByName(N))},getNewAlarmCount:function(N){return N?this._nm[N.name]||0:this._nac},setNewAlarmCount:function(N,Z){this._nm[N.name]=Z,this._f()},removeAllNewAlarms:function(N){N?delete this._nm[N]:this._nm={},this._f()},setAcknowledgedAlarmCount:function(N,Z){this._am[N.name]=Z,this._f()},removeAllAcknowledgedAlarms:function(N){N?delete this._am[N.name]:this._am={},this._f()},isEmpty:function(){return!this._hoa},clear:function(){this._am={},this._nm={},this._f()},getPropagateSeverity:function(){return this._ps},setPropagateSeverity:function(N){var Z;this._ep||(N=null),this._ps!==N&&(Z=this._ps,this._ps=N,this._f(),this._d.fp("propagateSeverity",Z,N))},isEnablePropagation:function(){return this._ep},setEnablePropagation:function(N){var Z=this._ep;this._ep=N,this._d.fp("enablePropagation",Z,N)&&!N&&this.setPropagateSeverity(null)}}),ht.AlarmStatePropagator=function(N){this._dataModel=N,this._enable=!1,this._isPropagating=!1}),N=(ht.Default.def("ht.AlarmStatePropagator",N,{getDataModel:function(){return this._dataModel},isEnable:function(){return this._enable},setEnable:function(N){this._enable!==N&&(this._enable=N,this._enable?(this._dataModel.mm(this.handleDataModelChange,this),this._dataModel.md(this.handleDataPropertyChange,this),this._dataModel.each(function(N){this.propagate(N)},this)):(this._dataModel.umm(this.handleDataModelChange,this),this._dataModel.umd(this.handleDataPropertyChange,this)))},handleDataModelChange:function(N){N.data&&this.propagate(N.data)},handleDataPropertyChange:function(N){var Z;"alarmState"===N.property||"enablePropagation"===N.property?this.propagate(N.data):"parent"===N.property&&((Z=N.oldValue)&&this.propagate(Z),this.propagate(N.data))},propagate:function(N){N&&!this._isPropagating&&(this._isPropagating=!0,this.propagateToTop(N),this._isPropagating=!1)},propagateToTop:function(N){for(this.propagateToParent(null,N);N&&N.getParent();)this.propagateToParent(N,N.getParent()),N=N.getParent()},propagateToParent:function(N,Z){var Y=null;Z.getChildren().each(function(N){N=N.getAlarmState().getHighestOverallAlarmSeverity();0<I.compare(N,Y)&&(Y=N)}),Z.getAlarmState().setPropagateSeverity(Y)}}),ht.AlarmStateStatistics=function(N){this.sumNew=0,this.sumAcked=0,this.sumTotal=0,this.severtiyMap={},this.dataMap={},this.setDataModel(N)},ht.Default.def("ht.AlarmStateStatistics",N,{ms_fire:1,getDataModel:function(){return this._dataModel},setDataModel:function(N){var Z=this._dataModel;Z!==N&&(Z&&(Z.umd(this.handleDataPropertyChange,this),Z.umm(this.handleDataModelChange,this),this.severtiyMap={},this.dataMap={}),this._dataModel=N,this.reset(),N.md(this.handleDataPropertyChange,this),N.mm(this.handleDataModelChange,this),this.fp("dataModel",Z,N))},dispose:function(){this._dataModel.umd(this.handleDataPropertyChange,this),this._dataModel.umm(this.handleDataModelChange,this),delete this._dataModel},handleDataPropertyChange:function(N){"alarmState"===N.property&&(this.increase(N.data),this.fireAlarmStateChange())},handleDataModelChange:function(N){"add"===N.kind?(this.increase(N.data),this.fireAlarmStateChange()):"remove"===N.kind?(this.decrease(N.data),this.fireAlarmStateChange()):"clear"===N.kind&&(this.severtiyMap={},this.dataMap={},this.fireAlarmStateChange())},fireAlarmStateChange:function(){this.sumAcked=0,this.sumNew=0,this.sumTotal=0,I.each(function(N){N=this.getSumInfo(N);this.sumAcked+=N.ackedCount,this.sumNew+=N.newCount,this.sumTotal+=N.totalCount},this),this.fp("alarmState",!1,!0)},getNewAlarmCount:function(N){return N?this.getSumInfo(N).newCount:this.sumNew},getAcknowledgedAlarmCount:function(N){return N?this.getSumInfo(N).ackedCount:this.sumAcked},getTotalAlarmCount:function(N){return N?this.getSumInfo(N).totalCount:this.sumTotal},getSumInfo:function(N){var Z=this.severtiyMap[N.name];return Z||(this.severtiyMap[N.name]=Z={newCount:0,ackedCount:0,totalCount:0}),Z},decrease:function(N){var Y=this.dataMap[N.getId()];Y&&(delete this.dataMap[N.getId()],I.each(function(N){var Z=Y[N.name],N=this.getSumInfo(N);N.newCount=N.newCount-Z.newCount,N.ackedCount=N.ackedCount-Z.ackedCount,N.totalCount=N.totalCount-Z.totalCount},this))},increase:function(N){var Y,f;this.decrease(N),this._filterFunc&&!this._filterFunc(N)||(Y={},f=N.getAlarmState(),this.dataMap[N.getId()]=Y,I.each(function(N){var Z={},N=(Z.newCount=f.getNewAlarmCount(N),Z.ackedCount=f.getAcknowledgedAlarmCount(N),Z.totalCount=f.getAlarmCount(N),Y[N.name]=Z,this.getSumInfo(N));N.newCount=N.newCount+Z.newCount,N.ackedCount=N.ackedCount+Z.ackedCount,N.totalCount=N.totalCount+Z.totalCount},this))},reset:function(){this.severtiyMap={},this.dataMap={},this._dataModel.each(this.increase,this),this.fireAlarmStateChange()},setFilterFunc:function(N){var Z=this._filterFunc;this._filterFunc=N,this.reset(),this.fp("filterFunc",Z,N)},getFilterFunc:function(){return this._filterFunc}}),ht.Data.prototype),N=(N.getAlarmState=function(){return this._alarmState||(this._alarmState=new Z(this))},(N=ht.DataModel.prototype).isEnableAlarmStatePropagator=function(){return!!this._alarmStatePropagator&&this._alarmStatePropagator.isEnable()},N.setEnableAlarmStatePropagator=function(N){N!=this.isEnableAlarmStatePropagator()&&(N?(this._alarmStatePropagator=new Y(this)).setEnable(!0):this._alarmStatePropagator.setEnable(!1))},(N=ht.graph.GraphView.prototype).getNote2=function(N){var Z=N.getAlarmState().getHighestNewAlarmSeverity();return Z?(Z=N.getAlarmState().getNewAlarmCount(Z)+Z.nickName,N.getAlarmState().hasLessSevereNewAlarms()&&(Z+="+"),Z):N.s("note2")},N.getNote2Background=function(N){var Z=N.getAlarmState().getHighestNewAlarmSeverity();return Z?Z.color:N.s("note2.background")},N.getBodyColor=function(N){var Z=N.getAlarmState().getHighestNativeAlarmSeverity();return Z?Z.color:N.s("body.color")},N.getBorderColor=function(N){var Z=N.getAlarmState().getPropagateSeverity();return Z?Z.color:N.s("border.color")},(N=ht.widget.TreeView.prototype).getBorderColor=function(N){var Z=N.getAlarmState().getPropagateSeverity();return Z?Z.color:N.s("border.color")},N.getIcon=function(N){return N.getIcon()?"__alarmIcon__":null},(N=ht.widget.TreeTableView.prototype).getBorderColor=function(N){var Z=N.getAlarmState().getPropagateSeverity();return Z?Z.color:N.s("border.color")},N.getIcon=function(N){return N.getIcon()?"__alarmIcon__":null},ht.Default.setImage("__alarmIcon__",{width:16,height:16,comps:[{type:"image",name:{func:function(N){return N.getIcon()}},color:{func:function(N){var Z=N.getAlarmState().getHighestNativeAlarmSeverity();return Z?Z.color:N.s("body.color")}},rect:[0,0,16,16]}]}),ht.Style);N["note2.expanded"]=!0,N["note2.color"]="#000"}(("undefined"==typeof global&&"undefined"==typeof self&&"undefined"==typeof window&&(0,eval)("this"),Object));