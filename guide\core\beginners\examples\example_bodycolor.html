<!DOCTYPE html>
<html>
    <head>
        <title>Body Color</title>
        <meta charset="UTF-8">       
        <script src="../../../../lib/core/ht.js"></script>   
        <script>                
            
            ht.Default.setImage('ball', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABGdBTUEAANkE3LLaAgAAIABJREFUeJzsvXmYXFW18P3b+5xT89jV1XN3ZkhCAggCBiQgSAABAf0Q5IqIAl5AeRXvpyIIVxRwABwQciUqKs6IgEwar1wGTQiBJMxDBjJ0p+euqq65zrC/P05VdXe6kzSTw/vd9TzdVXXOPsPea1577bXhf+F/4X/hf+F/4f+nIP7RL/B3ht31V/1d3+KfCP5vIgCxu8+vfvWrM3Svd5b70wY0wMayxy4uWdZr1375y9sYIwbFRML4v5JI/pUJQIz/+9o3vnGgz+c7UEo5Q+r6UpRC07SlhqahhEAphccwcIQEBVKAUgJLOaiKicJGOQ6mWaFimo8JpTKWbT1TNivPjGYLG7799a9vZYwodiWOf1n4VyKA2rtKQHztpm8cGPKGTpWGvlQTYqmUGkI30KWBFBLD6wUlkIYANDQBQkj3D4VCIDRQDkgUSgmEclA6KFNRqhQxbQuzWMQyy+RyoxnTch6z7MpjZrn4yHVfuW4DLhE41ff6lySIf3YCmID0m77//dMMXX+/1OT7dd2ISt3A0LxIXUOTEqRAIhGajhSApqEBUmoIIRBSgBBI4WJNExrKUSBACcBWKIl7DFDKwVYKZSsQinI2S7GcZzSXpZzLbrPMyn2ZbP5nN3796+v5FyWGf1YCqIl2ufwnd5y2ectrR/m93nOkR482xGLEIlE06UFIB01qICRSN5AKhCbRNelerOmAQGoSIcZwogkdhEIIgYNCKlcdoCuEUjhK4VgCJRS24yAAx7HBUZiOg2MrbKdCfnSU4cE+rEp5m2lWfp7O5n9SVRUO/yJq4p+NAAQut8trbrjhXEfx0YDXd6Th8eKgkUqN4gjYZ+YMAn4fQgh0wwAEmiYRAjRNQwiQUkcJ0KUEAUJVCQGJElUK0yRCqXFYcr8rpVCOQiBQysFRCsuxQYGjqgRgWlRMkzVPPkFXezuBgIfMyDCFYv6+UrH8vWu/8pVHGJMK/7SE8M9CAHXEf++25R/L5kpXlk2ny9A9SE2nXLGZ0dFEsWQyOJxi3sx2wpEoUkqkFAhNRwiBJmX9EyWQehX5gJQSUf2hCfdxriHo4sbRqoMxjiBsU6FwULZECBvHgYptohxBxSyxfv06WlpbaGluw7YslIJ0ZpDU0CDZ0fTjplm5+dqrr7mbf2JC+EcTQB3x37/tto8F/P4rzdSqLn/zcRQrJjv7B4mHg5QrJpFImHAoyGs7ulk4bw4BfwBN110uRqBLWeV8DYRASFcNIISrEhQIWccytm0zOpplNDtKNpslX8hTsUyU7VQlgIPfHwAhiEZihEMBYrE4mq5TMSs888wzNDclaWpqwbItcMB0bBzbwbFs8vlRBvq6yWbSjxfyha9+8/rr/4d/QtXwjyKAOuJ/9osfnFaxfZ/+y8o/H/mrn/8cAay696N0yw/T0z+IbdmEQ0Gk0NA9OsMjGfbfb586R2uahpAu4jUhUUKgSeFyuwYS6Rp+CEBRMU16+3oZzebw+/14DAMUBII+yqWKa/mXK1jKxjJNLMumXC6Rz+fJ5fJICRXTZv/F+9PS0orjOCgHbNPEwcGyFSgby7ZxFGTTI/T3bCWXH318qHfw/FtuuWUL/0SE8PcmgLpxd9/vv3eQ8iRWlMpi/w998OwJja66sIWDPvBzBodGqNg2QkFjIk7/wDCJxgbampuQUo7peyFBgiYkQroIFyiE0N3zSGxs+gcGqJQrhMNBBIJSpUwhn6dUrlCuFLFMG6/hxfDqOI7CMi0yoxmU4+D1eiiWy2zfto1gMIhyFM0tbXTNnEFDPAHKwbJslKOwrQoKhWk5OI6rOkYG+ujt3UwxX7r22q989RrciNQ/XC38PQmgxvXaPX/40dXFSuBLc2Y20xoboHPuWRMavnsRfOk7j7Ktt49IKMzg8AgzO9vZ1rOTgxYvrLp1rl+vVe0AKTWQVY6XoCFct1BISuUygyMj+H0eJJJcLkc2l0PTJbFIlEAwgK57cGwblMK0LXLZLFIKCoUCxVKZ3v4+htMZGsJh0ASOaWNbNunRFOFgmP32P4CmRALTVjiOjW07OMrGNi3Xc3AcrEqFHa+9Smqo/9lsOnvBt7/97XX8g6XB34MAxrj+vu8dZMvECuX49j/isANJJmfT/dpv6Zp95oTeH7kYvnzLal7auImmxkaUoxjNZeloayORiCOriHd1fpXrlXAJQQPQQCikkFQqJXL5AoZuUCqXyGRHiUWiRCNRLKtCvlhClxLLsslkR8nlsuTyOYL+EFITeAwP23fsoGKa+NU2TNFIrgQej5diqYht2vj9fgYH+umcMZMD9z8Ar9ePXUW6bTtYjoWyFJZtY9s26VQ/OzZvpJDPfeH6a6//Dv9AafB2E0Bd19//4IrzsgXfNzvaktElhx6JpgWwrQH6djxGx+wzJlx0/Wc6mHfc7eRyOSq2TTwapVCqsGDurLqol1JDSIEQAikEsh4Ici196f6jXLEwKxXypRKaFMRjMYrFIo5yCPgC5PI5+vr7MTwGiViMcDhKUE8jS+tJ7XySxx55nJV/fhaAl1+DeV1QMiEShfauWeyzYDE7R1so04TP48VxFPvtt5h99t0X27KwHQfbcVybwLFwLIWtHCrlClteeZaRocH7n9v47Pn3/fq+Yf4B0uDtJIAa8vX77//xjbmS95KDD5jD3LmH1RsMDzzKddfezE3fu2vChZuf+BRP9J6I1+tFapJUKsN++8zFMAzXl6+6e3XE1767Ch+BSwwCKJcrpEbTJBsasSzLRbzfT3o0w/DQMJFohKZEEr+5hnT3/3D/H+7lDw+9xl+fn35HIwa8b1mArn0PIy8WEQg2kEg0csSRR6FLiW06mMqda7AtB8cxMW0Xz9s2v8rO7Vu2pzIjH7r1u7euw5UGfzcieLsIQADaiu9cmoi0H3CjlMa/Hb30PTQ2dtQb2NYAfd1/pXPWByf09NiD4DPX3Ep3Kk57awuOErQ2JtC9BlJodaSP53hZdfuox/vHSYCyic/noVgsYxgGtm3RO9BPLBylOZwmvfWX/PT2H/HjX40yWHjzHT/2MJh/0FI8DUsByUnvO4lwJIpt2di2RcVyUFgoG/eYshjs62XLq89nhoeHzrzlu7c8jCsJ/i4qQXsb7ikB7fvfvLgx3vWOP+t64L2nnXQSoXDThEZ9Ox7mO9/9JaueeHHC8d8uX8awfgodLS30DQ7iWDbz580hEgri2Mo19KrWvi4kUquqAinRpURqonpeIIWGYbhBIsPwMJJJYZs289p92Nuu5rOXXMqnr36Cv60vUzDfms6/1gNrn9rG3MbnCEQ72bhlJ5FonIZ4DDSBIbSqmlIIXGL1BQKEowlfPpf6yDsOPLBnzRNrnnlr3mbv8FYTQB35DTMPWhkNBxefdOL70Y3whEbpoSfZtn0L513w9QnHzzoWZr77GiwRolSskGxs4ODFi+jp6yc1kqK9tYVwMEDFshESRFUSaFXE15AupKi6hC4xSE2QSqXoaGkm6dzNFy8+kfO++Bwvb3uLez8Onn25RL5/HS0tfnYOlIknGkjGGqj6rfUgFUqAlHg8HpLJDrLZ1MmLFi3seXLNk38XIngrCaCG/ES866CVyURy8fHLTkTKwIRGhfxGsunNzF/8kUkX33H759hZ3A8UKGUzo6sD23FoSiRoTCTY3ttLsViio6UJn9eLaVqIcYgfj3Rd1/D7veiaRiYzikcrk3rxCmYfdD1Pv/IW9noPMJCCF9Zt5sADQvQMWTTE4jQkYgghQEhELXZR9WZ0XaOxqYVsOn3yfgsW7Fy7du2zvM1q4K2yASSg3fS1CxsbZ7/zT02NycXHH3fapEZWqZuB3rW868hPsKMnNeHcn28/nMH4l/EYHkqVMgv2mYvX0KEq0g1dJxwIgIBt3d20NiVpbmxkJJOlbFlIITB0Hb/Pi89jYFoWg8MjZHJ5vLrJwLpPcfSHHt5jJxYvXsyyZcuYM2cOhxxySP345s2bWbt2LStXruS55557QwP0qUtOwt94CEe+eyldnTOxbQvLtrEcB2WZVEzbjRWYFuVSnnVP/Y3e3p6LbrvlttsBi7fJJngrCKDO+ZH2xSvj0dDik0/6ADCR861SN/29T/Lhc67i8b+9MOHcf17UwEEn30LRTmDbFrNndREKBKvTuDUjT0NKgUfXCYb8ZHM5Uqk0i/aZh60UXl0jXyozMJxitFDEYxiEQyE8skTm2U+yz3v+MOnFA4EAZ511FmeddRbxeHzaHV65ciVXXHHF6xqkd8yF937oYpSnmfcsfQ8dHR1YlhtMslT1s2JiOTaWZVMs5li/5m/09+286Lblbx8RvFkCcNNtwLjtR9/7YSAYPvvfzvwQUyK/70m+fPVt3P6zP00495Hj4YLP/4D+wizypRIL5s0hFAhMiXxZTeoQQuL3e/DoOpte20Y4HCZXLBH0+wkF/RiGxxWzQOa5z7HPu38w4ZlHHXUUV1xxRR3pmlD4AzZew8YwnAltTVNSNjWKBQ1bTRyu10sIpx9ncOBxX6FsCo5/7zJi8biLeNvBsSxMy8S0HSzbwjErDKeHWb/qr2zfuu3dv/3tb9fyNgSM3gwB1JF/+x3fu1EI30UfPuMDeDyJCY1c5K/hyqtW8JM7JiJ/Tivc+btv8UL/QkDR1dFBIhadgHypudk8UnODPkLIOiFEQkEGB0cIBoOu/y+q11VdQDl0O/P3+1TdvQsEAtxzzz3E4/E60qMhE8Nws0M9vjgeI4LQDACUbVIxR6mUXHVVLOpkcgal8pjplEqlOO200ygUpudDfuFzx9A45wxs0+SYY4/D6/Xi2DaW6WArB6tSpmIpbMvCNMukhodY+8RjmS2vbjr8D3/4wybeYiKQb/C6epDnBz/4+ummqV10wnvfMxn5xR309T4xJfLntsB9d13Osz37YtsmHW2tNMbHIV9KpO5a9FMhXwiJJgTFUgmhyfpsoJsfIJGFZzj/3DHkn3766Tz66KPE43FiUZP2tgKN8RLReAsNyQNpbDma3/5uLRdedDWx+Dxi8XnEGxfyobO+wD33byLesIhoPExLskhjvIRWzTCKx+M8+uijdHR0MB148pGHKWd70AyDDRvWIzUNqWlohpvHoBsGhibQdYmue4k3JtlnwaJoR1fHbw4//PDG6ri/ZfEb/Q1cU5/Uueaaz8ypOIFbjzlsMc3Ncyc0soo76Ot7gosvvZH77l8z4dzcFvjj7y/kycGlmFaRebO7SCYa6hE+TUp3nl+48X0hBJqo5fVViaBKELUkEFklCiEE0snx1z9cyt2Puc/7+Mc/zkUXXYQmFC1NJQzDJhybi9fXQSaT4d/P+zR33HEHAD6fj5tvvplkMll/39NOO40LLriAjRs3MnvWDIR4Hp+3wOCwj7LpSoO7776b008/ne7u7j0O3v88DSee9hTBwL4UCwW2btnM3NnzqAiBwEQ5OroBIFGqghIabbPmkM2kF5kV84ZVq1adz1uYmfx63cA68u/47T1z/QHxm3mz27oOO/TECY0yqbWMDL7Au5Z+kqee3jTh3JwWWPn7T7Km/3Sy2Sxz584kmWioxvZdv16OQ37dvdsF+YZHxyyXcQT4fX53gqjqBrLzBg447leAy/mXXXYZXsOmtaWI3xekoelgdL2B1atXM2/ePJ599llWrFhBpVLhlVdeoaenh5tuuon58+dz3XXX8eyzz7Jx40bmzp2LkD4CoS5scxifr0CppGE7riA9/vjj64S0J9i3q0io7Qga4nG6e3poaWkl4PWhhADhgHJdw2oKA6BoaGxmeKh/8dy5c3ZuWL+hFiN40wTwelRAfVbvoUceObiz3b9KF75Fxx5zwoRGg30P8/JLa1ly5AX07OLqzWmGlXddyN92nkRmdJSOtlaaGhqQWjWgUxX945Ff5/Z6+pcr4n2Gh0w2SyAQHFMTmkQUnuHjn7gGgI6ODr70pS+hCUUyUcLrCRFNHAx4Wb16NYcffjjnnHMOSinOP/98/vjHP5JOp1m3bh333nsvAHfccQfXXXcdc+dOlHDRxMF4PSGamyaqg2uvvXavA7nyv3sI+SSmZdHYmGTjpo2u2hIamm6g6a5k0zUDj6GjSwNd6Ox/0BKC4dD1S5YsmYvLvG9aHbxeG0ACeiIeuPKlF16NnnLCkUAIANvqpXvLXfztr6t41xGX0L0zPeHCOS3w0O8v4PGdp1CqmLS1NdPV2e5yfF23TxT7deRrVeSPE/NeQ1IqV9wJonHHX3786rro//GPfwxAsrGEz+erIh82bdrE4YcfznXXXcfNN9+M3+/H7/fz0Y9+lGg0ysaNG3n00Ufr7z5eHYyHaOJgPIaPZGOpfmzZsmV7HcRXe8Apb8fr8xIMBEin0wwND2HorrTTNR1D09B0HakJdF1H93gIh6PM2WdhJNmc/C/A4O9IAHWjD/Cs+evjM2Z2BkkkWrAK2xjo/W92bn2Ub95wB6ef8eVJF89pgfvu+iKre0+hXK6QbEgwu6uzPqkjxiNf7Ebnj0Oyz2OQGc0RDAXRNM01ADUJqQc5/LSHALj11lvrBp/PaxNrPLD+PscccwwAl19+OdFolFKpxHPPPccHP/hBVq9ezdy5c7npppvq7T/96U9P6lNNQsQaFuHz2vi8Y+vMPve5z+110D2GIBQMky/maW1tYdu27a4a0yWa1JCGgSEFUnrQNLd/hsfD7LkLaUgmjzjiyCPOreLjTRHB65EAEtD/z2X/5z2rVj+939133sm21x7kyafu4StXf48Z8z7MzcvvnXTRO/eB3991I2t2vot0ZpTmZAOzZ3a48fqpOF4bQ3Rd7NetfndwfB4PI+kM4VBk7Honx90/+xIOrug/5JBD8Bo2sXCZcGwu4AVg9erV7NixA3AlAcA555zD4OAgp512GkuWLJnw/itWrKBUKtHV1cXq1avJZDJcdtllnHbaaWQyGZBBPL440dDYbNLxxx+/18FsTkTx+13bJZFIUK6UyGTTbhi7OjZS19F0N0Ssaxpo4NE09j/kCHw+33WzZ89O8iZVwXQJQFYfZDS3tdz8q5//ktt+/BAz532EI476DLeuuG9Ka+TfPxTkh7d/i1VbuhgeydDR1s6sGV11sT/G8dWpXCkQjKmDXTlfSImuSxzLwlZgeMbEvz3wKz72eXcS/4YbbgAgFjExjDBe35iL9pWvfIXjjz8en8/HmWeeCcDNN9/MCy+40Um/38/q1avr7c8//3xWrFjBjh07OPzww4nFYixfvpzOzk4eeeQRAIKBdvx+q37N3qKKiQBEYxE3kKLpOJZFOBymr2/ANYY1UZ/Z1Ku5j4ZhoEmJputEGxponzEn0t7ZfgVvUhVMhwDq4v/zX/riOb/9+W+m5fB+5/MtnHnRbaze3EmukGdWVztzZrS7Gbz1RA6q6dtjblwN6eNdPdc1dGMCPkNnOJUmGo1UDUWJUHm+fs0XAZf758yZg64r/H6LcGzOhPd69NFHufrqq+nr62NwcJATTjiBaDRKMplkxYoVFItFlixZwqZNm/jhD38IuESglGLjxo2k02mKxSJHH310/Z6ax0W417CZDhxxMAQajwABDYkGLNshHo+RyqRdxAuXCFwVqaFLd07EkAbCMDB0jXnzFxMKRy5sn9k+B1cV1Iz01wV7I4C623fUUUc1xqOxr27YsGGvN3xgxaHMO/ZHvLhdUq6YzOhsZ2ZXhxvVY/fG3q5In9ROCryaJDWaJRwJV11GcPp/yfUrXKOzzv3hMrruR2qxSe+4ZMkSotEo27dv549//CMAp556Kueffz6rV6/m4IMPZt68eROMv20bvoUY/gnRqA+AO++8k6amWo6D602LacrTI4+Yy9BwCkc5aFJnNDeKYRik0hle27GdgdQIw6k0hUIR21bouoamXPtASAeP7sUTDNA1Zx7RcPRyXALQeBsIoNZGP/SIwy95ft0DkT01POoAeObRzzAcvZJXtg1iO9Da2sycrk6Xg+tpW1VkM070izGJUE/tEhMlg8/QGc6MVrlfIiVQ2MRHz74QmMj9oaBFODJn0jsmk0k++tGPVgnN/TvhhBNcfY5LHE8//TSrVq3i6KOP5t5772XTpk3MOPD/xTKzbF7zVTZt2kSpVJpkL9QglUpNebwGn/jE2QRCAdpb2oiGQyQbErQmkyxasA8NkTDzujqZ3dVOJBigWCmytacbpRykpqELDY9hIKSia84CwuHImYFo4A27hXuKBNa5HzBGR7NnvfOQgwib6/iv3+cnNFw0E2659ihKiY/w6OYAppVCCkFjPMrsrs56xg5UEcwUCGac6B+vDsZJA58m2TwwzIwZM1zk2wX+8quz627frtyveRqn7NgjjzzCPffcQ1NTE8uXL+eOO+5g8eLFbN++vd4mmUzS0tICQLFYBGDeoVew6s6jue2W7Rx00EH1to7tSh+z4vbx17/+9W4HdUEHiIaTyfZmaW1uoVgqYSmFEhpS6uRKJYoVE8e28Xi9dDQ3k4xGeXnTZpqam9A0Ha/HiyG9SN1i5px92fTKxi8UKFzIG5gn2FsoWAL6WR/5yDlli47BXBNHnv41zv3IX/n9g8/QmvRzzHFH4YSPYsMmjcFNw3g9NpqUJBoamDOj6upVU58m6Pca91ezeCeI/ilUgKFLCqUyoXAYXXfX+XSv/ggnn7+2/rJz5sxBE4pAwCYcmT9lhx5++OEJQZ0lS5bQ2NjIt7/9be69915OPfVUwBXxpVKJdDrN6tWreeGFFzj//PMBNzi0cePG+j0KuV5K5bHZwlr8YSr4wqWLyFhzCIe2u+6errufQqLrOrlcrhr7EDgWlMwKhsegJdlAOpMh6A+i6+6KaFNzmL3PwciVD50ZjUa/kclkXuF1ZhbvSQXUuT8QDn8yEo2SyeUYKcZ4tXQG+7/3G7Qc9FVezp3Mw0/lSWczSE3HUTZtbS3MnNlRzdcfM/TQqku2phDv7nqOWlbvRL0vhCSgS3b2D5FobEBI6H36S8w98u76y9YicOGIhSa13XL/rhE9oO7z33XXXQzvuB8oc/nllwMQjUY5/PDD2W+//YAyt/3yJc4555yx+zh5SsV+0qMeYM/cnwzAB8+7nvToKO2tbViOhWPbGB4Dx7EQctxClyrTaFJimibJxiZy+SxK2Hh0D7pXw0AnEPYSTyTxeDwX8QbiArsjgLrlf+GnLjrKsp2FDbEYSile3bSJjVu2smnrVp558SX6+/uxFTTGG9AQzOhsp7UpWV2L5+a+1bm/5uLtyv1M5P4JrqGQ+AydUrmC5tHRdYn52jUcuvT6CS+8bNkyNKGIhCqEojOn2/8JsGDBAnQjzIY/ng2U61z+2c9+lmQyyQ1XHElPcQk/+9nPqleUGR5cT7GoUyprpFIpbrzxxt3ef81/fxQ7ciK5Yo6GhgSakJTLZULhEA7g83oJ+AP1tDYha8veJCWzwvy58+ju7kN4BF7NgxICr66R7JgJUpzp9XobeZ22wF4JwDSds6PRGKWixdzZs4iEIoBGKBikpamJfCFPLBpkNF9k1qwOmhpdy7lekcP94T6s+lmfsx+Xvj2e+8dLAa+u45WwecdOmpqasYcf4NClV09I4f74xz8OQCjocv94v386cMIJ7nzGxRdfTLTlKACeuvcU4t6XKYxs4OwPHMz5Z84jy6H85eFV1avKDPevpVJRDI24QabTTpucBleDGz/fQtsh32L7ju3M6uyiXCohhLsiqTEax7IspK7j8/uRSJQEgbsEThMSRyk3nB0NMprOYnjdQBGaJBCM4vP5I+Fw+P2MSYFpwVQ2wATjr1SuHD8j0cTw0AgDQxCLRbAtm1Qmi9/nIRaLIoBEIs7smTNIZ3KuTqsbfQLlOAjDqP4eOz5J54/jfo+hEfK4nP/SaztpbWuF1AMc8o6TeWmXGdeLLroIgHCoQjDUNd2+A3DZZZfxpz/9iRUrVhCNRgE48IS7+MFN5/KZK08BQPk7ueH7q+pWf7nUTTa9iWJRZ2jEh60E//Ef/7HbpJDLz5NcctUfGcx48XhtYrEI/UMjWI6NL+CrIt7HQP8gjYnEOKnoMosSIIVGrlBg3zmzeXztUxiaH50SlpT4fV4isUZKpcKZwM9w08fs6t8eYXeUIgH9gov//ZRINBaJhCMkkgnK5RKDg0M4DrS2NhENh8nly8xob6elsRGlwDA0cFwLRI7jfEepCb93x/0+r4d40IdwbF55bQc7Bgbp7OpC779+SuTXdH8oaKHrCl/w9RHATTfdxKpVq3jsscfqk0J+v5/bfvE8hx73WX5y90b+9uR2lixZgl0ZIj20hkxqM6m0l/5hF/nLly+fMHk0Hi4/T3LVt+4nbc0llU4xq6OVQqlC0B8gnRkl2dCIWSnj9/opV8pEIxHXZpJUw+CgUQ2JIylUTGY0tdHTsx2pQJMGHr+PSKIVXdPfFYlEZvM61MBUEqDG/XoiFjvRG4oxODTAgnlzEDM6kZpGKpNjcHCYcDjI/H3nIFA0JRvY3tNPZ0czo6kMtRX5dY5HGyOK2vIt4X5qmkYo4CPg0RlOj9LXN4pCkGxqwuvzMrD+i5x0+jcmIf+oo46q6/5YuEwg1M4byXRfsmQJS5aM1+3jwaZc6qaY68GyiuTyOulsAMtyx3ZPSSA3fr6FS676I0VtAanBQfaZMxPTAbNi4jg2miZoTMQZGk5hGIp4Qxxd190iE9VqZkJoCGkjHIE0JGbZoq2jDXvd0261EqEIBULYOPgCQRzbOXl0dHQTYDIuo2B3sCsB1MKJGqB7vJ5ls7pmseGFl3jx1c0EA6FqCpZgZmcnycYEmtSxHIVVsdwZKzR03UApJvj+wJhawE158vu8BH0+LMtieHiYzaM5QpEQ8cZGdF1HU3n6nriQue+ejJhAIFD3+xsbyhiGRiA0b099nSaUqZRTWGaWSmkEyypiWYJc0UMuH6wjfvPmzZx11llT3kECT/zhfbzjfbeTLoXIZUaZ29mOBRSHJ16sAAAgAElEQVSLJcKhIK9seY321jZK5QrhUIidfX20t7ZUI6UglERIG6kEbr6JQENiSkGxWOaIQw/h57/9DfMXH4A/GEACkUQzhVx2CXBzFYfWlC84DnYrAc4777yl8XgiYngMFi6Yx8jwCFJoNDTESSZiSN0twIhUCGyGUinmzezk+Zc2sXD+bEZGMjgKjJrYlwLD0PB5vISCfizLIp0epaenD13XCYVDtLa3IqsZwJQ28ujdl3Dyef8z5Yvfc889AERDJn6/RXzcdO/rAxfhpWJ/PfnTsgSmqVG2NIoFfz3tC9wo37XXXrt7kX9BjP+85lvIlvMZyowihUNncyOmA4VKhUjQx7aefhpicQI+H+nRUXRNJxwJ4fV6sW0boSQIG4GOEO4soyZcQjCkRsEqE4xF8XkNRlNpgpEwSkAg0oCu68s8Hk+iUqn0VnG5x8DQrgRQE/9atCF6RDgURqHweTy0tTSjaRJd86DQQOquklEChcBxbIrFMqGQn+F0lkRD1F1T7/Xh8fjw+3TK5TKFQoGBgUHX8PH5aEw2jlvqXTVJ0g/yjavOq8f3d4WVK1cSj8cJBS3iMXe6d6qY/+7BplzqrYt1yxKUyhqlio9SWatzeQ1SqRR/+tOf9rgw5PLzJP9x+eXI5ktxggkGR9IkGmIYQNkBpSDi89A7lMbjNWhpjJHK5AgHw6QyaVqSSWzbQSKwhUJHYAo3RV0IrUoQEiVtdKlRKpY47tiT+c2dP2efBQegqp6V1x8gFostHRgY+B1jdsC0CKCe8gXofp9/SSAYwlESgeMWWJKyGsRxA03KqRZR1N1SrEPDQyyeP48nnn4WbItwJEQ2l8ey0liWG9r0+3xEYrFqMQetHguQCLBzjLz0Td7//3xrkr4HN9Z/991u8CcUtGiMlwiE2qfv9jl5CoWdFHI9OEqQz+vk8pM5vLYKaHdcXoNkAL74qRYu/uxX8STfT2/GQ0jzYCtobYjVR91bpevekQyGYdAQDTKaL+Pz+RjJpEk2uLOJQgjX/bOrYt9xvSWUqgaGFBoaprDRdA1L2nR2dTGws5ug30+pWMYXClHM5ZYAdzMNQ3AqG0ADtEAweJgvEKZULLjlL6uFlxwUOgqpFEoXiOrYCSEwlWLrjh7e9c79+e/HVzOnqxPp8eDz+vAHdKSuYWjjgkBQt/5FcT2r7/sMp1zw1JQv+rnPfa6ucxOxMuGQSSDUPk29bzOafp5KKUWprJEr+Mjlx7q+efNmfvOb39SJa0+QDMAF/xZj6VFHcewp/4EeeTcAo+UKjjVKtCFWLxdaYz0bGM0XiIRC+DwauUIZXddIZ0dpbnRT6d1qdXbVOLar9YxFtbS1wNEF0hRYQlVrH0OhVOKwd7yTX/z650ip4Tg2wXCMzMDAIibOEO5WCuwqASQgzzjjjAPCoTCOaQHurJtj2/VUbSWk29QGp3prBxC2olAsu0XxlMvhmq6P0WAd52Pun7DzaKmfcsEnLq9P6oyH8at4vIZNY0MFj0cQiy/abbh3PNR8dtPUGE77JyzqWL58+R7j9uAi/LTj4INnnMpRRx2Np+E9yMABk9o5tkNzYupEEMuBSDCAA1Qst/BkJp2hOZlAk1UisamLf6CqDh0EGohxtpxw7QFLgA+DkWKWA/Y/kN/97k78gQg+fwghxaGMuYKSPcQDpiIALZFMLI7E4m51zHr0zp3OxXKvchyFrgsECpSDY4PhcSVEIBjAa3jcO9oOSPcx0hGocV6aLG3goV9czHlfmFgjACYiXtcVsXCJUNDC44sTic2nluK1eyiTGX6eciVHJuMlk3MDUXsz4sCdsbv8/yzmzI9dgR6YPyXCd4VcoUAs0DDluZoKAPDpkkIJ2poTdb5QVFm0iiopBY6tkAgcWeUuVQub6whshADd46EwVGHfBfPRHMjn04SDjWi6TjKZ3H9wcHANe7EDpiQAv9/faXi8SEN3S6kLCbp7B0OXIBRKcwurOvWbuDYBEgq5PB59TL/X7+6WaEY4feQ2fpcLL17B47vYVB0dHfz4xz+uL9+KRV1xr+t+wpH50+J6x06TGnqeSkXRPzzms//617/eY6z+9KXw7W+cy4z9PwvTQHoNKg74PJ4pz40fea36vSHkJpU4UzSsslQdFFWXUNiuqlQKpQkEGgHDNcR7hzMc8e6j+c1vfoZ/fhyvz0+pUFwMrGUvmUI1AhhvAGrBYPAwr+51Q7hS1t4MgcByHKQUaA44mkLW6us6YCubRCTGYCqNz1/lUCXqHQPwZe7mjhVX8YUbBya9zK233lpflh0KWjTEymhSm7a4B7ArQ6RGnieX1xlK+YG9r99b0AEP3XkuMw78LPimj/gaZIsFwkF//XfN95oujPHImP4X1AzCWgVjgahON4vqP90ncITAKlbY98ADUb/9Gen+PjTDQAjRyTQiglPaALquRwLhEIVCCSUdpAJHKTc27U7RueXVlUIpWY3wKVCCWCTClh3dhMPBKr/jGjRWL93rruaYDz0waXCuvfbaej6917CJxyr4vDah8Ax8wVnTHsga8rM5g+G0S4Br167l4osv3u01d3xrMR/51B1vCPE1KBSLJIKBvTfcG1TRpMZ9r02jUy1hjwai4n73eAJ4NYmlKcr5LAe883DWrXqU9hmdIFjIRAKYUg1MKQG8Xu98XdfcYsq2A1JDKou6tlKKaqgPBwfNFijp3jsaCjKcSRMItIINSgNf8SF++cMr+PL3hic8fLxlrwlFPFYe0/ORuSCD0x67GvLTWS/pjKvv92TkHbkYfvWL62lf/Fn2bk/sBZwp1esbBonEUePstnFqtFaaVqEQDugeDaegqFiKZHM7/mCAXCaPclSEicifEqaMA4SjUWzHQUqJXTXzpZTYVVkvlIFyqtyt1ejB3YdD1yVmsVyltVEqm25h2fk/YXPf2INOPfVUrrzyyvrvWNQkEqrgMXzT1vMToUw69bIbp58G8r90QYyvfPOX6LETpzxfAxswLYtCqYxj2ZiWidfjpWJWKJumK9mkYGBohM6m1/vOE0FK1xOAyWyqVNVDANzYUE0aK/weP4PKxhFQLI6y7/4Hs37Vo+hStTPmBUxfAnzwgx9cVEtPKlN2r3Icav6KEIyJfJzqVhsaDg6xUJznXt1ENBZFOGn6136Gkz45lrIVCAS46667aGhoQCmF3+fQGC9hGBqh6JzXPY9fg/TQBoolVdf5e0L+/f81n5POexA8k1VLqeJWDnWHWGHoOkGfj1gouMcJ9rbmJjbv6GZO5xt7/+mAu0ZU4eC48RhH4Ujw+TyuRHAUkWiYnm1b3BrKym5jIgFMCeP7JQARCoWiALatkFr1+mq0R9WbMeG3qmr1RDTMjh09hP02G+6/cALyP/zhD/OXv/yFhoYGdF3R2lSkJVkgFG4i0bzkDSO/XOqmYpYYHHIt6z0h/6GfHMNJn9xQR37JshkeHWV4dJTRQgFd10lEIySiERqjUaLBILqm7TW7QgM621rZ2tPzhvoA1K3GXT0AcO0vlOtiu6pX4SiBciS6oSGFwlHQOXMOO7duxesLYng9MJH7pySC8RLADe0rJULhMJZZcb2A8UkJEiplC48PHKW5xolpYQJBj4GuG5RLIzz34GVccs3L9YecccYZXHzxxSiliIbKxOIOXsNPrGG/16Xnp4JcZiuZjAdbCdauXbtH5J9w7oPYeEllcoBDwOcjEdljpvu0waNpblJnoUAo8PoNwsleg6r/Fwps7KrAFa4qdhwQlltQwuujWMzS0dRKuVygpWsOI31bYS/Ih8kJIVIJJTy6gZQ6UnMjiTWxXygUsR2HkZEMQimEgszoKD6fB4lgOJMiOvTtCcj/wAc+wCWXXIImXK5viJVJNu1PrPHQN438cqkb07TrQZ7dWfv3/9d8jvu3XzGQKZPKjNIYDdEYjRDwTu27v1HoaG6mb2DwTd9HKafO/m6I2MFR41S4UuAobBQC8HjcSucDA9vx+QLoUqtR1B6RD2MEUKcUoYRwlKrP3Vc/GBlJ4Sib0VyWUChIwSxTLlfcPXkcCEcCJDI3cOolT9dvvnTpUj71qU+hS0V7a5FISCfZdiRSm35Frj1BLrOVXNFF4vLly6ds86ULYpx43oP0pnUawi7i304Ih0P0DQy9rlW3NXDUmByYIP4dXA+guoOZjYNTDSErBX7lJeAL8sJzz9Lc2VmdoQWmkRE02UDQqObnUd1iRWJWTHAUynLq26NoCPqH+gmGA6AUQfNJzr7g9vptPB4PX/6yu1S8tamA1yNoaF7Km3a5qmBXhlDKIjvqarGpRP+CDrj6m78k67QR8nldYn2boTmRoH9kqP57Og6imuKHQjGOHlAOddfQUu7cg1MyUUrhC+hE40G2v7aF/RYfNGYruPC6VABKKeGWWJNoAjQpSGezGB6DYrFE0OenWC4gNA0Dzd22TWW460f/yZoxyc+PfvQjAOKRMj6/RXPHkdMYiulDvtBDLq/Xc/KmgpUPXocndiLZXI5Y6M2pm9cDna2tdPe5fq8NlIESkLdhtOyQKVmMFCpkyxbZkkWpbOE4LrYVrnh31Xx1nqUaC3SUq/plycbBdmnFsQl4AmRTKaQ0aG1vIxSJTJAmVZiSCCZ5AZqmYZkmmhxrn89nsSomuUKWUDCEpusMDw8SjkVQCpqte7j2h2OO/rJly0gmk2g4NMZKxBMLAd+EB69Zs4ZkMskf/jC5gOPewaZSSlEs7Z77H/zhIbQt/qyrCt/iQM3uwMFFdjQaZUf/ICbu3JmkWllDA58h8Xt0gj4Phq6jcMgWSuwYGKRveMRdKoZyy+XWPhXYtsvtjuPmASoTbAWWI/BGvWx+6RWa2jpwlCAWb2AsYLxnmCQBPJoHhMLjdZM4c/kCuqFTsUw0TadUKuLVDfLZPAG/D6/ayY3f+e2Ee1x66aU4jkM0XkHTdPzByatxTj75ZM4//3y6u7vp7e19XQNdLvViWYJ8UWft2rWTzp++FI47awUSH+lcgVDAN8Vd3nqQuIv1BbDPjE6G+vvxVY/p1T+PdP8MCT4Nwj4PiWiItsZGAl4v/YPD9Pb117ehc7Crk7kKx1HYSmHigHDnXhAOIU+A4dFh5sybjyYVkVhk2kQ/ngAUoLb1bOtxhEI3PEipUSjk8Xt8VMwKHq+HslWmUrEwDC8CQaPzKHc+PFYj57DDxjaEiIWKRBr3nfTQhQsXctVVV9HX18fFF1/MLbfcwic+8YlpD3Qx11Pn/uuuu27S+W9/53L0oBvbd8V/aNr3fjNQK/JvAsFIjO6+AYqWQ9GBvEVd9I8UKowWKgyNFhjNlzEtGykFIV+AtpZmlIRXt26jWCiCBWVV3Y6uOvWuHFF1C92wcE/3duLRGD6fD9uWNDU2Ui8vtheYlBT6pwf+1H3uRz+GrmnomqRYLKHrGma5QjwSpVIxyZRS9UUU3/7eryZcf/rpp1eLOJnohkNwXMbOL3/5S84++2xefHFs/n/nzp384Ac/YHBwkP3335/bb7+dgw8+eA+vXK6mZ/tJpVKTUrIvvyBGxzuuwsGl7mLxLdgFYhpQK+RbE/dCQntzknwmTWOiwU3x0t3hrmlnBw/ZQpmBVArbrBCNRvFIndbGJgyp8+q2bczoaEGX3vpupk51LyJVcZEf8npZt/FlOmbMxuP1M9g3iOYL4g/4dxWrUxKD3OWESzaOg+bxIDUNyzbx+H2USiUsR4EmyGRyRCMhmsRf+N1fyhNuuHDhQgCCfhspx/zsSy65hEsuuYSGhoZ6jR6AAw44gMHBQU488UQOOeQQ7rnnHu67777dDnQpv9PN2DW1ScafBK665luIqr2Rr1j4fX8/8V/jJgsoApHGJl7YtJnRsk2mbJOuSoC+dI6h0QLpXAlD12ltbCQajdK9s4/+oSGUYxONRGhsiLNl8zYss+wiHxvLAsexUMJxM4kcix2vbWXu7Ln4fX6UgEo2RTAQrBHAHlcKy10aKUBVypV1dtldZ2dLzRU1SuHYFl7dwMbVT8WBdVPOezuOg9DA63N97ieffJJf/OIX9fMHHHAAxx57LACDg4Pceuut9PT0sHPnTk499VROOeWU3RJBPtdTz+fbNYfvx9fPxGgZ24dgNJcjGnz7xX9N7Ju46lrgOrshXTKrvYVKLkPUqxHz6TQEPLTEQsRCATQh6BsaobuvF2U7zGhrI18qsq17J3bFoiXegDAEW7u7sZWDshUOFkopKspBSOjv3UEkFsVSNqFQgEgwTLlSJBwJ72QaKmCSDYDrhGSVsN1VqrgbHbmJiIpsoVgt4Ki44zerJtyso6ODWpVucNwduYF0Os13vvMd9yFV/3TDhg0kk0l+//vfc/HFF/Pss8/y0EMPsXPnTg444ACuvPLK+nr8+kDbaZSyyJcmG38SOPPCWxnvbWRGM0Qiby8B1BjAYMzYq2ViWkBTUyvbunfUKzfU2msSAn4vHc1JQoEQ3X0DDA6PMKOtDVvZbO3ZgYnD7M4uenv7GB5OYynH3dpWuK5B0O/nibVrWLjf/igEutAwgj4yIxkcx+llGiVlp5IATj6ff7VSqeDxuIZgsVisOqDg2BbhQJCA3MZjT08U/6GqsaWUqjog7nOXLVvGxz72MVKpFOvXrx8bPMfhk5/8JIsWLQLgqaeeqhdkamtrqy/6rEGpOIhpunn7uxp/P75+JkbDifXOAO6K210G/q0EZ5fvJSDvQNGBrA25ikNJubH77t5+RnIlekZGGUjnGc4UyBfLOMohFAgwq72dXLHAy5u30N7SQiaXY8vWbQgBne2tbHptE8oESyls00EoRSGXwXEUoVAMWU0li4RCCKdMqVB6mWlUCxlvBFbnd3H6+/tfMcslfN4AmgJhVGlaQSGXwxsIYFBk58T8DrZs2YKqh5FFNVI9EWbOnMnIyAiXXXYZP/3pTwHo7+8nmUzy4IMPMjDgpoo1NTVNMgaL+f563H9X4++McyfW7S/ZDro20cutGWlvFpxxn7U93gTgtTYRyD+BU9pOsTTGW/sEK1gpi5gt0cOzKWgHk1VzGUmPUCyZhAI+oqEQMzra2bZjBy++spn5c+eyZs0agj4fzS0tvPTqywwP9xOKxHCUg8fjYc3qNbS2dGApdz9CNAj4PEhNki/le9hFtU/VF73+luMkwPpn1r/0riOOIK55iEQi9A8MIIRrAxSKebyBAEH10iSuqlQq7syhUpRLgnK+j93BTTfdxE033cTChQsZHHQnUN73vvdxyimn8LWvfY1TTjllQvta6LdY8E4y/i6/IIbePHEPomwuR9DvZ1eovfMbitVP8d2ghEzfTmHoJfrysxi1FlBWbnmazZs3s2nTJp56agMjIyMcdNBBtCXWccy7XmbfzmGSDQeTCp/GzqESO/sGaUk20NHSyshIii1bttDZ1cnzL7/COw98By3NbWzv7mb+/BDYCk04bNu2jWOPPwGrYOEYAse0cKTA0DQ2v7r5RSZuPzctAnAA54F7H9hx3rnn5QyvHoqEw+zY2YPX56dimti2IpfN0Tuw50pYpYoBqohj9yK11t22e/HFFznnnHPq5druu+8+du7cWf9dg/Gh310jf1d8+Sqk9NU5ESCXy9Mcj+5W9O96fCqC2JPakFXEp/s2syP/XsrKTWZduXIly5cvn3LF8GOPTVz48M6FP2H5f/6AhUd+mj7/KWzdsR3d0Jk7cy5PPvs0Hb4QuiHo3vEaba3NvPziC8ycPQuv0Nj46ks0NCRQpgIpkZYFSpDLpHAcaz1j9QH2qAZ2NQJrUs3OF/LrzUqJhsYE5ZK7B082k0NKyc6+PuLR8JQ3fOyxx5BSUi5DydTJZnZM2W483HHHHfWJI2AS8muh31zBYPPmzRPOnL4UjNZ/m9AJBaTSI4QCgb1bQVVwpvjbHUhrE+bmS3jptSY25c6irBpZuXIlhxxyCFdcccVe9wyowVMvmhzyofXEWz/O0BMfY96MGKPZHM+/+AKz2tp46ZUXaG9u5dXXXgNhEIwEGewdwOPz8exzz7Jo0TvcvRRRoLk9LOZHKRaLtR1I91owaioCcAB7eGT46WJulFAoTCQSwXJs0pkUuaK7B28wMPWs3q9//WuUUgghGBzxkkltYRqFKrj00kt54IEH6upgPOQyr9YXcO4q/m+64RLQmyZdY1Ym7wQ5HULYG8jcfQy9/H2eS11E3p5BKpWqL2J5o1ABDvvAA3z89IM4aEYepRTDIyNEw2H6+gcwNIPurVtJxpsZSQ2yYf1aovEEHq8HoYtqlNBN1xseGmSgf+ApmOB47Lbru3oBNQlgvfDMC2tzuSxew0NLczP5fI7hdBqPrrnNxNTzDLUgjxCCdEanUhGMDDw5rYE49NBDJx1z7DSlYh+pjEtw41f0LOiAlgWuq7grx+r67gtF7NUy2k1bmbuPbZs3sbXk2hsrV65k2bJl094vaG/w36szvPuwZTRoL/Dcc8/Q39fLK6++SMhn0N2zlXgkwLMb1vHSiy+w38LFrrdlO9UFI65XVcznePzRx9fwBlWAql34ox/96MVcLtun6x462jrYtm0H4XCUREOcXDZLutK8W0Pqu9/9bl0KdPcFyGd3YBemJxYngk1q8DlyeZ18UZ9Ugu0Lly5ChMbqAtSIYCSTI1JzSXl9SN4dcWhV5A+a7rT28uXL3xTX7w5e64f3HPcpFsxvp294mGxmlA0bNrBh3XqeWreOQrHIwe9aisfvxa6628LFPpn0EKPZ9OO4IYhadPp12wBO7QbDQ8OP5/NpShWTRKKRSDhAKpWiOZlk444iHVPvo8Cf//xnahs/FYoaIxkPvQNPAfmpL9gNpIfWUa44jKTcWPiuy7o+eO712M5k61zBpOSPNyP+tfJ6+rc+MQH5e1tU+mbhonMvYP9Fi+lo7yKWSLLoHQfS1NzGwv0OoJyvFpOszQ9obiGu1NAAI0MjjzGxSNTrtgFqF1qr/rbqvu6ebspli+OPPYZioYBuGAhdZ9MOjWOXTHazavC1r32tHhPoHfBRKjn0bP0z0yMCm/TQk5QrWfqG3EJMuxp/l18Qg+iRZHN5HGcimadHMzhKTWnI7U38T7ZWShS2focdpdOBvw/yAfr7U6y882Ysx8RyLGzboa+vG7/PSyqdcmMaQqCQKMdGaZJsJsWTTzz5MGMSoKYCdgu7UwEWYP70pz990TZLffstnEsimaSrowvLrFAulcnmcxxyyMLd3nj16tV1g04pxdbtAYoFRc/Wv4CT2e11jp1iqPdR8vkcPX1jCzs//OEPT2j3+cu/hCNqkUc3UFnrgFmp1AlibxHA8cRgMxbPrwd4+m9ic8599sqVK98U8vfZZx8+8YlPsPz/a+/Mg+y6qzv/uftb+73ed7W6tbQ2bNl4k+wABkQwGENNClxMJVOhMgybJ4HULBkmNTVJDROTmiJDytjGWRxIObIDsTAYhB0bMHa8CdmRZW1Wt1rq7fW+vf3e3zJ/3Pe6X7fakm28McWpuvX6ve6+9937/Z7z+/3O7yx33MFPfvITHn30UR555BFuueUWrr322vP+/lt/fxDPdQnKPkHZZ3Z2jnRDE4VCHiH0cu9hW5ssTk+xOL/wxOTk5FzNbVx0FbB2O7j6zETlJEFmPPPjng1zvxuJJdm1cwcjYyN4rovvO+R1ByaHX/Yhf/rTn+bAgQOVrCKD4bEo7a1l9PCjpNIbiSe7MQ0XMCn7CxSXRikHiyxlPRYWI2EKhAHf+MY3Vp33Y78BNHyisj2qwyhaTJSEUrnMwtIilpJYGzeuUoG1c5aw+MKK+IBbuXkXMMvPMz2dp6ybmJ+ff81j/s0338wXvvCF5bCv5fAvrdFac+ONN3LjjTcipeTAgQPLfQoA/ux//DG/+7lbmJqcIBaPsTg/Q66Qx0XjizAuWKLJLc0zOzP7c1gORHpFrWbXmypX6wTZgKOkmrvksktv7uzsIxACpQRT01P4pRJL5Tidsec4eXb9k0spGRoa4t3vfnd4s5gs5mykcgjK8wTFc+Sy58gtnSOXm2ZhQTI9G2Ep5yy7kQ8ePMjXv/71Ved94L4/JNJ+E0u5HBHPIZvLMTkzQy6bxTZhS08PtmWRzeWoTySWzXqttgesbNr4ld8rwg2dSgkEjOmvM7jwQRQuH/zgBwmC85eWF5IbbriBu+++myuuuGIZ7DCsK9xdjTgSKRRKh88KoL+/n+uvv355N7RYKLB95y4M00AKSaq+gampCbZs6UcqA0NJhO9z5qVjub+7++++DBQqR5kVErysrCVAbSKBBdiDg4PlD3xg387GxoYNTiRCLJZgbHwUlGZxqciVOyI8/POXd/aMjo4yMzPDnj17lncKy77FUs5les5jZs5jbjHC3IJDvuQgZKWUnGFw2223cdttt60630evg5s//edkywlGJjKYWlMXi9LV0kJjfR1uJIrAoL4uwfTMDMdPn2ZDeztWpdxKdfHqE4JcJUKZlfx9DdhykpnhQyyIS7j33nvP8+JdTL71rW/x0Y9+dBlsAM/xaUiXaW0q0tpYIl3n09RQpLHBJ5UIcB1JoWQSicTxPI8jR8L2gHMzM2zo21Rpr+ewMDvLtu2XYEqfQEkmx84yePql/UdfOPok4SSrVLnFV00AqMkSrjwjp6Ozo7Bx08Yb2lo7CXyf+lSKYydOEItHycxB2jjG2fGXv8jg4CDPP/88+/btA1abwOreQbV5A8Ds7Cy33HLLck+eWrnnjo9Sqvs4lmmyoa2FlqYmTNuh5At8KZEq1DKpoT5dT1tTI0//4hcI36eloWFZ+6s3XyVCWUHECH9nA272ewxNb0cS41Of+tSFnuEqaW9v5+DBg6TT6WWtjngBLU15mhpKRGMxovENONENuLFeLK8Zx2vDtkwiXo50Ko/vw8bendx3330ALC7Mc+W1ewmEwDYMvIhH79bNBL6P1pqh08d5/PHH/9f05PQkofaXWJkHvOohAFaTwDn07KHZD+zbd2VHV8wo4MYAABs5SURBVFebYdhoDOLJGOeGhij7Bju3uDz+9IXz4qanp7nnnnuYnp6mr68vbPjMyjhoGAYnTpzg1ltv5S/+4i+YnZ097xwf2Qtf+G/3k2rsY2ZhnmgsTjkQSKWW79IwAG1USKDBstnUswGlNT9/6knqYjFSibA6t1SViNuK2tsmlEQYtCkmv0smfxl33HHHqi3sC8mHPvQhbrvttuWwLcvQNKVLtDQW8GJJvPh2vMgmTCuFaUYxDAfLimOaMSynCS/WBypLXXSGxZzN8Mgk586dA6CxqRk3HgcZEIlE2bihhyAImJnIMDR0+qGHfvTQAULtLxLyOuAV7IKv586rmn+HsA98Ekh/5nOfefcnf+d3/u/WrbuYyGRYWFzg+LFjHH7+MAvzC3iLP+DeH+de0YN6rfLi4/+ZRP9/JZcvoDU01qeQUuM6NtlCnnSiruIYMZZr61bf26aBa5ucPnWKc5kJLt+1k1RTE0JBMZBhw2bDpCgkMceicPyLnC399nLFkovJJz/5yeWJnlIKzwlobSrheJpIbDu2s376uGEYYXHIGs9qbuEZ8osZHnvG57Of/SwAqYYGPvbxmxFa0t7SxhVX70X5PsdfOMQD93/vE0ePHj0FLABZQhJULcAFZT1nXq0/ICBkU/mbd3zz2dGhsy/4QYl0OkUsGmPH9u30bewlkYhjtVzHVTtf0bN6TXLf13YS3/gZhB8wPTNNOpGgVCqD1pSDACnDrCWtFVpppFRIJdFSoZQkkIp8WdCzaQt79lxLZnKChx95lNzMFLZh4BgmgZAYWmOWTpCXPef5Hl5Otm3bxuc//3mECBM8PDugsyOHF40QS16BZTeumgRWpXb4q4rWmnhqJ14UtmxMLX++ODeHUAotIV8qoaRgYuwcU5OTDx89evQc4TSmOu5fdP1flZfz5q5dDpaB0hOPP3H32dOnSCZTpOrqsL0IV115FVu2bEXoVnr6L3sNpZovLp//LZPLPvyXBMQYHB6hqb4BKQTFYgnQTE3NEItEKZbKKKlD4KtE0Cr8TIZk8IWgUBZs7t/Otdf9BiPjGR56+CCzmVGijoVtgBKL+LppeQy+kESjUe66667l8d5zynS253DdFqLx3RjG+UGptaDXgl+dB5lmEi/eQkPjagylCHBsk4XZWYQvmJoayz/5xJN/Q4hPmZUl4Ct2el6MAJKKBQDKd9555zODZwaenJubJFGXIplM4EXjvPOKK9m+bTvEd3HzJ69+mVO+Nvn3HzX54pe/RtnaxOkzgziGRSTiMZ/P4rkOs3ML2JaJFIIg8JnPZs+3AFouk8EXCrSk7AuKZcHmbdu5du91+ELw/R8+yMDJY8T0ACXZzEMPPXTR77d///5lrXZtn472Am6kGS+2Y7muwlrRWi8Tpvq/Vdd5tZVu3GkhEVvtpsktLIROLhkwnTnHZGby/hdffLGq/VUCvKrGUa9EYatVJizAXpxfHOzbsvGGTb1bXa01UglQiubmFjzXZWJGsWubx9LUKIuvzvV/nvz2ByP8pz/+U3Lxmzg9NIihoburi6VsDlMbFEslZhcW6GxtIVsohGXYtSbiuWFKu9bL6RGaMJ1dKoWszPr8isnP+z5NDY20dXSiZUB2/PtkuZrbb7/9gt/v4x//OO9617sAMJSgo62A40aJxt/Bek0Ea8Gumv/qe2CZAI7joM0CcSfDrf/nn5b/P1ZXhxeJEI/HQPiT93/3/q/kcrl5Vk/+Lrr0q5ULRUZVl8SCFStQeuyxx86ePHbywEuDx6hLpknE40RiCSLRCNv6t3Pd3mshuoO9+27k+r3rB428ki/1tf+ylf/4x39DRr6P557/V/yyoLujm5nZOcpln0KpwLnxMdqaGplfWkJrzWI2R100ipSKQKhwSbjGAkgpKQs/LLEiBUpLhBAIJSkGAfXNbXT07SNqDF30e1YnfUII0uk8riuIJXeBEWpu7dJ2LfhVqb4PG0RbuK6L53nEE024a+oXjJ47h1aS5oYUJ186efvExMQM4ZKv1vy/Yu2vPusLSe1ksJrkWvryH335r07869Ez+dwiybo6ErEYbiSC63p0dHTwm+/bR6phE6muD/NvPnYFe3ZbOK/gy8Qc+NK/bebnB7/Ept/8W54fMnnq6SeJJ+J0dXUxPjmODAIWFhcZOjdMd1s7vtRhbZ9CgXRdXcW8CpQQCCEIlCKQCqFkaHa1xpcCA4UvFVKGw4KUCqXColBF5WLqC69oPvaxj+H7PkIIoExjoogb3QREV034pJRhnkRNo8pav4dZ6Q1s2zae52HbdsUSRImkYzjOypPzS0U62tooF0tPPfi9B39CqPW1BHhV4MPF+wbWTgb9ysVcwH3ggQe+2tDc8M337rsxdBFrMJRGKUEqneZ979/H8RPHOHbUpq1/CzdsnCSqh5mdmmR6Ns9oRpGIQToGV1zWyU3v20zW3Uqm/E4eOpJnaeFhOro76N+yHWVrMiNjGK7N2HgGUOzavp1iyUflC8RjMZSh8VybQMlKZU2wlIGsVNhUleqamBAUA6TjUfZ9Eq6D9H1KxRIIQUlrbLuNmPWTCz6Ym2++Ga01Qgg6WvIIwyLmblh5cGvM+0q09PkTv6rmW1btiJwAomzevJkTJ06Ef2tZ1KeS+W99+1t/VsGierzqsb8qFyMArJCg1gq4P/jBD47t3Lnzm+mGxs9cctlVKKUIRJlAh2OsVkW2b91Oe2sbhw8fZnoGlN1JKqGJdZbZfIWLBhazWeyuXv7+qQXicYdobJB0YyPdPV1EI1EWlmbwS4JiuYQf+DQ2NdO/dRtzi4uYpk0iFmdidpaNnZ34UmJJBaaBbVookwoZNKYwMAyFIU18FRCIACkEIggIlKQQ+DiWTa6Qx6lrI25dOOg1lUrh+z5KKRKJEk60/7xl3lqpBd6ywtrLtm3juu6q361IZJk0AB+58UM894vn/ufk+OQMKx6/qtPnol6/9eTVEKC6IihRSYS59dZb97e2tV7a1tx6TVtnT1jCBCrjrgKjTKouxW+85z0MDw9z7IUjlMs+sUQSx3EIAkHEEyTTKSKJFIlEDK0dbBdmpmcpCx+/VMZxXFramunduAHbjjBydphUOkUxyJPNFtjY00W55GM5FoFtYkkTZUpMLLDkSnVybYMlIZDkimWUUuSKPiawuJSjuSHNYi5PXTSG51rEolAonv9Auru7V9b8ThlTChy3ednFvR6YtUBWx3vHcZaJsL5El1cLV119FaZl3vPII488Q2j6q+bf5zVqP7z6Dku1m0UmYJ06eepId3fX1T0beutjiSRUcgg1GikUpgFKQiKZYEPnRizbIptdQgmB57poHTY9KhQKFPMFyqU8xWKRIBDE40namtvo3tCN60XI5XJYlonhmGTGMkgp2dDTBdIEUyGUACHDaysQWmIohdQapcJKW1oqgkCQKxVwDJN8MU/Mc5mcnSUWiVAulSj7AhXpZfT5uzm2zlywvb2d66+/Hikljh2Qri8SjV96nmmvvlaXdtXx3jRNXNfFtu0LgA8wzO3f+C5lP+AjN33k6F133vW/CWf8OUILUOvxe00BT6/Vb1MlgLmwsCCFEIMNLan3bN22w3U9DyoTqupSCy1BGmhLU1/fQE9PD5bnEQQ+c/PzoCSJWIJIJEJDQwNNzS3UpepwXQ+tBYEQ+OUi5XKJ8cwkM7MzdHZ00NHWgUSHhSyFRGkTocP1v5IStAoLKkmFUgJVmfRZJkzOz5NKJJhbXCQZjbCYLVAu+SSScUbGJ0jWddGbPs4/PHDmvJtfWlripptuQkqJbQsak2UiiR2rHDzVJV0V/CrwjuMsj/cXBh9GBh5leNznqmuuG9p/z/4/yufzC4Tg5wkJULvse1MJADWWYGBgYNE0zKFUMvb+Hdt2gOWACjVRy0otG1OjK/VSDG2SiidobWknlW5ACInn2UxNTZPJZMhMjDGZmWBmaoLp6SmmpqbILhWwbIvm5mb6t2zDdSOht0+CkCIEXwmQYRq1luHyTEkDoVVl2SdRSqKUQaFYQEiF70vypTLxqMfZzBh18ST5Yo6lQpGO3t08+J1/ILtmGFBKLXcJlUrT0lTEc9sxrOiqYaAW/OpYb9v2KwJfKcWpU88wNqHzP/npz/7k9Eunz7Aa/Nc886+VX9Zzu0yC48eOTzU2Nc7HYtE9m7ZswzIcpBbLFccCIbAswigeI0xktNBEI1GwDC6//ArS6Xo2buyhqaGZ+sZmWptbaW1rZ9u2bXR3b6CxoZlkIoYQKkyX1hoRKDAkQgcoYaCRCCEr2m4QGAKtFFIKpNIICYGU2NpgZDxDfSrJyPgETekGsktZZmZnaG1uZWDwNG60jUu2xfjRwfPD2vv6+mhqakIITVODj2UJvGj3shWoarxpmnietzzmXwz4qhx78SkOPnKEs2eOf/VHP3z0aVab/urY/0uBD788AapiAOahZw+dTdYl5pOxyDX9O/oxLSfsV1JZB0ulwu5g2kBLCMvOK4aHh+nfupnFpSUMbeK4DvFoDC/iEvG8MGJGKAwtCXRY/saUGlGpYC6EQkgwqu5frRFShjX1fEWgBIYCGQiEKiNl+NyWinkKxQKu6zAxmaGttY2R8VFy2TzNDU0cP3WS7i17eOnFw0xOrE5YeeGFF7jhhjAbWUiTdHwGN9aM66aXJ3dVzbdte9V6/mJy7MWnuP+BH9NULx75ylfu+jYr4K/n8XtLCVB78VoSzEVc95rtO7ZhGk6oFVYYb6OkwrTMMKTZ0JiGyVJ2kb6eHuaWcmil0NJAGxJthqXSDB0WRpZaVyI1AwIdjv1agw7CYklSVXb+hAxjBYVEIlCCSjibWt41lErhWjYDZ4epr6tjdnaBol+kqamJwaEBXCdCLBrj1MkT/LtPfYEHvnvvqpCwIAgYGRnh6quvpuw7eC54bgbXdXDcZhzHwa60xjNfRX3CYy8+w4EHDuLY5Uf/8A9vvZUQ9Cwr4NeGev2yiU6vmQB6zWutGIeePXS2LpWci3juNb2bt2LbHhD2HzIrla6llNiWg9KK6dkZNm3oZWF+AdMMrYICTA3osEuWIcKVjgS0AEOr0LRriTSM0PsnCbXbqJBAa6QIhwcCgQgU0hAgDISUoDSea/HSwABtHW0Mnx3CtRyaG5s4efoUQgbU1zdy6uQJduzo5/ix4wT+Sk2ETCbD2NgYe/fuJV+MEbM1MW8Gk3ksxyYaOz9l7UJy4tizPPjQI9Sngke/9KU/q4JfO+6XeB3Bh9dnCFgv7DgkQV1yzi/lrunfvhMvGsVQhE0itcZyQkeNBWgp8IUgnUqTLxVA2RiGRouwSLWlwmrYyqCi9TLMYJEGhlIgBRgWSgWh2zJQIUGCsKSqDBRC6jBXQGh8ESCVQCuJadhopRgaHKS1uZXB4bMYStPZ3sbw6DC5pSxt7R0MnRngimuuZWJ0hOzS0vKNjo+Pc//997Njxw7q6jdRDuJ4jiLuzQEZLKfqNrlwraLHfv5jfvbEk5hy9t4/+OJXbyMEPMfqcf+XWvKtJ6/XHGA9i2AcevbQ2fmF+cORiPWujtYOt6G5GcPQYUs0o9KH0DRI1iU5euR5rrzqSnLZLEJLLG2CrbE0yErzREMS9tIxdE1PVAGmhQwUYIAQYJqhM4pwwmdpjdQCIYNwjmCEyRS+CKN3YtEoWHB26CxdnV1MTE+ylF1i6+bNzM0tMD4+QndHB1MzM+x+55V0dHVx6tiLqx7AY489xv79+xkbn6Wr53KwOsCIYpsCx80RBusssIKlAcxQWDrBTx97nBMnBxg599Jf/smf3n4fqzW/GuRZC/7bjgCwfsKNMXRmaD67mH0B5DYvGqnv2rAR0zTRlolrhp3FwnbyBmeHz9LX20s6XUcxXw7j/SshXYZWmBYYmJgVx45hEnbZVuFWr9IGmOGSL4wNVGF8IAqFWu50o5VACoVlgEahpSQRjWNYJmeGBknWpVFKc+7cEC1trbS1tJLJjDI7PU1bRxduJMp7P/xhFmZnmJ6cXPUQqk0o77zzLu6553v88EdPs7jocfjwWRw7TXt7P5Bkfm6Moy+8yBNPH+PQ4YFjh5957Ct/e/f9/8Jqza8F/3U1/csAvY7nWa6ISmjvYoQ7GnEg3tnZ2fR7/+H3Prt792Xv3fuu96ExyC3lKZcKFPJ5CqUiC0tLvPDiEaK2w5VXXUEQaMYyY5RLPkJqMMIiiSCQysRCo7SJtiSmsjHMMB3M0ibaNsOeR1pjmxaayi6cNjAdKySGoak4JsKQawzy+TwDp08iAkF9czNL83OgoXdzL8/+y7/Q0NJC1AvLsRlKYbsRnvzpwzzx+BNoedFm3avk97/4+/i+f9+dt9/5bUKgC6xo/doZ/+tq+qvyekdwrU29W2ZsNpv1H/vZY8/GY/G5hYXpXc2NjW5zayu2ZWGYJqZh4NoOne0d2LbH4eefY3JynEt37SaZTiPLRZQMy8+Z2NiOidYGll3xuFlUum1KwjLHdpjcgYnUYQo1GGDJsPI2GsfQGAhMw8Yywx1Ez/Xo7OwmkILpiQlszybiRinkCkxMjLFj16VEY1Gk7yOkxLFNevo28959H+Da97wHtGZmZm7VZHGt9Pb18uX//uX8L579xZ/eu//eg6wGvlbz31Dw4fWzALXnq0YPuYTl8mKVI145opfsvqT7E5/4xJcu2X3Zzqv3XIeJRTafpZgvUSqH1bP8UoG5uTmOHT+OF3XZc+WVGFiMTkwh/ACpwUQRVEqnYWg0RqWPoYmhDaShMLWJNkBhYEOlkIIZ9lOzwk6mpmViKLNSHr+yRWtY+OUC50ZHmMxM0NLYwIvHj9LV04ttWKTTCRwvhiiXwyFGGShDYpkmjmGSbmlhenSUqZl5fvjAP7G0tMjs1BSf+exnaG5pfvav/vqvvjY5PjlHqOWFmqO6yVM1+28Y+FXA3ohzLucUEJIgygoRYpX3kc997nO/1b+j/+arr94b37HznZTKeQr5IvlSIWw1XypRzuWZW5zlxIkTOKbD3muuIRqPMZyZxC8VUSoc4w00UhiYRmW8x0Aa4ChCC2AYoE20qTENC2UaWEqDY6ClxnJcTHRYG9Eww+7mhhXuT6QTfP973+PsyAjdPRtwbAcpBZZtEY8nsA0b2zYquQaKiOMwPTtLU2MDw8MjBH6ZpoZ6krHo9NNPPf033/nH7/ycEORa8Ks7fD6/5A7fqwXrjTpvNZbQYfW8oJYMkXg83rD78t2fTqbS12/q20JbWzeFoIQol5FKgxHmxBmWQeD7jI2OIoMy+97/fjo39FDMl8kHRWJeAs+1kSpAm3bYcdMMI4NNM2yyhGFiEs4ElWFjajCs0FVtAtqwcCyIRCLUp+uJRGyGh4d57vBzxOuSXLvnXWRmJpifW2BuejrcVxAByjTQhoFlOfilMslEnNm5aYYGBujqaKers5MzgwP/+J37vnP/+Pj4PCtg14JfDeuu+vffcPCrQL2R564mmdQOCVFWSBAlJEakpaWlc/PWzX+QqEvvbGvvpLWtm0KpiAjKlMoBIvCRIkD4gnypyMjAKcpKsGVzPzt27iZfzJLP5YnFk8TiMeoSKdINSaKRBM2N9RiOGS47jXA/AmlgOxbaUNiGTSoZJ5mMY2rN6Pg4h488TzmfZ2PfZvq3biMSi+PaNrZj47kRHMeiUCySz+dYyuXIlkv4+RLDg6fITE4Qi0Roamxganripz848IP9R44cGWUlgqcKfpUI68X0veHgV0F6M65RJUGtNYjWHJHK4fb19V3W2Nz4kVgsflVH1wYamzsIlED7knK5jBACTbinrw04M3iS3MIi6fo0l1+1Fy8WYXp8ivnsIlIGGEISTSRobmmltbWV9vZWuju76erqIpVMEgQ+E5kxBk4PcG58lFgkTmtrC5s2byGZSGFZJrbt4NgOtmtieS6e4eBGPSIxF4/QegwMZxg6M8DszCS57CJjw6M/PXDgwP4jR46MsRJIUwt+9X1tRM+bovW18mYQoHqd6pBQXSp6rABfSwIPcJqamjq7N3R/MhKLXJlMpeJtnX3EE3GEr5HSRwWCQGlQoEWZidkJRs6dxZCS1o5OLrn0MqTtMjk2ilCCVKIe13FAlBgZG0YGglRjA729m9i+bRtdnV00t7RgOV6lF5+B50WwbAvLsHFdG8+J4EZsHNvAATKzC0xkJhk6c4pisUB2cSE/ODD4w39++J8fOXLkyDg10dSsTO6qx1qtf9PBhzePANVrrZ0gVolQS4YqCVzAjcQjqZ6unj11qbob3UhkYzJdT1NzO4m6OrQIE0AtBWVVwi/6SCk5ffIoU3PzOCZ4joOybYq5HH7Rx3RM0uk09Y1t1Nc3Eq2L8o5LdrNrez9be3uJx+twI2HbPMt1cBwHz3UwzfCLZeaWGB8dZ2T4DIVCjnK5SGZ87NDQmaFn/vquv/5nVmdTLUdSs1rj16ZwvWkmf628mQSovWatNagW2q4lQvXVrRwOYDc1NbU3tzbvicfiV0WisR2JRJxUfQPRZB3xZD3SLyF9GeYNlEso6TOdGSeXL9LU0YZUEkeDNgwc28a2okSTEeKRGNFYjK39m7l09+Vs37adVCKKZcLsfJbZ2Rky46NMTU0i/DJ+UGZqcurQmYEzzz744INPZTKZRVaAX86kYoUAtZk7teb+LdH6WnkrCFC97loiVIeGWqvg1bxfVZE9Ho/XdXR0vKMuVbfLtu1eL+rtcJxI6NfHwI3EsB0HGQC2jVJlhNBoLdEG2IaJY1u40ThoAy/uEXUi2Da0tTTT2tyE47oIESCCMvPz88enp6ePvXTqpaMPHHjgBWqKabECfPWoJUB1jK8tP1TN3HlLwYe3jgC1168dFqpDw1oy1FoClxXCWDWH2dfX9w7P81ptx25xHGejVDKOQbNlWM2YZqUJowZzJSnDNExsx8bSoA19NuJFCoZpTCUTyWkMhuZn5yd/9rOfDVJTRZWVGjzVfIlazfdZDfp6NfvecuCr8lYToCovZxFqhwi35udaa1BLBnPNURvFXHutWtFrfq51YdeCvlbjaw9/zfu1ZdredsBX5e1CgKqsJUJVu9cSovZ1LQnWEmEtCV6OAGv3MKrALxfOXHMEa16rgNe6b9+2wFfl7UaAqqzNP6glQy0p1nu9EAng4gRYT+trtX+9V1nzP9Vz1J77bStvVwLUSi0R1hJi7eurHQYuZv5rTXgtyLWfrzXxb3vQa+VXgQC1YrC+daj9ufb9xcx/VdazAuvNBdZ+/isJeq38qhGgVmpBvdix9n/WytqQtrXgrgf2ryzotfKrTID1ZK2mr3d/F1oF1H62dnj4tfxafi2/lv/P5P8B2aZzp0LNiogAAAAASUVORK5CYII=');                        
            
            function init(){                                
                var dataModel = new ht.DataModel();
                new ht.graph.GraphView(dataModel).addToDOM();
                                                  
                var x = 70;
                [{ name: 'Critical', color: '#FF0000'},
                 { name: 'Major', color: '#FFA000'},
                 { name: 'Minor', color: '#FFFF00'},
                 { name: 'Warning', color: '#00FFFF'},
                 { name: 'Indeterminate', color: '#C800FF'},
                 { name: 'Cleared', color: '#00FF00'}].forEach(function(alarmInfo){
                    var node = new ht.Node();
                    node.setImage('ball');                 
                    node.setPosition(x, 100);
                    node.setStyle('body.color', alarmInfo.color);
                    node.setStyle('select.color', alarmInfo.color);
                    node.setStyle('note', alarmInfo.name);
                    node.setStyle('note.color', 'black');
                    node.setStyle('note.offset.x', -20);
                    node.setStyle('note.offset.y', 20);
                    node.setStyle('note.background', alarmInfo.color);
                    dataModel.add(node);
                    x += 142;
                 });
                
            }
                        

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
