<!doctype html>
<html>
    <head>
        <title>HT for Web PropertyView Manual</title>
        <meta charset="UTF-8">    
        <meta name="viewport" content="user-scalable=yes, width=1024">
        <style type="text/css"> 
            h1, h2, h3, h4, h5, h6, p, blockquote {
                margin: 0;
                padding: 0;
            }
            body {
                font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif;
                font-size: 13px;
                line-height: 18px;
                color: #737373;
                background-color: white; 
                margin: 10px 13px 10px 13px;
            }
            table {
                margin: 10px 0 15px 0;
                border-collapse: collapse;
            }
            td,th {	
                border: 1px solid #ddd;
                padding: 3px 10px;
            }
            th {
                padding: 5px 10px;	
            }
            a {
                color: #0069d6;
            }
            a:hover {
                color: #0050a3;
                text-decoration: none;
            }
            a img {
                border: none;
            }
            p {
                margin-bottom: 9px;
            }
            h1, h2, h3, h4, h5, h6 {
                color: #404040;
                line-height: 36px;
            }
            h1 {
                margin-bottom: 18px;
                font-size: 30px;
            }
            h2 {
                font-size: 24px;
            }
            h3 {
                font-size: 18px;
            }
            h4 {
                font-size: 16px;
            }
            h5 {
                font-size: 14px;
            }
            h6 {
                font-size: 13px;
            }
            hr {
                margin: 0 0 19px;
                border: 0;
                border-bottom: 1px solid #ccc;
            }
            blockquote {
                padding: 13px 13px 21px 15px;
                margin-bottom: 18px;
                font-family:georgia,serif;
                font-style: italic;
            }
            blockquote:before {
                content:"\201C";
                font-size:40px;
                margin-left:-10px;
                font-family:georgia,serif;
                color:#eee;
            }
            blockquote p {
                font-size: 14px;
                font-weight: 300;
                line-height: 18px;
                margin-bottom: 0;
                font-style: italic;
            }
            code, pre {
                font-family: Monaco, Andale Mono, Courier New, monospace;
            }
            code {
                background-color: #fee9cc;
                color: rgba(0, 0, 0, 0.75);
                padding: 1px 3px;
                font-size: 12px;
                -webkit-border-radius: 3px;
                -moz-border-radius: 3px;
                border-radius: 3px;
            }
            pre {
                display: block;
                padding: 14px;
                margin: 0 0 18px;
                line-height: 16px;
                font-size: 11px;
                border: 1px solid #d9d9d9;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            pre code {
                background-color: #fff;
                color:#737373;
                font-size: 11px;
                padding: 0;
            }
            sup {
                font-size: 0.83em;
                vertical-align: super;
                line-height: 0;
            }
            * {
                -webkit-print-color-adjust: exact;
            }
            @media screen and (min-width: 914px) {
                body {
                    width: 854px;
                    margin:10px auto;
                }
            }
            @media print {
                body,code,pre code,h1,h2,h3,h4,h5,h6 {
                    color: black;
                }
                table, pre {
                    page-break-inside: avoid;
                }
            }                        
            iframe{
                width: 100%;
                border: 1px solid #34495E;
                margin: 0;                
            }            
            .logo{
                vertical-align: middle;
            }

        </style>        
        <script>
            function init() {
                var logoSrc = 'data:image/png;base64,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',
                    logos = document.querySelectorAll('.logo'),
                    i = 0;
                for (; i < logos.length; i++) {
                    logos[i].src = logoSrc;
                }
                
                var iframes = document.querySelectorAll('iframe'),
                    func = function(){};
                for (i=0; i < iframes.length; i++) {
                    var iframe = iframes[i];
                    
                    // a small hack to make it work on android
                    iframe.ontouchstart = func;
                    
                    var div = document.createElement('div');
                    div.innerHTML = '<a href="' + iframe.src + '" target="_blank">Run→</a>';
                    iframe.parentNode.insertBefore(div, iframe);
                }   
            }
        </script>    
    </head>
    <body onload="init();">
        <a href="http://www.hightopo.com"><img class="logo"></a>HT for Web PropertyView Manual
        <hr style="margin: 1px 0 20px 0">
<p>Index</p>

<ul><li><a href="#ref_overview">Overview</a></li><li><a href="#ref_property">Property</a></li><li><a href="#ref_propertyview">PropertyView</a></li></ul>

<hr/>

<div id="ref_overview"></div>

<h2>Overview</h2>

<p>The <code>HT for Web</code> provides the property component class <code>ht.widget.PropertyView</code>, which displays the currently selected <code>Data</code> type object properties in the <code>DataModel</code> data container, which supports grouping, sorting, and filtering, etc, way to display properties. Additionally <code>HT</code> provides the <a href="../../plugin/propertypane/ht-propertypane-guide.html">PropertyPane Plugin</a>, which provides a visual component encapsulation of grouping, sorting, and filtering.</p>

<p>Initialize building a property component object by <code>propertyView = new ht.widget.PropertyView(dataModel);</code> , <code>dataModel</code> parameter is a data model bound to a property component, the internal of model will create a new data model for binding the property component constructor when the model is empty.</p>

<p>The <code>getPropertyModel()</code> function of the property component returns the property model object, which is essentially a <code>DataModel</code> type object, except that the object is only used to add <code>ht.Property</code> type object, <code>ht.Property</code> type parent class is <code>ht.Data</code>, adds a function interface associated with the property definition.</p>

<p>So what the user needs to do is build the <code>ht.Property</code> object based on the property information to be displayed, and then added to the properties model returned by the <code>propertyView.getPropertyModel()</code> function, so that when the data model of <code>propertyView.getDataModel()</code> selects a change in the data, the relevant property information for the currently selected data <code>Data</code> will displayed by the <code>ht.Property</code> object stored on the <code>propertyView.getPropertyModel()</code> configuration.</p>

<div id="ref_property"></div>

<h2>Property</h2>

<p><code>ht.Property</code> attribute class inherits from <code>ht.Data</code>, you cannot set up a parent-child relationship and it has the following property functions:</p>

<ul><li><code>getName()</code> and <code>setName(name)</code> Gets and sets the <code>name</code> property, the property combines <code>accessType</code> property to get and set the <code>Data</code> property</li><li><code>getDisplayName()</code> and <code>setDisplayName(displayName)</code> Gets and sets the display text value of the property name, or display the <code>name</code> property value if empty</li><li><code>getIcon()</code> and <code>setIcon(&#39;icon&#39;)</code> Gets and set the icon to the left of the property name</li><li><code>getColor()</code> and <code>setColor(color)</code> Gets and set the text color of the property name</li><li><code>getCategoryName</code> and <code>setCategoryName(&#39;name&#39;)</code> Sets the category name that properties in </li><li><code>isEditable()</code> and <code>setEditable(true/false)</code> Sets whether the property editable, the default is <code>false</code></li><li><code>isBatchEditable()</code> and <code>setBatchEditable(true/false)</code> Sets whether this property allows more than a batch edit, the default is <code>true</code></li><li><code>getAccessType()</code> and <code>setAccessType(type)</code> Gets and sets the access attribute type:<ul><li><code>null</code>: Default type, if <code>name</code> is <code>age</code>, using <code>getAge()</code> and <code>setAge(98)</code> <code>get/set</code> or <code>is/set</code> access type</li><li><code>style</code>: If <code>name</code> is <code>age</code>, using <code>getStyle(&#39;age&#39;)</code> and <code>setStyle(&#39;age&#39;, 98)</code> access type</li><li><code>field</code>: If <code>name</code> is <code>age</code>, using <code>data.age</code> and <code>data.age = 98</code> access type</li><li><code>attr</code>: If <code>name</code> is <code>age</code>, using <code>getAttr(&#39;age&#39;)</code> and <code>setAttr(&#39;age&#39;, 98)</code> access type</li></ul></li><li>The <code>valueType</code> is used to prompt the component to provide the appropriate <code>renderer</code>, the appropriate editing control, and the necessary type conversions when changing values:<ul><li><code>null</code>: Default type, display as text</li><li><code>string</code>: String type, displayed as text</li><li><code>boolean</code>: Boolean type, display as check box</li><li><code>color</code>: Colour type, displayed in a way that fills the background color</li><li><code>int</code>: Integral type, automatically convert by <code>parseInt</code> when text editor changes value</li><li><code>number</code>: Float type, automatically convert by <code>parseFloat</code> when text editor changes value</li></ul></li><li><code>getAlign()</code> and <code>setAlign(&#39;left&#39;/&#39;center&#39;/&#39;right&#39;)</code> Determines the horizontal alignment of the text rendering, the default is <code>left</code></li><li><code>isNullable()</code> and <code>setNullable(true/false)</code> Determines whether the property is null, the default is <code>true</code>, set to <code>false</code> to avoid input <code>null</code> or <code>undefined</code></li><li><code>isEmptiable()</code> and <code>setEmptiable(true/false)</code> Determines whether the property can be an empty string, the default is <code>false</code>, to avoid input an empty string, and an empty string convert into <code>undefined</code></li><li><code>property.getValue = function(data, property, view){return value}</code> Custom get value function</li><li><code>property.setValue = function(data, property, value, view){}</code> Custom set value function</li><li><code>property.drawPropertyValue = function(g, property, value, rowIndex, x, y, w, h, data, view)</code> Custom attribute value rendering function</li><li><code>property.drawPropertyName = function(g, property, rowIndex, x, y, w, h, view)</code> Custom attribute name rendering function</li><li><code>property.formatValue = function(value)</code> Generally used to convert a number to a text format that is easier to read, and can be overloaded with custom</li><li><code>property.getToolTip = function(data, isValue, propertyView)</code> Custom tooltip content, <code>isValue</code> represents whether the mouse is in a range of property values currently</li></ul>

<p>Enumeration is a common property-editing selection application, rendering the drop-down list while editing, so <code>ht</code> takes a lot of scenarios for enumerated type attributes, <code>setEnum(params)</code> function to set a single <code>json</code> parameter, or to set parameter information <code>setEnum(enumValues, enumLabels, enumIcons, enumEditable, enumStrict)</code> separately, the following are common cases:</p>

<ul><li><code>setEnum([&#39;C&#39;,&#39;C++&#39;,&#39;JS&#39;])</code> Passing a numerical array </li><li><code>setEnum([1,2,3], [&#39;C&#39;,&#39;C++&#39;,&#39;JS&#39;])</code> Passing values and text array</li><li><code>setEnum([1,2,3], [&#39;C&#39;,&#39;C++&#39;,&#39;JS&#39;], [&#39;c_icon&#39;, &#39;c++_icon&#39;, &#39;js_icon&#39;])</code> Passing values, text and icon arrays</li><li><code>setEnum({values:[1,2,3]})</code> Passing a numeric array</li><li><code>setEnum({values:[1,2,3], labels:[&#39;C&#39;,&#39;C++&#39;,&#39;JS&#39;]})</code> Passing values and text arrays</li><li><code>setEnum({values:[1,2,3], labels:[&#39;C&#39;,&#39;C++&#39;,&#39;JS&#39;], icons:[&#39;c_icon&#39;, &#39;c++_icon&#39;, &#39;js_icon&#39;]})</code> Passing values, text and icon arrays</li></ul>

<blockquote><p><code>HT</code> automatically detects whether the user has introduced <a href="../../plugin/form/ht-form-guide.html">Form Plugins</a>, if the <code>ht.widget.ComboBox</code> component of the form plug-in then use it as an editor, otherwise use the <code>select</code> component of the native <code>html</code>, because of the original <code>html</code> <code>select</code> drop-down component is text-only, so many of the parameters above work only for the <code>ht.widget.ComboBox</code> component.</p></blockquote>

<ul><li><code>enumValues</code>: Array of enumerated values</li><li><code>enumLabels</code>: Enum text array</li><li><code>enumIcons</code>: Enum icon array</li><li><code>enumEditable</code>: Enum whether the drop-down editor allows input, the default is <code>false</code></li><li><code>enumStrict</code>: Whether the value match use strict <code>===</code> to compare, the default is <code>true</code>, if <code>false</code> uses <code>===</code> to compare</li></ul>

<p><a href="../../plugin/form/ht-form-guide.html">Form Plugin</a> <code>ht.widget.Slider</code> slider is also a common and easy-to-use editing component, and this <code>ht</code> also increases the set of corresponding properties for that type, through <code>getSlider()</code> and <code>setSlider(parmas)</code> can specify the slider information that this property renders in edit state.</p>

<p><iframe src="examples/example_property.html" style="height:250px"></iframe></p>

<div id="ref_propertyview"></div>

<h2>PropertyView</h2>

<p>Attribute component class <code>ht.widget.PropertyView</code> main configurable properties and functions are as follows:</p>

<ul><li><code>isEditable()</code> and <code>setEditable(true/false)</code> Whether the total switch editable, the default is <code>true</code>, each <code>Property</code> attribute can be controlled</li><li><code>isBatchEditable()</code> and <code>setBatchEditable(true/false)</code> Whether the total switch editable in selection in bulk, the default is <code>true</code>, each <code>property</code> attribute can be controlled</li><li><code>isCategorizable</code> and <code>setCategorizable(true/false)</code> Whether grouping, the default is <code>true</code>, <code>false</code> ignores <code>Property</code> <code>categoryName</code> attribute</li><li><code>getIndent()</code> and <code>setIndent(20)</code> Controls the left indent, the left space is used to draw the grouping <code>toggle</code> icon, and the attribute hint <code>icon</code></li><li><code>getSortFunc()</code> and <code>setSortFunc(func)</code> Gets and sets property sort function</li><li><code>getVisibleFunc()</code> and <code>setVisibleFunc(func)</code> Gets and sets the property filter, and can also be overloaded by the <code>PropertyView#isVisible(property)</code> function</li><li><code>getExpandIcon()</code> and <code>setExpandIcon(icon)</code> Gets and sets the icon for grouping expansion</li><li><code>getCollapseIcon()</code> and <code>setCollapseIcon(icon)</code>: Grouping merged icons</li><li><code>getScrollBarColor()</code> and <code>setScrollBarColor(color)</code> Gets and sets scroll bar color</li><li><code>getScrollBarSize()</code> and <code>setScrollBarSize(6)</code> Gets and sets scroll bar width</li><li><code>isAutoHideScrollBar()</code> and <code>setAutoHideScrollBar(true/false)</code> Gets and sets whether the scroll bar is automatically hidden, the default is <code>true</code></li><li><code>getSelectRowIndex()</code> and <code>setSelectRowIndex(10)</code> Gets and sets the currently selected row index</li><li><code>getSelectBackground(data)</code> and <code>setSelectBackground(color)</code> Gets and sets row selected background color</li><li><code>getBackground()</code> and <code>setBackground(color)</code> Gets and sets the background color of the border and grouping rows</li><li><code>getRowHeight()</code> and <code>setRowHeight(20)</code> Gets and sets row height</li><li><code>isRowLineVisible()</code> and <code>setRowLineVisible(true/false)</code> Gets and sets whether line lines are visible, the default is <code>true</code></li><li><code>getRowLineColor()</code> and <code>setRowLineColor(color)</code> Gets and sets row line color</li><li><code>isColumnLineVisible()</code> and <code>setColumnLineVisible(true/false)</code> Gets and sets whether the column line is visible, the default is <code>true</code></li><li><code>getColumnLineColor()</code> and <code>setColumnLineColor(color)</code> Gets and sets the column line color</li><li><code>getColumnPosition()</code> and <code>setColumnPosition(0.5)</code> Gets and sets the column line position ratio, the default is <code>0.5</code>, the allowed range from <code>0</code> to <code>1</code></li><li><code>getRows()</code> Returns an array of all the row information currently displayed, the array element is a <code>string</code> type representing the grouping name, <code>{data: d, property: p}</code> structure object represents attribute information</li><li><code>getPropertyName(property)</code> Returns the property name displayed in the left column, which can be overloaded with custom</li><li><code>getLabelFont(property, value, rowIndex)</code> Returns the label font of attribute value, which can be overloaded with custom</li><li><code>getLabelColor(property, value, rowIndex)</code> Returns the label color of attribute value, which can be overloaded with custom</li><li><code>getPropertyFont(property, rowIndex)</code> Returns the label font of attribute name, which can be overloaded with custom</li><li><code>getPropertyColor(property, rowIndex)</code> Returns the label color of attribute name, which can be overloaded with custom</li><li><code>getCategoryFont(categoryName)</code> Returns the label font of category, which can be overloaded with custom</li><li><code>getCategoryColor(categoryName)</code> Returns the label color of category, which can be overloaded with custom</li><li><code>isExpanded(categoryName)</code> Determines whether the grouping is expanded</li><li><code>toggle(categoryName)</code> Toggle grouping expansion and merging</li><li><code>expandAll()</code> Expands all groups</li><li><code>expand(categoryName)</code> Expanding group</li><li><code>onExpanded(categoryName)</code> Callback when expand the group, can be overloaded for subsequent processing</li><li><code>collapseAll()</code> Merge all groups</li><li><code>collapse(categoryName)</code> Merging group</li><li><code>onCollapsed(categoryName)</code> Callback when merge the group, can be overloaded for subsequent processing</li><li><code>collapseAll()</code> Merge all groups</li><li><code>getPropertyModel()</code> Gets the <code>propertyModel</code> attribute, which is <code>DataModel</code> type, can be deleted and added <code>property</code> attribute object </li><li><code>getDataModel()</code> and <code>setDataModel(dataModel)</code> Gets and sets the binding <code>DataModel</code> data model</li><li><code>updateCurrentData()</code> Updates the <code>Data</code> type object for which the property is currently displayed, the default is the last object selected in <code>SelectionModel</code></li><li><code>getCurrentData()</code> Gets the current display object</li><li><code>drawCategoryName(g, name, rowIndex, x, y, w, h)</code> Draws the category name, which can be overloaded with custom</li><li><code>drawPropertyName(g, property, rowIndex, x, y, w, h)</code> Draws the property name, which can be overloaded with custom</li><li><code>drawPropertyValue(g, property, value, rowIndex, x, y, w, h, data)</code> Draws the property value, which can be overloaded with custom</li><li><code>isPropertyEditable(property)</code> Determines whether the property is editable, and can overload with custom</li><li><code>getRawProperties()</code> Returns the original unfiltered sorted attribute <code>ht.List</code> collection to be displayed, which defaults to return <code>propertyModel.getRoots()</code> and can be overloaded with custom</li><li><code>getRowIndexAt(event)</code> Returns the row index where event events are located</li><li><code>getPropertyAt(event)</code> Returns the property information of the row in which the event is located</li><li><code>enableToolTip()</code> and <code>disableToolTip()</code> Enable and disable the tooltip</li><li><code>isDisabled()</code> and <code>setDisabled(true/false, iconURL)</code> Gets and sets the entire component in an unusable state</li><li><code>addTopPainter(func)</code> and <code>removeTopPainter(func)</code> Adds and removes top-level painter <code>function(g){...}</code></li><li><code>addBottomPainter(func)</code> and <code>removeBottomPainter(func)</code> Adds and removes bottom-level painter <code>function(g){...}</code></li><li><code>addProperties(array)</code> Using <code>json</code> array parameters to add attribute information in bulk</li><li><code>setProperties(array)</code> Using <code>json</code> array parameters to set attribute information in bulk, clear all the property when the parameter is empty</li></ul>

<p><iframe src="examples/example_custom.html" style="height:260px"></iframe></p>

<p><code>PropertyView</code> provides a function of <code>addProperties</code> and <code>setProperties</code>, which can be easily added <code>property</code> by the <code>json</code> format in bulk, and the above example used the following code: </p>

<pre><code>propertyView.addProperties([
    {
        name: &#39;name&#39;,
        displayName: &#39;Name&#39;
    },
    {
        displayName: &#39;CPU&#39;,
        drawPropertyValue: function(g, property, value, rowIndex, x, y, w, h, data, view) {
            drawFunc(g, data.a(&#39;cpu&#39;), x, y, w, h);
        },
        getToolTip: function(data, isValue, propertyView){
            return isValue ? data.a(&#39;cpu&#39;) + &#39;%&#39; : &#39;CPU usage percentage&#39;;
        }
    },
    {
        displayName: &#39;MEM&#39;,
        drawPropertyValue: function(g, property, value, rowIndex, x, y, w, h, data, view) {
            drawFunc(g, data.a(&#39;mem&#39;), x, y, w, h);
        },
        getToolTip: function(data, isValue, propertyView){
            return isValue ? data.a(&#39;mem&#39;) + &#39;%&#39; : &#39;Memory usage percentage&#39;;
        }        
    }
]);  </code></pre>

<p>The above example case in the definition of <code>CPU</code> and <code>MEM</code> <code>Property</code>, did not specify any <code>name</code> and <code>accessType</code> information, because they all define the drawPropertyValue function, so <code>HT</code> does not need to get the value of the configuration information, but this also brings up another problem, after <code>propertyView.enableToolTip()</code>, <code>HT</code> is not able to get the value information to tooltip, so in this example, when defining the <code>Property</code> of <code>CPU</code> and <code>MEM</code>, defines the getToolTip function for customization.</p>

<p>In the example when the indicator is presented green when the percent value in the <code>0~40</code>, yellow when <code>40~70</code>, red when <code>70~100</code>, so the definition of <code>getColor</code> unified color conversion function, while <code>drawPropertyValue</code> custom functions also call the uniform percent drawing function of <code>drawFunc</code>.</p>

<pre><code>getColor = function(value) {
    if (value &lt; 40)
        return &#39;#00A406&#39;;
    if (value &lt; 70)
        return &#39;#FFCC00&#39;;
    return &#39;#A60000&#39;;
};

drawFunc = function(g, value, x, y, w, h){
    g.fillStyle = &#39;#A1A1A3&#39;;
    g.beginPath();
    g.rect(x, y, w, h);
    g.fill();                    
    g.fillStyle = getColor(value);
    g.beginPath();
    g.rect(x, y, w * value / 100, h);
    g.fill();
    ht.Default.drawText(g, value + &#39;%&#39;, &#39;12px Arial&#39;, &#39;white&#39;, x, y, w, h, &#39;center&#39;);                
};</code></pre>    <hr id="contact" style="margin: 20px 0 1px 0">
    <a href="http://www.hightopo.com"><img class="logo"></a>Welcome to contact us <a href="mailto:<EMAIL>"><EMAIL></a>
</body>
</html>
