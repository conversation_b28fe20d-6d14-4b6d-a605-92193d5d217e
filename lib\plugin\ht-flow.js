!function(A){"use strict";function K(A,l){for(var r=[],J=A.length,V=0;V<J;V++)for(var m=A[V],k=(m=m._as?m._as:m)[0],G=1;G<m.length;G++)r.push([k,m[G]]),k=m[G];for(var N=[],V=0;V<r.length;V++){var _=w(r[V][0],r[V][1],l);N.push(_)}return{distance:N,segments:r}}function Z(A,l,r){if(A){for(var J,A=K(A,l),V=A.distance,m=A.segments,k=1/0,G=null,N=0,_=V.length;N<_;N++){var Z=V[N];Z.z<k&&(J=N,k=Z.z,G=Z)}if(G.z<(r=null==r?.1:r)){for(var f=0,B=0;B<=J;B++)f+=i.getDistance(m[B][0],B<J?m[B][1]:G);return f}}}function f(A,l){var r,J;if(A)return A=K(A,l).distance,r=1/0,J=null,A.forEach(function(A){A.z<r&&(r=A.z,J=A)}),J}function g(A,l,r){if(!A)return r;if(0===l)return O=A[0][0],B=A[0][1],r+Math.atan2(B.y-O.y,B.x-O.x);if(100===l)return O=(A=A[A.length-1])[A.length-2],B=A[A.length-1],r+Math.atan2(B.y-O.y,B.x-O.x);for(var J=0,V=[],m=A.length,k=0;k<m;k++){var G=A[k],G=F(G);J+=G,V.push(G)}for(var N=J*l/100,_=u(N,V),Z=0,f=0;f<_;f++)Z+=V[f];for(var B=X(A[_],N-=Z),H=A[_],z=0,K=0,g=0;g<H.length-1;g++){var a=H[g],i=H[g+1],p=i.x-a.x,i=i.y-a.y;if(N<(z+=Math.sqrt(p*p+i*i))){K=g;break}}var O=H[K];return r+Math.atan2(B.y-O.y,B.x-O.x)}function a(A){var l=0;if(A)if(Array.isArray(A[0]))for(var r=A.length,J=0;J<r;J++){var V=A[J];l+=F(V)}else l=F(A);return l}function o(A,l,r,J){return m.set(l,r).rotateAround(null,J),A?{x:A.x+m.x,y:A.y+m.y}:{x:m.x,y:m.y}}function V(A,l){var r=A._data,J=z(A);if(J){if(!l)return J;var V,m=r.getScale?r.getScale():c,k=r.getRotation?r.getRotation():0;return m&&(V=r.getPosition(),J.forEach(function(A,r){A.forEach(function(A,l){J[r][l]=y(V,A.x-V.x,A.y-V.y,m),J[r][l]=o(V,J[r][l].x-V.x,J[r][l].y-V.y,k)})})),J}}function E(A){var l=A._data,J=z(A);if(J){for(var V,r,m,k=l.getScale?l.getScale():c,G=(k&&(V=l.getPosition(),J.forEach(function(A,r){A.forEach(function(A,l){J[r][l]=y(V,A.x-V.x,A.y-V.y,k)})})),l.s("flow.reverse")&&(J.reverse(),J.forEach(function(A){A.reverse()})),0),N=[],_=J.length,Z=0;Z<_;Z++){var f=J[Z],f=F(f);G+=f,N.push(f)}l[D]=N,l[b]=G,l[H]=J,l instanceof B.Edge&&(r=(m=i.unionPoint(J)).x+m.width/2,m=m.y+m.height/2,l.$10e={x:r,y:m}),lO(A,!0)}}function h(A){var l,r=A.data,J=this.dm();r&&"add"===A.kind&&(l=J.$3e)&&r.s(G)&&l.indexOf(r)<0&&l.push(r),"clear"===A.kind&&(J.$3e=[])}function d(A){var l=A.property,r=A.data,A=A.newValue,J=this.dm().$3e;if(J&&"s:flow"===l)if(A)J.indexOf(r)<0&&J.push(r);else for(var V=J.length,m=0;m<V;m++)if(J[m]===r){J.splice(m,1);break}l==="s:"+j&&((A=this.getDataUI(r))[s]=c,A[k]=c)}function C(z){var A,K=this,g=K._data,a=g[b],i=g[D],p=g[H],l=g.s(U),r=K[s],J=g.s(Q),V=g.s(q),m=g.s(x),k=g.s(P),G=g.s(t),O=g.s(v),N=(G-k)/(m-1),_=(J-V)/(m-1),L=g.getRotation?g.getRotation():0,I=g.getPosition?g.p():g.$10e,S=[],R=[];if(r!=c){if(1===m)S.push(J);else if(2===m)S.push(J),S.push(V);else{if(!(2<m))return;S.push(J);for(var Z=m-2;0<Z;Z--)S.push(V+_*Z);S.push(V)}if(1===m)R.push(G);else if(2===m)R.push(G),R.push(k);else{if(!(2<m))return;R.push(G);for(Z=m-2;0<Z;Z--)R.push(k+N*Z);R.push(k)}var Y=0,f=0;S.forEach(function(A){Y<m-1&&(f+=g.getFlowElementSpace(A)),Y++}),A=(a-l*(f+=(J+V)/2)+f)/l,z.save();for(Z=0;Z<l;Z++){var $=r,j=0,B=K._overCount,n=0;g.s("flow.autoreverse")&&B&&l-(Z+1)<B||($-=Z*(A+f),Y=0,S.forEach(function(A){var l=$-j;if(0<=l&&l<a){for(var r=u(l,i),J=n=0;J<r;J++)n+=i[J];l-=n;var V=X(p[r],l),m=L;if(O){for(var k=p[r],G=0,N=0,_=0;_<k.length-1;_++){var Z=k[_],f=k[_+1],B=f.x-Z.x,f=f.y-Z.y;if(l<(G+=Math.sqrt(B*B+f*f))){N=_;break}}var H=k[N];m+=Math.atan2(V.y-H.y,V.x-H.x)}L&&(V=o(I,V.x-I.x,V.y-I.y,L)),K.$5e(z,V,A,R[Y],m,Y)}j+=g.getFlowElementSpace(S[Y]),Y++}))}z.restore()}}function W(){var A=this,l=A._data,r=l.s(Q),J=l.s($),V=!1,m=c,r=Math.max(r,r*J);return A._6I||(V=!0),m=(l instanceof B.Edge?GO:NO).call(A),l.s(G)&&V&&(J=l.s(t),0<(r=l.s(R)&&r<J?J:r)&&i.grow(m,Math.ceil(r/2)),E(A)),!l.s(G)&&V&&(l[D]=l[b]=l[H]=A[s]=A[k]=c),m}function M(A){this.__flowBatchGroup={}}function e(l){var A=this.__flowBatchGroup;if(A){for(var r in l.save(),A)l.fillStyle=r,l.beginPath(),A[r].forEach(function(A){l.moveTo(A[0],A[1]),l.arc(A[0],A[1],A[2],0,2*Math.PI,!0)}),l.fill();l.restore()}}var B=A.ht,i=B.Default,p=i.getInternal(),A=p.ui(),c=null,D="__segmentLengths",b="__lineTotalLength",H="__linePoints",s="__distance0",k="firePercent",U="flow.count",O="flow.step",Q="flow.element.max",x="flow.element.count",q="flow.element.min",l="flow.element.space",v="flow.element.autorotate",L="flow.element.background",t="flow.element.shadow.max",P="flow.element.shadow.min",I="flow.element.shadow.begincolor",S="flow.element.shadow.endcolor",R="flow.element.shadow.visible",G="flow",Y="flow.element.image.stretch",$="flow.element.image.ratio",j="flow.begin.percent",n="flow.element.image.body.color",T="flow.onFlowPercentChange",r=B.Math.Vector2,m=new r,r=(new r,new r,B.List,B.Default._edgeProtectMethod),z=r.getStraightLinePoints,w=function(A,l,r){var J=A.x,V=A.y,m=l.x,k=l.y,G=r.x,r=r.y,m=m-J,k=k-V,N=Math.sqrt(m*m+k*k),_=m/N,N=k/N,Z=(-J+G)*_+(-V+r)*N,J={x:J+Z*_,y:V+Z*N};return J.x>=Math.min(A.x,l.x)&&J.x<=Math.max(A.x,l.x)&&J.y>=Math.min(A.y,l.y)&&J.y<=Math.max(A.y,l.y)||(J.x=(Math.abs(J.x-A.x)<Math.abs(J.x-l.x)?A:l).x,J.y=(Math.abs(J.y-A.y)<Math.abs(J.y-l.y)?A:l).y),m=G-J.x,k=r-J.y,J.z=Math.sqrt(m*m+k*k),J},F=r.calculateLineLength,u=r.calcSegmentIndexByDistance,X=r.calculatePointAlongLine,y=function(A,l,r,J){return m.set(l,r).multiply(J),A?{x:A.x+m.x,y:A.y+m.y}:{x:m.x,y:m.y}},AO=r.getPercentPosition,lO=function(A,l){var r,J=A._data,V=J[b],m=J.s(U),k=J.s(O),G=J[D],N=J.s(Q),_=J.s(q),Z=J.s(x),f=(N-_)/(Z-1),B=[],H=J.s(j);if(G&&!isNaN(m)&&0!==m){if(1===Z)B.push(N);else if(2===Z)B.push(N),B.push(_);else{if(!(2<Z))return;B.push(N);for(var z=Z-2;0<z;z--)B.push(_+f*z);B.push(_)}var K=0,g=0,a=(B.forEach(function(A){K<Z-1&&(g+=J.getFlowElementSpace(A)),K++}),r=(V-m*(g+=(N+_)/2)+g)/m,A[s]);for(null==a&&(a=V*H),l||(a+=k);V+g<a;){var i=A._overCount;i?i++:i=1,m<=i&&(i=null),A._overCount=i,!J.s("flow.autoreverse")||i?a-=r+g:(a=0,J.s("flow.reverse",!J.s("flow.reverse")))}A[s]=a}},r="prototype",J=B.graph.GraphView[r],rO=B.Data[r],JO=A.DataUI[r],N=A.ShapeUI[r],A=A.EdgeUI[r],r=B.DataModel[r],_=B.Style,VO=(_[Q]==c&&(_[Q]=7),_[q]==c&&(_[q]=0),_[U]==c&&(_[U]=1),_[O]==c&&(_[O]=3),_[x]==c&&(_[x]=10),_[l]==c&&(_[l]=3.5),_[v]==c&&(_[v]=!1),_[L]==c&&(_[L]="rgba(255, 255, 114, 0.4)"),_[I]==c&&(_[I]="rgba(255, 255, 0, 0.3)"),_[S]==c&&(_[S]="rgba(255, 255, 0, 0)"),_[R]==c&&(_[R]=1),_[t]==c&&(_[t]=22),_[P]==c&&(_[P]=4),_[Y]==c&&(_[Y]="fill"),_[$]==c&&(_[$]=1),_[j]==c&&(_[j]=0),_[n]==c&&(_[n]=c),J.calculatePointLength=function(A,l,r,J){A=this.getDataUI(A),A=V(A,J);return Z(A,l,r)},i.calculatePointLength=function(A,l,r,J){A=z(c,A,l);return Z(A,r,J)},i.calculateClosestPointOnLine=w,J.calculateClosestPoint=function(A,l,r){A=this.getDataUI(A),A=V(A,r);return f(A,l)},i.calculateClosestPoint=function(A,l,r){A=z(c,A,l);return f(A,r)},J.getPercentAngle=function(A,l,r){var J=this.getDataUI(A),J=V(J,r),A=A.getRotation?A.getRotation():0;return g(J,l,r?0:A)},i.getPercentAngle=function(A,l,r){A=z(c,A,l);return g(A,r,0)},J.calculateLength=function(A,l){A=this.getDataUI(A),A=V(A,l);return a(A)},i.calculateLength=function(A,l){A=z(c,A,l);return a(A)},J.getPercentPosition=function(A,l,r){A=this.getDataUI(A),A=V(A,r);return AO(A,l)},i.getPercentPositionOnPoints=function(A,l,r){A=z(c,A,l);return AO(A,r)},J.setDataModel),mO=(J.setDataModel=function(A){var l,r=this,J=r._dataModel;J!==A&&(J&&(J.umm(h,r),J.umd(d,r),J.$3e=[]),A.mm(h,r),A.md(d,r),l=A.$3e=[],A.each(function(A){A.s(G)&&l.indexOf(A)<0&&l.push(A)}),VO.call(r,A))},rO.getFlowElementSpace=function(A){return this.s(l)},A.drawBody),kO=(A.drawBody=function(A){mO.call(this,A);var l=this._data,r=this.gv;l.s(G)&&r.$7e!=c&&C.call(this,A)},N.drawBody),GO=(N.drawBody=function(A){kO.call(this,A);var l=this._data,r=this.gv;l.s(G)&&r.$7e!=c&&C.call(this,A)},A._79o),NO=N._79o,_O=(N._79o=W,A._79o=W,JO.$5e=function(A,l,r,J,V,m){var k,G=this._data,N=this.gv,_=G.s(L),Z=G.s(I),f=G.s(S),B=G.s(R),H=N.$8e,z=G.s("flow.element.image"),K=G.s(Y),g=G.s($),a=G.s(n);H==c&&(H=N.$8e={}),A.beginPath(),z!=c?(i.isArray(z)&&(z=z[m%z.length]),m=i.getImage(z,a),k=r/2,A.translate(l.x,l.y),A.rotate(V),A.translate(-l.x,-l.y),i.drawStretchImage(A,m,K,l.x-k,l.y-k*g,r,r*g,G,N,a),A.translate(l.x,l.y),A.rotate(-V),A.translate(-l.x,-l.y)):N.__flowBatch?(z=(z=N.__flowBatchGroup)||(N.__flowBatchGroup={}),m=[l.x,l.y,r/2],z[_]?z[_].push(m):z[_]=[m]):(A.fillStyle=_,A.arc(l.x,l.y,r/2,0,2*Math.PI,!0),A.fill()),B&&((g=H[K="22_"+Z+"_"+f])==c&&(a=document.createElement("canvas"),p.setCanvas(a,22,22),V=a.getContext("2d"),p.translateAndScale(V,0,0,1),V.beginPath(),(N=V.createRadialGradient(11,11,0,11,11,11)).addColorStop(0,Z),N.addColorStop(1,f),V.fillStyle=N,V.arc(11,11,11,0,2*Math.PI,!0),V.fill(),g=H[K]=a),i.drawImage(A,g,l.x-(k=J/2),l.y-k,J,J,G))},J.$9e=function(){var l=this,A=l.dm().$3e;l._24I;A.forEach(function(A){l._24I[A._id]=A}),l.iv()},r.prepareRemove);r.prepareRemove=function(A){_O.call(this,A);var l=A._dataModel.$3e;if(l)for(var r=l.length,J=0;J<r;J++)if(l[J]===A){l.splice(J,1);break}},J.setFlowInterval=function(A){var l=this,r=l.$11e;l.$11e=A,l.fp("flowInterval",r,A),l.$7e!=c&&(clearInterval(l.$7e),delete l.$7e,l.enableFlow(A))},J.getFlowInterval=function(){return this.$11e},J.enableFlow=function(A){var J=this,l=J.dm(),r=l.$3e;J.$7e==c&&(r.forEach(function(A){A=J.getDataUI(A);E(A)}),J.$7e=setInterval(function(){l.$3e.forEach(function(A){var l,r=J.getDataUI(A);lO(r),A.s(T)&&!r[k]&&(l=r[s]/A[b],A.s(T)(l),1<=l&&(r[k]=!0))}),J.$9e()},A||J.$11e||50))},J.disableFlow=function(){clearInterval(this.$7e),delete this.$7e,this.dm().$3e&&this.$9e()};J.setFlowBatch=function(A){var l=this;!!l.__flowBatch!=!!A&&((l.__flowBatch=A)?(l.addBottomPainter(M),l.addTopPainter(e)):(l.removeBottomPainter(M),l.removeTopPainter(e)))}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);