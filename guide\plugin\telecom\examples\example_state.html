<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>AlarmState</title>
        <script src="../../../../lib/core/ht.js"></script>
        <script src="../../../../lib/plugin/ht-telecom.js"></script>
        <script>
            
            var dataModel = new ht.DataModel();
            var graphView = new ht.graph.GraphView(dataModel);
            
            function init() {
                // Add nodes with random new, acked or propagated alarm
                for (var i=0, x = 0, preNode; i < 7; i++) {
                    var node = new ht.Node();
                    node.setPosition(x += 100, 100);
                    addRandomAlarm(node);
                    dataModel.add(node);
                    
                    if (preNode) {
                        var edge = new ht.Edge(preNode, node);
                        addRandomAlarm(edge);
                        dataModel.add(edge);
                    }
                    
                    preNode = node;
                }
                
                // Add graphView to document body
                var style = graphView.getView().style;
                style.left = '10px';
                style.top = '10px';
                style.right = '10px';
                style.bottom = '10px';
                document.body.appendChild(graphView.getView());
                
                // Invalidate graphView when window size changed
                window.onresize = function () {
                    graphView.iv();
                };
            }
            
            function addRandomAlarm (data) {
                    var alarmState = data.getAlarmState();
                    if (randomBoolean()) {
                        alarmState.setNewAlarmCount(randomNonClearedSeverity(), randomInt(5) + 1);
                    }
                    if (randomBoolean()) {
                        alarmState.setAcknowledgedAlarmCount(randomNonClearedSeverity(), randomInt(5) + 1);
                    }
                    if (randomBoolean()) {
                        alarmState.setPropagateSeverity(randomNonClearedSeverity());
                    }
            }
            
            function randomInt (n) {
                return Math.floor(Math.random() * n);
            }
            
            function randomBoolean () {
                return randomInt(2) !== 0;
            }
            
            function randomNonClearedSeverity () {
                while (true) {
                    var severity = randomSeverity();
                    if (!ht.AlarmSeverity.isClearedAlarmSeverity(severity)) {
                        return severity;
                    }
                }
                return null;
            }
            
            function randomSeverity () {
                var severities = ht.AlarmSeverity.severities;
                return severities.get(randomInt(severities.size()));
            }
            
        </script>
    </head>
    <body onload="init();">
    </body>
</html>