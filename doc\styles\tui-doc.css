body {
    font-size: 12px;
    font-family: Helvetica Neue, Helvetica, Arial, Malgun gothic, '돋움', AppleSDGothicNeo;
}
ul, ol, li {list-style:none; padding-left:0; margin-left:0;}
footer{
    display: none;
}
/* Navigation - LNB */
.lnb {
    width: 290px;
    position: absolute;
    bottom: 0;
    top: 0;
    overflow: auto;
    left: 0;
    background-color: #161b1d;
    padding: 0 20px;
}
.lnb .logo {
    width: 88%;
    max-width: 281px;
    margin: 20px auto 10px;
}
.lnb .title {
    text-align: center;
    padding: 0 0 15px;
}
.lnb .title .link {
    color: #fff;
    font-style: italic;
}
.lnb h3 {
    font-size: 1.5em;
    color: #fa3282;
}
.lnb h3 a {
    color: #fa3282;
}
.lnb h3 a:hover {
    color: #9a3282;
}
.lnb .lnb-api li,
.lnb .lnb-examples li {
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #1f292e;
}
.lnb .lnb-api h3 a {
    color: #fa3282;
}
.lnb .lnb-api h3 a:hover {
    color: #fa3282;
    text-decoration: underline;
}
.lnb .lnb-api a,
.lnb .lnb-examples a {
    color: #7cafc2;
}
.lnb .lnb-api a:hover,
.lnb .lnb-examples a:hover {
    color: #a3cfdf;
}
.lnb .lnb-api .toggle-subnav {
    padding: 0 3px;
    margin-bottom: 0;
}
.lnb .lnb-api .toggle-subnav:focus {
    outline: 0;
}
.lnb .lnb-api .toggle-subnav {
    font-size: 10px;
}
.lnb .member-type {
    margin-top: 5px;
    margin-left: 5px;
    color: #568c3b;
    font-weight: normal;
    font-size: 10px;
    cursor: text;
}
.lnb .inner li {
    margin-left: 15px;
    border-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    color: #bbb;
}
.lnb .inner a {
    color: #bbb;
}
.lnb .inner a:hover {
    color: #eee;
}

.lnb .version {
    color: #aaa;
    font-size: 1.2em;
}

/* LNB-TAB */
.lnb-tab {
    text-align: center;
    text-decoration: none;
}
.lnb-tab li {
    display: inline-block;
    padding-top: 15px;
}
.lnb-tab li a {
    color: #aaa;
    font-size: 0.9em;
}
.lnb-tab li.selected a {
    color: #fff;
    font-size: 1em;
}
.lnb-tab li+li a h4:before {
    content: "\007C";
    display: inline-block;
    color: #999;
    padding: 0 10px;
}

/* MAIN-CONTENT */
.main {
    padding: 20px;
    left: 297px;
    right: 0;
    top: 0;
    bottom: 0;
    position: absolute;
    overflow: auto;
    margin-bottom: 0;
}
.main article ol,
.main article ul {
    margin-left: 15px;
}
.main section header {
    padding-top: 0;
    border-bottom: 1px solid #999;
}
.main section header h2 {
    font-size:18px;
    font-weight:bold;
    padding-left : 5px;
    border-left: 5px solid #dc9656;
}
.main section article {
    padding: 10px;
}
.main section article .container-overview {
    padding: 15px 15px 0 15px;
    border: 1px solid #dedede;
    border-radius: 7px;
}
.main section article h3.subsection-title {
    font-size:16px;
    color: #fa3282;
    padding:35px 0 0 5px;
    border-bottom: 1px solid #dedede;
}
.main section article dl h4 {
    font-size: 16px;
    font-weight: bold;
}
.main section article dl h4 .signature {
    font-size: 9pt;
}
.main section article dl h4 .type-signature {
    font-size: 9pt;
    color: #31708f;
    font-weight: normal;
}
.main section article dl dt .name {
    padding: 3px 10px;
    background-color: #f4f7f8;
}
.main section article dl dd {
    padding: 0 30px;
}
.main section article dl dd h5{
    font-weight: bold;
}
.main section article .container-source {
    margin: -15px -15px 3px 0;
    font-weight: normal;
    font-size: 8pt;
    text-align: right;
    padding-right: 10px;
}
.main section article .container-returns {
    margin-bottom: 7px;
}
.main section article .container-returns span,
.main section article .container-params table {
    border-left: 3px solid #eee;
    margin-left: 7px;
    padding-left: 3px;
    margin-bottom: 5px;
}
.main section article .container-returns p {
    display: inline;
}
.main section article .container-properties h5,
.main section article .container-returns h5,
.main section article .container-params h5,
.main section article table th,
.main section article table td.type,
.main section article table td.attributes {
    font-family: Verdana, sans-serif;
    font-size: 90%;
}
.main section article table,
.main section article table th,
.main section article table td {
    font-family: Verdana, sans-serif;
    vertical-align: top;
    border: 0;
    padding: 1px 3px;
}
.main section article table td.name,
.main section article table td.type,
.main section article table td.attributes,
.main section article table td.default {
    max-width: 100px;
    min-width: 80px;
    word-break: break-all;
}
.main section article table td.type,
.main section article table td.attributes {
    color: #aaa;
}
.main section article table td p {
    padding: 0;
    margin: 0;
}
.main section article table td h6 {
    padding: 0 0 0 3px;
    margin: 3px 0 0 3px;
    font-size: 85%;
}
.main section article .container-properties table.props {
    margin-top: -3px;
}
.main .main-content article {
    padding:0;
}
.main .container-overview,
.main .main-datail {
    overflow: hidden;
}
.main .main-detail .tag-source {
    float:left;
    display:none;
}
.main .main-detail .tag-author {
    float:left;
}
.main .main-detail .tag-author a {
    color:#181818;
    font-size:11px;
    text-decoration:none;
}
.linenums li.selected {
    background: #faebd7;
}
.iinenums .number {
    color: #777;
    display: inline-block;
    width: 40px;
}

/* FOOTER */
footer {
    padding-top: 3px;
    line-height: 35px;
    height: 35px;
    position: fixed;
    width: 100%;
    bottom: 0;
    background-color: #00beaa;
    color: #ebf8ff;
    text-align: center;
}

/* README*/
.readme {
    font-size: 14px;
}
.readme p,
.readme ul,
.readme ol {
    padding: 3px 0 3px 5px;
}
.readme li {
    list-style: initial;
}
.readme img {
    max-width: 100%;
}
.readme h1 {
    font-size:24px;
    font-weight:normal;
    padding: 10px 0 5px 0;
    border-bottom: 1px solid #428bca;
}
.readme pre {
    margin: 15px 3px;
}
.readme li p {
    padding: 10px 0;
    color: #333;
}
.readme p a {
    color:#c7254e;
}
.readme h2 {
    padding-bottom: 3px;
    border-bottom: 1px solid #dedede;
    font-size: 22px;
}
.readme h3 {
    font-size: 20px;
    padding-bottom: 3px;
}

.readme h4 {
    font-size: 18px;
}
.readme h5 {
    font-size: 16px;
}
.readme h6 {
    font-size: 15px;
}
.readme table {
    margin: 5px 30px 20px;
}
.readme table th,
.readme table td {
    padding: 2px 20px 2px 5px;
    border-bottom: 1px solid #dedede;
}
.readme section header h2 {
    font-size:20px;
    padding-left:10px;
    border-left:5px solid #fa3282;
}
.readme section .container-overview {
    color:#333;
    border-radius: 2px;
    border:1px solid #dedede;
    padding:15px 15px 10px;
}
.readme section .container-overview .description {
    color:#666;
}
.readme section .container-overview dt {float:left; }
.readme section .container-overview dd {float:left; margin-left:10px; }
.readme blockquote {
    padding: inherit;
    margin: inherit;
    font-size: inherit;
    color: #777;
}

/* Search box */
.search-container {
    z-index: 100;
    position: relative;
    padding-bottom: 10px;
}
.search-container input {
    padding: 7px;
    width: 100%;
    color: #aaa;
    border: 1px solid #585858;
    background-color: #373737;
    border-radius: 2px;
}
.search-container a {
    color: #fff;
}
.search-container strong {
    color: pink;
    font-weight: normal;
}
.search-container ul {
    font-size: 13px;
    position: absolute;
    width: 100%;
    background-color: #456e82;
    border: 1px solid #1f292e;
    border-radius: 0 0 2px 2px;
    opacity: 0.9;
    filter: alpha(opacity=90);
}
.search-container ul li {
    text-align: left;
    width: 100%;
    padding: 4px 0 4px 7px;
    overflow: hidden;
    border: 0;
    cursor: pointer;
}
.search-container ul li:hover,
.search-container ul li.highlight{
    background-color: #fff;
}
.search-container ul li:hover a,
.search-container ul li.highlight a {
    color: #1f292e;
    text-decoration: underline;
}
.search-container ul li:hover strong,
.search-container ul li.highlight strong {
    color: #ff4141;
}
.search-container ul li .group {
    font-size: 11px;
    color: #ccc;
    margin-left: 10px;
}
.search-container ul li:hover .group,
.search-container ul li.highlight .group {
    color: #777;
}

/* ETC */
.logo {
    width: 90px;
    vertical-align: initial;
}
.hidden {
    display: none;
}
.footer-text {
    padding-left: 3px;
    display: inline-block;
}
#example-nav {
    margin-top: 15px;
}
#resizer {
    width: 7px;
    position: fixed;
    left: 290px;
    height: 100%;
    background-color: #00beaa;
    cursor: col-resize;
}
span.param-type {
    color: #aaa;
}
pre.prettyprint {
    font-size: 0.9em;
    border-radius: 0;
}
span.icon {
    font-size: 8pt;
    border-radius: 3px;
    padding: 1px 2px;
}
span.icon.green {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}
span.icon.blue {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1;
}
span.icon.yellow {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc;
}
span.icon.red {
    color: #A94443;
    background-color: #f2dede;
    border-color: #ebccd1;
}
span.arrow {
    font-size: 8pt;
    padding-right: 5px;
}
