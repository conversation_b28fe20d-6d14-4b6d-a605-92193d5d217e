<!DOCTYPE html>
<html>
    <head>
        <title>Node</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script>   
        <script>
            function init(){                                
                var dataModel = new ht.DataModel(),
                    graphView = new ht.graph.GraphView(dataModel),
                    view = graphView.getView();            

                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    graphView.invalidate();
                }, false);                         
                     
                var source = new ht.Node();
                source.setName('source');
                source.setPosition(100, 70);                
                dataModel.add(source);
                
                var target = new ht.Node();
                target.setName('target');  
                target.setPosition(260, 70);
                dataModel.add(target);

                var edge = new ht.Edge();
                edge.setSource(source);
                edge.setTarget(target);
                dataModel.add(edge);               
                
                edge = new ht.Edge(source, target);
                edge.toggle();
                dataModel.add(edge);                               
    
                edge = new ht.Edge(source, source);
                dataModel.add(edge);                 
                
                graphView.getLabel = function(data){
                    if(data instanceof ht.Edge){
                        if(data.isEdgeGroupAgent()){
                            return data.getEdgeGroupSize() + ' become 1';
                        }
                    }
                    return data.getName();
                };
                
                             
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
