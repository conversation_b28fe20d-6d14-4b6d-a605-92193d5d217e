<!DOCTYPE html>
<html>
    <head>
        <title>HT for Web AutoLayout</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>    
                 
        <script src="../../../../lib/core/ht.js"></script> 
        <script src="../../../../lib/plugin/ht-autolayout.js"></script>  
        <script src="../../../../lib/plugin/ht-forcelayout.js"></script>  
        <script>
            function init(){
                items = [ 
                    {
                        label: 'circular',
                        selected: true,
                        groupId: 'layout',
                        action: function(){
                            layout(this.label);
                        }
                    },
                    {
                        label: 'symmetric',
                        groupId: 'layout',
                        action: function(){
                            layout(this.label);
                        }                        
                    },    
                    {
                        label: 'towardnorth',
                        groupId: 'layout',
                        action: function(){
                            layout(this.label);
                        }                        
                    },                    
                    {
                        label: 'towardsouth',
                        groupId: 'layout',
                        action: function(){
                            layout(this.label);
                        }                        
                    }, 
                    {
                        label: 'towardeast',
                        groupId: 'layout',
                        action: function(){
                            layout(this.label);
                        }                        
                    }, 
                    {
                        label: 'towardwest',
                        groupId: 'layout',
                        action: function(){
                            layout(this.label);
                        }                        
                    },
                    'separator',  
                    {
                        label: 'Editable',
                        type: 'check',
                        selected: false,
                        action: function(item){
                            graphView.setEditable(item.selected);
                        }
                    }                    
                ];
                
                dataModel = new ht.DataModel();                   
                graphView = new ht.graph.GraphView(dataModel);     
                treeView = new ht.widget.TreeView(dataModel);
                toolbar = new ht.widget.Toolbar(items);
                borderPane = new ht.widget.BorderPane();
                borderPane.setTopView(toolbar);
                borderPane.setCenterView(graphView);              
                mainSplit = new ht.widget.SplitView(borderPane, treeView, 'horizontal', 0.7);                
                        
                view = mainSplit.getView();
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    mainSplit.invalidate();
                }, false);                                                                                
                                               
                names = ['SplitView', 'AccordionView', 'TabView',
                    'PropertyView', 'ListView', 'TreeView', 'TableView', 'TreeTableView', 
                    'TableHeader', 'TablePane', 'TreeTablePane','Toolbar','BorderPane', '...'];
                
                widgets = new ht.Node();
                widgets.setName('HT Widgets');
                widgets.setStyle('label', null);                
                widgets.setStyle('shape', 'circle');  
                widgets.setStyle('shape.gradient', 'radial.center');                  
                widgets.setStyle('note', 'HT Widgets');
                widgets.setStyle('note.background', '#E74C3C');                
                widgets.setSize(18, 18);
                dataModel.add(widgets);
    
                names.forEach(function(name){
                    var node = new ht.Node();
                    node.setName(name);
                    node.setStyle('label.font', '9px arial');
                    widgets.addChild(node);
                    dataModel.add(node);
                    
                    var edge = new ht.Edge(widgets, node);
                    edge.hideMe = true;
                    dataModel.add(edge);
                    
                    if(name === '...'){
                        node.setSize(20, 20);
                        node.s({
                            'shape': 'star',
                            'border.color': '#E74C3C',
                            'note': 'More to come',
                            'note.background': '#2C3E50',
                            'note.position': 19,
                            'note.offset.x': 6,
                            'note.offset.y': -8                           
                        });
                    }
                });
                
                autoLayout = new ht.layout.AutoLayout(graphView);                               
                forceLayout = new ht.layout.ForceLayout(graphView);
                
                graphView.enableToolTip();     
                graphView.addInteractorListener(function(e){  
                    if(e.kind === 'beginMove'){
                        forceLayout.start();
                    }                        
                    if(e.kind === 'endMove'){
                        forceLayout.stop();
                    } 
                });                
                
                treeView.setVisibleFunc(function(data){
                    return data.hideMe ? false : true;                    
                });                
                treeView.expandAll();   
                
                layout('circular');
            }

            function layout(type){
                autoLayout.layout(type, function(){
                    graphView.fitContent(true);
                });
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
