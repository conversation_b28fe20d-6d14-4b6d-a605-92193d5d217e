!function(g,o){"use strict";function a(){return document}function u(g){return a().createElement(g)}function c(g,o,M){g.style.setProperty(o,M,C)}function S(g,o){g.appendChild(o)}var M,m=g.ht,Z="innerHTML",L="className",C=null,r="px",f=m.Default,Q=f.getInternal(),_="0",b=Q.addEventListener,O=Q.removeEventListener,x=f.isTouchable,k=f.isTouchEvent;Q.addMethod(f,{menuLabelFont:(x?"16":"13")+"px arial, sans-serif",menuLabelColor:"#000",menuBackground:"#F0EFEE",menuHoverBackground:"#648BFE",menuHoverLabelColor:"#fff",menuSeparatorWidth:1,menuSeparatorColor:"#999"},!0),m.widget.Menu=function(g){var o=this,M=o._view=Q.createView(null,o),I=o.$1g=new m.widget.ContextMenu,b=u("ul");I._r=!0,I._view[L]+=" ht-widget-dropdownmenu",M[L]="ht-widget-menu",b[L]="header",c(M,"margin",_),c(M,"padding",_),c(M,"background",f.menuBackground),c(M,"-webkit-user-select","none"),c(M,"-moz-user-select","none"),c(M,"user-select","none"),c(M,"text-align","left"),c(M,"border-bottom",f.menuSeparatorWidth+"px solid "+f.menuSeparatorColor),c(M,"cursor","default"),c(M,"overflow","auto"),c(M,"white-space","nowrap"),c(M,"font",f.menuLabelFont),c(M,"color",f.menuLabelColor),c(M,"box-sizing","border-box"),c(M,"-moz-box-sizing","border-box"),c(b,"list-style","none"),c(b,"margin",_),c(b,"padding",_),c(b,"display","inline-block"),S(M,b),o.setItems(g),o.$2g=function(g){o.$3g(g)},o.$4g=function(g){o.$5g(g)},o.$6g=function(g){o.$7g(g)},o.$8g=function(g){o.$9g(g)},o.$9b=function(g){o.$10g(g)},o._autoShow=!0,o.setAutoShow(!1),I.afterHide=function(){o.$11g()},I.afterShow=function(){o.$12g()},O(a(),"keydown",I.$3b),o.$3b=function(g){o.$13g(g)},o.invalidate()},g="Menu",M={_items:C,$14g:f.menuHoverBackground,$15g:f.menuHoverLabelColor,$16g:{},_enableGlobalKey:!1,ms_v:1,$21g:"smallicons",$22g:0,$23g:0,$24g:"left",getDropDownMenu:function(){return this.$1g},setLayout:function(g){var o=this;if(o.$21g=g,o.setItems(o._items),"largeicons"===g){for(var M=o._view.querySelectorAll(".header-item"),I=0,b=0;b<M.length;b++)var m=M[b],I=Math.max(I,m.clientWidth);for(b=0;b<M.length;b++){m=M[b];c(m,"min-width",I+r)}}this.invalidate()},getLayout:function(){return this.$21g},setHeaderItemHGap:function(g){this.$22g=g;for(var o=this._view.querySelectorAll(".header-item"),M=0;M<o.length;M++){var I=o[M];c(I,"margin-left",g+r),c(I,"margin-right",g+r)}},getHeaderItemHGap:function(){return this.$22g},setHeaderItemVGap:function(g){this.$23g=g;for(var o=this._view.querySelectorAll(".header-item"),M=0;M<o.length;M++){var I=o[M];c(I,"margin-top",g+r),c(I,"margin-bottom",g+r)}},getHeaderItemVGap:function(){return this.$24g},setHeaderItemAlign:function(g){this.$24g=g,c(this._view,"text-align",g)},getHeaderItemAlign:function(){return this.$23g},enableGlobalKey:function(){!1===this._enableGlobalKey&&(b(a(),"keydown",this.$3b),this._enableGlobalKey=!0)},disableGlobalKey:function(){this._enableGlobalKey=!1,O(a(),"keydown",this.$3b)},setHoverBackground:function(g){this.$14g=g},setHoverColor:function(g){this.$15g=g},setItems:function(g){var o=this,M=o._view,I=o.$21g;if(o._items=g,M.children[0][Z]="",o.$16g={},g&&g.length){for(var M=M.children[0],b=0,m=a().createDocumentFragment();b<g.length;b++){var r,f=g[b],Q=u("li"),C=u("span");f.icon&&((r=u("canvas"))[L]="menu-item-icon","smallicons"===I?(c(r,"height","1.2em"),c(r,"width","1.2em"),c(r,"vertical-align","middle")):(c(r,"height","32px"),c(r,"width","32px"),c(r,"display","block"),c(r,"margin","0 auto")),r.$20g=f.icon,S(Q,r)),Q[L]="header-item",c(Q,"display","inline-block"),c(Q,"vertical-align","top"),c(Q,"padding","0 1.2em"),c(Q,"line-height","1.8em"),"largeicons"===I&&c(Q,"text-align","center"),c(Q,"background-color","rgba(0,0,0,0)"),Q.setAttribute("data-index",b),o.$16g[b]=f.items,C[Z]=f.label,"iconsonly"!==I&&S(Q,C),S(m,Q)}S(M,m)}},showDropdownMenu:function(g){var o,M=this,I=M.$16g[g],b=M.$1g,g=M._view.children[0].children[g],m=M.$17g;g&&g!==m&&(m&&M.hideDropdownMenu(),m=g.getBoundingClientRect(),o=f.getWindowInfo(),M.$17g=g,b.setItems(I),b.show(m.left+o.left,m.top+m.height+o.top,!1))},hideDropdownMenu:function(){this.$1g.hide()},getItemByProperty:function(g,o){var M=this._items;return M&&0!==M.length?this.$1g.getItemByProperty(g,o,M):C},$12g:function(){var g=this,o=g.$17g;o.style.background=g.$14g,o.style.color=g.$15g,g._autoShow||b(a(),x?"touchstart":"mousedown",g.$9b)},$11g:function(){var g=this.$17g;g&&(g.style.background="",g.style.color="",this.$17g=C),O(a(),x?"touchstart":"mousedown",this.$9b)},$10g:function(g){var o=this._view,M=this.$1g,I=o.children[0];!a().body.contains(o)||I.contains(g.target)||M._view.contains(g.target)||this.hideDropdownMenu()},$13g:function(g){var o=this.$1g;a().body.contains(this._view)&&o.$13b.$4b.call(o.$13b,g,this._items)},setAutoShow:function(g){var o=this,M=o.$1g,I=o._view;o._autoShow!==g&&(o._autoShow?(O(I,"mouseover",o.$2g),O(I,"mouseout",o.$4g),O(M._view,"mouseout",o.$4g),x||(b(I,"mouseover",o.$8g),b(I,"mouseout",o.$8g)),b(I,x?"touchstart":"mousedown",o.$6g)):(O(I,"mouseover",o.$8g),O(I,"mouseout",o.$8g),O(I,x?"touchstart":"mousedown",o.$6g),O(a(),x?"touchstart":"mousedown",o.$9b),x||(b(I,"mouseover",o.$2g),b(I,"mouseout",o.$4g),b(M._view,"mouseout",o.$4g))),o._autoShow=g)},$3g:function(g){var o=this._view.children[0],M=g.target;if(o!==M&&o.contains(M)){for(;"header-item"!==M[L];)M=M.parentNode;this.showDropdownMenu(M.getAttribute("data-index"))}},$5g:function(g){var o=this._view.children[0],M=this.$1g,I=g.target,g=g.relatedTarget;!o.contains(I)&&!M._view.contains(I)||o.contains(g)||M._view.contains(g)||this.hideDropdownMenu()},$7g:function(g){g.preventDefault();var o=this,M=o._view.children[0],I=o.$1g,b=g.target;if(f.isLeftButton(g)&&M!==b&&M.contains(b))if(k(g)){for(;"header-item"!==b[L];)b=b.parentNode;var M=b.getAttribute("data-index"),I=o.$1g,g=o._view.children[0].children[M],m=o.$17g;I.isShowing()&&o.hideDropdownMenu(),g!==m&&o.showDropdownMenu(M)}else if(I.isShowing())o.hideDropdownMenu();else{for(;"header-item"!==b[L];)b=b.parentNode;o.showDropdownMenu(b.getAttribute("data-index"))}},$9g:function(g){var o=this,M=o._view,I=o.$1g,b=g.target;if(M.contains(b)){for(var m=M.querySelectorAll(".header-item"),r=C,f=0;f<m.length;f++){var Q=m[f];Q.style.background="",Q.style.color="","mouseover"===g.type?Q.contains(b)&&(r=Q):"mouseout"===g.type&&I.isShowing()&&o.$17g===Q&&(r=Q)}I.isShowing()&&(r=r||o.$17g,o.showDropdownMenu(r.getAttribute("data-index"))),r&&(r.style.background=o.$14g,r.style.color=o.$15g)}},getShowingMenuIndex:function(){var g=this.$17g;return g?g.getAttribute("data-index"):-1},addTo:function(g){var o=this._view;S(g,o),this.invalidate()},dispose:function(){var g=this,o=g._view,M=g.$1g;o&&(g._autoShow?(O(o,"mouseover",g.$2g),O(o,"mouseout",g.$4g),O(M._view,"mouseout",g.$4g)):(O(o,"mouseover",g.$8g),O(o,"mouseout",g.$8g),O(o,x?"touchstart":"mousedown",g.$6g),O(a(),x?"touchstart":"mousedown",g.$9b)),O(a(),"keydown",g.$3b),M.dispose(),o.parentNode&&o.parentNode.removeChild(o),g._view=g.$1g=g.$16g=g._items=g.$17g=g.$2g=g.$4g=g.$6g=g.$8g=g.$9b=g.$3b=C)},$19g:function(g,o,M,I){g=Q.initContext(g);Q.translateAndScale(g,0,0,1),g.clearRect(0,0,M,I),f.drawStretchImage(g,f.getImage(o),"fill",0,0,M,I),g.restore()},validateImpl:function(){for(var g=this._view.querySelectorAll(".menu-item-icon"),o=0;o<g.length;o++){var M=g[o],I=M.clientWidth,b=M.clientHeight;I&&b&&(Q.setCanvas(M,I,b),this.$19g(M,f.getImage(M.$20g),I,b))}}},f.def(m.widget[g],o,M)}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);