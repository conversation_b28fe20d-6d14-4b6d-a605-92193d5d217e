<!DOCTYPE html>
<html>
    <head>
        <title>Border Color</title>
        <meta charset="UTF-8">       
        <script src="../../../../lib/core/ht.js"></script>   
        <script>
            ht.Default.setImage('mac', 'data:image/png;base64,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');  
            ht.Default.setImage('ap', 'res/ap.png');  
            
            function init(){                                
                var dataModel = new ht.DataModel();
                var graphView = new ht.graph.GraphView(dataModel);
                graphView.addToDOM();
                
                var ap = new ht.Node();
                ap.setPosition(0, 0);
                ap.setImage('ap');                
                ap.setStyle('border.color', '#40A3FC');
                ap.setStyle('border.width', 3);
                ap.setStyle('border.padding', 5);
                ap.setStyle('border.type', 'circle');
                ap.setStyle('select.padding', 10);
                ap.setStyle('select.type', 'circle');
                dataModel.add(ap);                
                
                var angle = Math.PI/6,
                    r = 120;
                [{ name: 'Critical', color: '#FF0000'},
                 { name: 'Major', color: '#FFA000'},
                 { name: 'Minor', color: '#FFFF00'},
                 { name: 'Warning', color: '#00FFFF'},
                 { name: 'Indeterminate', color: '#C800FF'},
                 { name: 'Cleared', color: '#00FF00'}].forEach(function(alarmInfo){
                    var node = new ht.Node();
                    node.setImage('mac');                 
                    node.setPosition(Math.cos(angle) * r, Math.sin(angle) * r);
                    node.setSize(64, 33);                    
                    node.setRotation(angle + Math.PI/2);
                    node.setStyle('select.width', 0);
                    node.setStyle('body.color', alarmInfo.color);
                    node.setStyle('border.color', alarmInfo.color);
                    node.setStyle('border.type', 'roundRect');
                    node.setStyle('border.padding', 5);
                    node.setHost(ap);
                    dataModel.add(node);
                    angle += Math.PI/3;
                    
                    var edge = new ht.Edge(node, ap);
                    edge.setStyle('edge.width', 3);
                    edge.setStyle('edge.offset', 32);
                    edge.setStyle('border.color', alarmInfo.color);
                    dataModel.add(edge);
                 }); 
                 
                 graphView.translate(150, 150);
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
