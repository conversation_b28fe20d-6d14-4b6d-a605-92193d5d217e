
<!DOCTYPE html>
<html>
    <head>
        <title>Export Import</title>
        <meta charset="UTF-8">                
        <script src="../../../../lib/core/ht.js"></script> 
        <script src="../../../../lib/plugin/ht-form.js"></script> 
        <script src="../../../../lib/plugin/ht-obj.js"></script> 
        
        <script>
            function getRawText(obj){
                var text = String(obj); 
                return text.substring(14, text.length-3);
            }            
        </script> 
        
        <script src="obj/meter_obj.js"></script> 
        <script src="obj/meter_mtl.js"></script>                         
        
        <script>
            function init(){                             
                ht.Default.setImage('meter', 256, 256, 'data:image/jpeg;base64,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');                
                
                dataModel = new ht.DataModel();
                
                g3d = new ht.graph3d.Graph3dView(dataModel);
                propertyView = new ht.widget.PropertyView(dataModel);
                formPane = new ht.widget.FormPane();
                rightSplit = new ht.widget.SplitView(propertyView, formPane, 'v', 100);
                        
                new ht.widget.SplitView(g3d, rightSplit, 'h', 0.65).addToDOM();                         
                
                var params = {center: true};
                var modelMap = ht.Default.parseObj(meter_obj, meter_mtl, params);
                var array = [];
                for(var name in modelMap){
                    var model = modelMap[name];
                    array.push(model);
                    
                    if(name === 'pointer'){
                        model.mat = {
                            func: function(data){
                                var start = Math.PI * 0.736,
                                    range = Math.PI * 1.49,   
                                    angle = start - range * data.a('meter.value') / 4;
                                return ht.Default.createMatrix([
                                    { t3: [0, -82.5, 0] },
                                    { r3: [0, 0, angle] },
                                    { t3: [0, 82.5, 0]  }
                                ]);
                            }
                        };                         
                    }
                    if(name === 'switch'){
                        model.mat = {
                            func: function(data){
                                return ht.Default.createMatrix([
                                    { t3: [0, 48.5, 0] },
                                    { r3: [0, 0, data.a('meter.angle')] },
                                    { t3: [0, -48.5, 0]  }
                                ]);
                            }
                        }; 
                        model.color = {
                            func: function(data){
                                if(data.a('meter.angle')){
                                    return 'rgb(186, 0, 0)';
                                }else{
                                    return 'black';
                                }
                            }
                        };
                    }
                }
                ht.Default.setShape3dModel('meter', array);

                for(var i=0; i<3; i++){
                    var node = new ht.Node();
                    node.setTag(i);
                    node.setName('Meter - 00' + (i+1));
                    node.s({
                        'label.color': 'white',
                        'label.background': '#5271B8',
                        'label.face': 'center',
                        'label.position': 23,
                        'label.scale': 2,
                        'label.reverse.flip': true, 
                        
                        'note.scale': 1.5,
                        'note.t3': [-30, -5, -90], 
                        
                        'note2.scale': 1.2,
                        'note2.position': 17,
                        'note2.t3': [0, -20, -30],
                        'note2.color': 'black',
                        'note2.background': 'yellow', 
                        
                        'shape3d': 'meter',
                        'shape3d.scaleable': false,
                        'wf.visible': 'selected',
                        'select.brightness': 1
                    });
                    node.a({
                        'meter.value': i+1,
                        'meter.angle': i * Math.PI / 3
                    });
                    node.p3(i*200-200, params.rawS3[1]/2, i===1?100:-100);                    
                    node.r3(0, -Math.PI/6*(i-1), 0);
                    node.s3(params.rawS3);
                    dataModel.add(node);                   
                }
                dataModel.sm().ss(dataModel.getDataByTag(1));
                
                g3d.setGridVisible(true);
                g3d.getNote = function(data){
                    return 'Value:' + data.a('meter.value').toFixed(2);
                };
                g3d.getNote2 = function(data){
                    var value = Math.round(data.a('meter.angle') / Math.PI * 180);                    
                    return value ? 'Angle:' + value : 'Switch is off';
                };

                var oldEye = g3d.getEye().slice(0),
                    oldCenter = g3d.getCenter().slice(0),
                    newEye = [200, 300, 650],
                    newCenter = [0, params.rawS3[1]/2, 0];

                ht.Default.startAnim({                    
                    duration: 1000,
                    easing: function(t){ 
                        return (t *= 2) < 1 ? 0.5 * t * t : 0.5 * (1 - (--t) * (t - 2));                      
                    },
                    action: function(k){
                        g3d.setEye(
                            oldEye[0] + (newEye[0] - oldEye[0]) * k,
                            oldEye[1] + (newEye[1] - oldEye[1]) * k,
                            oldEye[2] + (newEye[2] - oldEye[2]) * k
                        );
                        g3d.setCenter(
                            oldCenter[0] + (newCenter[0] - oldCenter[0]) * k,
                            oldCenter[1] + (newCenter[1] - oldCenter[1]) * k,
                            oldCenter[2] + (newCenter[2] - oldCenter[2]) * k
                        );    
                    }                  
                });

                propertyView.addProperties([
                    {
                        name: 'name',
                        editable: true
                    },
                    {
                        name: 'meter.value',
                        accessType: 'attr',
                        editable: true,
                        slider: {
                            min: 0,
                            max: 4
                        }
                    },
                    {
                        name: 'meter.angle',
                        accessType: 'attr',
                        editable: true,
                        formatValue: function(value){
                            return Math.round(value / Math.PI * 180);
                        },
                        slider: {
                            min: 0,
                            max: Math.PI,
                            step: Math.PI/180*5,
                            getToolTip: function(){
                                return Math.round(this.getValue() / Math.PI * 180);
                            }
                        }
                    },
                    {
                        name: 'rotation',
                        editable: true,
                        formatValue: function(value){
                            return Math.round(value / Math.PI * 180);
                        },
                        slider: {
                            min: -Math.PI,
                            max: Math.PI,
                            step: Math.PI/180*5,
                            getToolTip: function(){
                                return Math.round(this.getValue() / Math.PI * 180);
                            }
                        }
                    }                    
                ]);               
            
                formPane.addRow([
                    {
                        id: 'export',
                        button: {                              
                            label: 'Export JSON',
                            onClicked: function(){
                                var json = dataModel.serialize();
                                formPane.v('textArea', json);
                            }
                        }
                    },
                    {
                        button: {                            
                            label: 'Import JSON',
                            onClicked: function(){
                                dataModel.clear();
                                dataModel.deserialize(formPane.v('textArea'));
                            }
                        }
                    }
                ],
                [0.1, 0.1]);                                  
                formPane.addRow([
                    {
                        id: 'textArea',
                        textArea: {
                        }
                    }
                ],
                [0.1], 0.1);  
                formPane.getItemById('export').element.onClicked();
            }  
            

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
