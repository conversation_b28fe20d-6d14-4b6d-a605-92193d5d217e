<!DOCTYPE html>
<html>
    <head>
        <title>Toggle Overview</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
            .overview{
                position:absolute;
                left:0px;
                bottom:0px;
                width:200px;
                height:150px;
                background:rgb(240, 239, 248);
            }
            .animation{
                -webkit-transition: width .3s,height .3s;
                -moz-transition: width .3s,height .3s;
                -o-transition: width .3s,height .3s;
                transition: width .3s,height .3s;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script> 
        <script src="../../../../lib/plugin/ht-overview.js"></script>
        <script src="topology.js"></script>
        
        <script>

            var ToggleOverview = function(graphView) {
                var self = this;
                ToggleOverview.superClass.constructor.apply(self, [graphView]);                
                self._expand = true;
                
                var div = document.createElement("div");
                div.style.setProperty("width", "24px", null);
                div.style.setProperty("height", "24px", null);
                div.style.setProperty("position", "absolute", null);
                div.style.setProperty("right", "0", null);
                div.style.setProperty("top", "0", null);
                div.style.setProperty("background", " url(shrink.png) no-repeat", null);
                div.style.setProperty("background-position", "center center", null);                
                self._view.appendChild(div);
                
                function handleTransitionEnd(e) {
                    if (e.propertyName === "width"){
                        self.invalidate();
                    }                    
                }
                self._view.addEventListener("webkitTransitionEnd", handleTransitionEnd, false);
                self._view.addEventListener("transitionend", handleTransitionEnd, false);
                var eventName = ht.Default.isTouchable ? "touchstart" : "mousedown";
                div.addEventListener(eventName, function(e) {
                    if (self._expand) {
                        self._view.style.setProperty("width", "24px", null);
                        self._view.style.setProperty("height", "24px", null);
                        self._canvas.style.setProperty("opacity", "0", null);
                        self._mask.style.setProperty("opacity", "0", null);
                        div.style.setProperty("background-image", "url(expand.png)", null);
                        div.style.setProperty("width", "24px", null);
                        div.style.setProperty("height", "24px", null);
                        self._expand = false;
                    } else {
                        self._view.style.setProperty("width", "", null);
                        self._view.style.setProperty("height", "", null);
                        self._canvas.style.setProperty("opacity", "1", null);
                        self._mask.style.setProperty("opacity", "1", null);
                        div.style.setProperty("background-image", "url(shrink.png)", null);
                        div.style.setProperty("width", "24px", null);
                        div.style.setProperty("height", "24px", null);
                        self._expand = true;
                    }                    
                    self.invalidate();                     
                    e.stopPropagation();
                });
                self.setContentBackground("white");
            };
            ht.Default.def(ToggleOverview, ht.graph.Overview, {                
            });

            function init() {                
                var dataModel = new ht.DataModel(),
                    graphView = new ht.graph.GraphView(dataModel),
                    overview = new ToggleOverview(graphView);

                dataModel.deserialize(topology);    
                graphView.getView().className = 'main';
                overview.getView().className = "overview animation";
                document.body.appendChild(graphView.getView());
                document.body.appendChild(overview.getView());
                
                window.addEventListener('resize', function(e) {
                    graphView.invalidate();
                }, false);
                
            }


        </script>
    </head>
    <body onload="init();">
    </body>
</html>
