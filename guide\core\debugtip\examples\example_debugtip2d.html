<!DOCTYPE html>
<html>
    <head>
        <title>2D Debug Tip</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
        <meta charset="UTF-8">
        <style>
            html, body {
                padding: 0px;
                margin: 0px;
            }
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>
        <script src="../../../../lib/core/ht.js"></script>
        <script>
            function init() {
                dataModel = new ht.DataModel();
                graphView = new ht.graph.GraphView(dataModel);
                graphView.addToDOM();

                ht.Default.xhrLoad('debug2d.json', function(text) {
                    var json = ht.Default.parse(text);
                    dataModel.deserialize(json);
                    graphView.fitContent(true);

                    graphView.showDebugTip();
                });
            }
        </script>
    </head>
    <body onload="init();">
    </body>
</html>
