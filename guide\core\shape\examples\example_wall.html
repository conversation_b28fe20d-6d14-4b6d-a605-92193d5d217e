<!DOCTYPE html>
<html>
    <head>
        <title>Wall</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style> 
        <script src="../../../../lib/core/ht.js"></script>
        <script>                
                                     
            ht.Default.setImage('brick1', 'data:image/png;base64,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');
            ht.Default.setImage('brick2', 'data:image/jpeg;base64,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');
            
            function init(){                                 
                dataModel = new ht.DataModel();                
                g3d = new ht.graph3d.Graph3dView(dataModel);   
                g2d = new ht.graph.GraphView(dataModel);                   
                mainSplit = new ht.widget.SplitView(g3d, g2d, 'v', 0.5);   
                
                view = mainSplit.getView();  
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    mainSplit.invalidate();
                }, false);                         
              
                g3d.setGridVisible(true); 
                g3d.setEye(0, 400, 400); 
                g3d.setCenter(0, 0, -100); 
                
                g2d.translate(400, 160);      
                g2d.setZoom(0.6, true);
                g2d.setEditable(true);
                
                wall1 = new ht.Shape();
                dataModel.add(wall1);                   
                wall1.setPoints([
                    {x: 200, y: 50},
                    {x: 400, y: 50},
                    {x: 500, y: 200},
                    {x: 100, y: 200}
                ]); 
                wall1.translate(-100, -150);
                wall1.setClosePath(true); 
                wall1.setThickness(20);
                wall1.setTall(100);
                wall1.setElevation(wall1.getTall()/2);
                wall1.s({
                    'shape.border.width': 20,
                    'shape.border.color': '#9E9E9E',
                    'shape.background': null,
                    'all.color': '#D9D9D9'
                });                 
                
                wall2 = new ht.Shape();
                dataModel.add(wall2);                   
                wall2.setPoints([
                    {x: 96, y: 209}, 
                    {x: 43, y: 211}, 
                    {x: -1, y: 199}, 
                    {x: 7, y: 126},
                    {x: 54, y: 127}, 
                    {x: 41, y: 89}, 
                    {x: 98, y: 60}, 
                    {x: 114, y: 95},
                    {x: 159, y: -3}, 
                    {x: 290, y: 66}, 
                    {x: 251, y: 137}, 
                    {x: 296, y: 155},
                    {x: 289, y: 199}, 
                    {x: 260, y: 213}, 
                    {x: 149, y: 213}, 
                    {x: 77, y: 261}
                ]); 
                wall2.setClosePath(true);
                wall2.setSegments([1, 2, 4, 4, 4, 4, 2, 2]);
                wall2.translate(-400, -150);
                wall2.setThickness(10);
                wall2.setTall(80);
                wall2.setElevation(wall2.getTall()/2);
                wall2.s({
                    'shape3d.resolution': 20,
                    'shape.border.width': 10,                    
                    'shape.background': null,
                    'repeat.uv.length': 32,
                    'all.uv.scale': [1, 2],
                    'all.image': 'brick2'
                });                                 
    
                wall3 = new ht.Shape();
                dataModel.add(wall3);   
                wall3.setThickness(20);
                wall3.setPoints([
                    {x: -500, y: 0},
                    {x: -500, y: -200},
                    {x: 500, y: -200},
                    {x: 500, y: 0}                    
                ]);               
                wall3.setTall(160);
                wall3.setElevation(wall3.getTall()/2);
                wall3.s({
                    'shape.background': null,
                    'shape.border.width': 10,
                    'left.color': 'yellow',
                    'right.color': 'red',
                    'front.image': 'brick1',
                    'front.uv.scale': [1, 3],
                    'back.image': 'brick1',
                    'back.uv.scale': [1, 3],
                    'repeat.uv.length': 64
                });                 
            }             
            
        </script>
    </head>
    <body onload="init();">                                
    </body>
</html>
