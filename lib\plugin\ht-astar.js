!function(D,G,M){"use strict";var P,u,S=D.ht;S.Astar={};function p(D){for(var G=D,u=[];G.parent;)u.unshift(G),G=G.parent;return u}function y(D,G){G=G||{},this.nodes=[],this.diagonal=!!G.diagonal,this.grid=[];for(var u=0;u<D.length;u++){this.grid[u]=[];for(var M=0,y=D[u];M<y.length;M++){var O=new _(u,M,y[M]);this.grid[u][M]=O,this.nodes.push(O)}}this.$13x()}function _(D,G,u){this.x=D,this.y=G,this.weight=u}function h(D){this.content=[],this.scoreFunction=D}P={search:function(D,G,u,M){D.$6x();var y=(y=(M=M||{}).heuristic)||(D.diagonal?P.$4x.diagonal:P.$4x.manhattan),O=M.closest||!1,_=M.punish,m=new h(function(D){return D.f}),c=G;for(G.h=y(G,u),D.$7x(G),m.push(G);0<m.size();){var X=m.pop();if(X===u)return p(X);X.closed=!0;for(var Z=D.neighbors(X),I=0,l=Z.length;I<l;++I){var H,g,j=Z[I];j.closed||j.$9x()||(H=X.g+j.getCost(X,_),(!(g=j.visited)||H<j.g)&&(j.visited=!0,j.parent=X,j.h=j.h||y(j,u),j.g=H,j.f=j.g+j.h,D.$7x(j),O&&(j.h<c.h||j.h===c.h&&j.g<c.g)&&(c=j),g?m.$10x(j):m.push(j)))}}return O?p(c):[]},$4x:{manhattan:function(D,G){return Math.abs(G.x-D.x)+Math.abs(G.y-D.y)},diagonal:function(D,G){var u=Math.sqrt(2),M=Math.abs(G.x-D.x),G=Math.abs(G.y-D.y);return+(M+G)+(u-2)*Math.min(M,G)}},$5x:function(D){D.f=0,D.g=0,D.h=0,D.visited=!1,D.closed=!1,D.parent=null}},y.prototype.$13x=function(){this.dirtyNodes=[];for(var D=0;D<this.nodes.length;D++)P.$5x(this.nodes[D])},y.prototype.$6x=function(){for(var D=0;D<this.dirtyNodes.length;D++)P.$5x(this.dirtyNodes[D]);this.dirtyNodes=[]},y.prototype.$7x=function(D){this.dirtyNodes.push(D)},y.prototype.neighbors=function(D){var G=[],u=D.x,D=D.y,M=this.grid;return M[u-1]&&M[u-1][D]&&G.push(M[u-1][D]),M[u+1]&&M[u+1][D]&&G.push(M[u+1][D]),M[u]&&M[u][D-1]&&G.push(M[u][D-1]),M[u]&&M[u][D+1]&&G.push(M[u][D+1]),this.diagonal&&(M[u-1]&&M[u-1][D-1]&&G.push(M[u-1][D-1]),M[u+1]&&M[u+1][D-1]&&G.push(M[u+1][D-1]),M[u-1]&&M[u-1][D+1]&&G.push(M[u-1][D+1]),M[u+1]&&M[u+1][D+1]&&G.push(M[u+1][D+1])),G},y.prototype.toString=function(){for(var D=[],G=this.grid,u=0;u<G.length;u++){for(var M=[],y=G[u],O=0;O<y.length;O++)M.push(y[O].weight);D.push(M.join(" "))}return D.join("\n")},_.prototype.toString=function(){return"["+this.x+" "+this.y+"]"},_.prototype.getCost=function(D,G){var u=D&&D.x!=this.x&&D.y!=this.y?1.41421*this.weight:this.weight;if(G){if(!D)return u;var M=D.parent;if(!M)return u;if(1e-5<Math.abs(M.x+this.x-2*D.x))return u+G;if(1e-5<Math.abs(M.y+this.y-2*D.y))return u+G}return u},_.prototype.$9x=function(){return 0===this.weight},h.prototype={push:function(D){this.content.push(D),this.$11x(this.content.length-1)},pop:function(){var D=this.content[0],G=this.content.pop();return 0<this.content.length&&(this.content[0]=G,this.$12x(0)),D},remove:function(D){var G=this.content.indexOf(D),u=this.content.pop();G!==this.content.length-1&&(this.content[G]=u,this.scoreFunction(u)<this.scoreFunction(D)?this.$11x(G):this.$12x(G))},size:function(){return this.content.length},$10x:function(D){this.$11x(this.content.indexOf(D))},$11x:function(D){for(var G=this.content[D];0<D;){var u=(D+1>>1)-1,M=this.content[u];if(!(this.scoreFunction(G)<this.scoreFunction(M)))break;this.content[u]=G,this.content[D]=M,D=u}},$12x:function(D){for(var G=this.content.length,u=this.content[D],M=this.scoreFunction(u);;){var y,O=D+1<<1,_=O-1,m=null;if(_<G&&(y=this.content[_],(y=this.scoreFunction(y))<M&&(m=_)),O<G&&(_=this.content[O],this.scoreFunction(_)<(null===m?M:y)&&(m=O)),null===m)break;this.content[D]=this.content[m],this.content[m]=u,D=m}}},u={astar:P,Graph:y},D.AStar=u.astar,D.AStar.Graph=u.Graph;D=S.Astar.Finder=function(D,G){this.gv=D,this.$13x(G),this.refreshMap()};(D.prototype={}).constructor=D,G.defineProperties(D.prototype,{_debugInfo:{get:function(){return this.gv._astarDebugInfo},set:function(D){this.gv._astarDebugInfo=D}}}),D.prototype.$13x=function(D){var G=this.$14x={},u=(D=D||{}).gridSize||10;G.gridSizeX=D.gridSizeX||u,G.gridSizeY=D.gridSizeY||u,G.rect=D.rect,G.filter=D.filter,G.extendBlocks=D.extendBlocks||3,G.fastOverlap=D.fastOverlap===M||D.fastOverlap,G.nodeRectExtend=D.nodeRectExtend||0,G.diagonal=D.diagonal===M||D.diagonal,G.turnPunish=D.turnPunish===M?.1:D.turnPunish,G.simplify=D.simplify===M||D.simplify,G.toGridCenter=D.toGridCenter!==M&&D.toGridCenter,G.closest=D.closest===M||D.closest},D.prototype.refreshMap=function(){var D=this,G=D.$14x,u=G.gridSizeX,M=G.gridSizeY,y=G.extendBlocks,G=(G.rect?_=G.rect:(_=D.$15x(),S.Default.grow(_,u*y,M*y)),S.Default.grow(_,u,M),Math.floor(_.x/u)*u),y=Math.floor(_.y/M)*M,O=Math.ceil(_.width/u),_=Math.ceil(_.height/M);D.$16x={x:G,y:y,w:O*u,h:_*M,gridX:u,gridY:M,xLen:O,yLen:_},D.$17x(),D.$21x()},D.prototype.$15x=function(){var G,u=this.gv;if(u instanceof S.graph.GraphView)return u.getContentRect();var M=S.Default.unionRect;return u.dm().each(function(D){u.isVisible(D)&&D.getRect&&(G=M(G,D.getRect()))}),G},D.prototype.$17x=function(){for(var D,G,u=this,M=u.$16x,y=M.xLen,O=M.yLen,_=new Array(y),m=0;m<y;m++)for(_[m]=D=new Array(O),G=O;G--;)D[G]=1;var c=u.$14x.filter;for(u.gv.dm().each(function(D){c&&!1===c(D)||u._debugInfo&&(D===u._debugInfo.grid||D==u._debugInfo.path)||u.$1cw(_,D)}),m=0;m<y;m++)_[m][0]=1,_[m][O-1]=1;for(G=0;G<O;G++)_[0][G]=1,_[y-1][G]=1;u.grid=_;M=new AStar.Graph(_,{diagonal:u.$14x.diagonal});u.graph=M},D.prototype.$18x=function(D,G,u,M,y,O,_,m){var c=this.$19x,X=c(u-D,y-_,M-G,O-m);if(X<=1e-6&&-1e-6<=X)return!1;_=c(y-D,y-_,O-G,O-m)/X;if(_<0||1<_)return!1;m=c(u-D,y-D,M-G,O-G)/X;return!(m<0||1<m)},D.prototype.$19x=function(D,G,u,M){return D*M-G*u},D.prototype.$1aw=function(D,G,u,M){for(var y=[-(G[1]-D[1]),G[0]-D[0]],O=0<=y[0]*(u[0]-D[0])+y[1]*(u[1]-D[1]),_=0,m=M.length;_<m;_+=2)if(0<=y[0]*(M[_]-D[0])+y[1]*(M[_+1]-D[1])==O)return!1;return!0},D.prototype.$1bw=function(D,G){for(var u,M,y=0,O=D.length;y<O;y+=2)if(this.$1aw([D[y],D[y+1]],[D[u=y===O-2?0:y+2],D[u+1]],[D[M=u===O-2?0:u+2],D[M+1]],G))return!1;for(y=0,O=G.length;y<O;y+=2)if(this.$1aw([G[y],G[y+1]],[G[u=y===O-2?0:y+2],G[u+1]],[G[M=u===O-2?0:u+2],G[M+1]],D))return!1;return!0},D.prototype.$1cw=function(D,G){if(G.getRect){var u,M,y,O,_,m,c,X,Z,I,l,H,g=this,j=G.getRect(),P=(S.Default.grow(j,g.$14x.nodeRectExtend),g.$1dw({x:j.x,y:j.y})),j=g.$1dw({x:j.x+j.width,y:j.y+j.height}),p=g.$14x.fastOverlap;for(p||(G=G.getCorners(),X=[],G.forEach(function(D){X.push(D.x,D.y)}),m=g.$14x.gridSizeX/2,c=g.$14x.gridSizeY/2),G=Math.max(0,P.x),M=Math.min(j.x,D.length-1),O=Math.max(0,P.y),_=Math.min(j.y,D.length?D[0].length-1:-1),u=G;u<=M;u++)for(y=O;y<=_;y++)p?D[u][y]=0:(Z=(H=g.$1ew({x:u,y:y})).x-m,I=H.x+m,l=H.y-c,H=H.y+c,g.$1bw(X,[Z,l,Z,H,I,H,I,l])&&(D[u][y]=0))}},D.prototype.$1dw=function(D,G){var u=this.$16x,M=(D.x-u.x)/u.gridX,D=(D.y-u.y)/u.gridY;return!1!==G&&(M=Math.round(M),D=Math.round(D)),{x:M,y:D}},D.prototype.$1ew=function(D){var G=this.$16x;return{x:D.x*G.gridX+G.x,y:D.y*G.gridY+G.y}},D.prototype.$1fw=function(D){var G=this.$16x,u=G.xLen,G=G.yLen;if(0<=D.x&&D.x<u&&0<=D.y&&D.y<G)return D;D={x:D.x,y:D.y};return D.x<0?D.x=0:D.x>=u&&(D.x=u-1),D.y<0?D.y=0:D.y>=G&&(D.y=G-1),D},D.prototype.findPath=function(D,G){var u,M,y=this,O=y.$1dw(D),_=y.$1dw(G),m=y.$1fw(O),c=y.$1fw(_);if(m!==O&&c!==_){var X=y.$16x,Z=X.x-X.gridX/2,I=X.y-X.gridY/2,l=Z+X.gridX*X.xLen,X=I+X.gridY*X.yLen;if(!(y.$18x(D.x,D.y,G.x,G.y,Z,I,l,I)||y.$18x(D.x,D.y,G.x,G.y,l,I,l,X)||y.$18x(D.x,D.y,G.x,G.y,l,X,Z,X)||y.$18x(D.x,D.y,G.x,G.y,Z,X,Z,I)))return[D,G]}m!==O&&(u=!0,O=m),c!==_&&(M=!0,_=c);var l=y.graph.grid[O.x][O.y],X=y.graph.grid[_.x][_.y],H=AStar.search(y.graph,l,X,{closest:y.$14x.closest,punish:y.$14x.turnPunish});if(!H||!H.length)return null;var g=[];g.push(D),u&&g.push(y.$1ew(H[0]));for(var j=1,P=H.length;j<P-1;j++)g.push(y.$1ew(H[j]));return M?(g.push(y.$1ew(H[P-1])),g.push(G)):(Z=H[P-1],_.x!==Z.x||_.y!==Z.y||y.$14x.toGridCenter?g.push(y.$1ew(Z)):g.push(G)),y.$14x.simplify&&(g=y.simplifyPath(g)),y.$20x=g,y.$21x(),g},D.prototype.debugOn=function(D){this.debugFlag=!0,this.debugSettings=D||{},this.$21x()},D.prototype.simplifyPath=function(D){var G=D.length;if(G<=2)return D;for(var u,M=[D[0],D[1]],y=M[0],O=M[1],_=2;_<G;_++)u=D[_],Math.abs((O.x-y.x)*(u.y-y.y)-(O.y-y.y)*(u.x-y.x))<1e-5?O=M[M.length-1]=u:(y=O,M.push(O=u));return M},D.prototype.$21x=function(){var D=this;if(D.$22x(),this.debugFlag){var G=D.grid;if(G){var u=D.gv;if(!1!==D.debugSettings.$16x){for(var M,y,O,_,m=[],c=[],X=D.$14x.gridSizeX/2,Z=D.$14x.gridSizeY/2,I=G.length,l=I?G[0].length:0,H=1;H<I-1;H++)for(var g=1;g<l-1;g++)G[H][g]||(M=(_=D.$1ew({x:H,y:g})).x-X,y=_.x+X,O=_.y-Z,_=_.y+Z,m.push({x:M,y:O},{x:M,y:_},{x:y,y:_},{x:y,y:O}),c.push(1,2,2,2,5));var j=new S.Shape;j.s("shape.border.color","rgba(50, 50, 50, 0.2)"),j.s("shape.border.width",1),j.s("shape.background","rgba(200, 50, 100, 0.2)"),j.s("2d.editable",!1),j.s("2d.selectable",!1),j.setPoints(m),j.setSegments(c),D._debugInfo={grid:j},u.dm().add(j)}!1!==D.debugSettings.path&&(m=new S.Shape,D.$20x&&(m.setPoints(D.$20x),m.s("shape.border.color","red"),m.s("shape.border.width",1),m.s("2d.editable",!1),m.s("2d.selectable",!1),m.s("shape.background",null),D._debugInfo||(D._debugInfo={}),D._debugInfo.path=m,u.dm().add(m)))}}},D.prototype.debugOff=function(){this.debugFlag=!1,this.$22x()},D.prototype.$22x=function(){var D,G,u,M=this;M._debugInfo&&(D=M._debugInfo.grid,G=M._debugInfo.path,u=M.gv,D&&u.dm().remove(D),G&&u.dm().remove(G),M._debugInfo=null)}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);