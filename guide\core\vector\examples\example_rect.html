<!DOCTYPE html>
<html>
    <head>
        <title>Rect</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>     
        <script src="../../../../lib/core/ht.js"></script>   
        <script>
            function init() {
                var dataModel = new ht.DataModel(),
                        graphView = new ht.graph.GraphView(dataModel),
                        view = graphView.getView();

                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function(e) {
                    graphView.invalidate();
                }, false);
                
                points = [
                    96, 209, 43, 211, -1, 199, 7, 126,
                    54, 127, 41, 89, 98, 60, 114, 95,
                    159, -3, 290, 66, 251, 137, 296, 155,
                    289, 199, 260, 213, 149, 213, 77, 261, 96, 209
                ];
                segments = [1, 2, 4, 4, 4, 4, 2, 2, 2];
                
                ht.Default.setImage('cloud', {
                    width: 300,
                    height: 300,
                    comps: [
                        {
                            type: 'shape',
                            background: '#F50010',
                            gradient: 'linear.north',
                            points: points,
                            segments: segments
                        },
                        {
                            type: 'shape',
                            background: 'yellow',
                            points: points,
                            segments: segments,
                            relative: true,                            
                            rect: [17, 0.3, 0.3]
                        } 
                    ]
                });
                ht.Default.setImage('cloud-relative', {
                    width: 300,
                    height: 300,
                    comps: [
                        {
                            type: 'image',
                            name: 'cloud',
                            relative: true,
                            rect: [0, 0, 0.5, 0.5]
                        },
                        {
                            type: 'image',
                            name: 'cloud',
                            relative: true,
                            rect: [24, 0.5, 0.5],
                            rotation: Math.PI/4
                        },
                        {
                            type: 'image',
                            name: 'cloud',
                            rect: [150, 0, 150, 150]
                        },
                        {
                            type: 'image',
                            name: 'cloud',
                            rect: [22, 150, 150]
                        },
                        {
                            type: 'image',
                            name: 'cloud',                            
                            relative: true,                            
                            rect: [17, 0.8, 0.2]
                        }
                    ]
                });                               

                var node = new ht.Node();
                node.setPosition(100, 120);
                node.setSize(160, 160);
                node.setImage('cloud-relative');
                dataModel.add(node);                                

                graphView.setEditable(true);
            }

        </script>
    </head>
    <body onload="init();">
    </body>
</html>
