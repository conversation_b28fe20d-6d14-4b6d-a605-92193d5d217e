<!DOCTYPE html>
<html>
    <head>
        <title>Host</title>
        <meta charset="UTF-8">   
        <style>
            html, body {
                padding: 0px;
                margin: 0px;                
            }            
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style> 
        <script>
            htconfig = {
                Default:{                     
                    toolTipDelay: 300
                }
            };            
        </script>
        <script src="../../../../lib/core/ht.js"></script>   
        <script>    
            
            ht.Default.setImage('port', 32, 32, 'data:image/png;base64,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');
                        
            function init(){                                                                                                                            
                dataModel = new ht.DataModel();
                g3d = new ht.graph3d.Graph3dView(dataModel);                  
                view = g3d.getView();  
                view.className = 'main';
                document.body.appendChild(view);    
                window.addEventListener('resize', function (e) {
                    g3d.invalidate();
                }, false);   
                
                g3d.setEye([0, 300, 600]);
                g3d.enableToolTip();
                g3d.getToolTip = function(e){
                    var data = this.getDataAt(e);
                    if(data){
                        return '<pre>' + JSON.stringify(data.getStyleMap(), null, 4) + '</pre>';
                    }
                    return null;
                };                
                g3d.getMoveMode = function(e){
                    return 'xyz';
                };
                g3d.getBrightness = function(data){                   
                    if(data.s('isFocused')){
                        return 0.7;
                    }
                    return null;
                };
                lastFocusData = null;
                g3d.getView().addEventListener('mousemove', function(e){
                    var data = g3d.getDataAt(e);
                    if(data !== lastFocusData){
                        if(lastFocusData){
                            lastFocusData.s('isFocused', false);
                        }
                        if(data){
                            data.s('isFocused', true);
                        }
                        lastFocusData = data;
                    }
                }); 
                
     
                floor = createNode([0, 0, 0], [600, 5, 400]).s({
                    'all.color': '#A0A0A0',
                    'label': 'This is a floor',
                    'label.face': 'top',
                    'label.background': 'yellow',
                    'label.position': 22,
                    'label.t3': [10, 0, -10],
                    'label.font': '28px arial, sans-serif'
                });                
                table = createNode([0, 120, 0], [400, 10, 280], floor).s({
                    'shape3d': 'cylinder',
                    'shape3d.side': 60,
                    'shape3d.color': '#E5BB77',
                    'label': 'This is a table',
                    'label.face': 'top',
                    'label.background': 'yellow',
                    'label.position': 23,
                    'label.t3': [0, 0, -10],
                    'label.font': '20px arial, sans-serif'                    
                });                
                foot1 = createNode([100, 60, 80], [20, 110, 20], table).s({
                    'shape3d': 'cylinder',
                    'shape3d.color': '#E5BB77',
                    'label': 'Foot 1 hosting on table',                    
                    'label.background': 'yellow', 
                    'label.position': 20
                });   
                foot2 = createNode([-100, 60, 80], [20, 110, 20], table).s({
                    'shape3d': 'cylinder',
                    'shape3d.color': '#E5BB77',
                    'label': 'Foot 2 hosting on table',                    
                    'label.background': 'yellow', 
                    'label.position': 14                    
                }); 
                foot3 = createNode([100, 60, -80], [20, 110, 20], table).s({
                    'shape3d': 'cylinder',
                    'shape3d.color': '#E5BB77',
                    'label': 'Foot 3 hosting on table',                    
                    'label.background': 'yellow', 
                    'label.position': 25,
                    'label.reverse.flip': true
                }); 
                foot4 = createNode([-100, 60, -80], [20, 110, 20], table).s({
                    'shape3d': 'cylinder',
                    'shape3d.color': '#E5BB77',
                    'label': 'Foot 4 hosting on table',                    
                    'label.background': 'yellow', 
                    'label.position': 21,
                    'label.reverse.flip': true
                });
                chassis = createNode([0, 150, 0], [100, 50, 60], table).s({
                    'all.color': '#2e2f32',
                    'front.color': '#BDC3C7',
                    'back.uv.scale': [2, 1],
                    'note': 'This is a chassis with two panels and 8 ports',
                    'note.face': 'top',
                    'note.position': 7,
                    'note.t3': [0, 0, 10],
                    'note.autorotate': true
                });
                panel1 = createNode([0, 162, 30], [90, 20, 2], chassis).s({
                    'note': 'Panel 1',
                    'note.r3': [0, -Math.PI/4, 0]
                }); 
                for(var i=0; i<4; i++){
                    var port = createNode([-30+i*20, 162, 31], [16, 12, 2], panel1).s({
                        'all.color': '#303030',  
                        'front.image': 'port'
                    });
                    if(i === 0){
                        port.s({
                           'all.blend': 'red',
                           'note': 'Critical Alarm',
                           'note.position': 1,
                           'note.t3': [0, 0, 5],
                           'note.r3': [Math.PI/10, 0, 0],
                           'note.background': 'red'
                        });
                    }
                }
                panel2 = createNode([0, 138, 30], [90, 20, 2], chassis).s({
                    'note': 'Panel 2',
                    'note.r3': [Math.PI/7, Math.PI/6, 0]
                }); 
                for(var i=0; i<4; i++){
                    createNode([-30+i*20, 138, 31], [16, 12, 2], panel2).s({
                        'all.color': '#303030',  
                        'front.image': 'port'
                    });
                }                
            } 
            
            function createNode(p3, s3, host){
                var node = new ht.Node();
                node.p3(p3);
                node.s3(s3);
                node.setHost(host);
                node.s({
                    'wf.visible': 'selected',
                    'wf.color': '#FF6B10',
                    'wf.width': 2,
                    'wf.short': true                    
                });
                dataModel.add(node);
                return node;
            }
                        
          
        </script>
    </head>
    <body onload="init();">                  
              
    </body>
</html>