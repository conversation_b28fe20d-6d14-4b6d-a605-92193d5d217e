<!DOCTYPE html>
<html>
    <head>
        <title>ContextMenu</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <script src="../../../../lib/core/ht.js"></script>  
        <script src="key.js"></script>
        <script src="../../../../lib/plugin/ht-contextmenu.js"></script>
        <style>
            .ht-widget-contextmenu ul {
                background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAABAQMAAADO7O3JAAAAA3NCSVQICAjb4U/gAAAABlBMVEXi4+P///9V63aNAAAACXBIWXMAAAsSAAALEgHS3X78AAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M1cbXjNgAAABZ0RVh0Q3JlYXRpb24gVGltZQAwNy8wNi8xMZANh+UAAAAKSURBVAiZY3AAAABCAEGV6TQ4AAAAAElFTkSuQmCC") repeat-y rgb(240, 240, 240) !important;
                background-position: -webkit-calc(1.4em + 4px) 0px !important;
                background-position: calc(1.4em + 4px) 0px !important;
                border-radius: 0 !important;
            }
            .ht-widget-contextmenu .menu-item {
                background: rgba(0,0,0,0) !important;
                color: black !important;
                border: 1px solid rgba(0,0,0,0) !important;
                border-radius: 2px !important;
                margin: 3px 2px !important;
            }
            .ht-widget-contextmenu .menu-item.menu-item-hover {
                background: -webkit-linear-gradient(top, rgba(193,222,255,0.2),rgba(193,222,255,0.4)) !important;
                background: linear-gradient(to bottom, rgba(193,222,255,0.2),rgba(193,222,255,0.4)) !important;
                color: black !important;
                border: 1px solid rgb(183,212,246) !important;
            }
            .ht-widget-contextmenu .menu-item.disabled {
                color: #888 !important;
                background: rgba(0,0,0,0) !important;
            }
            .ht-widget-contextmenu .menu-item.disabled.menu-item-hover {
                color: #888 !important;
                background: rgba(0,0,0,0) !important;
                border: 1px solid rgba(0,0,0,0) !important;
            }
            .ht-widget-contextmenu .prefix {
                margin-right: 0.8em !important;
            }
            .ht-widget-contextmenu .suffix {
                margin-left: 3em !important;
                margin-right: 1em !important;
            }
            .ht-widget-contextmenu .separator {
                margin-left: -webkit-calc(1.4em + 5px) !important;
                margin-left: calc(1.4em + 5px) !important;
                height: 2px !important;
                background-image: -webkit-linear-gradient(top, rgb(226,226,226), rgb(226,226,226) 50%,rgb(252,252,252) 50%,rgb(252,252,252)) !important;
                background-image: linear-gradient(to bottom, rgb(226,226,226), rgb(226,226,226) 50%,rgb(252,252,252) 50%,rgb(252,252,252)) !important;
            }
        </style>
        
        <!--[if IE 9]>
            <style>
                .ht-widget-contextmenu ul {
                    background-position: 1.6em 0 !important;
                }
                .ht-widget-contextmenu .menu-item.menu-item-hover {
                    background: rgba(193,222,255,0.4) !important;
                }
                .ht-widget-contextmenu .separator {
                    margin-left: calc(1.6em + 1px) !important;
                    background: url(data:image/png;base64,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) repeat-x !important;
                }
            </style>
        <![endif]-->
        
        <script type="text/javascript">
            ht.Default.setImage('contextmenu_icon', "settings.png");
            var iconSrc = 'contextmenu_icon';
            
            function init() {
                var json = [
                    {
                        label: "CheckMenuItems",
                        items: [
                            {
                                label: "Check1",
                                icon: iconSrc,
                                type: "check"
                            },
                            {
                                label: "Check2",
                                icon: iconSrc,
                                type: "check"
                            },
                            {
                                label: "Check3",
                                icon: iconSrc,
                                type: "check",
                                items: [
                                    {
                                        label: "AAAA"
                                    },
                                    {
                                        label: "BBBB"
                                    },
                                    {
                                        label: "CCCC"
                                    },
                                    {
                                        label: "DDDD"
                                    },
                                    {
                                        label: "EEEE"
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        label: "RadioMenuItems",
                        items: [
                            {
                                label: "Radio1",
                                icon: iconSrc,
                                type: "radio",
                                groupId: 1
                            },
                            {
                                label: "Radio2",
                                icon: iconSrc,
                                type: "radio",
                                groupId: 1
                            },
                            {
                                label: "Radio3",
                                icon: iconSrc,
                                type: "radio",
                                groupId: 1
                            }
                        ]
                    },
                    "separator",
                    {
                        label: "Menu1(disabled)",
                        disabled: true
                    },   
                    {
                        label: "Menu2",
                        action: function(item, event) {
                            alert("you clicked:" + item.label + ",this=" + this);
                        },
                        scope: "hello"
                    },
                    {
                        label: "Menu3",
                        icon: iconSrc,
                        action: function(item) {
                            alert(item.label);
                        },
                        items: [
                            {
                                label: "Homepage",
                                href: "http://www.hightopo.com",
                                linkTarget: "_blank",
                                key: [Key.ctrl, Key.enter],
                                suffix: "Ctrl+Enter",
                                preventDefault: false
                            },
                            {
                                label: "submenu2",
                                action: function(item) {
                                    alert(item.label);
                                }
                            }
                        ]
                    }
                ];
                var contextmenu = new ht.widget.ContextMenu(json);
                contextmenu.enableGlobalKey();
                contextmenu.addTo(document.getElementById("div"));
            }
        </script>
    </head>
    <body onload="init();">
        <div id="div" style="width: 200px; height: 100px; background: #ddd; text-align: center;">Right Click Here!</div>
    </body>
</html>
