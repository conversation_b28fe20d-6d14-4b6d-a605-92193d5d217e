!function(a,i){"use strict";function O(a){return(/ble$/.test(a)||/ed$/.test(a)||q.IsGetter[a]?"is":"get")+a.charAt(0).toUpperCase()+a.slice(1)}var q=a.ht;q.QuickFinder=function(a,i,X,O,q){var l=this;l.$9j={},l.$1j=a,l.$2j=i,l.$3j=X,l.$4j=O||l.getValue,l.$5j=q||l.$5j,a.each(l.$7j,l),a.mm(l.$11j,l,!0),a.md(l.$12j,l,!0)},q.Default.def(q.QuickFinder,i,{$6j:"__ht__null__",getValueFunc:function(){return this.$4j},getFilterFunc:function(){return this.$5j},$11j:function(a){"add"===a.kind?this.$7j(a.data):"remove"===a.kind?this.$8j(a.data):"clear"===a.kind&&(this.$9j={})},$12j:function(a){var i=this,X=i.$3j,O=i.$2j;i.$5j(a.data)&&(null==X&&O===a.property||"style"===X&&"s:"+O===a.property||"attr"===X&&"a:"+O===a.property)&&((X=i.$10j(a.oldValue))&&X.remove(a.data),i.$7j(a.data))},$10j:function(a){return a=null==a?this.$6j:a,this.$9j[a]},find:function(a){a=this.$10j(a);return a?a.toList():new q.List},findFirst:function(a){a=this.$10j(a);return!a||a.isEmpty()?null:a.get(0)},$7j:function(a){var i,X,O=this;O.$5j(a)&&(i=O.$4j(a),(X=O.$10j(i))||(X=new q.List,i=null==i?O.$6j:i,O.$9j[i]=X),X.add(a))},$8j:function(a){var i,X,O=this;O.$5j(a)&&(i=O.$4j(a),(X=O.$10j(i))&&(X.remove(a),X.isEmpty()&&(i=null==i?O.$6j:i,delete O.$9j[i])))},dispose:function(){this.$1j.umm(this.$11j,this),this.$1j.umd(this.$12j,this),delete this.$1j},getDataModel:function(){return this.$1j},getAccessType:function(){return this.$3j},getPropertyName:function(){return this.$2j},$5j:function(a){return!(null==this.$3j&&this.$4j===this.getValue&&!a[O(this.$2j)])},getValue:function(a){var i=this.$3j,X=this.$2j;return null==i?a[O(X)]():"style"===i?a.s(X):"attr"===i?a.a(X):void 0}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);