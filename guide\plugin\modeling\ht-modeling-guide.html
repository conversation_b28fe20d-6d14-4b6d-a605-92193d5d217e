<!doctype html>
<html>
    <head>
        <title>HT for Web Modeling Manual</title>
        <meta charset="UTF-8">    
        <meta name="viewport" content="user-scalable=yes, width=1024">
        <style type="text/css"> 
            h1, h2, h3, h4, h5, h6, p, blockquote {
                margin: 0;
                padding: 0;
            }
            body {
                font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB", Arial, sans-serif;
                font-size: 13px;
                line-height: 18px;
                color: #737373;
                background-color: white; 
                margin: 10px 13px 10px 13px;
            }
            table {
                margin: 10px 0 15px 0;
                border-collapse: collapse;
            }
            td,th {	
                border: 1px solid #ddd;
                padding: 3px 10px;
            }
            th {
                padding: 5px 10px;	
            }
            a {
                color: #0069d6;
            }
            a:hover {
                color: #0050a3;
                text-decoration: none;
            }
            a img {
                border: none;
            }
            p {
                margin-bottom: 9px;
            }
            h1, h2, h3, h4, h5, h6 {
                color: #404040;
                line-height: 36px;
            }
            h1 {
                margin-bottom: 18px;
                font-size: 30px;
            }
            h2 {
                font-size: 24px;
            }
            h3 {
                font-size: 18px;
            }
            h4 {
                font-size: 16px;
            }
            h5 {
                font-size: 14px;
            }
            h6 {
                font-size: 13px;
            }
            hr {
                margin: 0 0 19px;
                border: 0;
                border-bottom: 1px solid #ccc;
            }
            blockquote {
                padding: 13px 13px 21px 15px;
                margin-bottom: 18px;
                font-family:georgia,serif;
                font-style: italic;
            }
            blockquote:before {
                content:"\201C";
                font-size:40px;
                margin-left:-10px;
                font-family:georgia,serif;
                color:#eee;
            }
            blockquote p {
                font-size: 14px;
                font-weight: 300;
                line-height: 18px;
                margin-bottom: 0;
                font-style: italic;
            }
            code, pre {
                font-family: Monaco, Andale Mono, Courier New, monospace;
            }
            code {
                background-color: #fee9cc;
                color: rgba(0, 0, 0, 0.75);
                padding: 1px 3px;
                font-size: 12px;
                -webkit-border-radius: 3px;
                -moz-border-radius: 3px;
                border-radius: 3px;
            }
            pre {
                display: block;
                padding: 14px;
                margin: 0 0 18px;
                line-height: 16px;
                font-size: 11px;
                border: 1px solid #d9d9d9;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            pre code {
                background-color: #fff;
                color:#737373;
                font-size: 11px;
                padding: 0;
            }
            sup {
                font-size: 0.83em;
                vertical-align: super;
                line-height: 0;
            }
            * {
                -webkit-print-color-adjust: exact;
            }
            @media screen and (min-width: 914px) {
                body {
                    width: 854px;
                    margin:10px auto;
                }
            }
            @media print {
                body,code,pre code,h1,h2,h3,h4,h5,h6 {
                    color: black;
                }
                table, pre {
                    page-break-inside: avoid;
                }
            }                        
            iframe{
                width: 100%;
                border: 1px solid #34495E;
                margin: 0;                
            }            
            .logo{
                vertical-align: middle;
            }

        </style>        
        <script>
            function init() {
                var logoSrc = 'data:image/png;base64,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',
                    logos = document.querySelectorAll('.logo'),
                    i = 0;
                for (; i < logos.length; i++) {
                    logos[i].src = logoSrc;
                }
                
                var iframes = document.querySelectorAll('iframe'),
                    func = function (){};
                for (i=0; i < iframes.length; i++) {
                    var iframe = iframes[i];
                    
                    // a small hack to make it work on android
                    iframe.ontouchstart = func;
                    
                    var div = document.createElement('div');
                    div.innerHTML = '<a href="' + iframe.src + '" target="_blank">Run→</a>';
                    iframe.parentNode.insertBefore(div, iframe);
                }   
            }
        </script>    
    </head>
    <body onload="init();">
        <a href="http://www.hightopo.com"><img class="logo"></a>HT for Web Modeling Manual
        <hr style="margin: 1px 0 20px 0">
<p>Index</p>

<ul><li><a href="#overview">Overview</a></li><li><a href="#model-basic">Model Basic</a></li><li><a href="#model-register">Model Register</a></li><li><a href="#modeling-function">Modeling Function</a><ul><li><a href="#createboxmodel">createBoxModel</a></li><li><a href="#createspheremodel">createSphereModel</a></li><li><a href="#createsmoothspheremodel">createSmoothSphereModel</a></li><li><a href="#createcylindermodel">createCylinderModel</a></li><li><a href="#createsmoothcylindermodel">createSmoothCylinderModel</a></li><li><a href="#createconemodel">createConeModel</a></li><li><a href="#createsmoothconemodel">createSmoothConeModel</a></li><li><a href="#createtorusmodel">createTorusModel</a></li><li><a href="#createsmoothtorusmodel">createSmoothTorusModel</a></li><li><a href="#createroundrectmodel">createRoundRectModel</a></li><li><a href="#createstarmodel">createStarModel</a></li><li><a href="#createrectmodel">createRectModel</a></li><li><a href="#createtrianglemodel">createTriangleModel</a></li><li><a href="#createrighttrianglemodel">createRightTriangleModel</a></li><li><a href="#createparallelogrammodel">createParallelogramModel</a></li><li><a href="#createtrapezoidmodel">createTrapezoidModel</a></li><li><a href="#createextrusionmodel">createExtrusionModel</a></li><li><a href="#createringmodel">createRingModel</a></li><li><a href="#createsmoothringmodel">createSmoothRingModel</a></li><li><a href="#createframemodel">createFrameModel</a></li></ul></li><li><a href="#model-attribute">Model Attribute</a></li><li><a href="#model-group">Model Group</a></li><li><a href="#data-binding">Data Binding</a></li><li><a href="#extend-type">Extend Type</a><ul><li><a href="#symbol">Symbol</a></li><li><a href="#csgnode">CSGNode</a></li><li><a href="#csgbox">CSGBox</a></li><li><a href="#doorwindow">DoorWindow</a></li><li><a href="#csgshape">CSGShape</a></li></ul></li></ul>

<hr/>

<div id="ref_overview"></div>

<h2>Overview</h2>

<p>The <code>HT for 3D Web</code> provides <a href="../../core/3d/ht-3d-guide.html#ref_cube">hexahedral</a>, <a href="../../core/3d/ht-3d-guide.html#ref_shape3d">shape3d</a>, etc., a variety of conventional 3D models, such as predefined spheres, cylinders, etc., <a href="../../core/shape/ht-shape-guide.html">ht.Shape</a> type can achieve 3D model effects of wall, pipe and polygon, etc., this manual will introduce more flexible 3D model function to meet the modeling requirements of different industry equipment and different environment scenes.</p>

<p>The custom <code>3D</code> model has the following advantages:</p>

<ul><li>Greater flexibility to customize the <code>3D</code> model of any shape and style</li><li>Reusable, save memory, share a copy of <code>3D</code> model data with type datas</li><li>Multi-model combination, infinite nested combination, merging and merging into a single model</li><li>Custom model color, size rendering attributes and data model binding rules</li><li>Import the model of <code>Blender</code>, <code>3ds Max</code> and <code>Maya</code> and other third-party modeling tools, see <a href="../../plug-in/obj/ht-obj-guide.html">OBJ Manual</a></li></ul>

<p>This manual describes some of the extended model library functions provided by the <code>ht-modeling.js</code> modeling extension package.</p>

<div id="ref_basic"></div>

<h2>Model Basic</h2>

<p>The <code>3D</code> model is synthesized by the most basic triangular plane stitching, for example, <code>1</code> rectangle can be composed of <code>2</code> triangles, <code>1</code> cubes consist of <code>6</code>, <code>12</code> triangles, and so on, more complex models can be synthesized by many small triangular combinations. So the <code>3D</code> model is defined as the description of all triangles of the constructed model, and each triangle consists of three vertices <a href="http://en.org/wiki/Vertex_(geometry)">Vertex</a>, each vertex <code>vertex</code> determined by <code>x, y, z</code> three dimensional coordinates that <code>HT</code> uses the right-hand rule to determine the front of the three-vertex tectonic triangular surface.</p>

<p><img src="data:image/png;base64,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"></p>

<p>The simplest <code>3D</code> model description is described by a top-point group, such as <code>p1</code>-<code>p2</code>-<code>p3</code>, which is a three-point triangle that can be described as an array of <code>[p1.x, p1.y, p1.z, p2.x, p2.y, p2.z, p3.x, p3.y, p3.z]</code>, more triangles are shown in the following illustration:</p>

<p><img src="data:image/png;base64,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****************************************+8HgeUZhWdWtGNvJQIsLgnXCx7KbDVCpwNprBdScXNlJk5uZesL6R9MjIf1nKBJQISAYlApCHQ5Zn8knW5mM+a8pZoO8wxSWEVslqjhTk2iaEsajqG5UORNZbb7L4Vpg9PRW4YW2tsKnRaXRhzs8nKNYjDxqxCvPr16jbrTzYkEZAISAQkAodGoMsz+UWb8mh/98NAKV7Fco2CwrZiiw06pocMBfzwuJ2sR93loTj0XdBG3wgMQx4PwiUtVVoYTLYmfweF8Gqt0WHmv3pbMQL+Tl81pI1Qkc1IBCQCEoHfD4EuzdnW7irGez9uQHR0NKXIZISaOe7ooYU1oQfrUQdpmy+Cn68iLaSkI0NAoZrexxxdteX5DHUKIjo2mc5G+yIXFHrbR0XFIDY2AUu48Zq9ZPuRdSSvkgh0VQS0avjL9rJaXDkCms7xaBamTMVRgey8vfBzfJIiD4EuvWorNuaGGbvBFAUVQxFEfutGIkuHnvZinVpPSd9VX9lJZCiRdEQI6IXDnc/PohpOhgrpoKcUfyAJBySdOYplL1X4cXXOgV/LzxKBViEg7i8js4PFWAytdFVq1TCO6GIRYmfS+fD9fy7DfS+uZFEZ5p1nSyJhl0atgS2K/i1Mxy3muB81fGePMiHGajykPpKlelhHXg9xnvizGPg83Pc4DLer1e/73s6soEImUlPbqcmfizuu/RfW1lEwap0P2H5Dlx86BoEuy+S35Vbi7W83MZWkBca41P2keAGt8KZX0THMzoQ4CkO9PBUFTarljoG+6/XiLMuDmtntrPZEaIzmg/AMEnVjdDys0TFYsiEXIupBkkSgtQgIxmc2G5hZcQce+kMKks+5D0V+HZ1tD2CIre2oHa8XmSHrNn6MB9/JxcS7pkLPNNYh+rPEWNVY+fEDGJveGzPnliLaUD8nwbSNlKxzfngUwxITkRgbj5gRFyLXwefaARsBesXAUbQLD00xIzE+HkmJyTjvia/hMhioz+SzkL5JNk01Pp15JpIT4nlOInpc8RJ8ioabhgAMwy/D2cZ3cd1dH8JF7VyXZRrtuL6/Z9Nddr1WbMxGDUstCo96YXtvLsU3Ah5kiJfOGsOELTr4mCAn5PdJlX0jOIfxKlT1noCXGhEngkJjYo5mqqZf9qAXfhF6+kMID/s12woPoxd5qkTglxFgWga4c1bjqWuuwDsbXFC270GkFVoTuSXKNvwAVdTxOJU/Hx8Zr8JSs5s/uBfX/Ot1bCwvRG6lNxwlJFDQclPg3Twbt9zwH+T7KO3TMube9gU++XEdJf99IrpCJ2NbsBBP3Xgu3thsC2efBH2QFjx+Hd74aS801A4Y9WpsfXMmbn/tJ27M1bzeh+pP/oXb3lnJZ6OWWUE1OOmkIfCvXIxiT+RsnH75bjn6jnZJJi9qS7/0zVpYLGZY49K5qvtu+uZLLBi/VmeA3ZbA4i+sv1yax13wL5/b/Dr5fn8EhKreybh4b9CPWGJpNVrCmpL9z6r/JPwirMxVYLPb8cnPW7GUJhVJEoEjRUAhU9IaS/HS1TfCePmXWPjIWIQYqqmJoJ+xUNWrNKV45+aPgAvPQbpJE2biRfMfxH2fJWHp4rk4eQAZNH87YuscVr17SvDP88/DrhOfRWHVXpSWlqPiuwfw9Qdv0VSmb5K2VcEAajUpuPaJuVidvRdl5WUocxTjH4lleOvWB1BABh5Y/xrOnfk5pr24CdXlpSgtq8Y3N6fhp5nXYna+l07JChLPugOxO7/GOwUuJriSjP5I79ff47ouyeRzSuvgrPPSPmemSl4oq379F6+Y6PVN25aQRH30+hYJXSS1HAEfHyRBVx20lOIVSvGBX7mrxMZKoVOR0WQR7zB/VXbLO5JnSgQOQIBWagS9MZj+9Lu4aRJDZD0UayOMVMyU41UVYltdNFJSEql5pCTvDyDx2KvxwAvXIiWBSnMP8983PJaEWdzpz8HObAOGTZkAE0OAy+r8MB57AhKqvWDx2f3ICz0yBmQgJuhDebWHavgQBRuhbVPRPwaoyM+CTx2P8RO6w+9wo9IVRMakaeyvHOvqPGxLgdHWD6mGUuxYX07pXj4f9wO4k3/4lcdxJx/5rwxv3upd0Bq0sMR1o/PIrzN4EddtpTOYlRJoQCGzqiwKJ3T5leblV80QEBsiV3kefHyA6M0x1J5EU1P/y6r6xsvE9yZmwrNFReHz5TuwYXdp41fyVSJw+AgEtYjvmQbqmuENRF4SzyAFkbh1y7EwwYbjBvWBhZE+KkrtOkYEJUaTpVe79qsZr1Ag8eYuxmazHb27G/nbC1FzQb8i4zGIs7ngDOzPhBnYCpfLCw+L5GgM1OvvWYxXsrwYdtuN6MUqpllbV7N+fS+kW0OgSw2E9K/vfRL6+WpRksVkYVwRO9N/9xvixfwfVzNqpkuyjcO/7yLkii65WnsKqmA00havoyL5N5i8WCdxjt7C1Ku0F3tctQynYxF0SS1CQGhJPF5R55vSudXeIrxFwzo+qHT0l/CwTvjSTVKabxHY8qQuiYCKbu5eJ/1Z+BrOcd+CWWpokvQzZLV59kiVooWajnmH8jcUDooWfr/szWdQFBqJc8/sEw539TNDZZD/9qOQDtqgCsFGqZ2MXa1l4DE1DJIiC4Eux+Q9Xh/W7CyBNT6NTiotm56Q5g10BrMy9aqQSOvoad+YOCeylrPjR+txu5ga2E0GHwMz/1pa7MfHB5o1htK81YxXv1yDndyYSZIISAR+GwG1EFy6ZyCF+vtAg/VdqPwV2vXdJb98vXDss0bX4bk/JuP8l9V4kL0NqwAAQABJREFUfft8nGljIjDqLePSMmCi9iAkMlaRVNwl+H0FKKb5DXTKkxTZCHS5FVy7cy9qPUHoDJYWM5zGJVSTSQnyel0IBALS074RmEO8hpMH+Zyi7By09GvgzugQZx58WGhP1DotCwRFwe0LYsW23INPkkckAkcBAgolbIPFQpU7mesBAvUvTV8UftKaeiHFU4P8Qg8d4Wizpxe8yVkOB2tyWNQhFuHSI4ax9cLmHmS7ZosOW19/GI8s8+GM51/AyTHUwPkUes4zfC4pHSFzPor4UxaahCDLQ2uLtqJCa0WvVEu9jE+TgCg8FeBvVlJkIdDyp3IEzMtLKf6pj5aRcdgOOxmGsBOLamlmSvN+Fq3x0Tav549D0qEREFG0LkcNrGTwVlssJYEWPKGaNSc87S2x3Zikw4D/zVqD7L3Vzb6VbyUCLURAHURVdjZ2785CbpmD5qNaZO/Kxc68UvrZdP7fsIZJpCqGj8H4slqs2LwbTpqyRKY5v6sae3btQVZ2HlxkyHvL8rFjxy7kOxjFEjUC518cjRXXjceTPxSiMn8V7j9vMmyjRiOaTHrHa9dh0OhT8HOlBnZTOZ6e0Run3voFbvp8LW4f4MS6zVvZVi5KHUH0H302eseU4Oaz/4pvs/NRu+ojnDHtOVSNuRLX9o3mJkFBTTWd7jbrMfGEodyI/LrPTQtXTZ7WQQh0qW3ZrqJq5JfQHs/kNzQn0T58eCiKJBJ6en3DWQk3vcVNVONLOjQCGv74FZ8bBiYUEmFAhwpVPFQL4dwFVAmaLFZUVVRg1dY8ZCTZD3W6PC4ROAgBNSVRjaUU7194Kp4tEVEeTvrUrMNlZyyCt+8MrPjyv+hmEtLsYT4MDuqp/Q4ojGs3KGkYYK/BF8WlHD+15BSpi9a+gksveh4Vaj+qHS5oHzobE/5rwoQH5+HTawfgpD//Ez1m3YmHLhqF/+pDqK7x4t/XjIObc/XkbkbJtj3wCMk7bws+/qYcLuayeOHy8XiWFSKF9sCo9MCNX87BbWNH4pbLR2P6f97CxSd/AVPQDacqEddccR6SdUG4AwrcdbsYI2/FwN5xYUe89kNDttzWCHQZJh+kev3JdxbD7fXTzttyB7DmgApJ1GRPgsldQ0eYWrgYfyoqqR2uhNq8za76Xksbn6OqOMzXTdHMM3CEGyKRP8vGKAi304GXZ6/DpDGZsJmMXRU2Oa82RiBE1XTIHYcL35+HiSw2tR/p7Ug0KJ2awYvxilBSdTABf7rzRHzx5CwU3zcNqdoQEkdchbd/OBMHOMvDktwd1QwRjh33VyzOno6NOeXhaetjuyMzyQqNaw9embUC/W5agKnxATgdx+GtJYtQd4CiTRUyIL6HDVUMmTvprnnIvmwHcqvqHetiew5AD6tC0yc97Q16lM56FFV9z8PVx8bA64u0VEP73RVH3Ycuw+Rz9jp4s5fRtsX8zc0Koxzuigo1mZGhKa46MnpvHcyHqYI+3P4i9fwAcXG562hXZ55w2uL5qD2iqQhHPZ3eEI6br6ioZha8Epw8oucRtSUvOjoRCDGELmXgMcg4ILRL+H3UODxHeGd2LJZ+2tnTxp2H0L1P4PsaBdfa+XtiWO+wkckH5e3wk8nWeRgOF/LBZ07G6BEMHyQpgRBqKeTUrp6D5TXDcMdVo6hpC9GObkLfIUPCNSMOnJXH7YXLL+zzAUSnH4PRGSIKn9GIDX3wcchImCAWLNiIwPF/RzI1BqxEJSmCEOgSTF7hj/nJDxbDz0Q29pSe9AMTrgZHpp4Ttnkzf1yeump4aW/21JQjihnaWuo1HkFrf8RDFZEHTuLjc1YjKr47f/MisW3ryBqfyvZq8ATXcXi/ZNi5WZMkEWgJAmra5TwiDrwlJ3fSc0L0uLONugT/uvgTfPDwV7jsualQsUR2LZPc/BIJ5iuecApTcVcfeErqafhw1RXoHU9cqGpnglw4uNk5FIm2BHmYSIiVoptIHFcxf75/7Rt4x3Eynrl9OoUeZgZtOkO+iQQEuoTj3ewF2/Dz2myGZMXWx2ofIYNvXLAg7+6ohO4I0QGmrrqYYXXS074RG8HOQ4yV9ZbmhmPjtcwvELatN55wBK8i1t5gsiI6LgVZBZV495t1R9CKvEQiELkICJW906XD2c/Nw+znz2Qi+iM1gAFGCjo97VoEyeBbTRScAoMuxdJl83B6CtX3/tZu51s9ItnAYSLQJZj8og2sfsZ7T5SUFfGgrSWh5tOwQpNBa0CAO2Wvu5be+l0CqtZCE1bN+6im91JVqNGxpCxDd9qKNGYbi2Vomc++kOpCqRNsK1xlO5GBgNBI+BghVOf2i4yznYLC42AMfS3T21LzH9YKdIqByUG0GIGI5lw/b8iG+Ju3dg9LxsbAFMPc1W1kQxebBQud7gQ5Kwu5KxY/vE7yy2vx8rbtiWGlPH/wbjrcMf9VuPiPyEPfFhQOYWQN+tiYeGzMKsZLTJAjSSIgEZAISARah0DbiWGtG8cRXb1wdXb4ugALNBjIINRkwsI+3xYklGV6agb0WhMCDBPzMUGOSRMVEU48bTH/X2yDexw/cXAzja1eo4fBEEWjYNvg3difSEikrixjOF0xRBy9utFg2HiCfD1qEeDtEJEkfiL0qxNiMIWQiJyCHHQEIxCxTH7VjmJ8smBrGPooWxR0MYn8AbXeAaxxLYXKXkdVtDUhFVUlWXCW58PUrT89URrPOPpeRXKgMibkEAoNSyy9flkboK00JwJN4dxoYoGbOHsc1m4vwIc/bcKFpw45+oCWMz4IAS01RlZD601xBzXcQQfoq0pTl4pzEJqvo/gh0kF4d5Vu1G2gKY1YJr+aaVCDTIcqyBAfBYNay+xWBwSCtnKlBdPRCg0BtKxuxRKObgcr1tmOSk97Ec/u8wqNhieMh54lZYW2o80fV4yMULMqINTlWLQ6FxecMjicS7uVSykvj3AEfA3lVNv8fusAXFR8LBmNGsabs6BcXSC8Se6AbmUXXQABFiaEhhkMW0MRyeS3Zpfhla83wBZDZkCyxKe3OYNvBNXAmHsbi91U7s2Bk7ZoUTmtbazQjT1ExqvY8NRU5iEQZErN+G7Q62nGaONNlUBCaAbMTK4T46rBok25+G7lbkw6rm9kgCRH2W4I+FkmtbrKG5EMUqjrrX4tolR6VLPe+1Hu2tNu90hXa1j4gEVbWd+jlROLSCb/87os+Fw+2BPqC8roKP21Vxy7UNsbKc1rWPAh4HExuYQXisHY4pKqrVyfTnF5OGyOxSk8bie03PRoKWkLXNqLREIioSlQV1ViwbocyeTbC+gIa1cwx8hkkAyQE5ViSJE7hwi7WbrEcNvmGRtxQmkOi5h8OG8LDGYTTHHp4b9gK+Pif+1+EJsHxWRCjDUBCuPla8ryWC3q167oet+JjHauMpbfpWd9jD0hrM1or02VQE843Jlt8YiyRePrJTvw8+rdXQ9UOSOJgERAItABCEQck1+zLR+VVU5YmDxFx2xM4k8kU2lXYhY8A6usqegEEaJdOuD3UmUfidbBw0dJqIxCzBUQYGW+EJm9SiS/aWcXYZFcR81ERFqrleaBEBZtKTz8gcsrJAISAYmARCCyzMsV1U488/FKmMxGFjVhGlSRPOJwS80dwaLXJ8cxI5rV1rwBDxzVRfU1lo+grUi7RGgt3BUFcHNjE03pWm+0tP+miiAJe7/VnoroaBtmMaPh+h1FkQadHK9E4LAQEBYwrdUMm83Qruaw/QfFTunYFR1jpfNyfarc/b+XnyIdgYiS5FfyQV/lcNNT1QKFrqqC+banbbj54oowV+bMhYaagwDL0KpY9a49kuO46WBUy1qTtSwa8Wt/zccm3ruYjsrtDsHBP1dbpLNs6EAUznB5HbTFa6Az2cN1rg/su70+a6ktEY6OXhbdmLc6q726ke1KBDoFAmqjCp78LGzZVIAQS812CFFjpmZp7e0btqNOaM/aWyvaIZOSnTRHIGIc78rr3Hjx05Vhxy8ji5mIkK72tAs3B0m8F17fJqqqYYljGEwZaqvyEZ3Uq82S74T7oxr8bFadSo0z/apKXBgnPl1RhGqnH1a9GskxRpw9JgUWvgdNCruKnJi7sgSVroDYpB8xiax/zrJcJgPywBQVC7M1mrWuw9udI27zcC4UabLNsWksQ1uDT37aiknHH4OBGQmH04Q8t8sjIJzatLBbjSjcsQ5lbhXUig6Jmf0Ro7hZCz0yotLVnIO5biuum3ISqm5YjPeOSULAaIauKg9bCqvCq2g2p6N7z2gKGV4+jzgv5qmwaFzYvi0bPjqrKvo4ZHZPgEgO1pyEIKQ2mmBwlWB7bhmCQmKPSsfQbtFweIKwGPbisT9PhPe67/HB1QNRxTA/SV0HgYhg8sIR65UvViO/tAaxCSmMNzV3KINvXG7B6LVMdavzOeCprWFGvGoYyPhaW6ClsX0ff7hjB8ZiRBo1FXwvmLnI4ldPfJiJAw1U6fBheVY1bpvWC91i+GPXUe7lD13F80d1t2Fc/3h8s2YvPllZxEx9jW00Xv3br2IT5SBzddZWQKs3wkLVeVtUm/vtnvedEX44cZdijUlDVdEevDxrJR6++lRYZYW6fSAd5e9Ceoa4Vm/FKy98hVL+LkM+HTylm7HFn4YLLpuJ049jqmvB6Ts5kcdj+9xnsNUyFY/N6E9NoR95P76Bz1bvgp+aOVFWNm/bRsQNvQZX3jYVaVoFhQvfxhsff406yzFIi/Fiz7adGH3OLbhgytjwNfVTZqSKxo8Nn/8Hb/6wAZbETMSZarBx4y5kjL4QV1w5HXrbUMy8fQouf/JxLDzrDYyIogDFsrWSugYCEcHkN2WV4JOFWyhJm8L55H/P289AM4HZlkgv+1x4qoqgs0SFM1m1xe2gJ5N++8c8FA9JhJeV3hIooY/vFxNm3KW1HszfXAVxjuD1367di1OGJaBPvBEbC5zI3eumEK/CiQPsiLMa0DPOgHPGJOHjZXRaOwImL5wZhe8Bq1HTFs+NDaWKtsxu11K8BKM3RcciQJXivDXZ+GrpDlwwcWhLL5fndXEE1JRavYZUTJh6CZIHZCBep4YX63CN/US8ZB6FU0+4nHHGfvrQHP5Gt+Ogo3Orvgxz7v8Q2gvfR39WkHMzRDim3zhccOwFyEyjkzFV+SuePxeX3vt39PnLmbjGUog3774BXw15Hiueuxpx/L7yh8cx/ZnPcfF5J8Nf7Q4PX2PQIVi4DE+wTr1y3Vy8MvME6HUq7HhxBs64726kjP8DrhmtQ/Ifr0T3Oy7El5tKMOrEFMnkO27x272nTs/kvazK9Ph7SxDwBhAr1OMizV8HqowPXAHhEGZiCl23u77evL66FFZK923CAPkcyipxYl0B8wAwm98Zw5Nw0jGx4SHkV/rwyJe7YDPrw59t/KHOXVWC+evLUFZLGVuvQS0fDH8qSsU/z+rTMOxmov+BE/mVz2E1fUXh/7N3HYBRFmn72d7SewOCdBRBURGxnXiC9cB21lNPTtRTOc/uf7bTs/d2igV7Fz0VwQZKB+m9hZZGerLZbN+d/5lNJUIIYZPs4oyG3f2++aY8M9+8M2+F31ELM2XixgTpMnh3FmAbj4f9llT+i0vuASfbM2X6SvzxmL5IibeFvR5VYBQiQC6fTxeDtKwYBKsdKNGbkeCvRAXZ99lZOYw90bF3oEuRoJ6PqehX/IhMHHX4QFi5JSEXHWY64TJzm11RXgNrrAUurjW6QF/0TjGhomwuFuxKwVlP/xEapxPFlfTOedLZyHngXlT7tbA0LJPSlW5x4c8o8GThqsuHwlFuh0doEXfeBPS97wfMpvvoq45Og9U6GCOOKcMbn83Hgydc0KXdV5V1LgKcCpGddpbVYf2OUprMWWGi4ltnm2+1Fw0LldCCJMpuqYQXij7R3if3kY9lxvE0YiTR1vNk3pgkKz6GLHlJ3OVfKPEFrvQEEG8jy5LX4s16JPF7Ywp2MF6ljguLly58pe2FhW58m0tsLLlrP0MmknRGZI6xoYgim1Ubd3VtA1RtUYOAwaxF7fL52AITjj1lOKweT4Sf4imW48HFXFaKElLm1AQGxQr6dsdb6GASVVg8cz5cx4/HkSaBYGUxqvh+mpvWCIrzggnQJ/BtkQL7hqThDjlQWgIf9RSM2sbr0kQ1BclUaIW7ni9q0uuQlEzfaszbmKuxDPUZ3QhEPJF//IPZoKk0ZUnZIc32TreJb8d4Sh0Bc3wKNx1x9AJXg9rqEsrDuwFK0norPWlJTKiUT9m8FX8emdHUg7lrK6VQv+l3e77IftTWVqGOnAqTORZ6mg22WnLaU0zY80i/BFIJz0xi//D7c+F0ecJehyowihHghtZAM7CqeY9hzJXP4swnZ+Kqo2LgrOs+DlR70ZR6NPbyUgif7zeKslp5yk8txwujc/BF9U34+I1rYSZXM3yhuJpbqdWaKfuPfLyaW6y+tQeBbqBM7WlWfZ4dJXas217OUy13oSSoMiBKJCQpFddT89zKULRSzTVAd68yAl53JUkAa10BKtslIYnyeJlcbh++W1kG636OsOSUeOuqeIjnokkFRxn4J1yKhQeCj9zISIVLEzk6VdVOrMgrPZDi1LMHGQJ6bv7gXI4Xrn4AjuHX4uoz+0FH65MOMrO6FB3ZRis9eAq6jDbwbWtKvGFOMmDTa49i8vYEnPGvf2CQ0U9nXCTyyRlI5HLobjq1U+lWWw1fFd/WFjo4IqCBLi0dBo0PXsl6DCUeDIIVKDfpEbA0XgsZ5kDfjaLQpn6rL2FFYD9JQFjr3mdhL3y2ADV0gBNLxzdasqojKUnZvCk5k37trdRCr4SvpiJk1tfVbZTE2F7nx/kjMzFxdHZoifB5fbjrw43IK63br5N8vUZ9LdyO6pBGvZQJdkYQmo5iJLkMFuo/SNv9Zz5aAKdbneY7iuXB9JyBYqqSda/hkmHj4b5vKTZ//zAGW+kaOdFG4hb5PdX4AvD36oNMTxD5FXaaw5HUc67rbGV458/DcMMUF95eV4KHxvWgAp4JNka0i7Mch6OSyzH9nR8hrFZkZsUi+MOnKM/ugUwd40xQKTh/xw44aIeanTkKaaYifP/uSobOjkdmug1lH72MzcZ0XHREP1gYdMpDBcYSugz39OjRcpsR+eCpFu4TgYgl8rPo/GTGgs2ITaF9NqOSiQjcYYYUwlJ6StE1WfZF8PFlkUS3q5LcxFfTcc5dF/bDzWf0hpbmZlIL/5o31mNlgQNWaTffziQd+/iCfngrC2iqF0QMFd3M5FZEUpJzIIbWDHHJqVi7dRfenrY8kpqn2tINCOh4AnbOewJjxt2BNbXVmPfYGRiQnY6B/bIxsv+FWOggR2o/RVZd3g2ay3myjsapxmKsWb4BTuhgocLgO+MG4OGZ21BVORdXD89Cbo8MHN43C1c//Qs0Of3xj6efQtZPt+GoQzIwuGcKjrjsUYw9/1I4yeLf/s7fcOmfxuLXanqO7HsaHrznAqx/agwG9s7CoNxUnPngzxh286sYd1gM6uh4y+Vcj8VLrRh14jB2PzI4pl0+Dgdphe2nAi0BIHHRSy33Tkw/LdnK0gUdNcTQ60Pn1tXRbpATxuh01tCp18MobR53XZd5hNNxM1FHFv1phybjtMNSQl2QJ/gpMwuwamcNT+D1nvOkOKE9Sdrj17kZL97rgoFONkwmC8Uj7Xu2PeWHLY+UvdoSeaLRY+Ga4rAVu6eCDsSR0J7KU9fCj4CG9MiPWAzJzkZGRjKtcLzhr6TTS6QdvC8Fg08EtuzMh4aq9UFuaGOzE5CVxc120BnSuWnZDBdP/8nHXIm7J53ODQHgDeiQPvoGjB01gIrAbhQXlSPHchZ6ZhtRQ6ubwWffizsvGkJuo586NgYcdvkTePDiYyCIl5ATvWQpNpnScdQAHqi67pzSskvqeychQDPk/RMmfzyvGP/+ciPOGZqOGCsdT0ituDAmLdlUpSVlWLh4BTW7bUjMGRDRLqukuZnXXoby0m0hYp+YMzgsdvPSte0ZQ1Jx1/h6c7hlO2tx42urkWCtP11L17Xjj87ATWNzQ+hLAj8vz47iCi/M5FHKUaXCLBZsqMLC7TTBaQh1ubehkoo8VTs3wEOHIompvUJmgpHIPZHtl9yS6pJtqKkqw7DBA9C7b+/Qori3vnX0+iZyQ/rncJPZSSnAMTw0Nw6XUtSiUtsI2Os82EFPjtyL7pakHwWNwUR/78bf6JjKpc3BYFY8qHZrku2wxhhp/mrArlLnb/ogG6c3G+FZ+RrGX/gcrpizCpfE+xkMygqrdCjfKvmcbprLSq4hl0ay6hvXhCAV8uxcB7xLJ2P8xA9ww4yfMT4+ACf7LyEw0i9+bEN5frcbtQ6ps6CFPtaK+bcNwI0Fl2LWe/cg3uXsdsxadfl3+VOOWS86Rouz1OtZdRSEDgm6PfSGlEu5zklDUjqBja7B5C92QBKtxJRYytS61n3t/gIZIGtbb4mHUWOEl9HaAjzNG6iQFxYrgFYLWsu2SQLRJ9XadEnH3fiI3nEw9mteFOSCWOnwY/amKhL5vbPeJYEPeL0UN7ggtXkNtviQImEb1TfV2x1fZLx5E9tIY2FqT9txIZ3+NHsGDF+LXqdjoktGZYWvwFYlyRNTnKW7DRRbNSrKfkoxkyAXrZp+JKI5BbmmJvQdhQG2OzF92kZcdlU/uBxO6sfsuVdSI18SAeFyobLe700oo5amtzU7CpA19jr8IdsMT60sgEp5/NfH8ipblicXCOo6WezL8f7MUgy76gxkGnw8+YeKUv8cJAh0iMgbSGASuCsdmNFMZMKFxypqTc9avBExsbEwJ2SEosx1QaC5Djdfvmoy3K0tvQd8xXlwVhTDTA9VcvGpfw07VrSJz1dUe7GDJ3Pp0KO82k2N/mayS9NZFFaRLVfTSvmMyjstU63bv9tzLe81fpftdFRsBwIBKjlSmVBvCim3Nd6PtM+QbD4mGb6EKqzbWoiCnbswZmS/sDfTxjk+iE5WVFIIdDYCQQa8clj74ZFpy1Bs1zPIVMcYmIKn+dQLHsVHfw7Abncg0GLN2GMf+M7XeTJw+9uzyDXtD0eN3Cw1rzN7fEZdjCoEOkTkO7OH81ZshZeanom2mJBGfaSyjFtiIGXXepONbmUN8HqcoT8LHfeQGdYy2359N9EMZmuJA//5dAMdV9Akjux7Kx19NKY4vRY/ryrDsq3VjZf2+Gmv9e32XOtMUqPe66Esnl6zNBo9uRA8IUdBkouXyRYHfXUFfly2vVOIfBTAoJp4ECEQdNP/Ro8+OFQbQG2NO3RQ2N/uhTgbPN3XyAf3ReBlHtrFB6njMnhoJjyOOgb0qT/1y1sqHRwINFONg6M/qhcKAYWAQkAhoBBQCDQgEFEn+a3F1fjgpzVUUKHCSXJOJ8j7O2fcpfzdZDAjhj7eayoLUVdRCH32AbKPyTHzUDO+sKpZ4LabJRDv15A1J//2lXZ7rlVmurlGXbU0m/MjkZHmTBYbWfWR7/VK+tI3xaUglt75fli4Cd8O74kzjqWSpkoKgShFQB68/ZSb22X723MKD0s/WSlZ9tIfSajasJSpCokkBCKKyC9YuwMVdieSU6hIpZUe1DvO7u5qkINUwDOR7aWpKqEpHVnfdFEpDJGvVCUoC3TX0WOfxJvKbDpqAu8u1e9qJNtfn45KmWZrLCorK7FobZEi8u2HLipzcmpGZRLUsNRQj0mmaO1DVAIf5Y2WopdwpIgh8qXcSb7w6SJYLBZ6qsoOR9+6tAy5IdGy7fGMUFddWYTasnwkZvWlXD5ySaZ0zVvNkLk+xqqOj09nKF/qEUSg06G9DaSMb2+mb/0ERxW++GUdzjl+IIYPUOZoe8Mrmq8bqKOSkGiKSpUwjYwoRy91RjqnSkhgH8KzdkfzcKq2txMBGVr8QFPEEPmlG4pgd3iQQA93RvqhjqZTfOMg6EnPA1aan5HIezx19DHtofJgZPZF7hJ99LTl9Uo2nQZaa2JjN6LmU1oFSI6PgeFw/fZazFyap4h81Ize/jXUSBPQVIaKiNYklWcl58kQJ5fcA1+4oxUH1e79Q8DQhulze0uKCCJfWVOHJz+aD4vJgNikXjT74GsQhaw5eWqPo418gLJ5ByPTOUnsrZm9Q0Fs2jsgXZUv5OGO+gMeWgNY4pJhiY2LGh2IlhhJ2XxsUg68jDX/0ay1OJun+YF08anSwYWAn063HAyrHI1JsusNdEZlpimso5WJazT2R7W56xBI4Ele+kA5kBQRRH7JxiKUV9QiJi4eeinHjmLBlWTOa6Vs3l4Op8dBl5QB7tsjb+cuTRNdLqniwxCuZuLOhkdCSNmOTGYDT/PGmFjUFRVh3urtish3BMQoeCYMnMtu6aVcE3iID6Vo7UO3AKcqDQsC3W5C56Hv5Ne+WBLytBaTkhPycBeWnnVTIfJkGUP7bZslAX6vGy7K5qXsO5KSbI+roijUvhhbEgzxdCwTwboD+8JO+imw0nGSmW47X/tiKbYzmpZKCgGFgEJAISDdgHdz2rizElsKq2C0mum/2RrVxKYRSnkqNsQmhvQK3F4n/AHGf44QbRvpwjZI7oKLFgCS+WmgdnpnuIRtxKIrPqX+hkFvpNJmDOwuLxatye+KalUdCgGFgEIg4hHoViLvp5nZv9+egyBjtsYmZYcCj0Q8Yu1ooDwVx8YkId6WTPM0B5w1pTBGSGgnI4m81BfwuWpgpnc7K23Ng+2MVNeOrndbFknoY1NzKHow4dWvlmJXVUsn3d3WLFWxQqDdCAS5RhhNRsQynkGk6CSF2kFxWGwMD2HsSTACRY/tBvh3mrFbifzm4hoUkrVqNVg4uW0hhbuDZRwCFMJpLVZGhtbAW1cTURwKr5uyeHIbrFQSFAwZfCA+9iNqvELBdawoZxSOxeo0H1FDoxqzbwS0jB5ZV5CP5euK4aPb6khIQZouCnspVq3bARfbpOVmWqXoQqDbZlKQXpae/mgeHG4PrKnZ0HGBlmEjD5YUpAc5E224TdY4eJy1cNJJjgxL251J1u+sKYOLkdtMZNNbE9OjUqN+bxhKP/wx5AgZGFnrpamLUcNwmiod/AgEOa9jGVS9cssmbNq8CVs25cGlNcHE1y1qTp7UoLa6NuHvI/rh7llViGXb5aleV1cS6pPs185dLlog6UMy1sYTdowxgG28J+9vouhTt4fNQVC+FzYGnSrf0lRWuU8HC+32JT6yLAPNlnXOiqb7W2oCiGNEOxBbq3cV/nr8MbhvRgniaO+vUnQh0G1Efkd5LVZsKoHNbKajCBrA0mPcwZYki94Yk0DZt6AXPAfDt3azCRAxrnPVEmoG1KH+w8HmlUOKSYwmK8UQMSgsq8W6vLKDbUqp/rRCQK4apqAH6xZ9jElnnoCzThuNP51zAR77dhUjWOrJYo6Og4OOavf27Yuxui4HZ44fGPKQ53AV4tP7L8PZ7NPZY/6Iv978FBbTrYXM6+efWSuwZvrTuGLsqTjr1NEYe9mt2F5Na55WokET/CjavBbPTTwqhM/ZY8fg9lemoRIGGIlPgEb8Jn8ZPn/0WpwzZjTz/BHn/d8UlHKPrJeefLJOxYSjyjBj2hw4gvI0r1I0IdBt4/XMB/MY/cyL2BSevHiKj0bnN/saaC+3yFLmbSFb3FtXDVdtOV+r7oFcMuVdTjsjTVXwFG+DJSnroBKPNI6F7Kc5OZvhebV47K3ZVDBsFYq3MaP6PCgQ0DAssq1kNm69+X2M/mQJZs1fgDduPgqTLx+BGz/Kg8kcEVbC+8TaSCvbn5+dgPzTJuGSVLaZ/694eCJetk7AtNnzMP3793D4yodx3ZE3YpvGiEQ+8OO9x+OUy1/AJW8txtzFP+A/Q1bg9rufQay52WRX6PSI9efjiesmovyv8zDz57n49q1/Y/vDV2LMeVNQZzYgTuThhiMycdPPqXjrp/mY89MUHDvtnzj6+Buw0y3ZIVqcdeON8Hz0EmZVBUKbjH12SGWIGAS6heJIpah1O8tDNvEmSQBDvKeIwSRsDZHiBwNl3jqrBUG+dz5HKABk2Mrfn4K01BEI1laGHrFQ/8GgO7jEI41YyM2i2WTmRsaCnWU1WLlNneYbsTkYP4XfA0fqcXjyzcn427F9cPiAfjhzwpXoz1DPtYynruX7F+lJ+tEI6Cqw7H9Az949+G4KuL0Ch058Ae/ceRGOOXQAjj7yNFx1yaHwllbQjQjjS9SV4rupS2A9+1HcdHovDOp3BK64+q8oLdkBPwl7Y681tOyp1WfjhufexX3jj8TQIQNx9JmX4KJsBzxbl8JOn/rO9bMxszgW599yD84c2h+Dh52JSdf0R2DbfKys9LJ1gOWQY5DiL8CvFV567ot0RFX7WiLQOBdaXuv0789+tAC7SmqQmJwOndF8UMniW4Pn4wsZl9STMi8z3DxJO3ii7+rTvJTF19G/e62jEnotTc3oIa7bRQetgQrjb41eDysx91Ms8ernSxDwd7OYJIx9U0XtjoBcwLw6M7IyDNi0ci0VxJbixRvuR12/c3D9n4ci4I58F08yOJS2ZD6miUwcN3wwrNogOZugtUgmzBXbsHD5Jiyb8QzufM+Noa/ciX485ZfXzsTs4lScfc1Y6Dw+VFWTY3XiePSurEGldBnaIvnIFkjPzYLF60G5w0t329UoMTKgVuogpJEjsGXZzxDOYbjozJ7U2XGhmnkOufp2xPt3YsYmeTARSM44GkP7FuKj91dxE7J7+S2qUl8jEIEu52W5yaJfuL4ABiqC6Ojn/WBSttvb+Gp4mjBZ4ugBrxQBZw38sQkh7fa95Q/3dbkQBundzk9lR2N8PFmBZMEdREqOrfEKsm9mWjbE0Jxu5fZd2Er9j34ZxFylgxIBHTd1jhXv4tJzn0AVNWDcjM584bPP44Q0Hbx2hmKOdF0xEnlrcTEKqDx4ZlICjNyc1lFObjaV45NbxuGZX6sYg9aFxNRL8fz4YTylC4hSegllHqkbJ5OgfB7BJJgSuEFoQw1BukgN7lyF2YVWDJp0GhL4XGVZCbzc/Gs19XpRklNgMKcjjcrDwslomizfQAuopMQgqoqLGrgjB58OVQjIg/CfLj/JP8NIc1U0cUqihzILWWrR7GmtvfNBw7culv3VG02w0xzFQT/rXXWalzJqj8eFGnsZY96bkJjUg+Fk29vy6MwnN47yNG9J7RHa2DzyOk8qB/GmJjpHKXytDjBcsnnQ5Xhvxg+YNncB3nvwLHx4/Sic89RMhk/uqjet4/3RUlxZXVlChVgvPX7Wv5waup12u5Jw4VNfYtqMnzB95uc4LvgaLkk4Dz9S+c7M0/7+JKmMp6cGvb/0W4wdcB5sf/8Kr13VF35yudraFDTWIajoxzBWdH+9f/U2Pq8+uw+BLiXySzcW47OfVodCmhoYxCXcsngzT8yxBu6KDdrf/MnrJk5UD2VQ0nZdJkkA7T6e+hqek/c7YwpLObHWYA6Zd8l6fZUFXba5kX2tLd1Obd0gYhLSoaHYIFxKjhritSesG6/F8JhB5kEoyc9qp58nDy2SbebQXzzNgWrdRLw9q0x9Me3+V/rmt8UkIiE2GYs35ON/s9e1+1mVMXoQkFNHz0iPCQmpyO3XH70zemLMP97Ct1e6sPqdD1FaG/l9kU5wEpL4bvI07WvQjJck1WKzICu3Nw7p1xd9B43Gm7NmIiblR3z4v+1IGHAqhjirkLfTHXqnAhoDLDTBq3DEIEZHozkS9MRYOrDhUhdgmbZYE7Z8cCsGDb4cfT5Yi+//dSLg9MItDOh52NG0tslDgYNR8qSeHTfIYussbDAkoFefuJBnTHlQoSYAtfq7lGRE/uBFQQu7lF2/ZH0+Tbh8yGTMdb2cSGHcFUqzkj+NyKLPeD3khGyd5E7UQSLz+aJCEh7e5+Sv5O+MeCPO5XOxZi3WFtbh+9VlsMo3I8xJysCN9DCnFXpaFbjhpFvZ+E7mZMhNjNPvho8+9DVamhPZpDlfeCyH5WbpiENiMLJP8l6RMtG5x8/rK7B2q52mbVr0jbPhj0emol+WTa48qHUF8OXiYuSVOqXYj+zCvRbVoRsaKhvqaE6HYCl+WbUT4046tEPlqIciFwEN5cPeigJs9SfiyNy4UEOFz42lOwE3x17DORjxiW6mnZmZyHH5UVpZTdZ5CsWYPmxm4K7U3FyuUfWEtWjtQq5ZGvrfsJB45yC3hxdff/sLXJdciVTavJf9MBP2rETEkxtQUpKPDVVuZPQ6BFzisGvXD3jk7+8h+x9v4sXzc8kJoAiRm2yXL4j0tJ7wogyzf96Jqy8/BPKN/u6rafBbB+CkVJrach/u87lQWaVHQnY2gowIqFL0INBlRH5rcSUmT1uO2MRYmBiIJpwEXk45KzcNfzkpe5/I1/n8eH92IXolmnDbuL44oV8CHUjUC7ZstnJ8s7SEZXWOEE9LJy3xKZmoKN0Jb1UhfJn9SNvknv23m5J9dqQdGaT+jbuM9fi9SKDLVz19EoQLdx9DZh5LAn/usRlttuSPhydj7KO/4rEL+uPoQ34rFz/18BQs3lKNRz/bBI/cnIVxTfbLMLTkGLmp7Pj9oi347qjNGDOyX5vtVTejCwEj36ntv7yMiya9SXk8E+ePz14Ld6+T8PonD6Gn0Q+GM4jopCWRD6Yfh7P0xfhi6To4L+wPvakCH17RF//dliyt6bgJDsLuqMNxt32OR8dkwq4N4I7Xn8OS829EdtIddGwTRDWV5v79ySK4qGi7+aVLMe7lrfjvpiKMr/gBo4degNX0J5D09k3o8do1FF/R1FT0wk1ffYNbjrsGk//5Kc6bMBj/uzkOloALdST1l730Mf6QqoWLXMCKXcuwKi8O40cPoNW9StGEQJfxXn75dSt8ZA/FGOishJMw3EkqlRVUeUK7zJba1NIvu/wtPez5vD4UV7iRFGvAxLMOwcmDkpsIvGyPbw8cgHC2Uwau0fN0oaf5mt/lQtBHc5RwVtCiLBkQR5bvpgtbKZ/Wk2sQziQ5J/nE289NUz2+zbv7IBeFxmuri53onWbDET3q699e7sGaQnoAbLHyHtHThjiriQtZ+Dc78jRvpnc/HfGYr1zdhnMKRERZXs6/zLF34s27z6yfP3yHY+OG4dZXX8ZZA6zwepvnZUQ0eA+NkIxwXSAZh58KbNtWQD0SDXyBDNzy8lcY25c+RPhaSK91R5/xKB6/YwxihRceXwBpJ/wNj915Nqw8qfsDWvQ8cxLGnzCA+xw38ouK0cM2Docn0BGXx4T+/WORk5WJILl6ksA3Jb5zroAOf7j+Fdx1RhZ1hYK0VrDi1Nsm4//OGRCqh5p2cG1diDJ9H4zoY6McP/zvaVN71JewI9AlJ/mdZQ68+/1qsqvNsPEULz3AhTPJnYqbhGXiy+QUmI1kB1vx4MUDQ1VMXbQLU2buQCxZU16ymXaRJXZcbgJO6p+IDcUObC9z4rRDU7rEnjbkkY1a9rEM7WpnkJiaip1Iy+jP1yq8eMiOy8hyjrICbm78iItPh4nhb8N1ipfl2yw6TF1SjDmrSyn7M+KhSwcgI84sb+Hyl+htjMGHZBsqKBIxUfzx4o/5WLLZjqJyJxeVAPqkxmDyxEH0n2+ieaGBXg+58HSC/NTHE5CJuggWnua/nL8ep47ogxMO7xVqp/on+hGQojmt3oxDr30DJTc3b5lddQzzLOdglHSRZvE45eYX0Wv0k3i/7DpcS+JcS7n7lHm0RGqQg0sFwzpqu7uYV/aLDDocTSdAhXfV91twDbRLc7rZk3H/D0m4bdFz6Bn0wdlzJN5aWkAWfwvi3oCL2+XhhptmhtbeuO3jzbhHCuWZfDS3q3X74SPrX3IJvnnheeDS9zAmXY8AD0sqRQ8CXfIOrNxQiFI6wLHxFKulkkznELV60Avr6idn4xBImVNBrReFfDmquKtPpFKew+vHwrwqPPFlHtbtcNA9c5fAEGqSZCFrY5LpUVYLP7Xe/T5PSAGwsb3h+JQiAMEFwetzMsQtzV/oWldyEcKZ5BjGknhXctNUS2xbytOrOQa7nAEU1fnqbWq5EP+0ogyFVU56/9PCZtWjxEFdgRZLMPUfOyVJUYieZkNmqf/gCWDB6vxOqUcV2n0IyDkuSPGqa2nj3fAnRT/RFEwlwPYmDDoZh9sK8PWXG6ChDot0yVvHd6mxT7UkxtKpVmOS/Zbcusb7NSTYMlVu2Y6e596EcZkGeCUO3Og6+L415mv5KQ9HUrtfYuV2N5dVR3GcvC5P8Sj6Ea8tTcCfRo9CTIMNf2Mb1GfkI9DpJ/lKTq4nPp7HePF0wkIXtp20ltcjTUoTxz8DCXlj0nMXHEO5XVyLazvKXbjj7Q3UIgUG9YgNpxi4sdq9fkrzrhizDUGeLiupMOSsLIItPXev+TtyQ4aTrawspL98F0/xqbQZj+s0bX4zcTUT35bJLINkhGxuG1YkniDkt16plhBHPjvehAuoPxHPkJoyLc6rRhWVhDrLl77kYMQl58DLQEGf/LCaCniD0D9n7wqDoUapfxQCXYkAuVt1pr54adFm7HDoYKc1yu5vVTsbQ25lzmVPYcYVQSq2UnzZYlPQzhJ2yyY16p3GoXj9l3nI6ZXFgGLKsdRuAEXBjw7No/3p1688OdmpEGKhExYd7cQ74xS/P+2ReeWpM4YnSjtP+d2R/KSBQWra6/QlZJXVwcwXXDqoCZdjoCCJmpfe9eQJVk8CHyKenbq7ahtFibKJWtA3nN6bJ3k9UnmST4ujDJ6p1O7Ge78UhMZCsvU7K8mQugZrDOy1tfh5SZ4i8p0FtCq3wwgEyc6y5fTAEXRQ45BOaDrwOoS4Gjzd2yVHvQPPt268llYwIi4VR2ZmUYHVDTd3DdHEIWndn9/j7+Yjbyf03u5y47VvloXY4fEyEE0L9mwnVBc1RcqTZUwMQ73SpM1PT1be8gIq4IVnKHRkr3kqqFFPM6IYawIsCclU3On+3bdcb/pnWDCIp/kUOihpTDZyAs47LgupcRTjdOJGRHrBi03IIhfFgjenL0eFnWZ7KikEIggByR6XQbskW74jBL4zuhJqB7X/7bVusv6lLkAnvqSd0QFVZpgoy16AXLed8YmLyqGzWalcRU1XKeNRKYSAlJGb6KxFvkR13jCGoeUGopZhbSXUGirbRUKS2xcvTwRfLi7D/5aW4uvlJVi7i267mGwWI04amISTaOnglv4LOilJLomWVgZGbq6qa9woqFBEvpOgVsUqBBQCEYRAp7HrpcOER976had3DWKlK1UZBY0KICrVIxDStCeRj7MmUSGmAq6KIpjJ7TgQlr2epon2ql1wMWa8NTYelrjksGrUd3jseET3czMzZXY+6q1vGPmKSnk3n90Hlx2fHSr25MNT8fGCIn4PA49xLw2VfgNsDEPrZjTAOUs3Y2jvlL3kVJcjDQFpstlS1ybS2tdme7jsGdh+KSaM2j602UF1s7MQ0DRYVhxI+Z1G5DcVViK/wg4DzeYsVmunKX51pPPS1auLmxAXbU19LWw+BU+Sbmree6nZaqEMN9ymfq3bKsUXHtpwa+oq4HE5YJH86gOgcYKbKA8D0cgF0WiOo8mMjkS1+1n1Oiri2aW2LrGOo2dBueHzaP3wSLuhLkxyA2XQG6kEasXG/Pqwu11YvarqABCQoZKN++mv/QCqC++jZGVJ1dPQe3kgL3h4W6VKiwIE5MbwQFOnEHnpdObxd+bCyzCPiel9oKWcOJw22gfSaclLkPLZ049IxyFpJqTE19t2yzL7Z8Xg7vH9savajW/JUg6Jn8IA8t7aG/LIFp8GJ8PASuLs9Trp/pVuJDuQZDhZR/UueJzVdFcZQ8+CGRFB4J0U5J1yWAquG9MD63a68BXd2JbVeXH5ST1w3oi0pp7mFdeGTvtNFzrxS2xyT6zYshnS8iMppnn8O7FKVfQBImCnrDo/n2KoTnwfD7CJe31ccjAtnGdxMUaU7orOPuy1c+pGpyEgOY+5OTGIt9b7LuhoRZ1C5LeVOmh/XsrwqhaGLLSEfMl35ZlNRyK+tyQPtpnJFlx1chZ9x0tvUs15eyeZ0TvJRC9PQWwscmAN/yydHDs5wFXLbKWJG4m8oM08OkrkuZC4XTUhWbzZYiOPQNPpnIhGjOW6u7fFlwwTavlrkcJF7sTBZgzsYYGXvPu0GKoaUvtfJrkpnLakBHGdjLWsS/I1pJWH2yPoprMYJw/tLS+rFOEIyPnMo/ABcbq6q4syApyWczt0KovSPnQXdr/neqUuk2ZvC+t+ANMpRP6lT+ejjjvvtOy+MOtNXc6qr6UtZ3FNvWOIGrrS3ZOnp0oniaK/0XPT7t6YW9D9/YCyY1kDJM4yOpzHUQ1PTQWjRaXs9wlcnuLrHBXwUtasow2+kXJnL9kQkvh2dpITUYo4thbTsU8aXemSqDdvm0BPg1rMXVeOj9KsOGFQEjXrdTRlk08xKh1d2y7cUIMXpm+F3AzouoDIS+c4UkxijYnDI+/MwciHs8n5oF9QlRQCCgGFwEGIQNiJfGVNHVZuKYHFZIKZ2t1Swayrk/RP/8DHG0PV2h0+WCkHbkykhzSfcuORzzZL0/S9plI6Z7GEQyCy1xrqb0iiI2V1MZTN2+uqOqScKDkXAdrFS69ZVhJ5aR/fZeIR7iTqyPn47w87uKGrx9nXQkte+ox3kIJ/Or8QPywvxSgS+iSyLWVanW/H6q01sFNWn0BTuq7yoSDnpN5sRVFpOdbkVWD44Mz6wVD/KgQUAgqBgwyBsBP5R96bi9IaB9J65NKlqaFbiLyHRC+/wtU0VC1ptSRDkhgWVzffb8rY+ktXHIVZp4/tNSRnQdSUw8UTfQy17tu7OZJsTIezBlW15YztbqIsPiskguiipocQk8S5rIFz0hpCqbwoRR5uKjnu4t8H8+iJr4FVEmLPc3DiGA60qwh8Y/ss9CGg8e/A818swOQ+Z6nTfCMw6rPbEJCvhSHWCisjzNXSzFMGmer8xEp1jI4ZZ4Gb67abZ7KuqLXz+6VqaESg+YjbeOUAPqvrXFi3rQxGoxFW02/Dih5A0Qf9o9KJjVZvgIeneekRr92JWsdeKtvpuXExkhsgN1YHYobX7no7mFF6tZMuhkNuhlvuvjpYXkcf89Nm3kx9kY2cr+sLqjtajHpOIRA2BLQM0uTOz8Pa1Qws1RAoJmyF760gue7UVWLDyg2o5XfpL1+lgwuB/SEn++z5y58vwfZdVUhKSqdsOLLM5vbZ+G7O4Kds3soTvJua9u7aarTHPlLm8dImvs5eEQr8Y6YsvrPN/roZprBVL02yYhkR0UV9gmc/mBe2clVB3YkADdU0HpRt3YT8ncVwc4wpuOrOBrW7bumoyVa7EXedeSQemG8HPT83JY1JA3/ZTmzbvhkVDg89iPK0zWO/PBTEmnwoZH/lva1FldDvQQYp8+pMZlgD1di5dXN9XnI6YxlPRJr22UwlePyyUfjHu+sQxzDcKh1cCLSYSgfWsTmrduCrhRvoOpROWBjaNBJcqR5Yj7r2afkimmyJQFUxXFUlZB/bQv7s22qF8AfgpBMdLZ+NScgg101Gg46ORa2tfnXFPakLYY6JRzzd/q7J24WPZq7Cn/8wJCzarF3RflVHKwR4CjULelR87m68Q7fFds1QPDTtPYzQOUNKna1yR9xP0nhs+PY5rLOdjccvHAQ/lVKlqYyBliBVy9/BAw9Oweb8fBx2y2d4/KJhCOp8KJ77DqZ8Mg21toHITvRg6/pNOObcm3HxmSNb+P+gzwzmXTn1Mbz1w0rY0voj2VKDVas2o/cxl+DqCefBGDcUd956Jq566gnMHjcFR8Zq+XzX61JF3KAcJA0KC5HfmF+Bm5+dzp2lEUm9+oXs4hWx6cAMMdOWNqUHasp2oHrXViTl7D3WvJTF24u30LtdDeKTchijPr3dcvwOtOzgfITCx9jM3lRSDOCB135GcpwNfzyqz8HZ14O5V+RoJVlceGn8QLw08lN8cp0ZE/6dx/eBvtajYs/LsNP6VXjmtuno9X+f4bBkHXxSgmSxQjv7MYyb9AXunf425ow4BnluWs1wQ+OvWYj7r7gVhmd/xRvn96TSrg7xWIjL//wmzr1kNAwVDnL16CWQZsI16z7AHXe8gLOmbcZt/XiOCFpgKnkTJ5x0K+oGHYlHzxqAnmc/hOMf74eH35uEL24YTiLPTYZKBwUCYWHXz1mRBycd3xjoo95EmbAi8B2bG/I0b6TNvEajh4eBa3xezx6D+kgTMOFlRCiPk3kZd5qyeJU6hoAUeViJuTTfm7d8Z8cKUU91IwKS6BnhyP8R01dn4JILT4HB6+JJN4rUx3iMt+wqwI4YKzIzk2GS9qQk5BaTwKwvX4Et91IM72eCi8IHmTTsm8+zHcX6WGRmmUA3E/RF4oMzKQO2gJv3aMbaoO8i2fF1tTvg0SQjLlNHL5+8QgLuTeuDXrogihjDIcDIcnod6+4N5G/e2o1jqaruDAQOmMgXccc4Zfoq2GwWJNCTWCS4Ue0MoLqqTKPRAltSGgT9DNgr8vd6Oq8hWz/AF9vGMJBmS+xe83VVu6O1HmlqqE9MQzxDIX85dz1mLd8WrV35fbZbb0ZsYCUmjb0Wgdvfwz3DbHB0Uwjpjg6AoJKdfkceiulTokdyHMz032G0mbD1v+fivhnD8OhHk5BaQoVcXT1bIkgCrinciQqy85sV5bjZ8afBnEoCz8NCY9LwGQ/zephTz+NXfaI8X5uBrIAPWnIGZDJRlp+engBTUYES+NWDdND8e8BEfhFl8bUMQ2ixxEBIwZJKB4SAPM1rGGteY9Qz7GQdND6GneRuvGXykb3sc9YiKLVhGa5WPqNSxxGQnBETN0p+nx/zVqrTfMeR7PonDfTNkD/nM6y3jcCdV4xCnJGWG1YqjwX1FGGZqFS2u3Omrm/hvmuUESOdThc0JLrybRdaRkusKcQX38zEYdfeg5MyDTAnJ8JAP6dWztOkGAMV6RiBgyZ2enKiGlNQ6+UcJiFvtRzoaVqrlV73drNmYewI+gBryXWVDAR/i/Iay1Wf0Y1A8wzZz35IV6UutxvPfr6IbkJpMkfNbmMo+PB+FqSy74aAtI+P44scF0e/7tzRV1UVwNiCxsugMw5q2voDXsTEJtGJTvc4HNqt0VH+Q4ZAtiRmkijEYeqctSFnTlHepd9H80mQbJ4CvPn00yirXIZJR2UhJy0NZ9/7NcqcCzFhQCrumlYOGwl/JCd58o5LSYPGYECAfpd1ZhMqv/8XXl+hwbI3zsXgnDQM6Xc8pvNU/usDJ2LIGf9AQeY4DHXbsa3ADaPc0+iMiKvdgGpnDMz0NaU3mJGYJN1bA3F9RiBWW4LyQvrQNwia55koHliMZcxzSA+KqhoC/wSDbgR4cFDp4EKgw7NfQyK/dms5yqrqyC6mK1VqgbbXgcvBBWH4eyMDWhh4Qpcp4K6DjydMqWgnnWP4A4ySR1m8TDo6dFHpwBGQnBA9HYLIeeymS2SpY6JSdCDgDeiRPfBQpMZb4ONRNOTjPjqa3tRKDdvtTk1DuiuIsupaUGyOYHw/9EmjxcwejGJ9miBs2iz0yPFgxY9z4DFYkZ1uRs3iOXClJCGBK7GjdCtWLV+HGrcWCTHZ5HC4sfDHNdAlJCA7yYDtM7+Dx5yM4T0yYKSTKg8tdSrpKRSZGa34hk3NVF+iFIEO8dc1ZPtUVDnw6bRFVA4xID6VsngC0OLAGaVwREazJQvNTPe0cfRjX1VTBmdVIWJSc0MBLpyUxfsYrc4Uk0w/93ydpbN4lQ4YAalLYqGVQgy9B771/WqcdcIgmIzRc6opo8jMTCdDsfIY93tJnPt12gT85cWluJZeE+WJxcCIXSuePAeXPlKA5zauxB+8lSihG7eIXpvIsfNkHY1TjcXUCeFp/LxBsJ50B35c/S9pRce2c4MvNuKK5GM7el4AAEAASURBVKNQct9M/HTjUNTUeDHxqaex7K+34ahD7oFFH0B1rQu3vzobTprSbn/nb5jwxlY8MmcL/tT3NDx4zwW4+K4xGDg5HgzBBQc3FMfc+QXGHRaDOpcfGud6LF5qxahLhhFFtaYcTK9Qh07yZpplLFlfiIIyO0OjmqEzSKcKrQRBBxNK3dCXILXmtbTjln7o3W5HyKe9VBLzuKpDr6CZOhAqhRcBI0/zJloquBnUaNn6AhiihHVZ6/bisa+24qUf81FVVx+YKbzIRG5pct1x2+t4CnWgvKKOJ2EP/LZc9Og1CEHGqLBLc/PIbX5Dy+isxpeCwScCW3bmQ+uh8RutZ6rKHaF+VbBv9kovevdLRX+bH1Xso5vcveSjr8Ldk04ngQe8ASrOjb4BY0cN4MbAjeKicuRYzkLPbCNkkK7BZ9+LOy8aQmsdP8/5Bhx2+RN48OJjQgq+QkaDLFmKTaZ0HEURh5K6RvyE2a8GklO5f1pbH80vxpP/2wzU5CHo9yApsx+disR1XUCU/epedGc2kD1fXZhH3/RVSEzIonatFjUVBbBRMS82u5/aWIV5eCWr10/7o5KdGxgKV4+svofhzRuODnMt4S9Ohup94H9b8ePyslD0wVMGJmPCabkYmGFtV2XVvzyIUy74Bhd9/x1uH5bADWUpbh0xFCsu/RozJx3VrjI6O5Odm5cdRVRE3QfFlsuZzmpFHINS1VU64OUD+3iks5se0na3MiiTVAjcVSrNXn9bpZ4cGM/K1zD+wudwxZxVuDTejwbF94bM9EyXaIOGrsPrqDEnRXchJV32NaEh3niQ1+2cC96lkzF+4ge4YcbPGB9Ph1k8f8kjmJEmejIqpEx+6lPVMniXkCa49Jc//7YBuLHgUsx67x7Eu5yMpxHKpv7pRgTkEPTKJkfXQu3RA0j7fZKXmt4BH4MnBDwwUXFDTzlmkH7TVeoEBLil1sfRCx6Tw8PgEVS0kQNmssVD8k5UCi8CUkwiNZHNVht1TZyorpYeSSI/Gah5detZvXHBcdncnBixYGMVnv06D0u21bSr8eYhJyGpfCVefGRaKL93/mS8uxYYeWyvdj0fSZmkxnnQ5UI1dYV8EUDg24tNkB7mEvqOwgBbMaZP2whNAzFufl6gjiJSh5eOqxt2CfJT9lVyMeRftYMydaaaHQXIGnsd/pBtbgoGJVcLn8PZlNdeV79RgIE2+vbleH9mKYaNOgOZBmr4q+U8hOPB8s/+n+TnFuOhyd9SXulBXHIPyoYT0SKy6MGCS0T0o9EG1lG6kwSeJnN8U/W0o49L6w0Td+C/L8Zs1wyJ3DN7yKGqIgfFSuJ56uhjQ+Ko4P4xvLqmsS1qkeu+lbbOxdVOrNlBYsDwvUGyfXNSLDjv2EyMGZaMeEsb8vpt03AM3ZxmXnMnqt59Ase/uREPn0qj6whJ7T3JR0hzd2uG5C7s6yQvH5A+6n27ilBs1yOnTzrt3vdfNt54uo9lJDs7/dw3bgh2a1DLH7RQ0DLAVX5JCRLpYTOWDnKkpzyVuh+BcJ3k91vxzkv5sI9elSyU67gZGMVTW9X9aBykLQiEnF3Qux1t5ekCK+SiU0N2XF3JdvrmJltSCc/CPvKNuEqiWUdi/82czRBUctREiYKjDDrCYGb8k//osXGXA+/+nI8+ZN0P790Gke99Ou6/vBcuePZh+Ib/HV+cnBR2bFWBbSMQJH/e3KMPDj2AULOSqAue7kM8HDkH9pXocyPImBmDh2bC46iDy9/94o19NVnd3z8E9pvI982OweBDGGVOqn8pIrN/aHc4t7nDT6oHO4qAhecZgfEn9UP/vtSHoIlRJCcD2a4+mkJNX16OHzaUU3FLhGS1N445BBcflw4rlWXbSkHYUbmxFEaLBcbNy7DUrsFpis63BVnY70ma7CdL3S5Lbg+BDksLWCmN82uq60KltWNbEJZaVSFdh8B+E/mj+6TghX+MpRyeNH6/Jfpd1zFVk0IgHAhkx5kop2+bQIajngMtI8BNyNcry7GhkGId2vpnJ5hw7shsXDgilZHM9t1+94qP8Z95Htzx3Kv45J9X4flvNuO0vww40Gap5xUCCoFuRmC/ibxcMHKS971odHO/VPUKgd8NAuW0kZ/w8mpsKnPgqF5JePLKwRjVLzHk9rQ9IDgK38KIkXcg94k5dA07BP9I/AXxFwzFxSnr8OEZh7SniE7PI88T9PYashvv9MrCXEGQyjSh9lPEFq19CDMkqrh2ICAZ5VKR9EDTfhP5A61QPa8QUAiEFwEdnVMlxJpwYqIJE/7YC0f1jt+vCsp/+QnewaNpR31o6DnT6XfjxuEf4fnZ64EIIfLSR31aUvtMAver812QWepsGumsx0QT2GjtQxfApKrYAwLm3eIN7CFDOy7tt3Z9O8pUWRQCCgGFgEJAIaAQiAAElFQ9AgZBNUEhoBBQCCgEFAKdgYAi8p2BqipTIaAQUAgoBBQCEYBAmzL5qrIC+oJ2QpuSi76JbdjYRkBHDuYmlO5g0Aov7eSDFvQZ0EO5qoi0wQ64sHNLPry2JPTMSoYxDHK0SOuiao9CQCEQnQjs9SS/442J6NVnMI47fgSGZqTgkpcXKM92XT3Ga6bgsJ6ZGHrsiThu5CgMHdIP+hMuxwIGq1ApUhAIYOrf+6Lv4UPwxxNuwXb6D1dJIaAQUAhECgJ7JPKejR/g9H9+iHcXFaC8pArL7+2HH269Gh8WKEeqXTpw/c7FtIXrUVxcivLyMlR6puH4+VNx47vLurQZqrK9I7Dl7Qtw06LReOrG0dDT1dweX6i9P67uKAQUAgqBTkVgj2uSPX8jakUC+vaKDVWee86faLBXgRVV9QEQOrVFqvBmBEzx6JWV0PRbq0mGhV6IRCimVNNl9aWbEAiiDK8//QUsN92Ck2Mc9Iq3/77Gu6npqlqFgELgd4LAHol8wepFCNb1RbK+ftHS9B2NIa4a7MxrX1Sr3wl2Xd5N+5THsTRlIB48LzLCf3Y5ABFVYQBfXd0fH6Q8gXVXHg5HLcOgKm2JiBoh1RiFgEKAQc32BILPw+hFvNF4YtQJE0y8oFOL2J7g6pJrQezCs498iLgjn8NJmbouqVNVsncEgo75eOo9gREfnwcDvVLJCIEqKQQUAgqBSENgjyf5xJ65sAjGG25orde3AzvNZgiTIi7dMYBe51yM1/TBm2e8jw0zboSNnrNU6j4EgqjCQ+NOQeHlz+Ch4UFs2rwJO6pdDFFrx+qSHahyKeW77hsdVbNCQCHQEoE9nuQT0nMhLDNR5dchi5Zzhp2rUKal+VYPW8tn1fcuQcCDD/96KRamHIe37r0odGrskmpVJXtFQOOzY82mIMoX34YTvuQpnjn9Hifc3h24+ui5uO3LTbhrVOJen1c3FAIKAYVAVyGwR7e2onYVzhs6HDHXvol7T7PiytGXYN7wO1A+7V4kM6SlSl2DgK/0W4zrfyFmDpuAn9+6C2neGvgkW9iYjN49kxTB75ph+G0t5HKVFm1DtbOR1wUseWwcbvtpAF6c9QRO5iY50aLek98Cp64oBBQCXY3AHok8zyWY9/hZOO2BObBZdPD643Hp6zPx4vn9lFS+C0eo6P1zkfvXaTDorUiJNaDOX09UjD0uw+KFTyDHpAhJFw5Hm1WtuLkXLnh/JBYWvUeFVTUubYKlbioEFAJdhsBeiHx9/eUFW1DpCiKlZx8kKXl8lw1KY0V+Rxl2FlXB31qpS53kGyGKmE9PZSEKHQzDnJOqPN5FzKiohigEFAJtEnkFj0JAIaAQUAgoBBQC0YuAUtOO3rFTLVcIKAQUAgoBhUCbCCgi3yY86qZCQCGgEFAIKASiFwFF5KN37FTLFQIKAYWAQkAh0CYCisi3CY+6qRBQCCgEFAIKgehFQBH56B071XKFgEJAIaAQUAi0iYAi8m3Co24qBBQCCgGFgEIgehFQRD56x061XCGgEFAIKAQUAm0ioIh8m/ComwoBhYBCQCGgEIheBBSRj96xUy1XCCgEFAIKAYVAmwgoIt8mPOqmQkAhoBBQCCgEohcBReSjd+xUyxUCCgGFgEJAIdAmAorItwmPuqkQUAgoBBQCCoHoRUAR+egdO9VyhYBCQCGgEFAItImAIvJtwqNuKgQUAgoBhYBCIHoRUEQ+esdOtVwhoBBQCCgEFAJtIqCIfJvwqJsKAYWAQkAhoBCIXgQUkY/esVMtVwgoBBQCCgGFQJsIKCLfJjzqpkJAIaAQUAgoBKIXAUXko3fsVMsVAgoBhYBCQCHQJgKKyLcJj7qpEFAIKAQUAgqB6EVAEfnoHTvVcoWAQkAhoBBQCLSJgCLybcKjbioEFAIKAYWAQiB6EVBEPnrHTrVcIaAQUAgoBBQCbSKgiHyb8KibCgGFgEJAIaAQiF4EFJGP3rFTLVcIKAQUAgoBhUCbCCgi3yY86qZCQCGgEFAIKASiFwFF5KN37FTLFQIKAYWAQkAh0CYCisi3CY+6qRBQCCgEFAIKgehFQBH56B071XKFgEJAIaAQUAi0iYAi8m3Co24qBBQCCgGFgEIgehFQRD56x061XCGgEFAIKAQUAm0ioIh8m/ComwoBhYBCQCGgEIheBBSRj96xUy1XCCgEFAIKAYVAmwgoIt8mPOqmQkAhoBBQCCgEohcBReSjd+xUyxUCCgGFgEJAIdAmAorItwmPuqkQUAgoBBQCCoHoRUAR+egdO9VyhYBCQCGgEFAItImAIvJtwqNuKgQUAgoBhYBCIHoRUEQ+esdOtVwhoBBQCCgEFAJtIqCIfJvwqJsKAYWAQkAhoBCIXgQUkY/esVMtVwgoBBQCCgGFQJsIKCLfJjzqpkJAIaAQUAgoBKIXAUXko3fsVMsVAgoBhYBCQCHQJgKKyLcJj7qpEFAIKAQUAgqB6EVAEfnoHTvVcoWAQkAhoBBQCLSJgCLybcKjbioEFAIKAYWAQiB6EVBEPnrHTrVcIaAQUAgoBBQCbSKgb/OuutmpCPh9XixftRp6Xefttfw+P4YNPRwGo6lT+3Ighe8qKcHOHTvYRsOBFLPPZ70eH/r374fExMR95u3uDC63G6tXrep0TFr2U86V9PR09OzZs+XliPoe9Puxcs3qTm+TRqPhezOs0+sJRwXl5RXYujWvy+aKPxBEfFws+vfrH47mqzI6GQFF5DsZ4LaK57sCvwAMGiBIgu9wupidP8KYfFwUA0Kgc8nngTXY6/HA7qiFUd+509Hr9cHNuqIhBQL+LsGkJRZyrsTHxrW8FJHfa+32Tm+XTte5czGcHfB0yfvDhUroYEqIgVGrgV7DxUulqEBgt5nsKMlDvjsZg3ol7NZ4T+027Kg0I7dHRmiAd7upfnQYAYO/EpOvOw3TtgLa4X/GR//+GxJQiS07KxAgsQ+akzAwJx5utwfBVsRfCwGNyQKtYxe2ldSF2iASMjEoxQiHy8e9Qv1mQavRQsv3s2XyFGzANl0mBmTGtyoVcJdsx067l9l1yMjNRZxB1/LR3b4L1CFvUyG0sRk4JPO3xKFo4yY4JJNCH4/+vdN3e7blj5j4JBg95Sgur+doWFJykBOvg8vjh+wnjBYY3WXYUlS/uAdj0zAo3QqX27tHXHQWC/wVBcivrCfoupQeGBCvRUVQQKvdM9fEvnUDSmN7om+qtWXTQt9rduShxBvgdwN69ukF817KkJlFsAp5W8oQJPzmxF7omdKagxLAzo15cLMZQXMqBvbYG1dBi7iEJHhKN6PIXj+WWTk8YWsC0HPTpjVbobEXYlup3BgyJWZhYLJht7Gvv1H/r9lmgat4BwrtnBtM5oye6G0VqPVwsa4vHnKu6Jq4Si5s35QPL+9pDcnom5tcX1CLf8vzNqKSHRVaK/odks3nGwpqkSf01efEpu0Foa+xabnIjDe2yuHBtk3b4ePzvthMHJoR2+p+808tN4IBexFKnfVLV3JOHyTo3XBxeOQ8N1jM8Jdz7Kvqx16floP+sTrY3f6mfjaWFmR9sRYjqvO3ocRV/5LE5bB9hgDce6Jh/lqemIvhZzf1MQc252UbRG0+Nu+qH7/47L5It7aam8KBLZuLQnPJn5SLwcmtcavviZZjJjfIOr0RbnslypxBpGalwMp5wl7DarWgjmNf3GLsczn2jhZjX18S52QIEz1Ktu5AtTyBcB1I6tULKUFiYinA5NNOxf+Cicg67AJ8Nf1p5Joj+fjQ2Kvf9+dus2rD+7fg3LPvw04uri3Tknv+hIvveR91wT3N/JY59/Tdj9Itm1HoiI4T1J560FnX/MFCrFnHEzxPmDquUFrujlf/9DZuvu5aTLzmGlz171exstgLnTzqt0pCY4ApWI5Z7z2B6ydeg4kTr8V1z3+BkjoBvf63+Rsfd5Qvx71/Go6THvwUXhK9lslV9yv+fdGpOG7kKJxwwmjc8MYK+LhQ7Cn568qw+L+TcCLznnH+XZhd426RTaBi+Su4+NijQmWNOn0CpiypbnG/5dcgKvI34utn6vt87XXX4T9vTUcxTPxPLjoGmIPVWDT1Bdwg+0lcLnniQ2yvCkK7h34KrRF6TwG+feXfuJZ5r514HW5//XvYSa32hktF4U+49fSTcdZzs1o2LPTdUfEjbjvr+FA/TjzxLNz3ed5v8jRe8FYX4ecnrsDI40aF8p975X+wytHyXfIjf9ZTOOe440L3jzn3Jnyf52x8fLdPHZfnsp3r8cF/Lg31+drrrsesDcUwcjzk2Jt9u/DjO0/iOvZRjv3EF//HxZ3E5zeYcDOoM3JDsB6fPHF7PSYs66EPF8EVJFHfbQUg4dHqEfRUYe4rt+G04+r7Peasv+PTovqNZGMja/Kn4ppTRob6ccIpF+LxH4obb/3mc+W0V0L55Lw675+voFASl6bkwapP7sMfjz8hlGfoxfdheYXcZO4hBb0o2LYMk++ZQEz+hhuvn4T//rACLq0FBhFEkP00uXbg65fvaxr7O974CbW+PY093xPm91euxJQH/hkq7/rrb8BTU1exHH3jvqepEXK+//jkNTiefZD9OPuCuzGzqmGDFcolULXytXbP+arSrZjy9+H1uBx3Aq546EPYfdypNCZRhzkv/SP0fh038ngc/rdHUbTbXGrM2Pyp1Zsw8+OXcdcD72CbsPK0LTi+9WP/8aO37Tb2Ho69vvXun0UZiImncD5evuO6eoxvvB2vf7eFm0puIoIWWDJM8JKTUrp+O5yBPa8NzS1S3yICAdEi+dZ9KA5LsIpLP81vvlo1WxyeHicueS+v+dp+fAuIcnEuzOKU1zbtx1O/j6yu2rniCGu6uPJ/i8SmNSvFR0/eLOLie4h7X5smpn/9trjxGBt/54r/TF0oli9eIObOmxf6m7dgsVgx/3Px595aYcwdLV746Dsx7bMXxdhUk7D1GCs+nrlE/LpgfijvnNmzhcflFgFRIZ4cBcEduHjwmiMERj8g3IFgE9C+Le+I/tZYkXvrh2Ljpo1i3cwnRbaZ2/1bvxLNuZqyi+0vXS4Ov/I5sY55nz5XK9LNI8SX5d5QhrUvjxdGg0Xc+vXmUFnf3H2qMBpN4vYfS5sLaPgWFHbx1LgjxKjb/ys+nzpVfDrlKXFsvEn0z71afLpmtVi9cJq4brhOJKQfKR5/9zvx7ZeTxSXsd0La0eKVGb+KXxfVYxLCZsGvYtmsN8XJSUZhGX6xePuz78XXHzwmjo2xiJjDrxRf/bxQlJc2t8EfzBN3DYBIHfFXcdflvUXihW/s1r6axQ+LBKNFjHr8h1A/ln1ye6gfhz3z6275Gn/Mv+dsMXzS26G8GzetFXcR7xztmWJRbT0uP991rIi1JojHf97KPOvFO1f2F/G2FPHiMntjEc2f1ZvE+cMGiXGPviumTv1CfPzy/4l+2SeIGRtXi5XzPhHn9TIIU78x4qVPiMmnz4tT2efY3LPFp7OXioUL68deYjJv4RKxdMYz4vAYm0g6dZJ4f+p0MXXKPWKgJZZz4HYxe/GSpnkl58qWnbuE66vbxZCz7xWLt2xkO9eJ50+HyDAfIb4u5ZaPqejL60M4/PntpaG+znnxCmEwGsW5H+xpjfCJf915byjf5tXzxVV9IQ6PnSDyXCwr6BIfXZYqEuL7icm/bhUbNq8QT5/CdsYOFF9sdTdj0fAt4FwhLh55orjyxU/El1/+T7z/xESRbLSKoec+JWZtWCNWzHpdnBBnFLYRf+HYfye+fv9hcbTNKmKOnCBmLFwq5s9vnisSl/mf3CN6WWPEIRffLz754hvx8fM3inSTRcRd9LhYvHjxbvXL+T7k8qdD833D5mXiniMgepjOEstrKQxjqp/zpnbP+WfGjxQXTJ4TwmXL4q/FmASjOGzgA6Lc5xPBQLl48kQIa8YfxGdrtoh1efPEHf0hklJPE/PL68egZeOKiovFL3MXiTWz3xTD+2SKnPSR4oW5q8RS9nHJtCfqx/6Mf4oPps4QX0y+u37sx97Fsf+1aexD78+ipWLWa9eG5vzw618IYfz2g5cKK9/d9JveEEt/XSyWbSkS3z84WuQMOFOsr/ttW1q2S32PDASwezOc4vUT40X68H+GJpu8t/r58SJbP7ppQO0l35C4JInk5BSRljlY3PZ584u94aXzRdZfnxQzXrpWpPD+MeddL8YOjwtNEmtMfOiZM55teHl8ZeLdf4wOXZNlxV07RVS7/ELw+qPjssWAi/8rnL76F2jrG1eI3JEXiHklrSaVu0YI/i18924xJCG1oU39xY959Ytm+cfXiZ5/ukt89+H9Ijc5VQy+/CMhyr8Wx47+k3jru0/FOcnxovfAiWIbiWBR8Xxx/RBdQ3tSRf+/TxZ2j6zfLV65LFMcfvsPu0G19sETRPL5D4saL9vckNxVxaGXVhLJ3f62lwlv8LekUhL5I61p4s/v/yCWLVsq7rpgtBj7ykyxesl8MWfBMrFy5uOiryVeHH/bm2L+yuYXcuHSZWL6c1eLdGuGuH36SrFy4Rwxd/EyMeedq0WCJUFc9M4MsXhR/aagkcjLfpRtLwm1dMv9fQROum83Ij/twliRfswtosLX3J+f7hwsMox/FCsaiFRjP1t/Vq16XKSYEsRd6zzcTBSLCTwQHvPs8qZsPrFdXJesFUPOnCw8LTYWjRlKqmvE4nmzxdy588WiLQvFM2f2F5mxx4j/rtgs5rx7m8gxJ4irPlkkVi6aG8Jl0Re3s75YccYzn4lFSxY1LVQLVi0Tn945VmTEDxRPzl0rVhCXhb8uFzOeOVsk2TLFxC9+EjVlZY3Vksg4REl+Rej3jAkmYbjg9eZ7wi9eP5xE+k8vikDT2AXEOxfEiSGmv4hCTzNOLR7a7Wv+1H+IVI7RG8VBjv9KMZbn8NM/2tGUxxtYJsZwAzzqn7/dSNU66sTCJSTQkmDPXSAWb50tTu8/Qny8YYv47qnLiU+WuPP7VaGxn7douZgz5QqRaEkWl3zwvVi8sHFDOF8sWLNMvHHFoSI57STx+q/rxNIFc8Xi5cvEB3ccI5Kt/cQDcxaJJXPriV+IyG/67Wa88LlTRKZ1jFhB/q0QTnG/GeKQ6z9r6kdQOMRjxxrFkL7/ElX+tnFZ8+R4kWk8VMyq9ohg+bfiKG56rpnH97gh+V3fisNMSeKcl/e8kZLZfl3I9s5dSEy+E9dyrqUfc72Yvi1PfHLLH0Rm/GDx7Nx1obFftGS5+OaJsSIxJkvc/O0csaSJyM8XC9csFk+MThJpfc8TU1esFb/OnytWrFgsXprQR2QYjhZfLV3S2KQ9fi6+o4/o0/NKUe72c84Xhub84Cea27yvOd+60B8mDBO9MVZsdHqFf+1Lop85VfwnrwWWBa+ITOJy1YztrR8VRbtKxYoF34lJw7Riwi2TRI+kI8QLc7gZXLtEvHpJ/9DYv71knViygBisWBoa+xTzQPGfeYubxn7uvAVi0Zp54k5uwlKOu0n8uGaNWDB/vli5crZ4YGyKyOVmdcra5WLZ8tXis7tPFFkDzmqiCb9pkLoQUQi0YtZZMO6SU1FXuAwbQnJAgRXzv0DtqHHoZyEbL1iCx/9yOb6rqWcH+535+GTCPzGjrF7O5yxehdovn8RV/zeVXIrfsryaWRcCa5+/CTe/uTR0SUO2knj3Vkz6YDmC2lgcfWgidn37CL7aJtl2Lnz8zNuUlx6DQbGt2NCLnwf4d8aNL6FQV886CjiL8OkP80LlBsq3onLmf/G3G19HDesIJVcZ8hb/hHv+dhfmyTaSPWnSFuDhS0/H+zvj6/PofCh99x48+eMu/tZTTl4M1/vvgwSw/j5q8cnbc8hQTeSzzW3a9MYNGNXAzpMsvca/k897rEGm2/D4Hj7IVIWdxWekayHFh1q/Dy7K2Hu5vCiqsVN5rsVQ6aiUVV0MvT8ViSkB+PyUu1NRK5iai1RK83dQFq1p0S6ug6zRhJReaaGanaK1bM+NnZuM6KnvCXMLuWpGj6GUyBWgwLN3ubws0LVxKWW5PXFUKuWlYge2Ihm9ejbLVXVIwaFJgLOqGE6yVVsnb60dLj+vs82WOjeKKrchXt8PqXF+1JEF7tckIy1NwBfQhHDxp/ZAT+YtJruUTPuG4jSUmPtQWV4GS20WrAk++IlLQFAUknoIDH4HdlUFoNU1jxc0NqTlsGFMHo25oZz6jyCqsW2VEbmpGVRvaHxGi9ycIWT/FqHIu29WZfnWxZQ3DMChNuoW1G1GgTURubmWpnp02nQMgxtV1I7mct50XX6hpBueOgdlzZwZ1AEwV9WizmxGotWDqopiGFzpSEquH3tNkMqVlHUngXOlgmPf2FyWotU5UVrkRFpdJkyxnCOUoQf4X0xGX9ZSjZIqiolaDK9omDfuqiJs2rwJ6zbPwL8e3ooBf/8LBsTwWVGIje4EHJKV2tReDedW36xMeL2FqAnJcZtu/ebL9u3LEO8bglwT53nlRpRrYtCzR7PeQtDcG4d6XCgsq/nNs/KC1K73SK62TgdbWQU2UwafRJ2LOJMDFWXVsNT0gDnJx7kix94PU2pf6Hy1KKyhlktD34gKMbKjpNDH59Kht/qlhgu8nBuJqf0RNJRTH6R19dRAKcsPYbJ59Ud48JUABt50CZJNOtazMzTnD82JaXpoX3O+KWPoi5/6JmsRn3Eo0oxaVFMvxhtMxYC45nfFlzEI/T1uFJfU7v4of2nIkncVzcdPm6nj0Tub74RMGmj0Lo69OzT2Rr5Lgix6zpT6sddWopQQN469hveEphJFeVak2ZIh9D7qOAi4afGSltoTPssu1NTqqNMSKlz9E0UI1GuvtGhwwjVPYdy1uTjl5QVw3paGh6f2xWPrr4GORPL7v2XglcpJ+HHtPehj1sHrK8RzYw7DNbd8iZ3vXABLbDIsbhMe4aJ/RU69QkYQD2GiJgm7pqzH15f0DtVUu/QZnHP/L7jhq6WYNEwusgJ5b16G02+5G6vOn4FTHlqFT3b1wBXjrkPZyHm4L+X/UD79n4htNcPEMTeHyssrmMT2cfpS7hqo/BF/u+s7YOJYGGjmodPG49oZS3HX8AbFoY2TUUeZ4AlXvYrP7z+lvudcDB76YgcepfKKj/LxoNaFd8/ui+ceehW3jr0Pf/7X+7jn62tx/af34mP2oerz/8N/y7Mw+blrYGoh1Ox/9YuYd469vsyW/xqTkG5ssZK2vNfwXS7MdhK4RvIrtHxVAynokeTCDh+/t6ABBi781SVUyBFyI9Vwgy+k0ZCO9ADb72mReU91NT7TcC+ACmxx1MDTahMVl5MFi2EZ2qJnAWcezrniB4x4dRbOTeEKUJyHTeYgTm5YamQVGtiQM0wPb0mAnKM9NEhe4ibGlGDC1g/+g9c3cOzf/zsGVtdhTWkRFbLkolz/oMRFp0lDRsCD/Pq9ZVOBer8XFZXl8OiSuPA15BeUx9oykMoyqGDQlLf1lya62HAjgF1YpzVx4ds9Z0qfNPjjq7iw73699a/A2tdx3r0bceZXyzAiltuP1Vu4VZRtb35QizTkHkkLCyo1hXBpVVeoTDbAkqTHkmf/DfOIscgh8dtUVkyFOGu9UmIoUxAmfQZLI/VrKc5meWanA6W15VLYypwNRIObJWtcJuK44RKUV++WJE5MC1+YiPOfX8hvJH5OL86ITwxtdgQ3OJspn01q3kkwjx7Z/dPgXSP70dw/WU7LVPPT/bj6HT+uWP0qFbb0qNi+DdUk1iSTTdmo7on+KS6s36PmW0M2zpWYpACm33Uv1upH4V8PnoGUXVUoq9zFkjKbcJFtMcakI00qFLacxGy71WFHEfsViGteAoVfh9iUVL5Ha3d73+pr9eK7+y/GNR9vlJBAcBNxUUws30FarlBhWc75E1qMbbvmfEN3dr59He6b2w8PrfsPEojH5vzt3PpRt6RFeVptDg6BE8VUqmudLL41+OdfH4f3ulcxLLk0tO2Vo2qqq0NJbVXD2NePi2y3HHsb5wJVHJqS0Gthq6rEDiPnRMNmSN4UPhOSMpKhMZeDZwuVohCB5hne0HidpgdGjwHmT50F/4XDUJk6GCPTJcEOonAnTzyb38PZQz7gws/TN4mu5//buw74qIqt/9+eLclukk3vPSC9CIhSVFAUQVSKKKJgFywoothAeD4UCypiBxF4KuoTeGJHlN6L9BKSkEYapG62z3fu3d3sbghIECXJN/P7Jbt779yZM/8zM2fOmXPmVtAq3OByupHZqqAP7o7hUV6Bxtgp6rA0v9bU1MNTnZtP2lAV3h3bB2/XupbMcokV0rpE5NPg7kQDr/8zryC5492YdlyHcUsfOk3AC4VJVFqxTO3BrzF4+N3YfpKEHvXu5KvuEK9LLCZo0BYjL/GJFiDnQVmQEZ2vpdnVkyRy6GnF+snEtnjiG8HBiESnlQZDTyVs5JzmTOuPIdZqrFm3AyAhv3fzdhhrLkfPUGHAuSZFoSiVgSYo+jvfJKPBpfAWRxErdthMMkgaOMgJ5UsVCkiIB+45WazSSQsdOw1WGV1vShJ04AAhno8K8xnfYBSaI2h9ZyvtwE8/oFY5CC8MbeuqUqOCmqZtfy92RoKCNCmq40xlySiO31HxB+a99DNCb3wBQ2LIG9xG2oSCuihpnx4h76rEBitJWdq88WumkzQxGU2ScprIPFqbkEHUdOmTrO5++c/+Qw41QSKIIF8x6DARTWdph1CmYAV4b/w9cGY+jWmXR7uqIVzIdYlw8SsNFhoWkjM5tBK5sgAVLPlr8f5/9mDAnGdpLNGiRaEkLcu78BEqYLRQFby+VQ0sJU4BDwnVS/l9qxY0Yhs5ejaAkAoS+jTQc+L72HCrMGYd+OnJrpjx0iS8dXMfPJoegAAa0L74CvltJrISkDXG9bRwxT857PmYffN0aPu9jacyXONWSpgoSLD6+5VaQYEWFH1wppJoSaFTofKP5eQQVor2j7yELnIrTE4qR66g0Ui4+FTtEKwcdMHeYLXmoH6lpP4go+ue9YqTFoaMrHVM4MdpXUWJa6Z9hg2P1tHCrxaL7+iCjx+bjCuG/oqRFLkg9Pn6gsT6/7zPC9kc5j/w3J3zETnhc9wR5VriCzyXkmYtRpa42+KUWIj3pIl7iHVfFwg9vn4ZcgI6YPrNlyOw8H+is6raEEpWxkIRW7Gv+DRI4L1ou2gwHhy0EFQKVkGaB+qrkTlhp3FIjgLuBZwvuvVE8C/NGAEfkeKhUoohT76Iqv2v4KPv9iPj9kfQUSswljotjfmAyx/Dmh0bsWHjemxcvxb7co8j55vx4sNOmkgcWjLr+80cwsAXBqBX8AsTiUoViqnfb8eWDevFstZu2Y+d+b9jYIirE9WWZaNOEUgD1owju3I8xPl9Hni5A4S/iMsfRv+5v4vl7Ni8CCk+K2CKKxHN2fUP0uB1CN6zgnbsTrZjy9HNGI7p0pliGZu37sDsPjTBWqzihCGVRmHap8OQ85/3sOdkLuZ9uBERH7yEeJX/GmnPa7cgjDQBY4O/6G6TyeT9J8tgwiwqSIOcIidIySEPXxV0VYewiQUiMiqULBJOyClsKihQCzstrmJTOoKpClBeIidsaSAqaGLI345DTI20aDVpGqRhaIOgo4nUa272tNj/U9Aoe/UIJe/sbJosvbNb7vZVNHmnI1Pr1bR8n3TW7cITr/wP60oXokugCwunvhf6mctwMMdrVrSjAKu3MDKFJkFHtDdMakMgKnd/ghFXT0Tt05/gP89dCzUtCq00AYcmdoWWlaLkBPUh2pJh0gCoiv/ALrkaGZFamvacUGp00GsD4CAv66S0TNiCclFZqiBPc+p5MgWseZtwQhZIYX5KOGjBcC5JgVRc2aWaQtQK/BYTO7esgcGZigRV4+VYan/DEEkknrz8UxzYOhMJtM0lJGnClehiOoUjWV6PbBs7gO8PaxAWF0eLO//yGI0ltVGLop/nYPCQ5xD7/FLc0C6MTMpKxBDvQeFMpWUKkfcO8qpmhVuQRfpZUoxGFNwq0jINarIgyIKRnhmPYuVx1FXR4oAEmYzG4sms9TCzCCRGMrJK0HZAEPUVoeO5TRQBFJInHHaSntYGE76pwriYg5j+9gaisxMGaMpI2yz2gbEOG7bsg0adhhDfVao7h61yP67Qt8HKqT/j4LKHEOyeC7QZ/ZFEVqTDx71jg1l34jtVCNonNL5YFkLoDi56BoPHvI6+b/6PFg6JcNZYYZZR21PTYDFko/akkugU2kme4rmbUaIMQnKEsN1IGwt0DkAg8c6sjEC7DD2FEObDViun2Y3mJOpfRYc303xB5v8Af36QBIeWtokETDJSO2P6hgJcZtyEV+bvBQu8Quzzu48LiyJX+rM+L+Qq3zgH7Yx9UbtgF3a/Pbx+6y+yTW8KXc3DnkpX3xHyKmih91tAMNKSQoSf9cnJKrDopVdRUb0fzw7ujqvHPI3S6t149PI2eHK9Ap3bx4i8txDvJTSHKMjyKPDe5oxAXJSw/eLifaCCtoe0SeiabkJxVQmt2hTC2hoahxlZB/dCWZUIo4Hmcu+0WU8D/9K8ETh9xiV6QzIuQ4reiXVbdqFt33buFsiQkAyUVVciNibJPQGkIyEqBMG0kj1zUpDhyUExmd4c+shIWlmfgo0Gc3K6MJGkU+eNoThRiocWJwA7Fj7/LEoun4zX74zFkY/eRZnVOxF4Stq3cQ+EP1XyjZh0dXuxnGghxr/h+PQ8cIbPuqJDKCGNYtzkUWIZKUmxUDlqiWpvUl45EpGWI/jopyXYXdcR4wfEeW9egG92Eq5BQQHYt2Y37BojokK0KN68EUU0WWUmx0NLo8tUfIz2SHNIw1VAbxD216uweXM2VMERiNFJse/3X8msFo6uRgMJdtIns/fiwPFSv3YIpBo0tOBSaOonFeFaZodg1OSvwzoa30IStNHv1x6ApdeVSBMnf9orp7j3AjGGnjLY6/D1U08g9cbbEOKzgJOSuTWtG7lK/PIDLO4Fg/zoWqx30t5p/0vFcxbstacoLvowTbAuhKvLt2PhswuB/vdjxtj+CKb6tEEGqKlcnSachI8NazccgkwfjohgJbLXr4FFrUenqAixb1VSCN7+YwWwWJWIiAynvnYce3YUkUmXTNgBVuz6dS2sulRaiGhIqyOTPsXEV9QJ9iVv0qvraH/Wu6cq6PDplxLC+37Fbvfc7XQcw8/7alA4aCDCSetxUvx3AWFSLDhRUHLayrH4vodxqN0IbH9xlF88vVQaj5R4C3b8b1W9TiXZ8iO20tDJ7NFe7LJW8rM4mkX8JY1YRqFw1cfX4oMXliFi5DN4alhX6GicKehAkuCQMDLxn8L2zTm0/xyBaI0Ee9f8RryPQg99EGmaTpzK2o99xHu7TYuY2FBYbPtx4GAl9KHhMLJq7Ni8Dcrw9uhIFiEbs6Jo/z4cpfMWJLQnXEkLG79wrrJtOHKKLFuhAj5KZPQDSjevwgnx7AC6VLEDqwolKL22L/RkOXBYqugsgMM4JQSvU9ryxRc0lifil0n9SbP0Dk6lOhnhqjqsXLZRzCf8q/5+OXJItnVrHy9eM5fTPnhuIe3Du6RLae6X+PDdzUh54A1M7JcMVYCaxo2WtHI5wqMjUWHJxs7dJSSQIxCmJLx/WwOnMgNdSPsn2wVKDu/D4aJK0th1iEkNhKlqDw7l2sm/IRyBphJs2Z0DaVpnBDsIE/JJcPV3hrLCfNS6aRAJO7Se4s6l5A+go4WmUezz+3/+/pz7vKnmD7w8irYbR83E0rGeOVYsmc5XSEWQvAo/rdjrukD/c1Z8A5M6BFekurYdq4qO4Wh+ibhlF9M2CSFaWtDYfWcs6sE2NWLijCLv9x+qId5HINhZKfJeEdMJHcjq5+F9VpmJFB8d4jKoz5PfRA4tqo2hYVCfysfWI1XI7tcZ6WQB8LHw19PGvzRzBM7kBnhgaiaTJff388BmlUfYxG5garWu3iteH6hnjy8rEIvZN0XJ0vSjWJWPx7lw4/NxSgqxUYnP3DhvO12xse8pnEhDYTcG8qwXvOsN+kCWeuurzGYzsS9u0bJk9c1sW7WNFNIT7BEKc2qT+hg7QoGZvql21YtM+DMaAlmgIVQsJzLMyNqOeETMdvKtHsyo68P2V/o8t/t1piVv7WdXeb2sbTX72CNXBTMNhRWJUQPhkSw4UMPCej3j523+3ZMdxFChyHsXNOol7kvbuXz39a4XvOFXf/kui1FrmS7QwMIMegpT0rDUu99gW7ZuZ1sOb2NvXKliwSGZ7N3fD7K9O7exD+7vJoYyCW03BuqYPjCcDZn5Odu1bQfbfHANu58UPF2bUaycPIBtLJtNCXC1L1BBnuSEvdBWDHiO1TkIH1sV+9dVqOdTqEbDQlKHs19yXKFMjtq1rLMqiHWcu19s2tcjhbxURlikWI5QVo/7lor3rPuXs2siKKRL54rCUFE+yYhZ9dESOQvHi8++mWtnTks2Gx4oE38bw6NYiMHA9Hpqf0gsu/21/7I/9u9hi8hjXqA5UE/tJF6riU+9n5zPdgrtPLSJTU2hcKOYq9nS9QcpMmETmzUiQcRFbzASLhqmD0lhd7/9Pwor3Ez83MceIrPUzG92MAvbzsbSkSEC7QKNQpif8F0z+j0X+2pz2aQuZDCRq8XrgRROp+r+ANtb7oryMB9bxBICdOyW5UVi/jmXuTDRRyWxMCpHKEv4u+bF38X7lWvfZj2D1GI7Qo2hVJ+ahTy4gJntrv6567keFGIXyr4rdzBn4Rox1EnE2Bgh4hJmiGaPzv+J7d29g71DYZACvQLvwwTe66PYjf9eynYTJpsO/MbGURs1ncexH8ijftfmtWzKAK2IsSHESN7mhAmFJE5d+DPbunUr27j5C9ZLoWPBI+ewgpo8tvz+ziyEolE89Av1xI+exoqE6BchFW5i4zIkTOPuT8I4xpVPsmKzqx3la2eK/JqytZoym9hlYUoWHJNYj0lYaBQb+/Fuuudkx795iqVSGJtOGL9hIUyl1rAO076tH18/UD8LCenKdgvhWvsXUtiaxtXviD6Du69EhXYjfv7O9u3Ywv51cwzhomRe3qezB95dyXYK7dz1LRtEGxq47ln2+45dbMf6n9iDvSVUnooZQkKZyN+ofuytb9awLeuWsR7E2y5vC/3dxuYODmbBRi9PBUzaTnifVbujUSz7lrHrYs69z9+sVrj6fEx8Pc6R4Wls8nfHqT4H2/PB7Uwv9Hnil9GoZxqdgd3w7ub6SI/5meQFnzZKjIKqqa1jO3ftYHv27GXrlr5OfT6Tvb8nn+3btp6iKX735z3hK/B+2qJVbAeF0G3auFjkveGud9nmHTvY1lVfs9HtaCxQCGyIiImKKTOGssU/bRJDMzdx73riT8tKjWrywrokddDt6N6zu5+2h6BU/Pujhbg0XlO/dGk/7lVMvkbQKsmCGD8EVd0yaU/Hu1oXrg+6711cFSd4rkugCxK0fjnJls/w8phO9M2VgoxXY8ard5PHOB2yUhCEDtOmoKtOTqvkcDzw+B2wSPdiT4H/OlLTbzKEv08ev45M3ILPrBzXz/gYwzuli4XKorsi6JJ20PgeEKKORnD7rojRuxwDhYxybRtMe/MN9CSNSEiR3R/A/Fn3olObSL99zHYde1MdwFXde/wtJ/9p4jrg8VvbQThESjBVJ19/L/51e1/anyMzCPkEFJXYYYjoi+hQs3haVbeRT+OhfuQdzBywk3m/05hn8HD/NNhoD43UfuRZ1YhvR+Zu/01POtAiBklRXu9osdHyQEyZswRD2rq8zCWh8Xjq43m4KoF8LihZinejXKHDtf1ixN+q6GQkJCaSZumvPQg3FW2uxzuEX4jWtUVj7DsW3775GGnmru5WXHAEcdYB6EdrDAntn8e0VyCSNHDBOuDr0Cb4HFhIe2h7PUViDCYPeTrtzQYF2oyYjKk3dBL3CiXWMhSUkNaR2ROhehOqHXJcM24abusWRD4LtPdIFot+D0zDmG7hoqbjrD1KZu1ApMZFiz4NYmPonyGKPPbJKuWXNPGYMedN9Ex09XdlYme8+fEsihRw9dqqE/vIAS4C13YPFh8LSiFrF2Gid1aTJeT0FNT7brwz42boBH4QfxOGUmTKzNFu502G3PxiJGlvQmcd+RuQL0ZClIaiLcLA7OQb4d5FERwtTWRH7TVyKu7rZxR5byPed7vjWUzolwQz8V5GESYFgsc7nb2uIU3ZJNXilgf+heva0l44RWEwpRZDJz2PGzJ0hCEZqquOoZxM2x27xcBRwzBw+td45jrXmBZakZRyHxbMnYJIcrYVU1QPvPL6i0gJdfUNQ7tBWPzBC6SVu/hbkXeANPoOGJBKY53aGZcWBo21thFM6N7gZzD3icsQKDiw0r56+7EvY9nkQa7xxczIzlciPOkmpAhl27XomEpOl2Hk/U0Opn67gtQ3asgRddD46RjVjU5ydPP+qodewKjOZMkgY4uUooFKNFr07tQGKrLomFVhGPPgdPRO1FJ5dijJKnLX00+jV7QC9tqTKKMtnkFif5fjtg9+xoO9vBEjnbpPxZKZd0LntmIp2w7GvFkPnHOfT+oegKSEeDq4ybt94wJX+C9Fu7Fv4617O1C0C0HIVOjx6IeYf2dXcW51sjKyugBxXQYhmCwntVUVqKIInEo6pMZE4yk2OomsK2WgaDyYaVwLvB/YhvqwwHu1TuT9wHQ1YUIn4lXliLy/rAuNB4rocQQl494JD6NtBPnIkH+CJCoVjz05Ce2CKa+nE3oJ5d9aAAI0j/oNlRZA8sUjceu/B2DM1ED8ULdU9A7+q5SYa9ehd/gwRM6YjXvaRpOQonOhSShpBfM4JafNjGqK5RGc0ipWv4VxbxzF05+/jctk5IRDg1/cM6NBq3ILT4e5hk7Poz19jRI733oAk3e3wXw6lW5ox0sgVfmHiDWFduY4iWe7hGHV+PXY9HDPpjx6Wl7H8WXo2Xkyhq3fiamZLvP4qSoTcrMP0Qlk3oUXTW0w19WQCZ6EEM3vygAtLVZcQoaRQ2UVvWxGQf4GOYtpobNcjTlLpiODFgnCrpBwNKeW9umV7nA5O5VTTRNagFqKXc9cj+nGf+H4gnsbOMGdRupZLzBrDsantEPZ7G1YMSrzrHnP5aZpx9vofN2neHHnBoyMouNp6QU1Bw4epIWRZxkslEKY1FaLAstJe/gatdaP91XkZ6KhY1q3v3EPnjjYDZ+9+yAizF5MArU6OuWMOg4li6maBIJQhhWf33cDPkt7Gp89diViyWErKSVFzHM+/xymbbg+/gak/Hc33unjXSicT1klK59Cpwl78OXOFehtcC8wqKC9e71mbFe5dORqjUk0lTvJaUxHglxRz/tq4j2oP0jw45TheEV6O7556RZoaVwJzsMSKTmdajVQCrAIi8paCuckZWHvio8x33gntky67HxI93umsT7vl6FJPxiy37sNvd/X0BbW+0hRy1B04gSOHKbtLMHxkEKCtWS6r6uupe0JF6/Fo2ob8N5MDnaaABMW3TMES9s8g68nD4CEnKBp6USYKGlrKMClrJH/lEnoc7R3Lyfnvwo6hjfrhxfw79VRWLXza2RqfPtnkxrCM/9DCHAOnSPQzFGMRR/8gkKaiBPcQvgcHz1jNikdE0nRwlj9wsPY1fs2LJ42HlKrCZV+BgvyGCcP4RN5FUgeMgrd9BTS5N4jpu16mClEym93mca1wlpJZ3dr0W/EMMSSxiKELntC885IzFluSGqzcSJ2NJ6/7dKz5Dq3W7ZD+xF8/UQ8mCZYdFypls7bFjQRYZI6LVF7hHba62pR6af0UEwALYIKSxi6DB+BDBVp+UJInZifoY4mJt/sMvIwV5lKcdDZFRMeGPGXBLxAo6ToAKo63IHnrndZjU6ju4kXKvbnInXk4xge6RqSEoeDHOUqYT0jJiTwG/BekN8KSwWOV5AGevMNiKYp2+SmQ4h5rqWXAPkmmVRGZwoUoVDZFbcOvYKOyhWiKdwmA9+MTfguO0JvVbx8PJ4kR86/mooPl6HnuMf9BLzgGS5orGdKUrdQ8r0v8F5J++3HrUkYcdvVCCaNlQwWlMguQmOrTjinof4BukbasVWmxwt3/LUFrafIxvq8517TPx04kuvE1XSkryDgGyYntadaWNW4BbxwvzHeC2cvKKuKkK++1MV7qwWuaUXAxEZl+ManCnMQ1aU7ia8eHIMvyxk05AfkdEdiNKSB/25eCHBN/lz54ahD3sE8BKelQPcnMe/nWqTTasXvq1bSueoUtqIJQozoMNfI0zRBSylcUMNoIAoOiA22Qxo+QdmhIm1GTguGKrOt1b5qVminQrBk2E2oFc79PgsuQpiaiRzaunQiE2gzfu2uh5fn86pZeqURTbwkuDX08hoyj9MW+p9iYqfw0UBy6HOYagnDlvGq2XUUkdOkRB2F0YJGRY69Elosms/eVUSNXkVWkh6Xdm9SNRcr8/m8alYYD07ivdbNe/PZu4rYNGF4VRfnoazOiSBygr2iT6+/ZdvyYuHYWuvlQv4icpbGlc96mzQzOtHK98qFIk1FQu3PwuguVF3nU46DVHAbxeH+E0lBk72MPMqbe3KSJm8ljfOfTnLS8IQ4/OaaBE2ev0/enzv/5PhR0bYfyXqeWhACXMi3IGZxUjkCHAGOAEeAI9AUBFzusE15guflCHAEOAIcAY4AR6BFIMCFfItgEyeSI8AR4AhwBDgCTUeAC/mmY/a3PnH4RC1F8gi79Tz9EwiUVJvpBS5+8QkUO25HVqnX3/qfoOP/ex3HyupgppdDnW86UkxvJCQ/Bp7+GQQq6O2YRZU+x5j+M9XyWs4DAS7kzwO0v/ORn3eVUghLY0ep/J21/v8t+0AuvenO58xxAYka8sL/bU/5/19QLkLLf/ujHGWNvGHtXEn5aXcxHL7Hzp7rgzzfeSGQVWzGtiP0rlqemj0CPE6+mbFIOO59U1YlgnXN18O5IWR0qBnC6QU1NXRCT91f0MYalvt3/xboPlBYgw5JQX5VqShWqIxetrD7eBW9S6EFrYOpPVEGJYor6XXD7vcG+DWsmf4Q+FBUYaaX7Zy/BUtOPFuXVYVIvesUvmbaVD+yhHaH0ameZnrdbw2FuraktIdeQBUT0nKwbknYXmhauZC/0Ij+xfLaxOjw360noFa2HNYIAsWgoVPa6IUkTt83AP5FLP7uxwW6hYN2BneP9KsqgN4uGBaoxH9+L6CT9k4/cMQvc3P6QceOGmhxWFFNpyn5HuXcnGhshBY6Gp5OpKN31P+F8yfaxQfim20ta9wIp1QFahUw0Zm7LWqjgfqZnayNl7dNaISb/FJzQ4CH0DU3jrRQemYsO4pbekSiTZTvm9xaaGNaMNmTFuzHq3dkQEqntvHU/BF48/sc9M4MRrck4d0ePHEELjwCLcgWeeEbz0u8cAgkGzUIpPeX83RxEUiLo5etXFwSeO1NQCAumF6V24K25prQNJ61mSDx3DYrAAAVf0lEQVTANflmwoiWTka12Upv+6PT5Nxv5Wrp7Wmp9FeS17OeXlLDU8tAwEQvylHSmJErWs72XMtAllPpQYALeQ8S/JMjwBHgCHAEOAKtDAFurm9lDOXN4QhwBDgCHAGOgAcBLuQ9SPBPjgBHgCPAEeAItDIEuJBvZQzlzeEIcAQ4AhwBjoAHAS7kPUjwT44AR4AjwBHgCLQyBLiQb2UM5c3hCHAEOAIcAY6ABwEu5D1I8E+OAEeAI8AR4Ai0MgS4kG9lDOXN4QhwBDgCHAGOgAcBLuQ9SPBPjgBHgCPAEeAItDIEuJBvZQzlzeEIcAQ4AhwBjoAHAS7kPUjwT44AR4AjwBHgCLQyBLiQb2UM5c3hCHAEOAIcAY6ABwEu5D1I8E+OAEeAI8AR4Ai0MgS4kG9lDOXN4QhwBDgCHAGOgAcBLuQ9SPBPjgBHgCPAEeAItDIEuJBvZQzlzeEIcAQ4AhwBjoAHAS7kPUjwz4uAgB0njx5Dwcm6i1A3r9IXAVtNKXIP5sPiZL6X+fdmiYADFceycLykpllSx4lqXghIGKXmRRKn5oIh4LSh+HgWKm2Nr+VUYYlIMChPq85SeQJ5JyyISo6DVtH4s6c9dB4XnCjBa7HXYs8jc/Dp5D7nUYLwiAXZh7MRGJcCo1pxnmX8/Y9ZTuUht6zxxQxT6pEcGwaFrAHWjjrkH82DJDIBMXrV30rkseVTcO+NJ/BazYfoqD29T/xZ5SW5B1FhlULqVCMlIw6SP3ugOd9nDuTmZMFiP51IeUg8kkMDTrthqypBzolaRCbEI1AlO+3+hbzAUI0Pe1yNn3tMxedvDoWsKWCbSnG44JRITkBwAuKNf2+/upDt5mWdJwKCkOeplSJQeYQ91EXCQkONLDQshCmUSqZSaly/6Vr31zc32vBtc25hsfpebGW5s9H7F+qineWzxyBn/SavbHKRDks1O/TJBJYUGym26/7viptcxj/5wM5ZPetxD1SqRJoNxjDxmr7z3exITSNYV/7I+ssD2TVvrPvbST34yRiWiivYpqq6ptW1Zz5rExPFIiNdbdEoVAxXjmcbyi1NK6c55baVswFt9DRmQkU+KRXqet6FT/2xUUoPzL+TRYdcwj7OszV6/0JedLByNpPGTY9hC5m9kW5zprq2/2swtSNUbEtYmIHapmJXzFrNrM4mFHKmwvn1ZouA/DzXBvyxloBAUCrmbndiLtEqaM1jJW1Q9c5qLH+wQz31xXnHoI5IgLoyDzkngYjEGHR5aDEOjHFAHexVERwnc5FVbhGfk+hjkRauEb+bTZUocwYiRmcijboQdnokKiEVgUpfrdSKgkM5qPW5JNFHIyXcW349QfTFUVGIrFKXKTIwJhVRGp8H3Rlzlj2DF9Z2w7Y/xuL6PkOhcDh9i2h23ztN2YiyKS6yjr57M4Y9qMBvRUsQKndpfayqEAeLDciMkOHo4VxIDDGEzwD8eCIf9iAX1oQM4ZjlxlGO2OREaNzav9NcgaOFdqQnG1FZcBDFJsLMEIv0MM+zrrrNRUdwvMZrvGNSNWLjoiFtlBV2HD90DGYqyhkQhsy44NNxTbsJ3227GYmRQeI9C1uF/qobMXHR3dj2SM/T87eEK/IQ/LS/gii14FmJDhuf+xWrXuxdT3lZfjYkxjiEmItxpNSM8Ng4ZI59H4eG2qAJ9tHiq4pwuLhafM6hi0SbKBdGAENVSS5OKWPJkuagcZMDm0SC0LhkhAb4TskOFBK/a3y6v1BORlSjzAKrLcWRwlNQRacgQetDh5vytne/h32PRSFC7Spw53NtMerF91H62OWIVvrWW99U/qU1INBslx+csAuKgJUdYsMRwgbOXl9frsOezZ4Yfim7f+H3bOaVUSwuuAt7+2g1K/xhKrsK49i+GkEbc7KK8hz25o3x9dpMyKBH2e5qu1jO7m8/Zj2f/5HtnzOCRYYYWVh0PLvj073MUa8dONiRJc+ybiEh4vMxyaksOTGG6e9cwhys7DRN3mHOYa+N6FxfV5dnvmUmm6OeZs8Xc0UBK6+1M5t9O+uaGs4e+vaE51az/9zx6g0sE8PYIZO1ntbCBXexhJHT2Y+LprIowrHzXV8xGzvGHkcHNmHZYTFf0a4fWBcDWWXIChMWmcIeXpFD3HGlui2vsXY972Br1q1ld3UQNM8wZhzyFDtQ68XOWnGEPXe5Vnw+IjKOJWemsojkq9nKErKp/OeOBpq8jWX9Mo21DXZr6J1uZb8c+3Mt38p2siuhZl3mbKhvW0v90pilSdCiXxrdnQ2f9z82d3hbFhmexmZsPcnKt73M+itvYb9XuCwYpvJ89sk4bz/W9LmH/VbqsW7Y2MrJA9klE5ayowsmsETiVURkDBv25jpmtnv45WRFK19jV4SQRYH4HZ2YIo4b5S3vEZym0zV5SyH7+L4rxLzhExaxarNrfJ4N+y/GSFnkpTO5Jn82kFrBPZ81YmtYsvA2NA0BOypzd2LJ/bfi4Ogl+GXzf3FHrArVJfnIluSQHiNofHZ8PbkvvurxHxw9eoT+DuBN45cYk/AQCq0OOOoqsX3WDRi89zL8tnE9Vs0ehM/u7oqJK3JEUkq+uB2X3rsAg5esxvpNv+Ae21FUx47EDy8Pofsuy4CYkf7Zqn7H9QHtsabv62Jdh7K2YdjSwdCMmHeaQ5iKLAEhGhnktXWQNWlT0lNb8/p02GpR9M1LuGd2LT7ftgFfz7qWkK/AAeQjr8osEvvV8u8x++BREZu930/E0uFd8PjmMvGew2lG6a6vcMPwe9Fu3l6s2/g1hmbNwsjUJ1Bmo83lmgO4o1065kXOxgbi07KXb0RtXgVu+2gh+uoBk82r3Qt2n58mdcOwIcfw4bFDYn1bHtdjeOdkfLjXdFbgqha8gh3h7TDj5m5nzddyb9pRdeIPrHh4BH7sMgtr1vyECZdoUVOWjxxHFkwOV8t+mT4ArxifF7ETxs3SLpsxLmwkDppcG/2mU4U4/MHtuHxlMJZtWo/V80bjx8lXET/+EAuoIv5m3jIDXT5YRfxag0lh5bCo+2Pl3FuJO67+4MHQZtmBUcFtsCRsCtW3Hy/uHgP9wBdQZXMT487oRDneGNwWRmMYwoOD8OyWB7H596egICsCT60XAS7kWy9vz6llljoZQto+gjlj+yA9LQFBKgWcDt8JX47Rb+zEdxM7ucuTITohAyeth3DC6oTD7kCkfhAWzH4QGenpSBt9N3pbJdiZWyzmz9/2C5TtRmDi1ZcgI7Uj7rq3PcrINNjJqKElhG89QMXqpdhniMc1/VLEZ2VMg+5D2iN19y7UOPwnrHNqXAvKJHHWQKeKwyOfTkOfpDQkhWuJev8tiPGTZ6BbkAszaUgy0u0mHM2pFFvJpDZaCMlw7WuLMal3MjLTrsBt/dvjVNlelBCfUFmIwmoF7p18O/E5HT2Gj0F7Yx2Cgw3QKmXw2+2wl+CbL3ZDf89NiHPP/4YuPRFNdWzPEszYjScnTmDOvz9D6KV3oW/U6ebixp9qeVetFgsSE8dj7qPXIS0jEQa1Ek67f18eOHMdNj8/oL5xRhozFtVhZJtdgNptJnquJ+a+8yw6pqYhddjdGGiVYX92vvhM8Z6t0MQNxKTBHYlfbTDuzp6o0jJ0DvXvF8L6tm7TCmxShODq6y6hZ+XofN1ViPljL4rtZ57eHQolKot/xMc/ZTcYhfUk8y+tBAG+EdNKGHm+zbAqFCgeek393rBQTsOF/bq37sLIV34FU6kgpbnMYTFBLxtAIohmGJIfrOv16GNwebYrEY02pGnsdk8wyX2GQb7gN3y1Lht942qw6LU9iLxiPGz0nNdx3zXxlWYXo8KUjWf6dsFz7jlTptZCSXvBJTbas2y+zvPnC3/9c9K6WuhCLsXwVFKrz5CWzbgP9839DkqlgvbQnaimGf5miQsoidUCtTQFN/VtV/90ZGoyZFozbEwCZkzGJcl2zFv0A+4M7oiydR9jV3kgRoa6POk9PBf25pktD4VlSuxaOA6dF3sEhRR6QzQCyeLQWLKa1mG49hrah1+CnDdubdXaoaCLHxo8GPEB3oWMxAOgG5xd8ydg6LTvYZErIKMAJofVDIW6E+RiPlreOiwwJA7EDUZXp5YjCp0iTWS5cY2F6F5XQTL7S3y+OgtDU+1Y/s4PUGufAlmPqQZXHs9APZldgOq6Uswe0h2v0W25SgMDLRBOCYs7tZdGKULx2Lf78ZibxsJvnkSb4R1R/N1xvHdVqPsq/2htCHAh39o4eh7tkZHZ/YzJXoZlHyyHMmMKVn42DoFQoOi/k3D7vytBu4cQphCJ3Vb/uKCd09RSn5TteyCk9nM8MawHlHIJHI5IjB59HXRyqV8+4QE5s0Gn74C3dnyFTqZaOGkukyi10OkCySHJI2zqi25VX5gApNUGW+MecGJbN27biceW/ILbMoNI0OdhQuL1qKHFjycxCv0y+4DvpO+CTGCCUJAb0D4yDHXvj8FlnwXCUmeCuccUjIj0Pl9fjkQBG1ljes35Ee8PDIIgKwQNUWvQI1Cj82Tz+TTjs3G3YZPxMnzy/KhWLeDrG21tJL6ObrpkvQnfvvcZtKHj8eXPUxBhl6Pi15kY9cQWWnDVlwCZw0ZjwCW0GW3OOKzee9JLuiG5Yg5mjroMsxUSWM0hGPjyCATLT5+ypVSGWheLFzasRl9UQSKnMROkQ4judN56ayAH2YE3I9XwDirzBWsQF/K+2LSm76175mxNnLoIbZGQcHDaTyAnT4kTfboiSBAaJ9fjhenfkwavo/jcM08iMghdi2H5va+g+uonsPTzhViw4BN8/dsavHpjkkcXcbfKNfOl33gngsz78OjUpeRB7iq7ePVLmDr1W3Eq9IXAVnsKxw4fRtaRXJjMdpQX5OHwkSyU1PrMlL4PtODvcmG1Q6naqkXbONdk/NPTY/GzkkGn8JHqZ2ijkrR+074PMHdVLKZ/uwwLPlmAT1f8iCMLHkWQwqvpCY8LZ+FIA9ph9FglNj99C5YXuoSKQ5aPOUNH4Ody//y2ku8wUBuM+08Mw4qti5FZdpT4cBiHc8tJoPlItDPQ1hovO5wlyDsIFPbujhiS6tLa3Xhx6hJUS4JoAXT2Fsucril59cSZyOr+EL78YiHm07j56tf1WHBXe3dMvBtXwlfYWYsbNBqxjlxMePRt1NGYPLlnHqY8NB9WH/idKMSkgZdixoq9Lv4c2Y/nruuJPHN7DL0y7uxE8bstGoHTl4Utujmc+DMjQKdkwYRys1cICppfVaUFtTX+DnDMWoU6eSVN0k5IlakY8UAGfpl7J3ou1JCZWA6pIgBWabU4iUttdWBV3loFPb6S9PsCsR4JhtzVHxMmzMPE/RQ+RL2NmWuQccMsLJ59K4LUIFcgCSpr3DQlXoenRuhw36Kn0f/7l1xbA2SGbnNv/9Mmx7xfXsY14+ZTm6z0fB2OPTEAvwbocctHG/DOkGgvQc3wm81UgUoy41KMs5e6ukLYTqpFIeu96EC11AyrxWUpaZcRinv6dRctIq6tDgmq3J5eEqsJloAK1PkU6TCfpH16i+jeGNB2KHpdMgtvPfAwtGRRkQpqY1FHTDu8GLfGkHZurUS1UuC5UIAcg+6ajsxvnsFzN5BgoPx0yg2cqlT0biClSn/+CL/ZnVBsXYgRPf8DCngQyVfG3Y4tm2YjVtVypxjBKuXXP8WW0VE0FAsa4OmzbmYxay0qVVW0DUXWLUkMbnryMnw5ZxIuXekaM3IJHaDjoN7qXpOZKwugqKz24TeF1Z0EqmpdTnUDb78GltEfY8KDK+rHTQIdfrNk4X0IV9OYIc3/ZLXrcCVJxBV46q4kjJr3Gvr3+oD8WS2IvmVWg7BIBXTV2/HG+P54k1gkoW2eqioVrnn6RQyLabk8csPPP86CAD/x7izgtK5bNpQeyoU9Mg5RntPTyDx+ouA4zJpYJIZ4T74SjjgtzLcgMj0GKrdGXZmbhWIy60spXjo1To+CvCIYo6NIaFejsEqC5PoYYDvKsui0MDpNLyogBzd16YJDVy/Gxmn9RTgd1nzcl9gFK8Z9C+vcq+hY21zUhUQhJoQkvjvVleUg75RL8MspLthbticHeeKTJp9XUCrG5XuvymCIjkP4eZzY5i3j7/9mqSxGYbEDMalRULrxtVcX4ViZFCnxYRQt4DGwWVFyIA+O2BhEBQpCwozDx44TgRLEJKVASYKiRBGOmCAVhDj5Y7mVdD0WanKkE5KVTi7ML3ciJjEK+W9cid4vyLE060t0cLN6z8udceOHgfjvoV3orSpHQQOek0sXaX15YlmCpIlPSUSA1EOb67Kd+koOxWa7jQ3uvPShDEVSfEgLN90Lxy4fb9A/HSijMVOpikKK0Xvync1SjsJsE8JTY6CmrSgh1RVRP6bFgFQWitTkUBTn5UEXGUOnSEpQU5KHImsQUmMNbquWA6folL2aoHhEB5Xh/ivSsDL+Hex//yaxLAdO4vmMFCwd8DlKF4+gY22PoUoXgfhw7/aJ9RR5+JeZ3GO0kTMNqKSTxcdQVuXaatCHJCLC7ZMhVsL/tUoE+BKuVbK1sUYpEJaR6n+D9l4jY1P8r9EvhS4MCZn+l/UJKfB1CYuJi3Fl0NKRrILDb32Sw5iSIf5y0r5leQlNlOrA+ruwn0KpRY6URMHsTJ795BzWMKmNiUg3Nrzq/1tBJuLk9MYnMv+cze+XSh+BJF8wiUR5YBTSfWByUa1EeBsf/sgCRM/4+haFxsHNBZrYDUjNMNTfEr4o9bRActdjrjkKua0j9D4yurwgh4TKtdArJZA3wnPy2vKvz6901w/hudT0sEbutIZL8kb6pwzGmCQ07J4KVSiNGddWiqfl6ijqx54f9BkR5zWL68LjkeZzTxgLwYnpEHq0gyJJTtK4MWV4Bxa5WaKoRAklLQKFRZ4hOQX+3CZ+B9PhR38yJEIikhES4Vcx/9HKEeBCvpUz+GI2T6q8BKv27sW0WzORmhoikiIJCMYTq7LxWJ/wi0na/7u620w9jJXB92FQZhrIp05MyWQOzt41BtGBf7JR/P8OrYvbYJksAV9sysUbd8fVjxu5XI17lmfhs+ub91bUxUWO194YAtxc3xgq/NoFRcBUchD5lW4VUqJEWmqi20R5Qavhhf0pAtXkrFhUv8URGJNGRwZzAf+nsF2kDLZTR5Fd5nGslCExJbl+e+cikcSrbYEIcCHfApnGSeYIcAQ4AhwBjsC5IOCzQ3cu2XkejgBHgCPAEeAIcARaCgJcyLcUTnE6OQIcAY4AR4Aj0EQEuJBvImA8O0eAI8AR4AhwBFoKAlzItxROcTo5AhwBjgBHgCPQRAS4kG8iYDw7R4AjwBHgCHAEWgoC/wchP37sxy9dEgAAAABJRU5ErkJggg=="></p>

<p>The above description format is simple and easy to understand, but more data to repeat, for example, the vertices <code>1</code>, <code>2</code> and <code>3</code> are described in the triangle definitions of <code>T1</code>, <code>T2</code> and <code>T3</code> respectively, so the vertices and triangles can be separated in a way that describes the vertices with two arrays, an array describes vertex <code>vertices</code>, abbreviated as <code>vs</code> in <code>HT</code>, the other array describes the triangle corresponding vertex index <code>indices</code>, abbreviated as <code>is</code> in <code>HT</code>. The index described in the previous illustration is as follows:</p>

<p><img src="data:image/png;base64,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"></p>

<p>From the geometric model angle above the definition is enough to build a complete <code>3D</code> model, generally the interface renders also need to define the color or the tile determines the rendering effect, the use of tile rendering method needs to define its corresponding tiling location information, refer to <a href="http://en.wikipedia.org/wiki/uv_mapping">UV Mapping</a> hexahedral chapters, referred to briefly as <code>uv</code> in <code>HT</code>. This parameter is not defined if the model is rendered only with normal colors.</p>

<p>The effect of the 3D scene model also depends on the angle between the normal vector and the light of each triangle surface. Normal vector <code>normal</code> in the model of <code>HT</code> is represented by the abbreviation <code>ns</code>, generally <code>HT</code> can automatically calculate <code>ns</code> information according to the vertex, but some models to achieve special rendering effect, each vertex can pass <code>ns</code> specifies the corresponding normal vector value, which is understood by reference to <a href="http://en.wikipedia.org/wiki/phong_shading">Phong Shading</a>.</p>

<p><img src="data:image/jpeg;base64,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"></p>

<p>The following definition models are used for reference in the <code>xzy</code> axis size of the <code>1</code> range, <code>[0, 0, 0]</code> corresponds to the central position of the data, if the <code>xzy</code> axis has an absolute value of more than <code>0.5</code>, it means that it exceeds the size defined by the data <code>size3d</code>.</p>

<div id="ref_register"></div>

<h2>Model Register</h2>

<p>Through <code>ht.Default.setShape3dModel(name, model)</code> function, can register custom <code>3D</code> model:</p>

<ul><li><code>name</code> The model name, and if the name is the same as the predefined, the predefined model is replaced.</li><li><p><code>model</code> The <code>JSON</code> type object, and its specific attribute parameters are defined as follows, where the <code>bottom_*</code>, <code>top_*</code>, <code>from_*</code> and <code>to_*</code> respectively correspond to <code>shape3d.bottom.*</code>, <code>shape3d.top.*</code>, <code>shape3d.from.*</code> and <code>shape3d.to.*</code> control parameters. For example, the <code>3D</code> model can define the <code>top_*</code> model information, but whether the top plane display is controlled by the <code>shape3d.top.visible</code> parameter, and the top plane color is controlled by the <code>shape3d.top.color</code> parameter, whether the top plane is tiled is controlled by <code>shape3d.top.image</code> parameter, if the model does not define <code>top_*</code> information, then the <code>shape3d.top.*</code> parameter is meaningless.</p><ul><li><code>vs</code>: Vertex coordinates array            </li><li><code>is</code>: Vertex index array</li><li><code>uv</code>: Vertex map coordinates array  </li><li><code>ns</code>: Vertex normal vector array       </li><li><code>bottom_vs</code>: Bottom vertex coordinate array            </li><li><code>bottom_is</code>: Bottom vertex indices array</li><li><code>bottom_uv</code>: Bottom vertex tile coordinate array </li><li><code>bottom_ns</code>: Bottom vertex normal vector array</li><li><code>top_vs</code>: Top vertex coordinate array                </li><li><code>top_is</code>: Top vertex indices array</li><li><code>top_uv</code>: Top vertex tile coordinate array </li><li><code>top_ns</code>: Bottom vertex normal vector array</li><li><code>from_vs</code>: From vertex coordinate array               </li><li><code>from_is</code>: From vertex indices array</li><li><code>from_uv</code>: From vertex tile coordinate array  </li><li><code>from_ns</code>: From vertex normal vector array</li><li><code>to_vs</code>: To vertex coordinate array               </li><li><code>to_is</code>: To vertex indices array</li><li><code>to_uv</code>: To vertex tile coordinate array  </li><li><code>to_ns</code>: To vertex normal vector array</li></ul></li></ul>

<p>To sum up, the process of customizing the model is:</p>

<ul><li>First build <code>3D</code> model <code>JSON</code> data structure information object</li><li>Register the <code>JSON</code> model object into the system, <code>ht.Default.setShape3dModel(&#39;dragon&#39;, dragonModel)</code></li><li>Set the data attributes that need to be displayed as the effect of the model, <code>node.setStyle(&#39;shape3d&#39;, &#39;dragon&#39;)</code></li></ul>

<p>Similar to <code>2D</code> pictures do not require global registration, you can directly set the content of the data properties <code>node.setImage(&#39;www.google.com/logo.png&#39;)</code>, <code>3D</code> also supports this simplified approach, to build <code>3D</code> <code>JSON</code> model object is set to the data attribute: <code>node.setStyle(&#39;shape3d&#39;, dragonModel)</code>, but this approach is detrimental to the <code>DataModel</code> serialization data export, which results in the same model&#39;s data repeating output <code>3D</code> model information, and the <code>3D</code> model information data volume is very large in general, do not recommend the <code>DataModel</code> of the overall model information together, we generally see <code>3D</code> model information and picture information as peripheral resource information, proposed <code>DataModel</code> data model information separately storage management.</p>

<div id="ref_func"></div>

<h2>Modeling Function</h2>

<p><iframe src="examples/example_custommodel.html" style="height:400px"></iframe></p>

<p>The custom <code>3D</code> model is very flexible, but for complex models, hand-written vertex workloads and difficulty are high, so <code>HT</code> abstracts several types of models, providing a convenient way to build the function <code>API</code>, with the following functions in addition to <code>createFrameModel</code> to introduce <code>ht-modeling.js</code> expansion pack, the rest has been provided by <code>ht.js</code>:</p>

<div id="ref_createBoxModel"></div>

<h3>createBoxModel</h3>

<p><code>ht.Default.createBoxModel()</code> builds the hexahedral model, which displays the same colors and tiles on six sides of the model</p>

<div id="ref_createSphereModel"></div>

<h3>createSphereModel</h3>

<p><code>ht.Default.createSphereModel(side, sideFrom, sideTo, from, to, resolution)</code> building sphere model:</p>

<ul><li><code>side</code>: Total number of edges</li><li><code>sideFrom</code>: Starting edge index</li><li><code>sideTo</code>: Ending edge index</li><li><code>from</code>: Is there a starting plane</li><li><code>to</code>: Is there a ending plane</li><li><code>resolution</code>: Represents the number of differential segments</li></ul>

<div id="ref_createSmoothSphereModel"></div>

<h3>createSmoothSphereModel</h3>

<p><code>ht.Default.createSmoothSphereModel(hResolution, vResolution, hStart, hArc, vStart, vArc, radius)</code> building the smooth sphere model:</p>

<ul><li><code>hResolution</code>: Horizontal directional differential segment number</li><li><code>vResolution</code>: Vertial directional differential segment number</li><li><code>hStart</code>: The starting angle of horizontal direction, the default is <code>0</code> </li><li><code>hArc</code>: Horizontal direction total radian, the default is <code>Math.PI*2</code></li><li><code>vStart</code>: Vertial direction total Radian, the default is <code>0</code>  </li><li><code>vArc</code>: Total radian in vertical direction, the default is <code>Math.PI</code> </li><li><code>radius</code>: Sphere radius, the default is <code>0.5</code></li></ul>

<div id="ref_createCylinderModel"></div>

<h3>createCylinderModel</h3>

<p><code>ht.Default.createCylinderModel(side, sideFrom, sideTo, from, to, top, bottom)</code> building the cylinder model:   </p>

<ul><li><code>side</code>: Total number of side</li><li><code>sideFrom</code>: The index of the start side</li><li><code>sideTo</code>: The index of the end side</li><li><code>from</code>: Is there a starting plane</li><li><code>to</code>: Is there a ending plane</li><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createSmoothCylinderModel"></div>

<h3>createSmoothCylinderModel</h3>

<p><code>ht.Default.createSmoothCylinderModel(resolution, top, bottom, topRadius, bottomRadius, start, arc, height)</code> building the smooth cylinder model:   </p>

<ul><li><code>resolution</code>: Represents the number of differential segments</li><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li><li><code>topRadius</code>: Top radius, the default is <code>0.5</code></li><li><code>bottomRadius</code>: Bottom radius, the default is <code>0.5</code></li><li><code>start</code>: Starting angle, the default is <code>0.5</code></li><li><code>arc</code>: Total radian, the default is <code>Math.PI*2</code></li><li><code>height</code>: The height of cylinder, the default is <code>1</code></li></ul>

<div id="ref_createConeModel"></div>

<h3>createConeModel</h3>

<p><code>ht.Default.createConeModel(side, sideFrom, sideTo, from, to, bottom)</code> building the cone model:</p>

<ul><li><code>side</code>: Total number of side</li><li><code>sideFrom</code>: The index of the start side</li><li><code>sideTo</code>: The index of the end side</li><li><code>from</code>: Is there a starting plane</li><li><code>to</code>: Is there a ending plane</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createSmoothConeModel"></div>

<h3>createSmoothConeModel</h3>

<p><code>ht.Default.createSmoothConeModel(bottom, resolution, start, arc, radius)</code> building the smooth cone model:</p>

<ul><li><code>bottom</code>: Is there a bottom</li><li><code>resolution</code>: Represents the number of differential segments</li><li><code>start</code>: Starting angle, the default is <code>0.5</code></li><li><code>arc</code>: Total radian, the default is <code>Math.PI*2</code></li><li><code>radius</code>: Bottom radius, the default is <code>0.5</code></li></ul>

<div id="ref_createTorusModel"></div>

<h3>createTorusModel</h3>

<p><code>ht.Default.createTorusModel(side, sideFrom, sideTo, from, to, radius, resolution)</code> build the torus model:</p>

<ul><li><code>side</code>: Total number of side</li><li><code>sideFrom</code>: The index of the start side</li><li><code>sideTo</code>: The index of the end side</li><li><code>from</code>: Is there a starting plane</li><li><code>to</code>: Is there a ending plane</li><li><code>radius</code>: The tube radius of the ring, the default value is <code>0.17</code>, the desirable range <code>0~2.5</code></li><li><code>resolution</code>: The number of circular differential sections</li></ul>

<div id="ref_createSmoothTorusModel"></div>

<h3>createSmoothTorusModel</h3>

<p><code>ht.Default.createSmoothTorusModel(radius, tubeRadius, hResolution, vResolution, start, arc)</code> building the smooth torus model:</p>

<ul><li><code>radius</code>: Ring radius position</li><li><code>tubeRadius</code>: Circular radius size of section</li><li><code>hResolution</code>: Ring horizontal direction differential segment number</li><li><code>vResolution</code>: The number of circular differential sections</li><li><code>start</code>: Starting angle, the default is <code>0</code></li><li><code>arc</code>: Total radian, the default is <code>Math.PI*2</code></li></ul>

<div id="ref_createRoundRectModel"></div>

<h3>createRoundRectModel</h3>

<p><code>ht.Default.createRoundRectModel(top, bottom)</code> build the round rectangle model:</p>

<ul><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createStarModel"></div>

<h3>createStarModel</h3>

<p><code>ht.Default.createStarModel(top, bottom)</code> building the star model:</p>

<ul><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createRectModel"></div>

<h3>createRectModel</h3>

<p><code>ht.Default.createRectModel(top, bottom)</code> building the rectangle model:</p>

<ul><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createTriangleModel"></div>

<h3>createTriangleModel</h3>

<p><code>ht.Default.createTriangleModel(top, bottom)</code> build the triangle model: </p>

<ul><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createRightTriangleModel"></div>

<h3>createRightTriangleModel</h3>

<p><code>ht.Default.createRightTriangleModel(top, bottom)</code> building the right triangle model: </p>

<ul><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createParallelogramModel"></div>

<h3>createParallelogramModel</h3>

<p><code>ht.Default.createParallelogramModel(top, bottom)</code> build the parallelogram model:</p>

<ul><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createTrapezoidModel"></div>

<h3>createTrapezoidModel</h3>

<p><code>ht.Default.createTrapezoidModel(top, bottom)</code> building the trapezoid model:</p>

<ul><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li></ul>

<div id="ref_createExtrusionModel"></div>

<h3>createExtrusionModel</h3>

<p><code>ht.Default.createExtrusionModel(array, segments, top, bottom, resolution, repeatUVLength, tall, elevation)</code> according to the <code>xz</code> planar polygon, squeeze to a <code>3D</code> model.</p>

<ul><li><code>array</code>: Defines all point coordinates of the <code>xz</code> plane, in the format of: <code>[x1, z1, x2, z2 ...]</code></li><li><code>segments</code>: Defines a point connection style, an array element is an integer value, and a null represents all points connected to a line <ul><li>1: <code>moveTo</code>, occupy <code>1</code> point information</li><li>2: <code>lineTo</code>, occupy <code>1</code> point information</li><li>3: <code>quadraticCurveTo</code>, occupy <code>2</code> point information</li><li>4: <code>bezierCurveTo</code>, occupy <code>3</code> point information</li><li>5: <code>closePath</code>, do not occupy a bit of information</li></ul></li><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li><li><code>resolution</code>: Differential segment number</li><li><code>repeatUVLength</code>: Default is NULL, the top and bottom of the tile will be set according to the length of the set value to repeat</li><li><code>tall</code>: Model height, the default is <code>0.5</code></li><li><code>elevation</code>: The <code>y</code> axis position of the model center, the default is <code>0</code></li></ul>

<div id="ref_createRingModel"></div>

<h3>createRingModel</h3>

<p><code>ht.Default.createRingModel(array, segments, resolution, top, bottom, side, sideFrom, sideTo, from, to)</code> according to the curve of the <code>xy</code> plane, around a week to a <code>3D</code> model.</p>

<ul><li><code>array</code>: Defines all point coordinates of the <code>xz</code> plane, in the format of: <code>[x1, z1, x2, z2 ...]</code></li><li><code>segments</code>: Defines a point connection style, an array element is an integer value, and a null represents all points connected to a line <ul><li>1: <code>moveTo</code>, occupy <code>1</code> point information</li><li>2: <code>lineTo</code>, occupy <code>1</code> point information</li><li>3: <code>quadraticCurveTo</code>, occupy <code>2</code> point information</li><li>4: <code>bezierCurveTo</code>, occupy <code>3</code> point information</li><li>5: <code>closePath</code>, do not occupy a bit of information</li></ul></li><li><code>resolution</code>: Differential segment number</li><li><code>top</code>: Is there a top</li><li><code>bottom</code>: Is there a bottom</li><li><code>side</code>: Total number of side</li><li><code>sideFrom</code>: The starting side of index</li><li><code>sideTo</code>: The ending side of index</li><li><code>from</code>: Is there a starting plane</li><li><code>to</code>: Is there a ending plane</li></ul>

<div id="ref_createSmoothRingModel"></div>

<h3>createSmoothRingModel</h3>

<p><code>ht.Default.createSmoothRingModel(array, segments, vResolution, start, arc, hResolution)</code> according to the curve of the <code>xy</code> plane, around a week to a smooth <code>3D</code> model.</p>

<ul><li><code>array</code>: Defines all point coordinates of the <code>xz</code> plane, in the format of: <code>[x1, z1, x2, z2, ...]</code></li><li><code>segments</code>: Defines a point connection style, an array element is an integer value, and a null represents all points connected to a line <ul><li>1: <code>moveTo</code>, occupy <code>1</code> point information</li><li>2: <code>lineTo</code>, occupy <code>1</code> point information</li><li>3: <code>quadraticCurveTo</code>, occupy <code>2</code> point information</li><li>4: <code>bezierCurveTo</code>, occupy <code>3</code> point information</li><li>5: <code>closePath</code>, do not occupy a bit of information</li></ul></li><li><code>vResolution</code>: Curve differential segment number</li><li><code>start</code>: Starting angle, the default is <code>0</code> </li><li><code>arc</code>: Total radian, the default is <code>Math.PI*2</code></li><li><code>hResolution</code>: Horizontal surround differential segment number</li></ul>

<div id="ref_createFrameModel"></div>

<h3>createFrameModel</h3>

<p><code>ht.Default.createFrameModel(dx, dy, dz, params)</code> building a framework body model</p>

<ul><li><code>dx</code>: <code>x</code> axle frame thickness, the default is <code>0.07</code></li><li><code>dy</code>: <code>y</code> axle frame thickness, the default is <code>0.07</code></li><li><code>dz</code>: <code>z</code> axle frame thickness, the default is <code>0.07</code></li><li><code>params</code> is <code>JSON</code> structure object parameters, object properties are:<ul><li><code>top</code>: <code>true</code> represents the overall coverage, <code>false</code> means that the surface is NULL, the rest of the attributes are hollow, or the default effect</li><li><code>bottom</code>: <code>true</code> represents the overall coverage, <code>false</code> means that the surface is NULL, the rest of the attributes are hollow, or the default effect</li><li><code>left</code>: <code>true</code> represents the overall coverage, <code>false</code> means that the surface is NULL, the rest of the attributes are hollow, or the default effect</li><li><code>right</code>: <code>true</code> represents the overall coverage, <code>false</code> means that the surface is NULL, the rest of the attributes are hollow, or the default effect</li><li><code>front</code>: <code>true</code> represents the overall coverage, <code>false</code> means that the surface is NULL, the rest of the attributes are hollow, or the default effect</li><li><code>back</code>: <code>true</code> represents the overall coverage, <code>false</code> means that the surface is NULL, the rest of the attributes are hollow, or the default effect</li></ul></li></ul>

<p><iframe src="examples/example_api.html" style="height:580px"></iframe></p>

<div id="ref_attr"></div>

<h2>Model Attribute</h2>

<p>In addition to the underlying properties associated with building model vertices described in the <a href="#ref_basic">Model Basics</a> section, model <code>JSON</code> objects can also define the following properties:</p>

<ul><li><code>s3</code>: Array types such as <code>[0.5, 2.0, 3.0]</code>, representing the model <code>x</code> axis to narrow the direction of the half, the model <code>y</code> axis to expand double, the model <code>z</code> axis direction of triple larger</li><li><code>t3</code>: Array type such as <code>[10,-20, 30]</code>, represents the model <code>x</code> axis direction translation <code>10</code>, model <code>y</code> axis direction translation <code>-20</code>, model <code>z</code> axis direction translation <code>30</code></li><li><code>r3</code>: Array types such as <code>[Math.PI/2, 0, 0]</code>, represents the model with <code>x</code> axis rotation <code>Math.PI/2</code> radians, <code>y</code> and <code>z</code> axes&#39; direction are unchanged </li><li><code>rotationMode</code>: Rotate model, control the three axes of <code>r3</code> rotation sequence, the default is <code>xzy</code></li></ul>

<p>The following property values are default to <code>style</code> corresponding to <code>shape3d</code>, if set on the model, the definition on the model has a higher priority:</p>

<ul><li><code>color</code>: The default is <code>#3498DB</code>, the overall color in <code>3d</code> graphic </li><li><code>topColor</code>: The default is <code>undefined</code>, the top surface color in <code>3d</code> graphic </li><li><code>bottomColCor</code>: The default is <code>undefined</code>, the bottom surface color in <code>3d</code> graphic </li><li><code>fromColor</code>: The default is <code>undefined</code>, the start surface color in <code>3d</code> graphic </li><li><code>toColor</code>: The default is <code>undefined</code>, the end surface color in <code>3d</code> graphic </li><li><code>image</code>: The default is <code>undefined</code>, the overall tile in <code>3d</code> graphic </li><li><code>topImage</code>: The default is <code>undefined</code>, the top surface tile in <code>3d</code> graphic </li><li><code>bottomImage</code>: The default is <code>undefined</code>, the bottom surface tile in <code>3d</code> graphic </li><li><code>fromImage</code>: The default is <code>undefined</code>, the start surface tile in <code>3d</code> graphic </li><li><code>toImage</code>: The default is <code>undefined</code>, the end surface tile in <code>3d</code> graphic </li><li><code>light</code>: The default is <code>true</code>, whether the <code>3d</code> graphic affected by light</li><li><code>visible</code>: The default value is <code>true</code> to determine whether the <code>3d</code> graphic is visible, which does not affect the <code>label</code>, <code>note</code> and <code>icons</code> and other parts of the element</li><li><code>fromVisible</code>: The default value is <code>true</code> to determine whether the starting surface of <code>3d</code> graphic is visible</li><li><code>toVisible</code>: The default value is <code>true</code> to determine whether the end surface of <code>3d</code> graphic is visible</li><li><code>topVisible</code>: The default value is <code>true</code> to determine whether the top surface of <code>3d</code> graphic is visible</li><li><code>bottomVisible</code>: The default value is <code>true</code> to determine whether the bottom surface of <code>3d</code> graphic is visible</li><li><code>opacity</code>: The default is <code>undefined</code>, which determines the transparency of the <code>3d</code> graphic, the value range <code>0~1</code></li><li><code>reverseFlip</code>: The default is <code>false</code> to determine whether the negative side of the <code>3d</code> graphic shows positive content</li><li><code>reverseColor</code>: <code>#868686</code>, determines the reverse color of <code>3d</code> graphics</li><li><code>reverseCull</code>: The default is <code>false</code> to determine whether the reverse side of <code>3d</code> graphics is displayed, hidden back can improve performance</li><li><code>transparent</code>: The default is <code>false</code> to determine whether the <code>3d</code> graphic is transparent</li><li><code>uvOffset</code>: The default is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics overall tile, in the format <code>[0.5, 0.5]</code></li><li><code>uvScale</code>: The default is <code>undefined</code>, which determines the <code>uv</code> scale of the <code>3d</code> graphics overall tile, in the format <code>[3, 2]</code></li><li><code>topUvOffset</code>: The default is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics top surface tile, in the format <code>[0.5, 0.5]</code></li><li><code>topUvScale</code>: The default is <code>undefined</code>, which determines the <code>uv</code> scale of the <code>3d</code> graphics top surface tile, in the format <code>[3, 2]</code></li><li><code>bottomUvOffset</code>: The default is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics bottom surface tile, in the format <code>[0.5, 0.5]</code></li><li><code>bottomUvScale</code>: The default is <code>undefined</code>, which determines the <code>uv</code> scale of the <code>3d</code> graphics bottom surface tile, in the format <code>[3, 2]</code></li><li><code>fromUvOffset</code>: The default is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics starting surface tile, in the format <code>[0.5, 0.5]</code></li><li><code>fromUvScale</code>: The default is <code>undefined</code>, which determines the <code>uv</code> scale of the <code>3d</code> graphics starting surface tile, in the format <code>[3, 2]</code></li><li><code>toUvOffset</code>: The default is <code>undefined</code>, which determines the <code>uv</code> offset of the <code>3d</code> graphics end surface tile, in the format <code>[0.5, 0.5]</code></li><li><code>toUvScale</code>: The default is <code>undefined</code>, which determines the <code>uv</code> scale of the <code>3d</code> graphics end surface tile, in the format <code>[3, 2]</code></li></ul>

<div id="ref_group"></div>

<h2>Model Group</h2>

<p>More complex models often need a combination of small models, using the current knowledge can set different parts of the model <code>shape3d</code> property separately through numbers of <code>Node</code> datas, if the effect of multiple parts to stick together, you can adsorbed these <code>Node</code> datas together to each other in rings through <code>Node#setHost</code> function.
But such a solution would result in inconvenient object management, for example, a chair can be composed of four legs and a plane, but from an object management perspective or user perspective, we hope that the whole chair is a <code>Node</code> object, there is only one node in the <code>TreeView</code> view, this is the best occasion to use the model composition feature.</p>

<p>The definition of a composite model is very simple and flexible, and multiple models can be combined in an <code>Array</code> array</p>

<pre><code>ht.Default.setShape3dModel(&#39;A&#39;, modelA);
ht.Default.setShape3dModel(&#39;B&#39;, modelB);
ht.Default.setShape3dModel(&#39;C&#39;, [&#39;A&#39;, &#39;B&#39;]); </code></pre>

<p>The above code defines the <code>A</code>, <code>B</code> and <code>C</code> three models, in which the model <code>C</code> is a combination of the <code>A</code> and <code>B</code> models.</p>

<pre><code>ht.Default.setShape3dModel(&#39;E&#39;, [&#39;A&#39;, &#39;B&#39;, &#39;sphere&#39;, modelD]) </code></pre>

<p>The array elements of the composite model can not only be the newly defined model names, also can be predefined model names, as well as model <code>JSON</code> objects directly, the above code defines the new model <code>E</code>, which is defined by a custom <code>A</code> and <code>B</code> model, predefined <code>sphere</code> spheres, and <code>modelD</code> combination of the four models.</p>

<pre><code>ht.Default.setShape3dModel(&#39;F&#39;, [
    {
        shape3d: &#39;box&#39;,                        
        color: &#39;yellow&#39;                 
    },
    {
        shape3d: &#39;box&#39;,
        s3: [0.5, 0.5, 1.1],                        
        color: &#39;red&#39;
    }
]);</code></pre>

<p>The attributes of the chapter <a href="#ref_attr">Model Properties</a> can be set not only on the <code>JSON</code> object of the model, you can also set on a new element object in a composite model array, which defines the new model <code>F</code>, which is composed of two <code>box</code> models, the model type is defined by the <code>shape3d</code> element attribute, you can also set model parameters such as <code>color</code> and <code>s3</code>.</p>

<pre><code>ht.Default.setShape3dModel(&#39;G&#39;, [
    {
        shape3d: [&#39;cylinder&#39;, {shape3d: &#39;cone&#39;, color: &#39;green&#39;, t3: [1, 0, 0]}],                      
        color: &#39;yellow&#39;,
        t3: [-0.5, 0, -1]
    },
    {
        shape3d: &#39;box&#39;,
        s3: [0.3, 0.3, 0.3],                   
        color: &#39;red&#39;
    }
]);</code></pre>

<p>The <code>shape3d</code> property values of a group element object, also can be an array type to achieve infinite recursive combination hierarchy nesting, the above code definition of the <code>G</code> model rendering effect as follows:</p>

<p><img src="data:image/png;base64,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"></p>

<div id="ref_binding"></div>

<h2>Data Binding</h2>

<p>Refer to <a href="../../core/databinding/ht-databinding-guide.html#ref_shape3dmodel">Databinding Manual</a>  </p>

<div id="ref_ext"></div>

<h2>Extend Type</h2>

<p><code>HT</code> provides a number of primitive types of primitives, but in order to quickly build a room building, a variety of scene decorations, the modeling expansion pack adds more primitive types, facilitating users to quickly build project scenes with APIs or visual editing tools.</p>

<div id="ref_symbol"></div>

<h3>Symbol</h3>

<p><code>ht.Symbol</code> inherits from <code>ht.Node</code>, often used to display flower pots, billboards and other decorative pictures of the plane, its default setting of <code>all.visible</code> to <code>false</code>, overloaded with <code>setIcon</code> function, in the setting <code>icon</code> of the same time through <code>addStyleIcon</code> will also be used to display pictures in <code>Graph3dView</code>. Generally using <code>Symbol</code> requires only the constructor or the <code>setIcon</code> to input the parameter, then the <code>p3</code> position can be set.</p>

<ul><li><code>ht.Symbol(icon, autorotate, transaprent)</code></li><li><code>setIcon(icon, autorotate, transaprent)</code><ul><li><code>icon</code> Specify pictures to display</li><li><code>autorotate</code> The default is <code>false</code>, which controls whether the direction of the eye is automatically oriented, can be set to <code>true</code> or <code>x</code>, <code>y</code>, <code>z</code>, where <code>y</code> represents a qualification to rotate along the <code>y</code> axis</li><li><code>transaprent</code> Represents whether to display as transparent, default to <code>false</code></li></ul></li></ul>

<p>The <code>setIcon</code> function returns the registered <code>json</code> object, which can be used to set additional parameters, see the <a href="../../core/3d/ht-3d-guide.html#ref_icons">3D Manual Icon Chapter</a></p>

<div id="ref_csgnode"></div>

<h3>CSGNode</h3>

<p><code>CSG</code> is the abbreviation of <a href="http://en.wikipedia.org/wiki/Constructive_solid_geometry">Constructive Solid Geometry</a> modeling technology, by tailoring <code>subtract</code>, fusion <code>union</code> and intersection <code>intersect</code> operations combine complex model effects, <code>HT</code> encapsulates <code>ht.CSGNode</code> and <code>ht.CSGShape</code> and other data types to support the combination of <code>CSG</code>, often used in the wall of doors and windows hollowed out the application scene.</p>

<p>&#39;<code>ht.CSGNode</code> inherits from <code>ht.Node</code>, when the <code>style</code> of the <code>shape3d</code> attribute is shown to be null show the cube effect, if the <code>CSGNode</code> adsorbed to the host <code>CSGNode</code> or <code>CSGShape</code> by <code>sethost</code>, the host <code>CSGNode</code> or <code>CSGShape</code> can be combined with the adsorption of <code>CSGNode</code> datas for <code>CSG</code> modeling.</p>

<ul><li><code>attach.cull</code>: The default is <code>false</code>, which determines whether the intersecting part is excluded</li><li><code>attach.operation</code>: The default is <code>subtract</code>, the following values are desirable:<ul><li><code>subtract</code>: Represents a knockout clipping of <code>host</code> <code>CSGNode</code> or <code>CSGShape</code> datas</li><li><code>union</code>: Represents the fusion of <code>CSGNode</code> or <code>CSGShape</code> datas of <code>host</code></li><li><code>intersect</code>: Represents the intersection part of <code>CSGNode</code> or <code>CSGShape</code> of <code>host</code></li><li>Set to other values to represent no action on <code>host</code> data</li></ul></li></ul>

<p>When the host <code>CSGNode</code> or <code>CSGShape</code> is combined to generate the new model part of the rendering by the <code>csg.*</code> parameters to control, is the same control mode with <code>all.*</code> and other facets of the six-face body, refer to <a href="../../core/3d/ht-3d-guide.html#ref_cube">3D Manual of the Cube Chapter</a>. </p>

<ul><li><code>csg.light</code>: Whether it is affected by light, when the light is affected by the brightness of the front, the side looks dark</li><li><code>csg.visible</code>: Is it visible</li><li><code>csg.color</code>: Color</li><li><code>csg.image</code>: Tile, the priority is higher than <code>csg.color</code></li><li><code>csg.blend</code>: Dyed color, the priority is higher than <code>csg.color</code>, if there is a tile, dying the tile</li><li><code>csg.opacity</code>: Transparentcy, the value range <code>0~1</code></li><li><code>csg.reverse.flip</code>: Whether the reverse shows the positive content</li><li><code>csg.reverse.color</code>: Reverse color, the inner face color of the cube</li><li><code>csg.reverse.cull</code>: Whether the reverse is visible, that means whether the cube interior surface is displayed, the general six-side closed cubes, do not display to improve performance</li><li><code>csg.transparent</code>: Whether transparent, if the <code>color|image|opacity</code> attribute appears in a transparent region, it should be set to <code>true</code> in general</li></ul>

<p>Note that there is no <code>csg.uv</code>, <code>csg.uv.scale</code> and <code>csg.uv.offset</code>, etc., tile-related parameters, <code>csg</code> related surface of the <code>uv</code> value is determined by the adsorption of the cube tile parameters, only the tile picture parameters determined by <code>host</code> <code>csg.image</code> parameter.</p>

<blockquote><p>We can understand the <code>CSG.*</code> as the seventh face except <code>left/rigth/top/bottom/front/back</code></p></blockquote>

<div id="ref_csgbox"></div>

<h3>CSGBox</h3>

<p><code>ht.CSGBox</code> inherits from <code>ht.CSGNode</code>, in addition to having the knocked out function of <code>CSGNode</code> of the parent class, can also be rotated and expand and closed on six surfaces, so the following related <code>style</code> attribute is added, the following is only described as <code>front</code> because of the identical parameters of six sides:</p>

<ul><li><code>front.toggleable</code>: Whether to allow double click to expand and close operation, the default is <code>false</code></li><li><code>front.expanded</code>: Is currently expanded, the default is <code>false</code></li><li><code>front.angle</code>: Current state rotation angle, the default is <code>0</code></li><li><code>front.start</code>: The starting rotation radian of the close state, the default is <code>0</code></li><li><code>front.end</code>: The end rotation radians of the expand state, the default is <code>Math.PI/2</code></li><li><code>front.axis</code>: The rotation axis of the expand and close operation, the default is <code>left</code><ul><li><code>left</code>: Rotate with left as an axis</li><li><code>right</code>: Rotate with right as an axis</li><li><code>top</code>: Rotate with top as an axis</li><li><code>bottom</code>: Rotate with bottom as an axis</li><li><code>v</code>: Rotate with an intermediate vertical line as an axis </li><li><code>h</code>: Rotate with intermediate horizontal line as an axis  </li></ul></li></ul>

<p>These two parameters of <code>front.expanded</code> and front.angle<code> are generally not operated by the user, </code>ht.CSGBox` provides the following function encapsulation, refer to <a href="../../plug-in/form/examples/example_unboxing.html">Form Manual Example Unboxing</a></p>

<ul><li><code>toggleFace(face, anim)</code>: The toggle surface is currently expanded or closed, <code>faces</code> desirable value <code>left|right|top|bottom|front|back</code>, <code>anim</code> represents whether animation or not</li><li><code>isFaceExpanded(face)</code>: Determines whether the surface is currently in a state of expansion, <code>faces</code> desirable value <code>left|right|top|bottom|front|back</code></li><li><code>setFaceExpanded(face, expanded, anim)</code>: Set surface expansion or close status, <code>expanded</code> desirable value <code>true|false</code>, <code>anim</code> represents whether animation or not</li></ul>

<blockquote><p>Note that there is no <code>all.*</code> corresponding to the six-plane uniform parameters, <code>CSGBox</code> related surface parameters can only be set independently of each surface</p></blockquote>

<div id="ref_doorwindow"></div>

<h3>DoorWindow</h3>

<p><code>ht.DoorWindow</code> inherits from <code>ht.CSGNode</code>, in addition to having the function of <code>ht.CSGNode</code> of father class, can also carry out the whole rotation to expand and close operation, often used as the business object of the door or window, adsorbed in <code>CSGNode</code> or <code>CSGShape</code> <code>host</code> as the wall of the data, <code>DoorWindow</code> adds the following <code>style</code> parameters to the control, where <code>dw</code> is the abbreviation of <code>DoorWindow</code>:</p>

<ul><li><code>dw.flip</code>: Flip the display of the data, equivalent to rotate <code>180</code> degrees along the <code>z</code> axis</li><li><code>dw.s3</code>: Zoom in on the original base of the data display size, the default value is <code>[0.999, 0.999, 0.5]</code> represents <code>x</code> and <code>y</code> shrinks <code>0.001</code>, <code>z</code> shrinks <code>0.5</code></li><li><code>dw.t3</code>: An absolute position offset on the original basis for the display position of the data, and the default value is NULL for no offset</li><li><code>dw.toggleable</code>: Allow double click to expand and close operations, the default is <code>true</code> </li><li><code>dw.expanded</code>: Whether the current data is expanded, the default is <code>false</code></li><li><code>dw.angle</code>: Current state rotation angle, the default is <code>0</code></li><li><code>dw.start</code>: The starting rotation radian of the close state, the default is <code>0</code></li><li><code>dw.end</code>: Expand the end of the state rotation radians, the default is <code>Math.PI/2</code></li><li><code>dw.axis</code>: Expand and close the rotation axis of the operation, the following values are desirable, the default is <code>left</code><ul><li><code>left</code>: Rotate with left as an axis</li><li><code>right</code>: Rotate with right as an axis </li><li><code>top</code>: Rotate with top as an axis </li><li><code>bottom</code>: Rotate with bottom as an axis </li><li><code>v</code>: Rotate with an intermediate vertical line as an axis </li><li><code>h</code>: Rotate with an intermediate horizontal line as an axis </li></ul></li></ul>

<p><iframe src="examples/example_bookshelf.html" style="height:500px"></iframe></p>

<blockquote><p><code>DoorWindow</code> type can also be combined with the <code>OBJ</code> format <code>3D</code> model to show more realistic windows and doors effects, see <a href="../../plug-in/obj/ht-obj-guide.html">Obj Manual</a> <a href="../../plug-in/obj/ht-obj-guide.html#ref_doorwindow">windows and doors application example</a></p></blockquote>

<div id="ref_csgshape"></div>

<h3>CSGShape</h3>

<p><code>ht.CSGShape</code> inherits from <code>ht.Shape</code>, currently only supports the not ignoring the <code>segments</code> parameters of the line wall effect, <code>CSGNode</code> if through <code>setHost</code> adsorption to the host <code>CSGShape</code>, <code>CSGShape</code> can be with the adsorbed <code>CSGNode</code> datas to <code>CSG</code> modeling.</p>

<ul><li><code>attach.index</code>: The default is <code>-1</code>, used for <code>CSGNode</code> adsorption to <code>Shape</code> datas, which represents the segment index of the <code>Shape</code></li><li><code>attach.offset</code>: The default is <code>0</code>, which is used in conjunction with the <code>attach.index</code> parameter, which represents the offset position of the segment where the <code>CSGShape</code> data resides</li><li><code>attach.offset.relative</code>: The default is <code>false</code>, and if <code>true</code>, the offset represents the length of the segment multiplied by <code>attach.offset</code> value</li><li><code>attach.offset.opposite</code>: The default is <code>false</code>, used in conjunction with <code>attach.offset</code> parameter to represent the positive or reverse direction of the line segment</li><li><code>attach.thickness</code>: <code>Node</code> defaults are null, <code>CSGNode</code> defaults to <code>1.001</code>, <code>CSGNode</code> the <code>height</code> attribute value by <code>Shape</code> <code>thickness</code> multiplied by that value</li><li><code>attach.gap</code>: The default is <code>0</code>, which represents the offset from the vertical direction of the line segment</li><li><code>attach.gap.relative</code>: The default is <code>false</code>, and if <code>true</code> the offset from the vertical direction of the line to <code>Shape</code> <code>thickness</code> value multiplied by <code>attach.gap</code></li></ul>

<p>When the <code>style</code> parameter is set by the adsorbent <code>CSGNode</code>, it will automatically adjusts its size, position, and rotation angle so that it remains positioned on the <code>Shape</code> corresponding segment, and this function applies not only to <code>ht.CSGShape</code>, also applies to <code>ht.Sha  pe&#39;s</code> parent data, note that the adsorption function on this segment does not take into account the <code>Shape</code> type of rotation.</p>

<p><code>CSGShape</code> type to <code>ht.Shape</code> has been simplified, and the <code>setRotation</code>, <code>setRotationX</code> and <code>setRotationZ</code> rotation functions are not supported except for <code>segments</code> parameters.</p>

<p><iframe src="examples/example_csgshape.html" style="height:520px"></iframe></p>    <hr id="contact" style="margin: 20px 0 1px 0">
    <a href="http://www.hightopo.com"><img class="logo"></a>Welcome to contact us <a href="mailto:<EMAIL>"><EMAIL></a>
</body>
</html>
