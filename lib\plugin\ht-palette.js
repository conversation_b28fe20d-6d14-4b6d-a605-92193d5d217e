!function(N,n){"use strict";function a(){return document}function p(){return S("div")}function t(){return S("canvas")}function k(){return document.body}function w(N,n,a){N.style.setProperty(n,a,R)}function W(N,n){N.appendChild(n)}function F(N,n){N.removeChild(n)}function L(N){this.$22h=N,this.addListeners()}var A="px",Z="innerHTML",P="className",$=ht.Default,h=ht.Color,o=ht.Node,g="top",l=$.animate,e=$.getInternal(),r="0",K="none",T="max-height",u="font",d="background",J="border-box",M="user-select",H="box-sizing",O="overflow",m=$.isTouchable,V=$.isTouchEvent,e=$.getInternal(),X=h.titleIconBackground,B=$.scrollBarInteractiveSize,c=/msie 9/.test(N.navigator?N.navigator.userAgent.toLowerCase():""),R=null,S=function(N){return a().createElement(N)},C=e.addEventListener,N=(e.removeEventListener,e.addMethod($,{paletteExpandIcon:{width:16,height:16,comps:[{type:"triangle",rect:[4,4,10,8],background:X,rotation:3.14}]},paletteCollapseIcon:{width:16,height:16,comps:[{type:"triangle",rect:[4,4,10,8],background:X}]},paletteTitleLabelColor:$.labelSelectColor,paletteTitleLabelFont:$.labelFont,paletteContentLabelFont:$.labelFont,paletteContentLabelColor:"#777",paletteContentBackground:"#fff",paletteTitleHeight:$.widgetTitleHeight,paletteTitleBackground:h.titleBackground,paletteTitleHoverBackground:h.titleBackground,paletteSeparatorWidth:1,paletteSeparatorColor:void 0,paletteItemHoverBorderColor:h.highlight,paletteItemSelectBackground:h.highlight},!0),".palette-item:hover{border: 1px solid "+$.paletteItemHoverBorderColor+" !important} .palette-header:hover{background: "+$.paletteTitleHoverBackground+" !important}"),X=document.createElement("style");m||(X.styleSheet?X.styleSheet.cssText=N:X.appendChild(a().createTextNode(N))),a().getElementsByTagName("head")[0].appendChild(X);$.def(L,n,{ms_listener:1,getView:function(){return this.$22h.getView()},$26h:function(){var N=this;N.$36h&&k().removeChild(N.$36h),N.$23h=N.$24h=N.$25h=N.$35h=N.$36h=R},handle_touchstart:function(N){for(var n,a,h=this,L=h.$22h,k=N.target,M=L.sm(),m=L.dm(),X="palette-header",V="palette-item",B=!1,p=!1,t=!1;k&&(k[P]||"").indexOf(X)<0&&(k[P]||"").indexOf(V)<0;)k=k.parentNode;k&&0<=k[P].indexOf("palette-header-tool")?B=!0:k&&0<=k[P].indexOf(X)?t=!0:k&&0<=k[P].indexOf(V)&&(p=!0),$.isLeftButton(N)?h.$27h(N)?(h.$24h=$.getClientPoint(N),h.$25h=L.ty()):B?($.preventDefault(N),n=k.parentNode.$11h,(B=(a=m.getDataById(n)).s("tools")[k.toolIndex]).action&&B.action.call(L)):t?($.preventDefault(N),n=k.$11h,(a=m.getDataById(n)).isExpanded()?a.setExpanded(!1):a.setExpanded(!0)):p?(n=k.$11h,B=m.getDataById(n),M.ss(B),L.handleDragAndDrop&&($.preventDefault(N),B.s("draggable")&&(L.handleDragAndDrop(N,"prepare"),h.$35h=0)),B.s("draggable")||($.preventDefault(N),h.$24h=$.getClientPoint(N),h.$25h=L.ty())):($.preventDefault(N),h.$24h=$.getClientPoint(N),h.$25h=L.ty()):h.$26h(N)},handle_mousedown:function(N){this.handle_touchstart(N)},handle_mousewheel:function(N){this.handleScroll(N,N.wheelDelta/40,N.wheelDelta!==N.wheelDeltaX)},handle_DOMMouseScroll:function(N){this.handleScroll(N,-N.detail,1)},handleScroll:function(N,n,a){var h=this.$22h;$.preventDefault(N),a&&h._41o()&&h.ty(h.ty()+20*n)},handle_mouseup:function(N){this.handle_touchend(N)},handle_touchend:function(N){this.$37h(N),this.$26h(N)},handleWindowMouseUp:function(N){this.handleWindowTouchEnd(N)},handleWindowTouchEnd:function(N){this.$37h(N),this.$26h(N)},$37h:function(N){var n=this.$22h;2===this.$35h&&(this.$35h=3,n.handleDragAndDrop(N,"end"))},handleWindowMouseMove:function(N){this.handleWindowTouchMove(N)},handleWindowTouchMove:function(N){var n=this,a=n.$22h,h=n.$23h,L=n.$24h,k=n.$25h,M=$.getClientPoint(N),m=a._29I,X=n.$36h;1===n.$35h||2===n.$35h?(n.$35h=2,a.handleDragAndDrop(N,"between"),V(N)&&(N=N.touches[0]||N.changedTouches[0]),X.style.left=N.pageX-parseInt(X.width)/2+A,X.style.top=N.pageY-parseInt(X.height)/2+A):"p"===h?a.ty(k+M.y-L.y):"v"===h&&a.ty(k+(L.y-M.y)/m.height*a._59I)},handle_mousemove:function(N){this.handle_touchmove(N)},handle_touchmove:function(N){var n,a,h,L;!$.isDragging()&&$.isLeftButton(N)&&(h=(a=this).$22h,n=a.$27h(N),a.$24h?a.$23h||$.getDistance($.getClientPoint(N),a.$24h)<2||(a.$23h=n?"v":"p",$.startDragging(a,N)):n?h._43o():0===a.$35h&&(a.$35h=1,h.handleDragAndDrop(N,"begin"),$.startDragging(a,N),V(N)&&(N=N.touches[0]||N.changedTouches[0]),n=a.$36h=new Image,a=h.$10h[h.sm().ld().getId()].querySelector(".image-box"),h=parseInt(a.style.width),L=parseInt(a.style.height),n.draggable=!1,n.src=a.toDataURL(),n.width=h,n.height=L,n.style.position="absolute",n.style.left=N.pageX-h/2+A,n.style.top=N.pageY-L/2+A,k().appendChild(n)))},$27h:function(N){var n=this.$22h,a=n.getView(),h=a.getBoundingClientRect(),L=n._29I,N=N.clientX-h.left+a.scrollLeft;return n._41o()&&L.x+L.width-N<B}}),ht.widget.Palette=function(N){var n,a=this,h=a._view=e.createView(null,a);a.$9h={},a.$10h={},a.$4h={},a._29I={x:0,y:0,width:0,height:0},a._59I=0,a.dm(N||new ht.DataModel),h[P]="ht-widget-palette",a.$29h=new L(a),w(h,d,$.paletteContentBackground),w(h,O,"auto"),w(h,H,J),w(h,"-moz-"+H,J),w(h,"-webkit-"+M,K),w(h,"-moz-"+M,K),w(h,"-ms-"+M,K),w(h,M,K),w(h,"position","absolute"),w(h,"overflow","hidden"),W(h,a._79O=(N=p(),(n=N.style).msTouchAction=K,n.cursor="default",m&&n.setProperty("-webkit-tap-highlight-color","rgba(0, 0, 0, 0)",R),n.position="absolute",n.left=r,n.top=r,N)),C(h,"dragstart",function(N){N.dataTransfer&&(N.dataTransfer.setData("Text","nodeid:"+N.target.$11h),N.dataTransfer.effectAllowed="all",a.$29h.$26h())})},h="Palette",N={ms_v:1,ms_fire:1,ms_dm:1,ms_sm:1,ms_vs:1,ms_bnb:1,ms_ac:["itemImageWidth","itemImageHeight","itemImagePadding","itemMargin","layout","autoHideScrollBar","scrollBarSize","scrollBarColor"],$30h:0,_itemImagePadding:4,_itemImageWidth:70,_itemImageHeight:50,_itemMargin:10,_layout:"largeicons",_autoHideScrollBar:$.autoHideScrollBar,_scrollBarSize:$.scrollBarSize,_scrollBarColor:$.scrollBarColor,getViewRect:function(){return this._29I},ty:function(N){if(N==R)return this.getTranslateY();this.setTranslateY(N)},setTranslateY:function(N){var n;this.$32h==R&&(N=this.$33h(N),n=this.$30h,this.$30h=N,this.fp("translateY",n,N))},getTranslateY:function(){return this.$30h},setLayout:function(N){var n,a,h=this,L=h._layout;"smallicons"===(h._layout=N)?n=a=20:"iconsonly"===N?n=a=50:(n=70,a=50),h.setItemImageWidth(n),h.setItemImageHeight(a),h.setItemImagePadding(4),h.fp("layout",L,N)},getDataAt:function(N){for(var n=N.target;n&&n.$11h==R;)n=n.parentNode;if(n&&n.$11h!=R)return this.getDataModel().getDataById(n.$11h)},$20h:function(){var N=16;return m&&(N*=1.2),N},$19h:function(){return $.paletteTitleHeight},$18h:function(){var N=$.paletteSeparatorWidth,n=$.paletteTitleBackground,n=$.paletteSeparatorColor||$.brighter(n);return N+A+" solid "+n},$17h:function(N){w(N,"cursor","pointer"),w(N,"display","inline-block"),w(N,"margin-right",(m?8:4)+A),w(N,"vertical-align",g)},$1h:function(N){var n=this,a=p(),h=p(),L=S("span"),k=(a[P]="palette-header",w(a,"position","relative"),w(a,d,$.paletteTitleBackground),w(a,"color",$.paletteTitleLabelColor),w(a,g,r),w(a,H,J),w(a,"-moz-"+H,J),w(a,"padding","0 5px 0 0"),w(a,"border-top",n.$18h()),w(a,"width","100%"),w(a,"cursor","pointer"),w(a,"white-space","nowrap"),w(a,O,"hidden"),w(a,u,$.paletteTitleLabelFont),w(a,"line-height",n.$19h()+A),a.$11h=N.getId(),t()),M=n.$19h(),m=n.$20h(),X=(n.$17h(k),e.setCanvas(k,m,M),W(a,k),N.s("tools"));if(X)for(var V=0;V<X.length;V++){var B=t();n.$17h(B),e.setCanvas(B,m,M),B[P]="palette-header-tool palette-header-tool"+N.getId()+"-"+V,B.style.position="absolute",B.style.right=(m+10)*V+"px",B.toolIndex=V,W(a,B)}return k[P]="palette-toggle-icon-"+N.getId(),h[P]="palette-content",w(h,"max-height",0+A),w(h,u,$.paletteContentLabelFont),w(h,O,"hidden"),h.$11h=N.getId(),n.$9h[N.getId()]=h,L[Z]=N.getName(),w(L,u,$.paletteTitleLabelFont),W(a,k),W(a,L),[a,h]},$2h:function(N){var n=this,a=n._layout,h=c&&N.s("draggable")?S("a"):p(),L=t(),k=p(),M=N.getName()||"",m=N.s("title")||N.getToolTip()||M,X=n._itemMargin,V=(L[P]="image-box",n.getItemImageWidth()),B=n.getItemImageHeight();return e.setCanvas(L,V,B),W(h,L),k[Z]=M,k[P]="label-box","iconsonly"!==a&&W(h,k),h[P]="palette-item",w(h,"vertical-align",g),w(h,"cursor","pointer"),w(h,"border-radius",5+A),w(h,"border","1px solid transparent"),w(h,"text-align","center"),w(h,"display","inline-block"),w(h,"margin-left",X+A),w(h,"margin-top",X+A),w(h,"color",$.paletteContentLabelColor),"smallicons"===a?(w(L,"vertical-align","middle"),w(h,"margin-left",2+A),w(h,"margin-top",2+A),w(h,"padding",2+A),w(h,"text-align","left"),w(k,"display","inline-block"),w(k,"min-width",n.$21h+n._itemMargin+A)):"largeicons"===a&&(w(k,"max-width",V+A),w(k,"overflow","hidden")),h.$11h=N.getId(),m&&(h.title=m),N.s("draggable")&&!n.handleDragAndDrop&&(c?(h.href="#",w(h,"text-decoration",K)):(h.draggable="true",w(h,"transform","translate(0,0)"),w(h,"z-index","0"))),h},$16h:function(N,n,a,h){N=e.initContext(N),e.translateAndScale(N,0,0,1),N.clearRect(0,0,a,a),a=(a-h)/2;$.drawStretchImage(N,$.getImage(n),"fill",0,a,h,h),N.restore()},$15h:function(N){var n,a,h=N.getId(),h=this._view.querySelector(".palette-toggle-icon-"+h),N=N.isExpanded()?$.paletteCollapseIcon:$.paletteExpandIcon;h&&N&&(n=this.$19h(),a=this.$20h(),this.$16h(h,N,n,a))},_drawToolsIcon:function(N){var n=N.s("tools");if(n)for(var a=0;a<n.length;a++){var h=this._view.querySelector(".palette-header-tool"+N.getId()+"-"+a),L=n[a].icon,k=this.$19h(),M=this.$20h();this.$16h(h,L,k,M)}},$14h:function(N){var n,a,h,L=this,k=N.getId(),k=L.$10h[k].querySelector(".image-box"),M=N.getImage(),m=N.s("image.stretch");k&&M&&(k=e.initContext(k),n=L.getItemImagePadding(),n="smallicons"===L._layout?n/2:n,a=L.getItemImageWidth()-2*n,h=L.getItemImageHeight()-2*n,e.translateAndScale(k,0,0,1),k.clearRect(0,0,a,h),$.drawStretchImage(k,$.getImage(M),m,n,n,a,h,N,L,L.getBodyColor(N)),k.restore())},validateImpl:function(){var a=this,N=a.$9h,n=a._layout,h=a.$10h,L=a.$4h,k=a._view,M=a.dm();if(a.$13h&&(delete a.$13h,L={},M.each(function(N){L[N.getId()]=N})),"smallicons"===n)for(var m in L){var X=L[m];X instanceof o&&(X=X.getName()||"",X=$.getTextSize($.paletteContentLabelFont,X).width,a.$21h!=R&&a.$21h>X||(a.$21h=X))}for(m in L){var V,B,p,t,w,P,g,e,K,u=L[m];M.contains(u)?u instanceof ht.Group?(p=a.$1h(u),(w=h[u.getId()])&&(V=w.nextSibling,F(k,V),F(k,w)),w=M.getSiblings(u).indexOf(u),(B=k.children[2*w]||a._79O)&&B.parentNode?(k.insertBefore(p[0],B),k.insertBefore(V||p[1],B)):(k.appendChild(p[0]),k.appendChild(V||p[1])),h[u.getId()]=p[0],t=N[u.getId()]=V||p[1],B=u.$12h,p=u.s("promptText"),B||(u.$12h=S("div"),u.$12h[Z]=p||"",B=u.$12h),0===u.getChildren().size()?t.contains(B)||W(t,B):t.contains(B)&&F(t,B)):(P=u.getParent())&&(p=a.$2h(u),g=h[u.getId()],t=N[P.getId()],g&&F(g.parentNode,g),w=M.getSiblings(u).indexOf(u),(g=t.children[w])?t.insertBefore(p,g):W(t,p),h[u.getId()]=p,a.$14h(u)):(e=(g=h[u.getId()]).parentNode,u instanceof ht.Group?(K=g.nextSibling,F(k,g),F(k,K),delete N[u.getId()]):(F(e,g),0===e.children.length&&(P=M.getDataById(e.$11h))&&(B=P.$12h)&&!e.contains(B)&&W(e,B)),delete h[u.getId()])}a.$4h={};function d(){var N=a._59I,n=0;a.$32h!=R&&(clearInterval(a.$32h),n=0,delete a.$32h),a.$32h=setInterval(function(){a.$31h(),N===a._59I?2<=++n&&(clearInterval(a.$32h),delete a.$32h):(n=0,N=a._59I)},30)}var J,H;for(J in N)t=N[J],u=M.getDataById(N[J].$11h),a.$15h(u),a._drawToolsIcon(u),u.isExpanded()?(t.style.maxHeight===0+A?(H=t.scrollHeight+a._itemMargin+A,l(t).duration(200).set(T,H).set("padding-bottom",a._itemMargin+A).end(function(){d()})):t.style.maxHeight=t.scrollHeight+A,t.style.paddingBottom=a._itemMargin+A):t.style.maxHeight!==0+A&&l(t).duration(200).set(T,r).set("padding-bottom",r).end(function(){d()});a.$28h(),a.$31h()},$31h:function(){for(var N=this,n=N._view,a=0,h=n.children,L=0;L<h.length;L++){var k=h[L];k.className&&0<=k.className.indexOf("palette-")&&(a+=k.offsetHeight)}N._59I=a,N.$30h=N.$33h(N.ty());var M=N.ty();n.scrollTop=-M,N._29I={x:0,y:-M,width:n.clientWidth,height:n.clientHeight},w(N._79O,g,-M+A),N._93I()},$33h:function(N){var n=this._29I.height-this._59I;return 0<(N=N<n?n:N)?0:Math.round(N)},redraw:function(){this.$13h||(this.$13h=1,this.iv())},onPropertyChanged:function(N){["autoHideScrollBar","scrollBarSize","scrollBarColor","translateY"].indexOf(N.property)<0&&this.redraw(),"translateY"===N.property&&(this.iv(),this._43o())},findDataByName:function(N){for(var n=this.dm().getDatas(),a=0;a<n.size();a++){var h=n.get(a);if(h.getName()===N)return h}},setDataModel:function(N){var n=this,a=n._dataModel,h=n._selectionModel;a!==N&&(a&&(a.umm(n.$6h,n),a.umd(n.$8h,n),a.umh(n.$7h,n),h||a.sm().ums(n.$28h,n)),(n._dataModel=N).mm(n.$6h,n),N.md(n.$8h,n),N.mh(n.$7h,n),h?h._21I(N):N.sm().ms(n.$28h,n),n.sm().setSelectionMode("single"),n.fp("dataModel",a,N))},$6h:function(N){var n=this,a=n._view,h=N.data,L=n.$4h;"add"===N.kind||"remove"===N.kind?L[h.getId()]=h:"clear"===N.kind&&(n.$10h={},n.$9h={},n.$4h={},a[Z]=""),n.iv()},$7h:function(N){N=N.data;this.$4h[N.getId()]=N,this.iv()},$8h:function(N){var n=N.data;"expanded"!==N.property&&(this.$4h[n.getId()]=n),this.iv()},$28h:function(){var n,a=this,N=a.sm(),h=N.ld();this.dm().each(function(N){(n=a.$10h[N.getId()])&&0<=n[P].indexOf("palette-item")&&(N===h?w(n,d,$.paletteItemSelectBackground):n.style.removeProperty(d))})}},$.def(ht.widget[h],n,N)}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);