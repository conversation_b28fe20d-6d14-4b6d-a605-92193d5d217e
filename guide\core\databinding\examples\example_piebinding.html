<!DOCTYPE html>
<html>
    <head>
        <title>Pie Data Binding</title>
        <meta charset="UTF-8">
        <style>
            html, body {
                padding: 0px;
                margin: 0px;
            }
            .main {
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 0px;
                bottom: 0px;
                left: 0px;
                right: 0px;
            }
        </style>
        <script src="../../../../lib/core/ht.js"></script>
        <script>
            function init() {
                dataModel = new ht.DataModel();
                graphView = new ht.graph.GraphView(dataModel);
                view = graphView.getView();

                view.className = 'main';
                document.body.appendChild(view);
                window.addEventListener('resize', function(e) {
                    graphView.invalidate();
                }, false);

                ht.Default.setImage('pie', {
                    width: { func: 'getWidth' },
                    height: { func: 'field@_height' },
                    comps: [
                        {
                            type: 'pieChart',
                            hollow: {func: 'style@isHollow'},
                            startAngle: {func: 'attr@angle'},
                            rect: { func: function (data) { return [5, 5, 190, 190]; } },
                            label: true,
                            labelColor: 'white',
                            values: [56.15, 19.15, 17.17, 5.55, 1.98],
                            colors: ['#E74C3C', '#E2E2E2', '#34495E', '#3498DB', 'yellow']
                        }
                    ]
                });

                node = new ht.Node();
                node.setPosition(120, 120);
                node.setSize(200, 200);
                node.setAttr('angle', 0);
                node.setStyle('isHollow', true);
                node.setImage('pie');
                node.setStyle('note', 'Try to drag and double click on me');
                node.setStyle('note.position', 17);
                dataModel.add(node);

                graphView.addInteractorListener(function(e) {
                    if (e.kind === 'betweenMove') {
                       node.setAttr('angle', node.getAttr('angle') + Math.PI / 15);
                    }
                    else if(e.kind === 'doubleClickData'){
                        node.setStyle('isHollow', !node.getStyle('isHollow'));
                    }
                });
            }


        </script>
    </head>
    <body onload="init();">
    </body>
</html>
