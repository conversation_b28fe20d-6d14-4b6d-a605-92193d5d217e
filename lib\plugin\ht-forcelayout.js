!function(P,c){"use strict";function C(P){return P*P}var D="ht.layout.",l=P.ht||module.parent.exports.ht,H=l.List,I=l.DataModel,B=l.Node,N=l.Edge,S=l.Group,P=Math,i=P.sqrt,v=P.random,V=P.max,q=P.min,m=(l.Default.getInternal().addMSMap({ms_force:function(P,c){P._interval=50,P._stepCount=10,P._motionLimit=.01,P._edgeRepulsion=.65,P._nodeRepulsion=.65,P._damper=1,P._maxMotion=0,P._motionRatio=0,P.init=function(P){var c=this;P instanceof I?c.dm=P:c.gv=P,c._nodeMap={},c._nodes=new H,c._edges=new H},P.start=function(){var P,D=this,c=D.gv;D._timer||((P=D.cdm=c?c.dm():D.dm).mm(<PERSON>.handle<PERSON>,D),P.md(D.handleDataPropertyC<PERSON>,D),c&&c.mp(D.handleGV,D),P.each(function(P){var c;D.isVisible(P)&&D.isLayoutable(P)&&P instanceof B&&(D instanceof m?(c=P.p3(),P.p3([c[0]+v(),c[1]+v(),c[2]+v()])):(c=P.p(),P.p(c.x+v(),c.y+v())))}),D._timer=setInterval(function(){D.relax()},D._interval),D._damper=1)},P.stop=function(){var P=this;P._timer&&(P.cdm.umm(P.handleDataModelChange,P),P.cdm.umd(P.handleDataPropertyChange,P),P.gv&&P.gv.ump(P.handleGV,P),clearInterval(P._timer),delete P._timer,delete P.cdm)},P.handleGV=function(P){var c,D=this;"dataModel"===P.property&&(c=P.oldValue,P=P.newValue,c&&(c.umm(D.handleDataModelChange,D),c.umd(D.handleDataPropertyChange,D)),(this.cdm=P).mm(D.handleDataModelChange,D),P.md(D.handleDataPropertyChange,D))},P.relax=function(){var c=this;if(!(c._damper<.1&&c._maxMotion<c._motionLimit)){this.cdm.each(function(P){c.isVisible(P)&&(P instanceof N?c.addEdge(P):P instanceof B&&c.addNode(P))});for(var P,D,H=0,C=c._edges,l=c._nodes,I=l.size();H<c._stepCount;H++){for(C.each(c.relaxEdge,c),P=0;P<I;P++)for(D=0;D<I;D++)c.relaxNode(l.get(P),l.get(D));c.moveNode()}c._isAdjusting=1,l.each(function(P){P.fix||(P.p?P.v.p3(P.p):P.v.p(P.x,P.y))}),delete c._isAdjusting,c._nodeMap={},l.empty(),C.empty(),c.onRelaxed()}},P.onRelaxed=function(){},P.isRunning=function(){return!!this._timer},P.isVisible=function(P){return!1!==P.s("layoutable")&&(!this.gv||this.gv.isVisible(P))},P.isLayoutable=function(P){if(!1===P.s("layoutable"))return!1;if(P instanceof S)return!1;var c=this;return c.gv?c.gv.isMovable(P)&&!c.gv.isSelected(P):!(c.cdm||c.dm).sm().co(P)},P.getNodeRepulsion=function(){return this._nodeRepulsion},P.setNodeRepulsion=function(P){this._nodeRepulsion=P,this._damper=1},P.getEdgeRepulsion=function(){return this._edgeRepulsion},P.setEdgeRepulsion=function(P){this._edgeRepulsion=P,this._damper=1},P.getStepCount=function(){return this._stepCount},P.setStepCount=function(P){this._stepCount=P,this._damper=1},P.getInterval=function(){return this._interval},P.setInterval=function(P){var c=this;c._interval!==P&&(c._interval=P,c._timer&&(clearInterval(c._timer),c._timer=setInterval(function(){c.relax()},P)))},P.handleDataPropertyChange=function(P){!this._isAdjusting&&this.isVisible(P.data)&&(this._damper=1)},P.handleDataModelChange=function(P){this._damper=1},P.damp=function(){var P=this._maxMotion,c=this._damper;this._motionRatio<=.001&&((P<.2||1<P&&c<.9)&&.01<c?this._damper-=.01:P<.4&&.003<c?this._damper-=.003:1e-4<c&&(this._damper-=1e-4)),P<this._motionLimit&&(this._damper=0)}}}),l.layout.ForceLayout=function(P){this.init(P)},l.Default.def(D+"ForceLayout",c,{ms_force:1,getLimitBounds:function(){return this._limitBounds},setLimitBounds:function(P){this._limitBounds=P,this._damper=1},getNodeSize:function(P){var c=this.gv;return c&&c.getDataUIBounds?c.getDataUIBounds(P):P.getRect()},addNode:function(P){var c=this,D=c._nodeMap[P._id];if(D)return D;var H=P.p(),H=(D={v:P,x:H.x,y:H.y,dx:0,dy:0,fix:!c.isLayoutable(P),s:c.getNodeSize(P)}).s,H=i(C(H.width)+C(H.height))*c._nodeRepulsion;return D.r=H<1?100:H,c._nodeMap[P._id]=D,c._nodes.add(D),D},addEdge:function(P){var c,D,H;P._40I&&P._41I&&(c={s:D=this.addNode(P._40I),t:P=this.addNode(P._41I)},P=P.s,D=D.s,H=P.width+D.width,P=P.height+D.height,c.length=i(H*H+P*P)*this._edgeRepulsion,c.length<=0&&(c.length=100),this._edges.add(c))},relaxEdge:function(P){var c=P.t,D=P.s,H=c.x-D.x,C=c.y-D.y,l=i(H*H+C*C),P=100*P.length,H=.25*H/P*l,C=.25*C/P*l;c.dx=c.dx-H,c.dy=c.dy-C,D.dx=D.dx+H,D.dy=D.dy+C},relaxNode:function(P,c){var D,H,C,l,I;P!==c&&((H=D=0)==(l=(I=P.x-c.x)*I+(C=P.y-c.y)*C)?(D=v(),H=v()):l<36e4&&(D=I/l,H=C/l),H*=I=P.r*c.r/400,P.dx+=D*=I,P.dy+=H,c.dx-=D,c.dy-=H)},moveNode:function(){var H=this,C=H._limitBounds,P=H._maxMotion,l=0,I=H._damper;H._nodes.each(function(P){var c,D;P.fix||(D=P.dx*I,c=P.dy*I,P.dx=D/2,P.dy=c/2,l=V(i(D*D+c*c),l),P.x+=V(-40,q(40,D)),P.y+=V(-40,q(40,c)),C&&(P.x<C.x&&(P.x=C.x,H.adjust(1,0)),P.y<C.y&&(P.y=C.y,H.adjust(0,1)),D=P.s,P.x+D.width>C.x+C.width&&(P.x=C.x+C.width-D.width,H.adjust(-1,0)),P.y+D.height>C.y+C.height&&(P.y=C.y+C.height-D.height,H.adjust(0,-1))))}),H._maxMotion=l,H._motionRatio=0<l?P/l-1:0,H.damp()},adjust:function(c,D){var H=this._limitBounds;this._nodes.each(function(P){0<c?(!H||P.x+P.s.width+c<H.x+H.width)&&(P.x+=c):(!H||P.x+c>H.x)&&(P.x+=c),0<D?(!H||P.y+P.s.height+D<H.y+H.height)&&(P.y+=D):(!H||P.y+D>H.y)&&(P.y+=D)})}}),l.layout.Force3dLayout=function(P){this.init(P)});l.Default.def(D+"Force3dLayout",c,{ms_force:1,getNodeSize3d:function(P){return P.s3()},addNode:function(P){var c=this,D=c._nodeMap[P._id];if(D)return D;var H=(D={v:P,p:P.p3(),d:[0,0,0],fix:!c.isLayoutable(P),s:c.getNodeSize3d(P)}).s,H=l.Default.getDistance(H)*c._nodeRepulsion;return D.r=H<1?100:H,c._nodeMap[P._id]=D,c._nodes.add(D),D},addEdge:function(P){var c,D;P._40I&&P._41I&&(c={s:D=this.addNode(P._40I),t:P=this.addNode(P._41I)},P=P.s,D=D.s,c.length=i(C(P[0]+D[0])+C(P[1]+D[1])+C(P[2]+D[2]))*this._edgeRepulsion,c.length<=0&&(c.length=100),this._edges.add(c))},relaxEdge:function(P){var c=P.t.p,D=P.s.p,H=P.t.d,C=P.s.d,l=c[0]-D[0],I=c[1]-D[1],c=c[2]-D[2],D=i(l*l+I*I+c*c),P=100*P.length,l=.25*l/P*D,I=.25*I/P*D,c=.25*c/P*D;H[0]-=l,H[1]-=I,H[2]-=c,C[0]+=l,C[1]+=I,C[2]+=c},relaxNode:function(P,c){var D,H,C,l,I,B,N;P!==c&&(N=P.p,l=c.p,(C=H=D=0)==(l=(I=N[0]-l[0])*I+(B=N[1]-l[1])*B+(N=N[2]-l[2])*N)?(D=v(),H=v(),C=v()):l<216e6&&(D=I/l,H=B/l,C=N/l),I=P.r*c.r/400,B=P.d,N=c.d,H*=I,C*=I,B[0]+=D*=I,B[1]+=H,B[2]+=C,N[0]-=D,N[1]-=H,N[2]-=C)},moveNode:function(){var P=this,c=P._maxMotion,l=0,I=P._damper;P._nodes.each(function(P){var c,D,H,C;P.fix||(c=P.p,D=(P=P.d)[0]*I,H=P[1]*I,C=P[2]*I,P[0]=D/2,P[1]=H/2,P[2]=C/2,l=V(i(D*D+H*H+C*C),l),c[0]+=V(-40,q(40,D)),c[1]+=V(-40,q(40,H)),c[2]+=V(-40,q(40,C)))}),P._maxMotion=l,P._motionRatio=0<l?c/l-1:0,P.damp()}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);