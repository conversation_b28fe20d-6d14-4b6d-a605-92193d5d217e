!function(r,z0,NT){"use strict";(function(g){"use strict";var y,i=function g(P,q,U,$){var m=P&65535|0,r=P>>>16&65535|0,Y=0;while(U!==0){Y=U>2e3?2e3:U;U-=Y;do{m=m+q[$++]|0;r=r+m|0}while(--Y);m%=65521;r%=65521}return m|r<<16|0},Q,k=new Uint32Array(function g(){var P,q=[];for(var U=0;U<256;U++){P=U;for(var $=0;$<8;$++)P=P&1?3988292384^P>>>1:P>>>1;q[U]=P}return q}()),F,_=function g(P,q,U,$){var m=k;var r=$+U;P^=-1;for(var Y=$;Y<r;Y++)P=P>>>8^m[(P^q[Y])&255];return P^-1},n=16209,z=16191,h=function g(P,q){var U;var $;var m;var r;var Y;var x;var v;var y;var b;var w;var A;var s;var S;var Q;var k;var F;var N;var o;var V;var B;var u;var l;var e,T;var c=P.state;U=P.next_in;e=P.input;$=U+(P.avail_in-5);m=P.next_out;T=P.output;r=m-(q-P.avail_out);Y=m+(P.avail_out-257);x=c.dmax;v=c.wsize;y=c.whave;b=c.wnext;w=c.window;A=c.hold;s=c.bits;S=c.lencode;Q=c.distcode;k=(1<<c.lenbits)-1;F=(1<<c.distbits)-1;g:do{if(s<15){A+=e[U++]<<s;s+=8;A+=e[U++]<<s;s+=8}N=S[A&k];P:for(;;){o=N>>>24;A>>>=o;s-=o;o=N>>>16&255;if(o===0)T[m++]=N&65535;else if(o&16){V=N&65535;o&=15;if(o){if(s<o){A+=e[U++]<<s;s+=8}V+=A&(1<<o)-1;A>>>=o;s-=o}if(s<15){A+=e[U++]<<s;s+=8;A+=e[U++]<<s;s+=8}N=Q[A&F];q:for(;;){o=N>>>24;A>>>=o;s-=o;o=N>>>16&255;if(o&16){B=N&65535;o&=15;if(s<o){A+=e[U++]<<s;s+=8;if(s<o){A+=e[U++]<<s;s+=8}}B+=A&(1<<o)-1;if(B>x){P.msg="invalid distance too far back";c.mode=n;break g}A>>>=o;s-=o;o=m-r;if(B>o){o=B-o;if(o>y)if(c.sane){P.msg="invalid distance too far back";c.mode=n;break g}u=0;l=w;if(b===0){u+=v-o;if(o<V){V-=o;do{T[m++]=w[u++]}while(--o);u=m-B;l=T}}else if(b<o){u+=v+b-o;o-=b;if(o<V){V-=o;do{T[m++]=w[u++]}while(--o);u=0;if(b<V){o=b;V-=o;do{T[m++]=w[u++]}while(--o);u=m-B;l=T}}}else{u+=b-o;if(o<V){V-=o;do{T[m++]=w[u++]}while(--o);u=m-B;l=T}}while(V>2){T[m++]=l[u++];T[m++]=l[u++];T[m++]=l[u++];V-=3}if(V){T[m++]=l[u++];if(V>1)T[m++]=l[u++]}}else{u=m-B;do{T[m++]=T[u++];T[m++]=T[u++];T[m++]=T[u++];V-=3}while(V>2);if(V){T[m++]=T[u++];if(V>1)T[m++]=T[u++]}}}else if((o&64)===0){N=Q[(N&65535)+(A&(1<<o)-1)];continue q}else{P.msg="invalid distance code";c.mode=n;break g}break}}else if((o&64)===0){N=S[(N&65535)+(A&(1<<o)-1)];continue P}else if(o&32){c.mode=z;break g}else{P.msg="invalid literal/length code";c.mode=n;break g}break}}while(U<$&&m<Y);V=s>>3;U-=V;s-=V<<3;A&=(1<<s)-1;P.next_in=U;P.next_out=m;P.avail_in=U<$?5+($-U):5-(U-$);P.avail_out=m<Y?257+(Y-m):257-(m-Y);c.hold=A;c.bits=s;return},W=15,R=852,L=592,f=0,Z=1,K=2,H=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),gT=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),PT=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),qT=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]),N,p=function g(P,q,U,$,m,r,Y,x){var v=x.bits;var y=0;var b=0;var w=0,A=0;var s=0;var S=0;var Q=0;var k=0;var F=0;var N=0;var o;var V;var B;var u;var l;var e=null;var T;var c=new Uint16Array(W+1);var n=new Uint16Array(W+1);var z=null;var i,_,p;for(y=0;y<=W;y++)c[y]=0;for(b=0;b<$;b++)c[q[U+b]]++;s=v;for(A=W;A>=1;A--)if(c[A]!==0)break;if(s>A)s=A;if(A===0){m[r++]=1<<24|64<<16|0;m[r++]=1<<24|64<<16|0;x.bits=1;return 0}for(w=1;w<A;w++)if(c[w]!==0)break;if(s<w)s=w;k=1;for(y=1;y<=W;y++){k<<=1;k-=c[y];if(k<0)return-1}if(k>0&&(P===f||A!==1))return-1;n[1]=0;for(y=1;y<W;y++)n[y+1]=n[y]+c[y];for(b=0;b<$;b++)if(q[U+b]!==0)Y[n[q[U+b]]++]=b;if(P===f){e=z=Y;T=20}else if(P===Z){e=H;z=gT;T=257}else{e=PT;z=qT;T=0}N=0;b=0;y=w;l=r;S=s;Q=0;B=-1;F=1<<s;u=F-1;if(P===Z&&F>R||P===K&&F>L)return 1;for(;;){i=y-Q;if(Y[b]+1<T){_=0;p=Y[b]}else if(Y[b]>=T){_=z[Y[b]-T];p=e[Y[b]-T]}else{_=32+64;p=0}o=1<<y-Q;V=1<<S;w=V;do{V-=o;m[l+(N>>Q)+V]=i<<24|_<<16|p|0}while(V!==0);o=1<<y-1;while(N&o)o>>=1;if(o!==0){N&=o-1;N+=o}else N=0;b++;if(--c[y]===0){if(y===A)break;y=q[U+Y[b]]}if(y>s&&(N&u)!==B){if(Q===0)Q=s;l+=w;S=y-Q;k=1<<S;while(S+Q<A){k-=c[S+Q];if(k<=0)break;S++;k<<=1}F+=1<<S;if(P===Z&&F>R||P===K&&F>L)return 1;B=N&u;m[B]=s<<24|S<<16|l-r|0}}if(N!==0)m[l+N]=y-Q<<24|64<<16|0;x.bits=s;return 0},P={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},UT=0,$T=1,mT=2,rT=P.Z_FINISH,YT=P.Z_BLOCK,C=P.Z_TREES,d=P.Z_OK,xT=P.Z_STREAM_END,vT=P.Z_NEED_DICT,j=P.Z_STREAM_ERROR,yT=P.Z_DATA_ERROR,bT=P.Z_MEM_ERROR,wT=P.Z_BUF_ERROR,AT=P.Z_DEFLATED,G=16180,sT=16181,ST=16182,QT=16183,kT=16184,FT=16185,NT=16186,oT=16187,VT=16188,BT=16189,X=16190,a=16191,D=16192,uT=16193,t=16194,lT=16195,eT=16196,TT=16197,cT=16198,M=16199,O=16200,nT=16201,zT=16202,iT=16203,_T=16204,pT=16205,E=16206,WT=16207,RT=16208,I=16209,LT=16210,ZT=16211,o=852,V=592,B,u=15,CT=function g(P){return(P>>>24&255)+(P>>>8&65280)+((P&65280)<<8)+((P&255)<<24)};function l(){this.strm=null;this.mode=0;this.last=false;this.wrap=0;this.havedict=false;this.flags=0;this.dmax=0;this.check=0;this.total=0;this.head=null;this.wbits=0;this.wsize=0;this.whave=0;this.wnext=0;this.window=null;this.hold=0;this.bits=0;this.length=0;this.offset=0;this.extra=0;this.lencode=null;this.distcode=null;this.lenbits=0;this.distbits=0;this.ncode=0;this.nlen=0;this.ndist=0;this.have=0;this.next=null;this.lens=new Uint16Array(320);this.work=new Uint16Array(288);this.lendyn=null;this.distdyn=null;this.sane=0;this.back=0;this.was=0}var J=function g(P){if(!P)return 1;var q=P.state;if(!q||q.strm!==P||q.mode<G||q.mode>ZT)return 1;return 0},e=function g(P){if(J(P))return j;var q=P.state;P.total_in=P.total_out=q.total=0;P.msg="";if(q.wrap)P.adler=q.wrap&1;q.mode=G;q.last=0;q.havedict=0;q.flags=-1;q.dmax=32768;q.head=null;q.hold=0;q.bits=0;q.lencode=q.lendyn=new Int32Array(o);q.distcode=q.distdyn=new Int32Array(V);q.sane=1;q.back=-1;return d},T=function g(P){if(J(P))return j;var q=P.state;q.wsize=0;q.whave=0;q.wnext=0;return e(P)},c=function g(P,q){var U;if(J(P))return j;var $=P.state;if(q<0){U=0;q=-q}else{U=(q>>4)+5;if(q<48)q&=15}if(q&&(q<8||q>15))return j;if($.window!==null&&$.wbits!==q)$.window=null;$.wrap=U;$.wbits=q;return T(P)},dT=function g(P,q){if(!P)return j;var U=new l;P.state=U;U.strm=P;U.window=null;U.mode=G;var $=c(P,q);if($!==d)P.state=null;return $},jT,GT=true,U,$,XT=function g(P){if(GT){U=new Int32Array(512);$=new Int32Array(32);var q=0;while(q<144)P.lens[q++]=8;while(q<256)P.lens[q++]=9;while(q<280)P.lens[q++]=7;while(q<288)P.lens[q++]=8;p($T,P.lens,0,288,U,0,P.work,{bits:9});q=0;while(q<32)P.lens[q++]=5;p(mT,P.lens,0,32,$,0,P.work,{bits:5});GT=false}P.lencode=U;P.lenbits=9;P.distcode=$;P.distbits=5},aT=function g(P,q,U,$){var m;var r=P.state;if(r.window===null){r.wsize=1<<r.wbits;r.wnext=0;r.whave=0;r.window=new Uint8Array(r.wsize)}if($>=r.wsize){r.window.set(q.subarray(U-r.wsize,U),0);r.wnext=0;r.whave=r.wsize}else{m=r.wsize-r.wnext;if(m>$)m=$;r.window.set(q.subarray(U-$,U-$+m),r.wnext);$-=m;if($){r.window.set(q.subarray(U-$,U),0);r.wnext=$;r.whave=r.wsize}else{r.wnext+=m;if(r.wnext===r.wsize)r.wnext=0;if(r.whave<r.wsize)r.whave+=m}}return 0},DT,tT,MT,OT,ET,IT,JT,hT,fT,KT,HT,g0,P0,q0,b={inflateReset:T,inflateReset2:c,inflateResetKeep:e,inflateInit:function g(P){return dT(P,u)},inflateInit2:dT,inflate:function g(P,q){var U;var $,m;var r;var Y;var x,v;var y;var b;var w,A;var s;var S;var Q;var k=0;var F,N,o;var V,B,u;var l;var e;var T=new Uint8Array(4);var c;var n;var z=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(J(P)||!P.output||!P.input&&P.avail_in!==0)return j;U=P.state;if(U.mode===a)U.mode=D;Y=P.next_out;m=P.output;v=P.avail_out;r=P.next_in;$=P.input;x=P.avail_in;y=U.hold;b=U.bits;w=x;A=v;e=d;g:for(;;)switch(U.mode){case G:if(U.wrap===0){U.mode=D;break}while(b<16){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if(U.wrap&2&&y===35615){if(U.wbits===0)U.wbits=15;U.check=0;T[0]=y&255;T[1]=y>>>8&255;U.check=_(U.check,T,2,0);y=0;b=0;U.mode=sT;break}if(U.head)U.head.done=false;if(!(U.wrap&1)||(((y&255)<<8)+(y>>8))%31){P.msg="incorrect header check";U.mode=I;break}if((y&15)!==AT){P.msg="unknown compression method";U.mode=I;break}y>>>=4;b-=4;l=(y&15)+8;if(U.wbits===0)U.wbits=l;if(l>15||l>U.wbits){P.msg="invalid window size";U.mode=I;break}U.dmax=1<<U.wbits;U.flags=0;P.adler=U.check=1;U.mode=y&512?BT:a;y=0;b=0;break;case sT:while(b<16){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}U.flags=y;if((U.flags&255)!==AT){P.msg="unknown compression method";U.mode=I;break}if(U.flags&57344){P.msg="unknown header flags set";U.mode=I;break}if(U.head)U.head.text=y>>8&1;if(U.flags&512&&U.wrap&4){T[0]=y&255;T[1]=y>>>8&255;U.check=_(U.check,T,2,0)}y=0;b=0;U.mode=ST;case ST:while(b<32){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if(U.head)U.head.time=y;if(U.flags&512&&U.wrap&4){T[0]=y&255;T[1]=y>>>8&255;T[2]=y>>>16&255;T[3]=y>>>24&255;U.check=_(U.check,T,4,0)}y=0;b=0;U.mode=QT;case QT:while(b<16){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if(U.head){U.head.xflags=y&255;U.head.os=y>>8}if(U.flags&512&&U.wrap&4){T[0]=y&255;T[1]=y>>>8&255;U.check=_(U.check,T,2,0)}y=0;b=0;U.mode=kT;case kT:if(U.flags&1024){while(b<16){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}U.length=y;if(U.head)U.head.extra_len=y;if(U.flags&512&&U.wrap&4){T[0]=y&255;T[1]=y>>>8&255;U.check=_(U.check,T,2,0)}y=0;b=0}else if(U.head)U.head.extra=null;U.mode=FT;case FT:if(U.flags&1024){s=U.length;if(s>x)s=x;if(s){if(U.head){l=U.head.extra_len-U.length;if(!U.head.extra)U.head.extra=new Uint8Array(U.head.extra_len);U.head.extra.set($.subarray(r,r+s),l)}if(U.flags&512&&U.wrap&4)U.check=_(U.check,$,s,r);x-=s;r+=s;U.length-=s}if(U.length)break g}U.length=0;U.mode=NT;case NT:if(U.flags&2048){if(x===0)break g;s=0;do{l=$[r+s++];if(U.head&&l&&U.length<65536)U.head.name+=String.fromCharCode(l)}while(l&&s<x);if(U.flags&512&&U.wrap&4)U.check=_(U.check,$,s,r);x-=s;r+=s;if(l)break g}else if(U.head)U.head.name=null;U.length=0;U.mode=oT;case oT:if(U.flags&4096){if(x===0)break g;s=0;do{l=$[r+s++];if(U.head&&l&&U.length<65536)U.head.comment+=String.fromCharCode(l)}while(l&&s<x);if(U.flags&512&&U.wrap&4)U.check=_(U.check,$,s,r);x-=s;r+=s;if(l)break g}else if(U.head)U.head.comment=null;U.mode=VT;case VT:if(U.flags&512){while(b<16){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if(U.wrap&4&&y!==(U.check&65535)){P.msg="header crc mismatch";U.mode=I;break}y=0;b=0}if(U.head){U.head.hcrc=U.flags>>9&1;U.head.done=true}P.adler=U.check=0;U.mode=a;break;case BT:while(b<32){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}P.adler=U.check=CT(y);y=0;b=0;U.mode=X;case X:if(U.havedict===0){P.next_out=Y;P.avail_out=v;P.next_in=r;P.avail_in=x;U.hold=y;U.bits=b;return vT}P.adler=U.check=1;U.mode=a;case a:if(q===YT||q===C)break g;case D:if(U.last){y>>>=b&7;b-=b&7;U.mode=E;break}while(b<3){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}U.last=y&1;y>>>=1;b-=1;switch(y&3){case 0:U.mode=uT;break;case 1:XT(U);U.mode=M;if(q===C){y>>>=2;b-=2;break g}break;case 2:U.mode=eT;break;case 3:P.msg="invalid block type";U.mode=I}y>>>=2;b-=2;break;case uT:y>>>=b&7;b-=b&7;while(b<32){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if((y&65535)!==(y>>>16^65535)){P.msg="invalid stored block lengths";U.mode=I;break}U.length=y&65535;y=0;b=0;U.mode=t;if(q===C)break g;case t:U.mode=lT;case lT:s=U.length;if(s){if(s>x)s=x;if(s>v)s=v;if(s===0)break g;m.set($.subarray(r,r+s),Y);x-=s;r+=s;v-=s;Y+=s;U.length-=s;break}U.mode=a;break;case eT:while(b<14){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}U.nlen=(y&31)+257;y>>>=5;b-=5;U.ndist=(y&31)+1;y>>>=5;b-=5;U.ncode=(y&15)+4;y>>>=4;b-=4;if(U.nlen>286||U.ndist>30){P.msg="too many length or distance symbols";U.mode=I;break}U.have=0;U.mode=TT;case TT:while(U.have<U.ncode){while(b<3){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}U.lens[z[U.have++]]=y&7;y>>>=3;b-=3}while(U.have<19)U.lens[z[U.have++]]=0;U.lencode=U.lendyn;U.lenbits=7;c={bits:U.lenbits};e=p(UT,U.lens,0,19,U.lencode,0,U.work,c);U.lenbits=c.bits;if(e){P.msg="invalid code lengths set";U.mode=I;break}U.have=0;U.mode=cT;case cT:while(U.have<U.nlen+U.ndist){for(;;){k=U.lencode[y&(1<<U.lenbits)-1];F=k>>>24;N=k>>>16&255;o=k&65535;if(F<=b)break;if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if(o<16){y>>>=F;b-=F;U.lens[U.have++]=o}else{if(o===16){n=F+2;while(b<n){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}y>>>=F;b-=F;if(U.have===0){P.msg="invalid bit length repeat";U.mode=I;break}l=U.lens[U.have-1];s=3+(y&3);y>>>=2;b-=2}else if(o===17){n=F+3;while(b<n){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}y>>>=F;b-=F;l=0;s=3+(y&7);y>>>=3;b-=3}else{n=F+7;while(b<n){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}y>>>=F;b-=F;l=0;s=11+(y&127);y>>>=7;b-=7}if(U.have+s>U.nlen+U.ndist){P.msg="invalid bit length repeat";U.mode=I;break}while(s--)U.lens[U.have++]=l}}if(U.mode===I)break;if(U.lens[256]===0){P.msg="invalid code -- missing end-of-block";U.mode=I;break}U.lenbits=9;c={bits:U.lenbits};e=p($T,U.lens,0,U.nlen,U.lencode,0,U.work,c);U.lenbits=c.bits;if(e){P.msg="invalid literal/lengths set";U.mode=I;break}U.distbits=6;U.distcode=U.distdyn;c={bits:U.distbits};e=p(mT,U.lens,U.nlen,U.ndist,U.distcode,0,U.work,c);U.distbits=c.bits;if(e){P.msg="invalid distances set";U.mode=I;break}U.mode=M;if(q===C)break g;case M:U.mode=O;case O:if(x>=6&&v>=258){P.next_out=Y;P.avail_out=v;P.next_in=r;P.avail_in=x;U.hold=y;U.bits=b;h(P,A);Y=P.next_out;m=P.output;v=P.avail_out;r=P.next_in;$=P.input;x=P.avail_in;y=U.hold;b=U.bits;if(U.mode===a)U.back=-1;break}U.back=0;for(;;){k=U.lencode[y&(1<<U.lenbits)-1];F=k>>>24;N=k>>>16&255;o=k&65535;if(F<=b)break;if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if(N&&(N&240)===0){V=F;B=N;u=o;for(;;){k=U.lencode[u+((y&(1<<V+B)-1)>>V)];F=k>>>24;N=k>>>16&255;o=k&65535;if(V+F<=b)break;if(x===0)break g;x--;y+=$[r++]<<b;b+=8}y>>>=V;b-=V;U.back+=V}y>>>=F;b-=F;U.back+=F;U.length=o;if(N===0){U.mode=pT;break}if(N&32){U.back=-1;U.mode=a;break}if(N&64){P.msg="invalid literal/length code";U.mode=I;break}U.extra=N&15;U.mode=nT;case nT:if(U.extra){n=U.extra;while(b<n){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}U.length+=y&(1<<U.extra)-1;y>>>=U.extra;b-=U.extra;U.back+=U.extra}U.was=U.length;U.mode=zT;case zT:for(;;){k=U.distcode[y&(1<<U.distbits)-1];F=k>>>24;N=k>>>16&255;o=k&65535;if(F<=b)break;if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if((N&240)===0){V=F;B=N;u=o;for(;;){k=U.distcode[u+((y&(1<<V+B)-1)>>V)];F=k>>>24;N=k>>>16&255;o=k&65535;if(V+F<=b)break;if(x===0)break g;x--;y+=$[r++]<<b;b+=8}y>>>=V;b-=V;U.back+=V}y>>>=F;b-=F;U.back+=F;if(N&64){P.msg="invalid distance code";U.mode=I;break}U.offset=o;U.extra=N&15;U.mode=iT;case iT:if(U.extra){n=U.extra;while(b<n){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}U.offset+=y&(1<<U.extra)-1;y>>>=U.extra;b-=U.extra;U.back+=U.extra}if(U.offset>U.dmax){P.msg="invalid distance too far back";U.mode=I;break}U.mode=_T;case _T:if(v===0)break g;s=A-v;if(U.offset>s){s=U.offset-s;if(s>U.whave)if(U.sane){P.msg="invalid distance too far back";U.mode=I;break}if(s>U.wnext){s-=U.wnext;S=U.wsize-s}else S=U.wnext-s;if(s>U.length)s=U.length;Q=U.window}else{Q=m;S=Y-U.offset;s=U.length}if(s>v)s=v;v-=s;U.length-=s;do{m[Y++]=Q[S++]}while(--s);if(U.length===0)U.mode=O;break;case pT:if(v===0)break g;m[Y++]=U.length;v--;U.mode=O;break;case E:if(U.wrap){while(b<32){if(x===0)break g;x--;y|=$[r++]<<b;b+=8}A-=v;P.total_out+=A;U.total+=A;if(U.wrap&4&&A)P.adler=U.check=U.flags?_(U.check,m,A,Y-A):i(U.check,m,A,Y-A);A=v;if(U.wrap&4&&(U.flags?y:CT(y))!==U.check){P.msg="incorrect data check";U.mode=I;break}y=0;b=0}U.mode=WT;case WT:if(U.wrap&&U.flags){while(b<32){if(x===0)break g;x--;y+=$[r++]<<b;b+=8}if(U.wrap&4&&y!==(U.total&4294967295)){P.msg="incorrect length check";U.mode=I;break}y=0;b=0}U.mode=RT;case RT:e=xT;break g;case I:e=yT;break g;case LT:return bT;case ZT:default:return j}P.next_out=Y;P.avail_out=v;P.next_in=r;P.avail_in=x;U.hold=y;U.bits=b;if(U.wsize||A!==P.avail_out&&U.mode<I&&(U.mode<E||q!==rT))if(aT(P,P.output,P.next_out,A-P.avail_out));w-=P.avail_in;A-=P.avail_out;P.total_in+=w;P.total_out+=A;U.total+=A;if(U.wrap&4&&A)P.adler=U.check=U.flags?_(U.check,m,A,P.next_out-A):i(U.check,m,A,P.next_out-A);P.data_type=U.bits+(U.last?64:0)+(U.mode===a?128:0)+(U.mode===M||U.mode===t?256:0);if((w===0&&A===0||q===rT)&&e===d)e=wT;return e},inflateEnd:function g(P){if(J(P))return j;var q=P.state;if(q.window)q.window=null;P.state=null;return d},inflateGetHeader:function g(P,q){if(J(P))return j;var U=P.state;if((U.wrap&2)===0)return j;U.head=q;q.done=false;return d},inflateSetDictionary:function g(P,q){var U=q.length;var $;var m;var r;if(J(P))return j;$=P.state;if($.wrap!==0&&$.mode!==X)return j;if($.mode===X){m=1;m=i(m,q,U,0);if(m!==$.check)return yT}r=aT(P,q,U,U);if(r){$.mode=LT;return bT}$.havedict=1;return d},inflateInfo:"pako inflate (from Nodeca project)"};function m(g){"@babel/helpers - typeof";return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(g){return typeof g}:function(g){return g&&"function"==typeof Symbol&&g.constructor===Symbol&&g!==Symbol.prototype?"symbol":typeof g},m(g)}var U0=function g(P,q){return z0.prototype.hasOwnProperty.call(P,q)},$0,m0,r0={assign:function g(P){var q=Array.prototype.slice.call(arguments,1);while(q.length){var U=q.shift();if(!U)continue;if(m(U)!=="object")throw new TypeError(U+"must be non-object");for(var $ in U)if(U0(U,$))P[$]=U[$]}return P},flattenChunks:function g(P){var q=0;for(var U=0,$=P.length;U<$;U++)q+=P[U].length;var m=new Uint8Array(q);for(var r=0,Y=0,x=P.length;r<x;r++){var v=P[r];m.set(v,Y);Y+=v.length}return m}},Y0=true;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(g){Y0=false}for(var v=new Uint8Array(256),q=0;q<256;q++)v[q]=q>=252?6:q>=248?5:q>=240?4:q>=224?3:q>=192?2:1;v[254]=v[254]=1;var x0,v0=function g(P,q){if(q<65534)if(P.subarray&&Y0)return String.fromCharCode.apply(null,P.length===q?P:P.subarray(0,q));var U="";for(var $=0;$<q;$++)U+=String.fromCharCode(P[$]);return U},y0,b0,w={string2buf:function g(P){if(typeof TextEncoder==="function"&&TextEncoder.prototype.encode)return(new TextEncoder).encode(P);var q,U,$,m,r,Y=P.length,x=0;for(m=0;m<Y;m++){U=P.charCodeAt(m);if((U&64512)===55296&&m+1<Y){$=P.charCodeAt(m+1);if(($&64512)===56320){U=65536+(U-55296<<10)+($-56320);m++}}x+=U<128?1:U<2048?2:U<65536?3:4}q=new Uint8Array(x);for(r=0,m=0;r<x;m++){U=P.charCodeAt(m);if((U&64512)===55296&&m+1<Y){$=P.charCodeAt(m+1);if(($&64512)===56320){U=65536+(U-55296<<10)+($-56320);m++}}if(U<128)q[r++]=U;else if(U<2048){q[r++]=192|U>>>6;q[r++]=128|U&63}else if(U<65536){q[r++]=224|U>>>12;q[r++]=128|U>>>6&63;q[r++]=128|U&63}else{q[r++]=240|U>>>18;q[r++]=128|U>>>12&63;q[r++]=128|U>>>6&63;q[r++]=128|U&63}}return q},buf2string:function g(P,q){var U=q||P.length;if(typeof TextDecoder==="function"&&TextDecoder.prototype.decode)return(new TextDecoder).decode(P.subarray(0,q));var $,m;var r=new Array(U*2);for(m=0,$=0;$<U;){var Y=P[$++];if(Y<128){r[m++]=Y;continue}var x=v[Y];if(x>4){r[m++]=65533;$+=x-1;continue}Y&=x===2?31:x===3?15:7;while(x>1&&$<U){Y=Y<<6|P[$++]&63;x--}if(x>1){r[m++]=65533;continue}if(Y<65536)r[m++]=Y;else{Y-=65536;r[m++]=55296|Y>>10&1023;r[m++]=56320|Y&1023}}return v0(r,m)},utf8border:function g(P,q){q=q||P.length;if(q>P.length)q=P.length;var U=q-1;while(U>=0&&(P[U]&192)===128)U--;if(U<0)return q;if(U===0)return q;return U+v[P[U]]>q?U:q}},r={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"};function w0(){this.input=null;this.next_in=0;this.avail_in=0;this.total_in=0;this.output=null;this.next_out=0;this.avail_out=0;this.total_out=0;this.msg="";this.state=null;this.data_type=2;this.adler=0}var A0=w0;function s0(){this.text=0;this.time=0;this.xflags=0;this.os=0;this.extra=null;this.extra_len=0;this.name="";this.comment="";this.hcrc=0;this.done=false}var S0=s0,Q0=z0.prototype.toString,k0=P.Z_NO_FLUSH,F0=P.Z_FINISH,A=P.Z_OK,s=P.Z_STREAM_END,S=P.Z_NEED_DICT,N0=P.Z_STREAM_ERROR,o0=P.Z_DATA_ERROR,V0=P.Z_MEM_ERROR;function Y(g){this.options=r0.assign({chunkSize:1024*64,windowBits:15,to:""},g||{});var P=this.options;if(P.raw&&P.windowBits>=0&&P.windowBits<16){P.windowBits=-P.windowBits;if(P.windowBits===0)P.windowBits=-15}if(P.windowBits>=0&&P.windowBits<16&&!(g&&g.windowBits))P.windowBits+=32;if(P.windowBits>15&&P.windowBits<48)if((P.windowBits&15)===0)P.windowBits|=15;this.err=0;this.msg="";this.ended=false;this.chunks=[];this.strm=new A0;this.strm.avail_out=0;var q=b.inflateInit2(this.strm,P.windowBits);if(q!==A)throw new Error(r[q]);this.header=new S0;b.inflateGetHeader(this.strm,this.header);if(P.dictionary){if(typeof P.dictionary==="string")P.dictionary=w.string2buf(P.dictionary);else if(Q0.call(P.dictionary)==="[object ArrayBuffer]")P.dictionary=new Uint8Array(P.dictionary);if(P.raw){q=b.inflateSetDictionary(this.strm,P.dictionary);if(q!==A)throw new Error(r[q])}}}function x(g,P){var q=new Y(P);q.push(g);if(q.err)throw q.msg||r[q.err];return q.result}function B0(g,P){P=P||{};P.raw=true;return x(g,P)}Y.prototype.push=function(g,P){var q=this.strm;var U=this.options.chunkSize;var $=this.options.dictionary;var m,r,Y;if(this.ended)return false;if(P===~~P)r=P;else r=P===true?F0:k0;if(Q0.call(g)==="[object ArrayBuffer]")q.input=new Uint8Array(g);else q.input=g;q.next_in=0;q.avail_in=q.input.length;for(;;){if(q.avail_out===0){q.output=new Uint8Array(U);q.next_out=0;q.avail_out=U}m=b.inflate(q,r);if(m===S&&$){m=b.inflateSetDictionary(q,$);if(m===A)m=b.inflate(q,r);else if(m===o0)m=S}while(q.avail_in>0&&m===s&&q.state.wrap>0&&g[q.next_in]!==0){b.inflateReset(q);m=b.inflate(q,r)}switch(m){case N0:case o0:case S:case V0:this.onEnd(m);this.ended=true;return false}Y=q.avail_out;if(q.next_out)if(q.avail_out===0||m===s)if(this.options.to==="string"){var x=w.utf8border(q.output,q.next_out);var v=q.next_out-x;var y=w.buf2string(q.output,x);q.next_out=v;q.avail_out=U-v;if(v)q.output.set(q.output.subarray(x,x+v),0);this.onData(y)}else this.onData(q.output.length===q.next_out?q.output:q.output.subarray(0,q.next_out));if(m===A&&Y===0)continue;if(m===s){m=b.inflateEnd(this.strm);this.onEnd(m);this.ended=true;return true}if(q.avail_in===0)break}return true},Y.prototype.onData=function(g){this.chunks.push(g)},Y.prototype.onEnd=function(g){if(g===A)if(this.options.to==="string")this.result=this.chunks.join("");else this.result=r0.flattenChunks(this.chunks);this.chunks=[];this.err=g;this.msg=this.strm.msg};var u0,l0=x,e0=B0,T0=x,c0=P,n0={Inflate:Y,inflate:l0,inflateRaw:e0,ungzip:T0,constants:c0};g.Inflate=Y,g.constants=c0,g["default"]=n0,g.inflate=l0,g.inflateRaw=e0,g.ungzip=T0,z0.defineProperty(g,"__esModule",{value:true})})((g=r).pako||(g.pako={}));var oT,VT,BT,uT,Y,l=19,n=16,z=8,i=16,lT=r.ht,_=r.pako,eT=null,g=Math,TT=g.abs,cT=g.max,nT=Number.MAX_VALUE,zT=lT.Default,iT=zT.getInternal(),x=zT.clone,v=iT.vec3TransformMat4,y=[0,0],b=iT.appendArray,w=(iT.addMethod(zT,{objDefaultValueRegexPattern:"( +[\\d|\\.|\\+|\\-|e|E]+| nan| [\\-]?inf| -nan\\(ind\\))"},!0),g=zT.objDefaultValueRegexPattern,oT=new RegExp("v"+g+g+g),VT=new RegExp("vt"+g+g),BT=new RegExp("vn"+g+g+g),uT=/^[og]\s*(.+)?/,Y=function(g,P){return 0<=(P=parseInt(P))?g[P-1]:g[P+g.length]},function(g,x,v){if(!g)return eT;(iT.isString(x)||x instanceof ArrayBuffer)&&(x=CT(x)),(v=v||{}).flipY==eT&&(v.flipY=!0);var P,q,U,Z,$,m,r,Y,y,b,C,w=v.model3d,d=(!w&&(v.s3||v.r3||v.t3||v.mat)&&(v.matrix=iT.createWorldMatrix(v.mat,v.s3,v.r3,v.rotationMode,v.t3)),lT.Style["wf.loadQuadWireframe"]),j=v.part,G=[],X=[],A=v.ignoreNormal?eT:[],a=v.reverseFlipMtls,s={vs:[],uv:[],ns:A?[]:eT},S={htdefault:s},D=new LT(g),t=[],M="",O="";for(A&&v.matrix&&(v.normalMatrix=iT.createNormalMatrix(v.matrix));null!=(P=D.stepNext());)if(0!==(P=P.trim()).length&&"#"!==P.charAt(0))if(P.indexOf("\\")===P.length-1)M+=P.substring(0,P.length-1);else if(M&&(P=M+P,M=""),0<=P.indexOf("#QNAN0")&&(P=P.replace(/#QNAN0/gi,"0")),q=oT.exec(P))G.push([parseFloat(q[1]),parseFloat(q[2]),parseFloat(q[3])]);else if(q=VT.exec(P))X.push([parseFloat(q[1]),parseFloat(q[2])]);else if(A&&(q=BT.exec(P)))v.flipFace?A.push([parseFloat(-q[1]),parseFloat(-q[2]),parseFloat(-q[3])]):A.push([parseFloat(q[1]),parseFloat(q[2]),parseFloat(q[3])]);else if("f"===P[0]){var E=P.split(/\s+/);if(!(E.length<4)){var Q,k,I,F=[],N=[],o=[];for(k=1,I=E.length;k<I;k++)Q=E[k].split("/"),F.push(parseInt(Q[0],10)),1<Q.length&&0<Q[1].length&&o.push(parseInt(Q[1],10)),2<Q.length&&0<Q[2].length&&N.push(parseInt(Q[2],10));for(k=0,I=F.length-2;k<I;k++)U=s,Z=G,$=X,m=A,r=v,Y=[F[0],F[k+1],F[k+2]],y=o.length?[o[0],o[k+1],o[k+2]]:eT,b=N.length?[N[0],N[k+1],N[k+2]]:eT,C=void 0,C=m&&m.length&&b,Y[3]===NT?(pT(U,Z,r,Y[0],Y[1],Y[2]),y?WT(U,$,r,y[0],y[1],y[2]):U.uv&&U.uv.length&&WT(U,$,r),C&&RT(U,m,r,b[0],b[1],b[2])):(pT(U,Z,r,Y[0],Y[1],Y[3]),pT(U,Z,r,Y[1],Y[2],Y[3]),y?(WT(U,$,r,y[0],y[1],y[3]),WT(U,$,r,y[1],y[2],y[3])):U.uv&&U.uv.length&&(WT(U,$,r),WT(U,$,r)),C&&(RT(U,m,r,b[0],b[1],b[3]),RT(U,m,r,b[1],b[2],b[3])));if(d){B=K=void 0;for(var J=s,h=G,f=v,V=F,K=V.length-1,B=0;B<K;++B)_T(J,h,f,V[B],V[B+1]);_T(J,h,f,V[K],V[0])}}}else j&&null!==(q=uT.exec(P))?O=(" "+q[0].substr(1).trim()).substr(1):/^usemtl /.test(P)&&P.substring(7).trim().split(" ").forEach(function(g){var P=j?O+"_"+g:g;if(!(s=S[P])){s=S[P]={name:P,vs:[],uv:[],ns:A?[]:eT,lvs:d?[]:eT},j&&w&&(s.mtlName=g,s.compName=O),v.ignoreMtls&&0<=v.ignoreMtls.indexOf(g)&&delete s.vs,(v.reverseFlip||"*"===a||a&&0<=a.indexOf(g))&&(s.reverseFlip=!0);var P=x,q=s,U=v,$=t;if(P){P=P[g];if(P)if(U.ignoreColor||(q.color=P.kd),!U.ignoreTransparent&&0<=P.d&&P.d<1&&(q.transparent=!0,q.opacity=P.d),!U.ignoreImage&&(m=P.map_kd)){for(var m=m.split(" "),r=-1,Y=0;Y<m.length;Y++)"-o"===m[Y]?(q.uvOffset=[parseFloat(m[Y+1]),parseFloat(m[Y+2])],r=Y+=3):"-s"===m[Y]&&(q.uvScale=[parseFloat(m[Y+1]),parseFloat(m[Y+2])],r=Y+=3);g=(m=m.slice(r+1).join(" ")).match(/[^\\/]*$/)[0];$.indexOf(g)<0&&$.push(g),U.assetsURIMap&&U.assetsURIMap[g]?q.image=U.assetsURIMap[g]:q.image=(U.prefix||"")+m}}}});var H=[];for(FT in S){var gT=S[FT],PT=gT.vs;if(PT&&0!==PT.length){var qT=gT.uv;if(qT)for(var UT=2*PT.length/3-qT.length;0<UT--;)qT.push(0)}else H.push(FT)}H.forEach(function(g){delete S[g]});var u,$T,mT,rT,l,e,YT,T=S,xT=w,vT=v.cube,g=v.center,yT=v,c=nT,n=nT,z=nT,bT=-nT,wT=-nT,AT=-nT;for(u in T)for(R=(l=T[u].vs).length,L=0;L<R;L+=3)($T=l[L+0])<c&&(c=$T),(mT=l[L+1])<n&&(n=mT),(rT=l[L+2])<z&&(z=rT),bT<$T&&(bT=$T),wT<mT&&(wT=mT),AT<rT&&(AT=rT);if(g){var sT=c+(bT-c)/2,ST=n+(wT-n)/2,QT=z+(AT-z)/2;if(!xT)for(u in T){for(R=(l=T[u].vs).length,L=0;L<R;L+=3)l[L+0]-=sT,l[L+1]-=ST,l[L+2]-=QT;if(e=T[u].lvs)for(R=e.length,L=0;L<R;L+=3)e[L+0]-=sT,e[L+1]-=ST,e[L+2]-=QT}YT=[sT,ST,QT]}var i=g?(_=bT-c,p=wT-n,AT-z):(_=2*cT(TT(c),TT(bT)),p=2*cT(TT(n),TT(wT)),2*cT(TT(z),TT(AT))),_=(g=yT.rawS3=iT.constrainModelScaleRatio(_,p,i))[0],p=g[1];for(u in i=g[2],T){if(l=T[u].vs,e=T[u].lvs,!xT&&vT){for(R=l.length,L=0;L<R;L+=3)_&&(l[L+0]/=_),p&&(l[L+1]/=p),i&&(l[L+2]/=i);if(e)for(R=e.length,L=0;L<R;L+=3)_&&(e[L+0]/=_),p&&(e[L+1]/=p),i&&(e[L+2]/=i);var W=T[u].ns;if(W)for(var R=W.length,kT=new lT.Math.Vector3,L=0;L<R;L+=3)kT.set(W[L+0]*_,W[L+1]*p,W[L+2]*i).normalize(),W[L+0]=kT.x,W[L+1]=kT.y,W[L+2]=kT.z}T[u].rawS3=yT.rawS3,YT&&(T[u].center=YT)}if(w)(S=ZT(S,v)).externalAssetURIs=t;else for(var FT in S)S[FT].externalAssetURIs=t;g=v.shape3d;return g&&(w?zT.setShape3dModel(g,S):zT.setShape3dModel(g,iT.completeObjModelMapToShape3d(S))),S}),ZT=function(g,P){var q,U=[],$={},m={model3d:!0,comps:U,matDef:$},r=new lT.Math.Box3,Y=new lT.Math.Box3,x=new lT.Math.Vector3,v=new lT.Math.Vector3,y=new lT.Math.Vector3,b={};for(q in g){var w,A=g[q],s=A.rawS3,S=A.center,s=((s||S)&&(x.copy(s||[1,1,1]).multiplyScalar(.5),v.copy(S||[0,0,0]),Y.set(y.copy(v).sub(x),v.add(x)),r.expandByBox(Y)),A.mtlName||q),S=($[s]=Q(z0.assign(zT.objDefaultMaterial,{map:A.image,diffuse:A.image?NT:A.color,transparent:!!A.transparent||NT,opacity:A.transparent?A.opacity:NT,uvOffset:A.uvOffset,uvScale:A.uvScale})),A.compName||q),A={mesh:Q({vs:A.vs,uv:A.uv,ns:A.ns,lvs:A.lvs}),name:S,mat:s},s=b[S];s!=NT?(delete A.name,(w=U[s]).comps?w.comps.push(A):(delete w.name,U[s]={name:S,comps:[w,A]})):(b[S]=U.length,U.push(A))}return P.batchByMaterial&&iT.batchModel3dByMaterial(m,P),iT.completeCubeCenterOfModel3d(m,{box3:r,preferBox3:P.box3,cube:P.cube,center:P.center,rotationMode:P.rotationMode,t3:P.t3,r3:P.r3,s3:P.s3}),P.matDef&&z0.assign(m.matDef,P.matDef),m},CT=function(g){var P={};if(g)for(var q,U,$,m,r=new LT(g),Y=/\s+/;null!=($=r.stepNext());)0!==($=$.trim()).length&&"#"!==$.charAt(0)&&(U=((m=$.indexOf(" "))?$.substring(0,m):$).toLowerCase(),$=(m?$.substring(m+1):"").trim(),"newmtl"===U?P[$]=q={name:$}:q&&("ka"===U||"kd"===U||"ks"===U?(m=$.split(Y,3),q[U]=[parseFloat(m[0]),parseFloat(m[1]),parseFloat(m[2]),1]):q[U]="ns"===U||"d"===U?parseFloat($):$));return P};function Q(g){var P,q={};for(P in g)g[P]!=NT&&(q[P]=g[P]);return q}function _T(g,P,q,U,$){g.lvs&&(U=Y(P,U),P=Y(P,$),$=q.matrix,q=g.lvs,$?(b(q,v(x(U),$)),b(q,v(x(P),$))):(b(q,U),b(q,P)))}function pT(g,P,q,U,$,m){g.vs&&(U=Y(P,U),$=Y(P,$),P=Y(P,m),m=q.matrix,g=g.vs,q.flipFace&&(q=$,$=P,P=q),m?(b(g,v(x(U),m)),b(g,v(x($),m)),b(g,v(x(P),m))):(b(g,U),b(g,$),b(g,P)))}function WT(g,P,q,U,$,m){var r;g.vs&&(r=q.flipY,U=U===NT?y:Y(P,U),$=$===NT?y:Y(P,$),P=m===NT?y:Y(P,m),q.flipFace&&(m=$,$=P,P=m),g.uv.push(U[0],r?1-U[1]:U[1],$[0],r?1-$[1]:$[1],P[0],r?1-P[1]:P[1]))}function RT(g,P,q,U,$,m){g.vs&&(U=Y(P,U),$=Y(P,$),P=Y(P,m),m=q.normalMatrix,g=g.ns,q.flipFace&&(q=$,$=P,P=q),m?(b(g,v(x(U),m)),b(g,v(x($),m)),b(g,v(x(P),m))):(b(g,U),b(g,$),b(g,P)))}var LT=function(g){var U,$,m,P,q,r;g instanceof ArrayBuffer?(this.isBuffer=!0,U=0,$=new Uint8Array(g),m=$.length,r=$.length,this.stepNext=function(){for(var g,P,q=U;U<m;)if(12==(P=(g=$[U])>>4)||13==P)U+=2;else if(14==P)U+=3;else if(U++,10===g)return String.fromCharCode.apply(null,$.subarray(q,U));return q<U?String.fromCharCode.apply(null,$.subarray(q,U)):null}):(this.isBuffer=!1,P=g.split("\n"),q=0,r=P.length,this.stepNext=function(){return q<r?P[q++]:null})},e=(LT.prototype={},LT.prototype.constructor=LT,iT.addMethod(zT,{objDefaultMaterial:{type:"litePhong"},objUseTextOnly:!1,loadObj:function(g,P,$){$=$||{};function q(g){var P,q=$.finishFunc,U=$.shape3d;(g=g?w(g[0],g[1],$):null)?(U=(g.model3d?g:P=U?zT.getShape3dModel(U):iT.completeObjModelMapToShape3d(g)).rawS3,q&&q(g,P,U)):q&&q(null)}var U,m=!1;!zT.objUseTextOnly&&r.navigator&&/(MSIE |Trident\/|Edge\/)/.test(r.navigator.userAgent)&&(m=!0);m?($.responseType="arraybuffer",U=function(P){zT.xhrLoad(g,function(g){q([g,P])},$)},P?zT.xhrLoad(P,function(g){U(g)},$):U()):zT.xhrLoad(P?[g,P]:[g],q,$)},parseObj:function(g,P,q){return w(g,P,q)}},!0),lT.ByteBuffer=function(g,P,q){if(void 0===g&&(g=e.DEFAULT_CAPACITY),void 0===P&&(P=e.DEFAULT_ENDIAN),!(q=void 0===q?e.DEFAULT_NOASSERT:q)){if((g|=0)<0)throw RangeError("Illegal capacity");P=!!P,q=!!q}this.buffer=0===g?$:new ArrayBuffer(g),this.view=0===g?null:new Uint8Array(this.buffer),this.offset=0,this.markedOffset=-1,this.limit=g,this.littleEndian=P,this.noAssert=q,this.bufferId=0}),$=new ArrayBuffer(0);function F(){var g=[],P=[];return function(){if(0===arguments.length)return P.join("")+q.apply(String,g);1024<g.length+arguments.length&&(P.push(q.apply(String,g)),g.length=0),Array.prototype.push.apply(g,arguments)}}z0.defineProperties(e.prototype,{offset:{get:function(){return this._offset},set:function(g){(this._offset=g)&&g>=this.limit&&this.trySwitchNextBuffer()}}}),e.LITTLE_ENDIAN=!0,e.BIG_ENDIAN=!1,e.DEFAULT_CAPACITY=16,e.DEFAULT_ENDIAN=e.BIG_ENDIAN,e.DEFAULT_NOASSERT=!1,e.METRICS_BYTES="b";var q=String.fromCharCode,g=e.prototype;g.trySwitchNextBuffer=function(){var g=this.buffers[++this.bufferId];g&&(this.buffer=g,this.limit=g.byteLength,this.offset=0,this.view=0<g.byteLength?new Uint8Array(g):null)},g.readUint8=function(g){var P=void 0===g;if(P&&(g=this.offset),!this.noAssert){if("number"!=typeof g||g%1!=0)throw TypeError("Illegal offset: "+g+" (not an integer)");if((g>>>=0)<0||g+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+g+" (+1) <= "+this.buffer.byteLength)}g=this.view[g];return P&&(this.offset+=1),g},g.readUint16=function(g){var P=void 0===g;if(P&&(g=this.offset),!this.noAssert){if("number"!=typeof g||g%1!=0)throw TypeError("Illegal offset: "+g+" (not an integer)");if((g>>>=0)<0||g+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+g+" (+2) <= "+this.buffer.byteLength)}var q=0;return this.littleEndian?(q=this.view[g],q|=this.view[g+1]<<8):(q=this.view[g]<<8,q|=this.view[g+1]),P&&(this.offset+=2),q},g.readUint24=function(g){var P=void 0===g;if(P&&(g=this.offset),!this.noAssert){if("number"!=typeof g||g%1!=0)throw TypeError("Illegal offset: "+g+" (not an integer)");if((g>>>=0)<0||g+3>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+g+" (+4) <= "+this.buffer.byteLength)}var q=0,q=this.littleEndian?(q=this.view[g+2]<<16,(q|=this.view[g+1]<<8)|this.view[g]):(q=this.view[g+1]<<8,(q|=this.view[g+2])+(this.view[g]<<16>>>0));return q|=0,P&&(this.offset+=3),q},g.readUint32=function(g){var P=void 0===g;if(P&&(g=this.offset),!this.noAssert){if("number"!=typeof g||g%1!=0)throw TypeError("Illegal offset: "+g+" (not an integer)");if((g>>>=0)<0||g+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+g+" (+4) <= "+this.buffer.byteLength)}var q=0,q=this.littleEndian?(q=this.view[g+2]<<16,(q=(q|=this.view[g+1]<<8)|this.view[g])+(this.view[g+3]<<24>>>0)):(q=this.view[g+1]<<16,(q=(q|=this.view[g+2]<<8)|this.view[g+3])+(this.view[g]<<24>>>0));return P&&(this.offset+=4),q},g.readArrayInBits=function(g,P,q,U){for(var $,m=void 0===U,r=(m&&(U=this.offset),Math.ceil(g*P/8)),Y=(q=q||new Array(g),0),x=0,v=this.view,U=this.offset,y=0,b=0;b<g;b++){for(x=Y=0;x<P;)Y=Y<<($=Math.min(P-x,8-y))|v[U]>>8-y-$&(1<<$)-1,x+=$,0===(y=(y+$)%8)&&U++;q[b]=Y}return m&&(this.offset+=r),Y},g.readFloat32=function(g){var P=void 0===g;if(P&&(g=this.offset),!this.noAssert){if("number"!=typeof g||g%1!=0)throw TypeError("Illegal offset: "+g+" (not an integer)");if((g>>>=0)<0||g+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+g+" (+4) <= "+this.buffer.byteLength)}g=function(g,P,q,U,$){var m,r,Y=8*$-U-1,x=(1<<Y)-1,v=x>>1,y=-7,b=q?$-1:0,w=q?-1:1,$=g[P+b];for(b+=w,m=$&(1<<-y)-1,$>>=-y,y+=Y;0<y;m=256*m+g[P+b],b+=w,y-=8);for(r=m&(1<<-y)-1,m>>=-y,y+=U;0<y;r=256*r+g[P+b],b+=w,y-=8);if(0===m)m=1-v;else{if(m===x)return r?NaN:1/0*($?-1:1);r+=Math.pow(2,U),m-=v}return($?-1:1)*r*Math.pow(2,m-U)}(this.view,g,this.littleEndian,23,4);return P&&(this.offset+=4),g},g.batchReadToFixedArray=function(g,P,q){var U=this.view,$=void 0===q;$&&(q=this.offset),new Uint8Array(g.buffer).set(U.subarray(q,q+P)),$&&(this.offset+=P)},g.batchReadUint8=function(g,P,q){return this.batchReadToFixedArray(g,P,q)},g.batchReadUint16=function(g,P,q){return this.batchReadToFixedArray(g,2*P,q)},g.batchReadUint32=function(g,P,q){return this.batchReadToFixedArray(g,4*P,q)},g.batchReadFloat32=function(g,P,q){return this.batchReadToFixedArray(g,4*P,q)};function N(g){if(g=W(g))return g.shapeModel}function p(g,P){g.buffer=P,g.limit=P.byteLength,g.view=0<P.byteLength?new Uint8Array(P):null}function s(g,P,q,U,$){var m,r,Y,x,v,y=P.readUint32(),b=(P.readUint8(),P.readUint32()),w=P.readUint32(),A=[];if("uv"===U)for(s=0;s<y;s++)m=o(P),r=o(P),A.push([m,r]);else if("ns"===U)for(s=0;s<y;s++)A.push(V(P));else for(var s=0;s<y;s++)A.push(T(P));if(Y=y<256?"readUint8":y<65536?"readUint16":y<16777216?"readUint24":"readUint32",b)for(x=g[U]=[],s=0;s<b;s++)v=A[P[Y]()],"uv"===U?x.push(v[0],v[1]):x.push(v[0],v[1],v[2]);if(w)for(x=g[$]=[],s=0;s<w;s++)v=A[P[Y]()],x.push(v[0],v[1],v[2])}var U,m,A=U={MAX_CODEPOINT:1114111,decodeUTF8:function(g,P){for(var q,U,$,m,r=function(g){g=g.slice(0,g.indexOf(null));var P=Error(g.toString());throw P.name="TruncatedError",P.bytes=g,P};null!==(q=g());)if(0==(128&q))P(q);else if(192==(224&q))null===(U=g())&&r([q,U]),P((31&q)<<6|63&U);else if(224==(240&q))null!==(U=g())&&null!==($=g())||r([q,U,$]),P((15&q)<<12|(63&U)<<6|63&$);else{if(240!=(248&q))throw RangeError("Illegal starting byte: "+q);null!==(U=g())&&null!==($=g())&&null!==(m=g())||r([q,U,$,m]),P((7&q)<<18|(63&U)<<12|(63&$)<<6|63&m)}},UTF16toUTF8:function(g,P){for(var q,U=null;null!==(q=null!==U?U:g());)55296<=q&&q<=57343&&null!==(U=g())&&56320<=U&&U<=57343?(P(1024*(q-55296)+U-56320+65536),U=null):P(q);null!==U&&P(U)},UTF8toUTF16:function(g,P){var q=null;for("number"==typeof g&&(q=g,g=function(){return null});null!==q||null!==(q=g());)q<=65535?P(q):(P(55296+((q-=65536)>>10)),P(q%1024+56320)),q=null},decodeUTF8toUTF16:function(g,P){U.decodeUTF8(g,function(g){U.UTF8toUTF16(g,P)})},calculateCodePoint:function(g){return g<128?1:g<2048?2:g<65536?3:4},calculateUTF8:function(g){for(var P,q=0;null!==(P=g());)q+=P<128?1:P<2048?2:P<65536?3:4;return q},calculateUTF16asUTF8:function(g){var P=0,q=0;return U.UTF16toUTF8(g,function(g){++P,q+=g<128?1:g<2048?2:g<65536?3:4}),[P,q]}},W=(g.readString=function(g,P,q){"number"==typeof P&&(q=P,P=NT);var U=void 0===q;if(U&&(q=this.offset),void 0===P&&(P=e.METRICS_CHARS),!this.noAssert){if("number"!=typeof g||g%1!=0)throw TypeError("Illegal length: "+g+" (not an integer)");if(g|=0,"number"!=typeof q||q%1!=0)throw TypeError("Illegal offset: "+q+" (not an integer)");if((q>>>=0)<0||q+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+q+" (+0) <= "+this.buffer.byteLength)}var $,m=0,r=q;if(P===e.METRICS_CHARS){if($=F(),A.decodeUTF8(function(){return m<g&&q<this.limit?this.view[q++]:null}.bind(this),function(g){++m,A.UTF8toUTF16(g,$)}),m!==g)throw RangeError("Illegal range: Truncated data, "+m+" == "+g);return U?(this.offset=q,$()):{string:$(),length:q-r}}if(P!==e.METRICS_BYTES)throw TypeError("Unsupported metrics: "+P);if(!this.noAssert){if("number"!=typeof q||q%1!=0)throw TypeError("Illegal offset: "+q+" (not an integer)");if((q>>>=0)<0||q+g>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+q+" (+"+g+") <= "+this.buffer.byteLength)}var Y=q+g;if(A.decodeUTF8toUTF16(function(){return q<Y?this.view[q++]:null}.bind(this),$=F(),this.noAssert),q!==Y)throw RangeError("Illegal range: Truncated data, "+q+" == "+Y);return U?(this.offset=q,$()):{string:$(),length:q-r}},function(g){if(g.length){for(var P=0;P<g.length;P++)if(!g[P])return;var q=g[0],U=12<q.byteLength&&0<(1&new Uint8Array(q)[12]),$=new e(0,U);if(p($,q),$.buffers=g,2===$.readUint8(4)){var m,r,Y,x=g,v=(S=$).readUint16(l),y=[];for(S.offset=l+2,m=0;m<v;m++){r=c(S);Y=S.readUint32();y.push({url:r,length:Y})}var b=S.offset;for(x=x.slice(S.bufferId),m=0;m<v;m++){r=y[m].url;Y=y[m].length;var w=x[0];x[0]=w.slice(b,b+Y);var A=W(x);var s=A.shapeModel;var S=A.byteBuffer;if(b+Y===w.byteLength)x=x.slice(1);else x[0]=w.slice(b+Y);b=0;y[m].shapeModel=s;lT.Default.setShape3dModel(r,s)}return y}var Q,q=$.readUint8(11);if(2!==(F=q)&&3!==F||(Q=_.inflate($.view.slice(F=l)),(N=new Uint8Array(F+Q.byteLength)).set($.view.slice(0,F),0),N.set(Q,F),p($,N)),0!==(Q=q)&&3!==Q)k=function(g,P){g.offset+=l;for(var q=c(g),U=zT.parse(q),$=g.readUint8(),m=g.readUint16(),r=(g.offset+=$-2,{}),Y=(r.uvBits=(m&31744)>>10||n,r.normalBits=(m&992)>>5||z,r.positionBits=m&31||i,g.readUint16()),x=[],v=0,y,b,w,A;v<Y;v++){var s=g.readUint16();switch(s){case 0:y="readUint8";b=g.readUint32();A=new Uint8Array(b);if(P)w="batchReadUint8";break;case 1:y="readUint16";b=g.readUint32()/2;A=new Uint16Array(b);if(P)w="batchReadUint16";break;case 2:y="readUint32";b=g.readUint32()/4;A=new Uint32Array(b);if(P)w="batchReadUint32";break;case 3:y="readFloat32";b=g.readUint32()/4;A=new Float32Array(b);if(P)w="batchReadFloat32";break;default:y=null;break}if(y){if(w)g[w](A,b);else for(var S=0;S<b;S++)A[S]=g[y]();x.push(A)}else x.push(L.dequantize(g,s,r))}var Q=function(g,P){var q=g[P];if(typeof q!=="number"||q<0||q>Y)return;g[P]=x[q]},k=function(g){zT.traverse(g,function(g){if(!g)return;var P=g.animations;var q=g.mesh;var U=g.instances;var $=g.skeleton;if(!P&&!q&&!$&&!U)return g;if(P)for(var m=0,r=P.length;m<r;m++){var Y=P[m];var x=Y.tracks;for(var v=0,y=x.length;v<y;v++){var b=x[v];Q(b,"times");Q(b,"values")}}if(q){Q(q,"vs");Q(q,"is");Q(q,"uv");Q(q,"uv2");Q(q,"ns");Q(q,"skinIndex");Q(q,"skinWeight");Q(q,"color");Q(q,"tangent")}if($)Q($,"boneMatrixInverses");if(U)Q(U,"aMatrixInstanced")},null,"comps")};if(U.lod){var F=U.lod.group;for(var N=0,o=F.length;N<o;N++){var V=F[N];if(typeof V==="string")continue;k(V)}}else k(U);return U}($,U);else{var k=[],F=$.readUint8(10),N=($.offset+=l,k),q=$,U=q.readUint8();if(U&1)N.center=T(q);if(U&2)N.rawS3=T(q);for(var o=k,V=$,B=((F||0)<<8)+V.readUint8(),u=0;u<B;u++)o.push(R(V))}return{shapeModel:k,byteBuffer:$}}}),T=function(g){return[g.readFloat32(),g.readFloat32(),g.readFloat32()]},c=function(g){var P=g.readUint32();return g.readString(P,e.METRICS_BYTES)},R=function(g){var P={},q=g.readUint32(),U=0,$=q&1<<U++,m=q&1<<U++,r=q&1<<U++,Y=q&1<<U++,x=q&1<<U++,v=q&1<<U++,y=q&1<<U++,b=q&1<<U++,w=q&1<<U++,A=q&1<<U++,U=q&1<<+U,q=2048&q;return($||m)&&s(P,g,0,"vs","lvs"),r&&s(P,g,0,"uv"),Y&&s(P,g,0,"ns"),x&&(P.name=c(g)),v&&(P.color=T(g)),y&&(P.transparent=!!g.readUint8()),b&&(P.opacity=g.readFloat32()),w&&(P.uvOffset=[($=g).readFloat32(),$.readFloat32()]),A&&(P.uvScale=T(g)),U&&(P.image=c(g)),q&&(P.reverseFlip=!!g.readUint8()),P},o=function(g){var P=g.readUint16(),q=(16383&P)/16383,U=0;return(16384&P?1:-1)*((U=32768&P?g.readUint16():U)+q)},V=function(g){var g=g.readUint32(),P=g&1<<28,q=(g>>14&16383)/16383,U=(16383&g)/16383;return[q*(g&1<<30?1:-1),U*(g&1<<29?1:-1),(Math.sqrt(1-q*q-U*U)||0)*(P?1:-1)]},L=(zT.getInternal().addMethod(zT,{loadBin:function(m,r){function Y(g){var P,q=r.finishFunc,U=r.shape3d,$=N(g);if($){if(U)P=zT.getShape3dModel(U);else{for(var m in P=[],$){m=$[m];m&&m.rawS3&&(P.rawS3=m.rawS3),P.push(m)}$.rawS3&&(P.rawS3=$.rawS3)}q&&q($,P,P.rawS3)}else q&&q(null)}function U(g,P){for(var q=[],U=(P||q.push(m),m.substr(0,m.length-4)),$=1;$<g;$++)q.push(U+$+".bin");zT.xhrLoad(q,function(g){P&&g.splice(0,0,P),Y(g)},r)}(r=r||{}).responseType="arraybuffer",r.packageNum?U(r.packageNum):zT.xhrLoad(m,function(g){var P,q;g=g,q=r.finishFunc,g?1<(P=new Uint8Array(g)[9])?U(P,g):Y([g]):q&&q(null)},r)},parseBin:function(g){return N([g])}}),m={4:function(g){g=C(g),g=B(u(g));return Z(g)},5:function(g){for(var g=C(g),g=B(u(g)),P=Z(g),q=0,U=P.length;q<U;q++)P[q]=S(P[q],8);return P},6:function(g){for(var P=g.readUint32()/12*16,q=new Array(P),U=0;U<P;U+=16)q[U]=g.readFloat32(),q[U+1]=g.readFloat32(),q[U+2]=g.readFloat32(),q[U+3]=0,q[U+4]=g.readFloat32(),q[U+5]=g.readFloat32(),q[U+6]=g.readFloat32(),q[U+7]=0,q[U+8]=g.readFloat32(),q[U+9]=g.readFloat32(),q[U+10]=g.readFloat32(),q[U+11]=0,q[U+12]=g.readFloat32(),q[U+13]=g.readFloat32(),q[U+14]=g.readFloat32(),q[U+15]=1;return q},7:function(g,P){var q=g.readFloat32(),U=g.readFloat32(),$=g.readFloat32(),m=g.readFloat32(),r=g.readUint32(),Y=$===q?0:$-q,x=m===U?0:m-U,v=P.uvBits,y=new Float32Array(r);g.readArrayInBits(r,v,y);for(var b=0;b<r;b+=2)y[b]=S(y[b],v)*Y+q,y[b+1]=S(y[b+1],v)*x+U;return y},8:function(g,P){var q=g.readUint32(),U=new Float32Array(q),$=P.normalBits;g.readArrayInBits(q,$,U);for(var m=0;m<q;m+=3)U[m]=k(U[m],$),U[m+1]=k(U[m+1],$),U[m+2]=k(U[m+2],$);return U},9:function(g,P){var q=g.readFloat32(),U=g.readFloat32(),$=g.readFloat32(),m=g.readFloat32(),r=g.readUint32(),Y=new Float32Array(r),x=P.positionBits;g.readArrayInBits(r,x,Y);for(var v=0;v<r;v+=3)Y[v]=S(Y[v],x)*m+q,Y[v+1]=S(Y[v+1],x)*m+U,Y[v+2]=S(Y[v+2],x)*m+$;return Y}},{dequantize:function(g,P,q){P=m[P];if(P)return P(g,q)}});function B(g){for(var P=[],q=0;q<g.length;q+=2)for(var U=g[q],$=g[q+1],m=0;m<U;m++)P.push($);return P}function u(g){for(var P,q=[],U=0,$=0,m=0,r=g.length;m<r;m++)U|=(127&(P=g[m]))<<$,0==(128&P)?(q.push(U),$=U=0):$+=7;return q}function Z(g){for(var P=g.length,q=P/4,U=2*q,$=3*q,m=new Array(P),r=0;r<q;r++)m[4*r]=g[r],m[4*r+1]=g[r+q],m[4*r+2]=g[r+U],m[4*r+3]=g[r+$];return m}function C(g){for(var P=g.readUint32(),q=new Uint8Array(P),U=0;U<P;U++)q[U]=g.readUint8();return q}function S(g,P){return g/((1<<P)-1)}function k(g,P){return(g=(P=(1<<P-1)-1)<g?-(g-P-1)/P:g/P)<-1?-1:1<g?1:g}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);