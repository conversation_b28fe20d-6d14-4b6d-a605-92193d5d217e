!function(K,g){"use strict";var U=K.ht,r=<PERSON><PERSON>Default,K=r.def,D=r.getInternal();U.HistoryManager=function(K){this._histories=[],this.setDataModel(K)},K(U.HistoryManager,g,{ms_ac:["dataModel","histories","historyIndex","maxHistoryCount","disabled"],ms_fire:1,_historyIndex:-1,_betweenTransaction:0,_maxHistoryCount:200,_disabled:!1,ignoredPropertyMap:{imageLoaded:!0,children:!0,attaches:!0,shape:!0,childChange:!0,agentChange:!0,sourceAgent:!0,targetAgent:!0,edgeGroup:!0,material:!0,animationStart:!0,animationStop:!0,animationPause:!0,animationResume:!0,animationIteration:!0,"*":!0},ignoreDataModelPropertyMap:{},beginInteraction:function(){this.beginTransaction()},endInteraction:function(){this.endTransaction()},beginTransaction:function(){var K,g;this._disabled||(g=(K=this)._betweenTransaction++,1===K._betweenTransaction&&(K._transactionHistories={}),K.fp("betweenTransaction",g,K._betweenTransaction))},endTransaction:function(){if(!this._disabled&&0!==this._betweenTransaction){var K,g=this;if(1===g._betweenTransaction){if(g._transactionHistories){var o,E=[];for(o in g._transactionHistories){var z=g._transactionHistories[o];r.isArray(z)||(z=[z]),E.push.apply(E,z)}E.length&&(g.addActions(E),g.setHistoryIndex(g._histories.length-1,!0))}delete g._transactionHistories}0<g._betweenTransaction&&(K=g._betweenTransaction--,g.fp("betweenTransaction",K,g._betweenTransaction))}},setDataModel:function(K){var g=this,o=g._dataModel;o!==K&&(o&&(delete o._historyManager,o.ump(g.handleDataModelPropertyChange,g),o.umm(g.$5p,g),o.umd(g.$6p,g),o.removeHierarchyChangeListener(g.handleHierarchyChange,g),o.removeIndexChangeListener(g.handleIndexChange,g)),(g._dataModel=K)&&(K._historyManager=g,K.mp(g.handleDataModelPropertyChange,g),K.mm(g.$5p,g),K.md(g.$6p,g),K.addHierarchyChangeListener(g.handleHierarchyChange,g),K.addIndexChangeListener(g.handleIndexChange,g)),g.fp("dataModel",o,K),g.clear())},setHistoryIndex:function(K,g){var o=this,E=o._historyIndex,z=o._histories.length;K<-1?K=-1:z<=K&&(K=z-1),E!==K&&(g||(0<(z=K-E)?o.$2p(z):z<0&&o.$1p(-z)),o._historyIndex=K,o.fp("historyIndex",E,K),o.dataModel&&o.dataModel.onHistoryManagerChanged())},setMaxHistoryCount:function(K){var g=this,o=g._histories,E=g._maxHistoryCount;E!==(K=!K||K<=0?10:K)&&(g._maxHistoryCount=K,g.fp("maxHistoryCount",E,K),o.length>K&&g.clear())},cloneValue:function(K,g,o){return U.Default.clone(K)},isPropertyUndoable:function(K,g){return K&&!this.ignoredPropertyMap[K]},isIndexUndoable:function(K){return!1},isDataModelPropertyUndoable:function(K,g){return K&&!this.ignoreDataModelPropertyMap[K]},$5p:function(K){this.handleChange(K,K.kind)},$6p:function(K){this.handleChange(K,"property")},handleHierarchyChange:function(K){this.handleChange(K,"hierarchy")},handleIndexChange:function(K){this.handleChange(K,"index")},handleDataModelPropertyChange:function(K){this.handleChange(K,"dataModelProperty")},toChildrenInfo:function(K){var g={};return g.data=K,g.children=[],K.eachChild(function(K){g.children.push(this.toChildrenInfo(K))},this),g},restoreChildren:function(K){var o=K.data;K.children.forEach(function(K){var g=K.data;g.getParent()!==o&&o.addChild(g),this._dataModel.contains(g)||this._dataModel.add(g),this.restoreChildren(K)},this)},handleChange:function(K,g){var o,E,z,v,V=this;V._disabled||V._isUndoRedoing||r.loadingRefGraph||(V._histories,o=K.data,E=K.property,o&&(o._refGraph||o instanceof U.RefGraph)||("property"===g?V.isPropertyUndoable(E,o)&&(v={kind:g,data:o,property:E,oldValue:V.cloneValue(K.oldValue,o,E),newValue:V.cloneValue(K.newValue,o,E),event:K}):"hierarchy"===g||"index"===g&&V.isIndexUndoable(K)?v={kind:g,data:o,oldIndex:K.oldIndex,newIndex:K.newIndex,event:K}:"clear"===g?v={kind:g,json:K.json,event:K}:"add"===g?((v={kind:g,data:o,event:K,childrenInfo:this.toChildrenInfo(o),parent:o.getParent()}).parent&&(z=V._dataModel.getSiblings(o),v.siblingsIndex=z.indexOf(o)),o instanceof U.Node&&(v.host=o.getHost(),v.attaches=o.getAttaches()?o.getAttaches().toArray():void 0),o instanceof U.Edge&&(v.source=o.getSource(),v.target=o.getTarget())):"remove"===g?v={kind:g,data:o,event:K}:"dataModelProperty"===g&&V.isDataModelPropertyUndoable(E,o)&&(v={kind:g,property:E,oldValue:V.cloneValue(K.oldValue,o,E),newValue:V.cloneValue(K.newValue,o,E),event:K}),V.addHistory(v)))},addHistory:function(K){var g,o,E=this;K&&(E._betweenTransaction?(g=(K.data?K.data._id:0)+"_"+K.kind+"_"+K.property,"property"===K.kind||"dataModelProperty"===K.kind?((o=E._transactionHistories[g])&&(K.oldValue=o.oldValue),E._transactionHistories[g]=K):(o=E._transactionHistories[g])?(o=r.isArray(o)?o:E._transactionHistories[g]=[o]).push(K):E._transactionHistories[g]=K):(E.addActions([K]),E.setHistoryIndex(E._histories.length-1,!0)))},addActions:function(K){var g=this,o=g._histories;(o=o.slice(0,g._historyIndex+1)).push(K),o.length>g._maxHistoryCount&&(o=o.slice(o.length-g._maxHistoryCount)),g.setHistories(o)},canUndo:function(){return!this._disabled&&0<=this._historyIndex&&this._historyIndex<this._histories.length},canRedo:function(){return!this._disabled&&-1<=this._historyIndex&&this._historyIndex<this._histories.length-1},undo:function(K){this.setHistoryIndex(this._historyIndex-(K=!K||K<=0?1:K))},$1p:function(K){if(this.canUndo()){var g,o=this,E=o._histories,z=o._historyIndex,v=0;for(o._isUndoRedoing=!0,r.setIsolating(!0);0<K;)0<=z&&z<E.length&&(v++,g=E[z],z--,o.undoImpl(g)),K--;r.setIsolating(!1),delete o._isUndoRedoing,o.afterUndo(v)}},undoImpl:function(K){for(var g=this._dataModel,o=K.length-1;0<=o;o--){var E,z=K[o],v=z.kind,V=z.data,i=z.property,H=z.event,k=this.cloneValue(z.oldValue,V,i);z.undo?z.undo():"add"===v?g.remove(V,{keepChildren:!0}):"remove"===v?g.contains(V)||g.add(V,H.rootsIndex,H.datasIndex):"clear"===v?g.deserialize(r.clone(z.json)):"property"===v?"parent"===i?k?k.addChild(V,H.oldIndex):(V.setParent(k),0<=H.oldIndex&&g.moveTo(V,H.oldIndex)):(E=null,0===i.indexOf("a:")?(E="attr",i=i.replace("a:","")):0===i.indexOf("s:")?(E="style",i=i.replace("s:","")):0===i.indexOf("f:")&&(E="field",i=i.replace("f:","")),D.setPropertyValue(V,E,i,k)):"dataModelProperty"===v?(E=null,0===i.indexOf("a:")?(E="attr",i=i.replace("a:","")):0===i.indexOf("s:")?(E="style",i=i.replace("s:","")):0===i.indexOf("f:")&&(E="field",i=i.replace("f:","")),D.setPropertyValue(g,E,i,k)):"hierarchy"===v?g.moveTo(V,z.oldIndex):"index"===v?g.moveToIndex(V,z.oldIndex):"selection"===v&&g.sm().ss(z.oldValue)}},afterUndo:function(K){},redo:function(K){this.setHistoryIndex(this._historyIndex+(K=!K||K<=0?1:K))},$2p:function(K){if(this.canRedo()){var g,o=this,E=o._histories,z=o._historyIndex,v=0;for(o._isUndoRedoing=!0,r.setIsolating(!0);0<K;)-1<=z&&z<E.length-1&&(v++,g=E[++z],o.redoImpl(g)),K--;r.setIsolating(!1),delete o._isUndoRedoing,this.afterRedo(v)}},redoImpl:function(K){for(var g=this._dataModel,o=0;o<K.length;o++){var E,z=K[o],v=z.kind,V=z.data,i=z.property,H=z.event,k=this.cloneValue(z.newValue,V,i);z.redo?z.redo():"add"===v?(z.parent&&!V.getParent()&&z.parent.addChild(V,z.siblingsIndex),g.contains(V)||g.add(V,H.rootsIndex,H.datasIndex),this.restoreChildren(z.childrenInfo),V instanceof U.Node&&(V.setHost(z.host),z.attaches&&z.attaches.forEach(function(K){K.setHost(V)})),V instanceof U.Edge&&(V.setSource(z.source),V.setTarget(z.target))):"remove"===v?g.remove(V):"clear"===v?g.clear():"property"===v?"parent"===i?k?k.addChild(V,H.newIndex):(V.setParent(k),0<=H.newIndex&&g.moveTo(V,H.newIndex)):(E=null,0===i.indexOf("a:")?(E="attr",i=i.replace("a:","")):0===i.indexOf("s:")?(E="style",i=i.replace("s:","")):0===i.indexOf("f:")&&(E="field",i=i.replace("f:","")),D.setPropertyValue(V,E,i,k)):"dataModelProperty"===v?(E=null,0===i.indexOf("a:")?(E="attr",i=i.replace("a:","")):0===i.indexOf("s:")?(E="style",i=i.replace("s:","")):0===i.indexOf("f:")&&(E="field",i=i.replace("f:","")),D.setPropertyValue(g,E,i,k)):"hierarchy"===v?g.moveTo(V,z.newIndex):"index"===v?g.moveToIndex(V,z.newIndex):"selection"===v&&g.sm().ss(z.newValue)}},afterRedo:function(K){},clear:function(){this.setHistories([]),this.setHistoryIndex(-1,!0),this._betweenTransaction=0,delete this._transactionHistories}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);