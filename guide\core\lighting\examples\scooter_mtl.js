var scooter_mtl = getRawText(function(){/*

newmtl Black
Ns 190.196078
Ka 0.000000 0.000000 0.000000
Kd 0.000000 0.000000 0.000000
Ks 0.100000 0.100000 0.100000
Ni 1.000000
d 1.000000
illum 2

newmtl Chrome
Ns 198.039216
Ka 0.000000 0.000000 0.000000
Kd 0.224000 0.224000 0.224000
Ks 1.000000 1.000000 1.000000
Ni 1.000000
d 1.000000
illum 2

newmtl Glass
Ns 778.431373
Ka 0.000000 0.000000 0.000000
Kd 0.384000 0.384000 0.448000
Ks 1.500000 1.500000 1.500000
Ni 1.000000
d 0.300000
illum 2

newmtl Headlights
Ns 778.431373
Ka 0.000000 0.000000 0.000000
Kd 0.256000 0.256000 0.320000
Ks 1.500000 1.500000 1.500000
Ni 1.000000
d 0.500000
illum 2

newmtl Mirror
Ns 998.039216
Ka 0.000000 0.000000 0.000000
Kd 0.224000 0.224000 0.224000
Ks 2.000000 2.000000 2.000000
Ni 1.000000
d 1.000000
illum 2

newmtl Reflector
Ns 998.039216
Ka 0.000000 0.000000 0.000000
Kd 0.640000 0.640000 0.640000
Ks 1.500000 1.500000 1.500000
Ni 1.000000
d 1.000000
illum 2

newmtl Taillights
Ns 778.431373
Ka 0.000000 0.000000 0.000000
Kd 0.640000 0.000000 0.000000
Ks 1.500000 1.500000 1.500000
Ni 1.000000
d 0.600000
illum 2

newmtl Tire_Rubber
Ns 92.156863
Ka 0.000000 0.000000 0.000000
Kd 0.964000 0.064000 0.064000
Ks 0.000000 0.000000 0.000000
Ni 1.000000
d 0.500000
illum 2

newmtl Tire_Tread
Ns 92.156863
Ka 0.000000 0.000000 0.000000
Kd 0.064000 0.064000 0.064000
Ks 0.000000 0.000000 0.000000
Ni 1.000000
d 1.000000
illum 2

newmtl Turn_signals
Ns 778.431373
Ka 0.000000 0.000000 0.000000
Kd 0.640000 0.384000 0.000000
Ks 1.500000 1.500000 1.500000
Ni 1.000000
d 0.600000
illum 2

newmtl White
Ns 386.274510
Ka 0.000000 0.000000 0.000000
Kd 0.640000 0.640000 0.640000
Ks 0.800000 0.800000 0.800000
Ni 1.000000
d 1.000000
illum 2

newmtl Yellow
Ns 386.274510
Ka 0.000000 0.000000 0.000000
Kd 0.640000 0.640000 0.000000
Ks 0.800000 0.800000 0.800000
Ni 1.000000
d 1.000000
illum 2

*/});